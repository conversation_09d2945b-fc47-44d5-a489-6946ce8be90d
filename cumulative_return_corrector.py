#!/usr/bin/env python3
"""
累计收益率修正器 - 修复跨周累计收益率重置问题

该工具从测试结果日志中提取所有日收益率数据，重新计算真实的累计收益率，
并提供修正后的分析报告。

Author: <PERSON> Code Assistant
Date: 2025-07-29
"""

import re
import json
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from pathlib import Path

@dataclass
class DailyReturn:
    """单日收益率数据结构"""
    date: str
    day_number: int
    week_number: int
    daily_return: float
    net_value: float
    raw_cumulative: float  # 原始累计收益率（可能有重置问题）
    
@dataclass
class WeeklyStats:
    """周统计数据结构"""
    week_number: int
    weekly_return: float
    weekly_sharpe: float
    start_date: str
    end_date: str

class CumulativeReturnCorrector:
    """累计收益率修正器"""
    
    def __init__(self, log_file_path: str):
        self.log_file_path = log_file_path
        self.daily_returns: List[DailyReturn] = []
        self.weekly_stats: List[WeeklyStats] = []
        self.initial_value = 1000000.0  # 初始净值
        
    def extract_data_from_log(self) -> None:
        """从日志文件中提取所有收益率数据"""
        print("📊 开始提取日志数据...")
        
        with open(self.log_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取日收益率数据
        daily_pattern = r'💰 第(\d+)天净值变化: 收益率=([+-]?\d*\.?\d+), 净值=\$(\d+(?:,\d+)*(?:\.\d+)?)'
        daily_matches = re.findall(daily_pattern, content)
        
        # 提取周收益率数据
        weekly_return_pattern = r'周总收益率: ([+-]?\d*\.?\d+)'
        weekly_return_matches = re.findall(weekly_return_pattern, content)
        
        # 提取周夏普比率数据
        weekly_sharpe_pattern = r'周夏普比率: ([+-]?\d*\.?\d+)'
        weekly_sharpe_matches = re.findall(weekly_sharpe_pattern, content)
        
        # 提取累计收益率数据（用于对比）
        cumulative_pattern = r'🤖 TRA 输入: 日期=(\d{4}-\d{2}-\d{2}), 累计收益=([+-]?\d*\.?\d+)'
        cumulative_matches = re.findall(cumulative_pattern, content)
        
        # 创建日期到累计收益率的映射
        date_to_cumulative = {date: float(cum_return) for date, cum_return in cumulative_matches}
        
        print(f"📈 找到 {len(daily_matches)} 条日收益率记录")
        print(f"📈 找到 {len(weekly_return_matches)} 条周收益率记录")
        print(f"📈 找到 {len(weekly_sharpe_matches)} 条周夏普比率记录")
        print(f"📈 找到 {len(cumulative_matches)} 条累计收益率记录")
        
        # 处理日收益率数据
        current_week = 1
        days_in_current_week = 0
        current_date = datetime(2025, 1, 2)  # 起始日期
        
        for i, (day_str, return_str, value_str) in enumerate(daily_matches):
            day_number = int(day_str)
            daily_return = float(return_str)
            net_value = float(value_str.replace(',', ''))
            
            # 估算日期（基于交易日计数）
            date_str = current_date.strftime('%Y-%m-%d')
            
            # 获取原始累计收益率（如果存在）
            raw_cumulative = date_to_cumulative.get(date_str, 0.0)
            
            # 检测是否是新的一周（通过day_number重置检测）
            if day_number == 1 and i > 0:
                current_week += 1
                days_in_current_week = 0
            
            days_in_current_week += 1
            
            self.daily_returns.append(DailyReturn(
                date=date_str,
                day_number=day_number,
                week_number=current_week,
                daily_return=daily_return,
                net_value=net_value,
                raw_cumulative=raw_cumulative
            ))
            
            # 推进到下一个交易日
            current_date += timedelta(days=1)
            # 跳过周末
            while current_date.weekday() >= 5:  # 5=Saturday, 6=Sunday
                current_date += timedelta(days=1)
        
        # 处理周统计数据
        week_idx = 0
        for i in range(len(weekly_return_matches)):
            if week_idx < len(weekly_return_matches) and week_idx < len(weekly_sharpe_matches):
                weekly_return = float(weekly_return_matches[week_idx])
                weekly_sharpe = float(weekly_sharpe_matches[week_idx]) if week_idx < len(weekly_sharpe_matches) else 0.0
                
                # 找到该周的日期范围
                week_days = [d for d in self.daily_returns if d.week_number == week_idx + 1]
                start_date = week_days[0].date if week_days else ""
                end_date = week_days[-1].date if week_days else ""
                
                self.weekly_stats.append(WeeklyStats(
                    week_number=week_idx + 1,
                    weekly_return=weekly_return,
                    weekly_sharpe=weekly_sharpe,
                    start_date=start_date,
                    end_date=end_date
                ))
                
                week_idx += 1
        
        print(f"✅ 数据提取完成：{len(self.daily_returns)} 个交易日，{len(self.weekly_stats)} 周数据")
    
    def calculate_corrected_cumulative_returns(self) -> List[float]:
        """计算修正后的累计收益率"""
        print("🧮 计算修正后的累计收益率...")
        
        corrected_cumulative = []
        cumulative_factor = 1.0
        
        for daily_data in self.daily_returns:
            # 复合收益率计算：(1 + r1) * (1 + r2) * ... - 1
            cumulative_factor *= (1 + daily_data.daily_return)
            corrected_cumulative.append(cumulative_factor - 1.0)
        
        print(f"✅ 累计收益率计算完成，最终收益率: {corrected_cumulative[-1]:.4f}")
        
        return corrected_cumulative
    
    def calculate_rolling_sharpe_ratio(self, returns: List[float], window: int = 5) -> List[float]:
        """计算滚动夏普比率"""
        print("📊 计算滚动夏普比率...")
        
        rolling_sharpe = []
        
        for i in range(len(returns)):
            if i < window - 1:
                rolling_sharpe.append(0.0)
            else:
                # 获取窗口内的日收益率
                window_returns = [self.daily_returns[j].daily_return for j in range(i - window + 1, i + 1)]
                
                if len(window_returns) > 1:
                    mean_return = np.mean(window_returns)
                    std_return = np.std(window_returns, ddof=1)
                    
                    if std_return > 0:
                        # 年化夏普比率（假设252个交易日）
                        sharpe = (mean_return * np.sqrt(252)) / (std_return * np.sqrt(252))
                        rolling_sharpe.append(sharpe)
                    else:
                        rolling_sharpe.append(0.0)
                else:
                    rolling_sharpe.append(0.0)
        
        print("✅ 滚动夏普比率计算完成")
        return rolling_sharpe
    
    def generate_analysis_report(self) -> str:
        """生成分析报告"""
        print("📝 生成分析报告...")
        
        # 计算修正后的累计收益率
        corrected_cumulative = self.calculate_corrected_cumulative_returns()
        rolling_sharpe = self.calculate_rolling_sharpe_ratio(corrected_cumulative)
        
        # 分析数据
        total_days = len(self.daily_returns)
        total_weeks = len(self.weekly_stats)
        final_return = corrected_cumulative[-1] if corrected_cumulative else 0.0
        final_net_value = self.initial_value * (1 + final_return)
        
        # 计算统计指标
        daily_returns_list = [d.daily_return for d in self.daily_returns]
        avg_daily_return = np.mean(daily_returns_list)
        volatility = np.std(daily_returns_list, ddof=1)
        
        # 计算最大回撤
        max_drawdown = 0.0
        peak_value = 1.0
        for cum_return in corrected_cumulative:
            current_value = 1 + cum_return
            if current_value > peak_value:
                peak_value = current_value
            drawdown = (peak_value - current_value) / peak_value
            max_drawdown = max(max_drawdown, drawdown)
        
        # 生成报告
        report_lines = [
            "# GOOG Phase 1 累计收益率修正报告",
            "",
            f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"**数据来源**: {Path(self.log_file_path).name}",
            "",
            "## 📊 总体统计",
            "",
            f"- **交易天数**: {total_days} 天",
            f"- **交易周数**: {total_weeks} 周",
            f"- **初始净值**: ${self.initial_value:,.0f}",
            f"- **最终净值**: ${final_net_value:,.0f}",
            f"- **修正后总收益率**: {final_return:.4f} ({final_return*100:.2f}%)",
            f"- **平均日收益率**: {avg_daily_return:.6f} ({avg_daily_return*100:.4f}%)",
            f"- **收益率波动率**: {volatility:.6f} ({volatility*100:.4f}%)",
            f"- **最大回撤**: {max_drawdown:.4f} ({max_drawdown*100:.2f}%)",
            "",
            "## 📈 修正前后对比",
            "",
            "### 问题识别",
            "",
        ]
        
        # 分析原始累计收益率重置问题
        reset_weeks = []
        for i, daily_data in enumerate(self.daily_returns):
            if i > 0 and daily_data.raw_cumulative == 0.0 and corrected_cumulative[i] != 0.0:
                reset_weeks.append(daily_data.week_number)
        
        reset_weeks = list(set(reset_weeks))
        if reset_weeks:
            report_lines.extend([
                f"发现 **{len(reset_weeks)}** 周存在累计收益率重置问题：",
                f"- 重置周数: {', '.join(map(str, sorted(reset_weeks)))}",
                "",
            ])
        else:
            report_lines.append("✅ 未发现累计收益率重置问题")
        
        # 每日数据对比表
        report_lines.extend([
            "### 每日数据详情",
            "",
            "| 日期 | 周 | 日收益率 | 原始累计收益率 | 修正累计收益率 | 差异 | 净值 |",
            "|------|----|---------:|---------------:|---------------:|-----:|-----:|",
        ])
        
        for i, daily_data in enumerate(self.daily_returns):
            corrected_cum = corrected_cumulative[i]
            difference = corrected_cum - daily_data.raw_cumulative
            
            report_lines.append(
                f"| {daily_data.date} | 第{daily_data.week_number}周 | "
                f"{daily_data.daily_return:+.4f} | {daily_data.raw_cumulative:.4f} | "
                f"{corrected_cum:.4f} | {difference:+.4f} | ${daily_data.net_value:,.0f} |"
            )
        
        # 周度汇总
        report_lines.extend([
            "",
            "### 周度汇总",
            "",
            "| 周数 | 日期范围 | 周收益率 | 修正累计收益率 | 原周夏普比率 |",
            "|------|----------|----------:|---------------:|-----------:|",
        ])
        
        for week_stat in self.weekly_stats:
            # 找到该周最后一天的修正累计收益率
            week_end_days = [i for i, d in enumerate(self.daily_returns) if d.week_number == week_stat.week_number]
            if week_end_days:
                week_end_cumulative = corrected_cumulative[week_end_days[-1]]
                
                report_lines.append(
                    f"| 第{week_stat.week_number}周 | {week_stat.start_date} ~ {week_stat.end_date} | "
                    f"{week_stat.weekly_return:+.4f} | {week_end_cumulative:.4f} | {week_stat.weekly_sharpe:.4f} |"
                )
        
        return "\n".join(report_lines)
    
    def create_visualization(self, output_dir: str = "test_results") -> str:
        """创建可视化图表"""
        print("📊 创建可视化图表...")
        
        # 计算修正后的数据
        corrected_cumulative = self.calculate_corrected_cumulative_returns()
        rolling_sharpe = self.calculate_rolling_sharpe_ratio(corrected_cumulative)
        
        # 准备数据
        dates = [datetime.strptime(d.date, '%Y-%m-%d') for d in self.daily_returns]
        daily_returns_pct = [d.daily_return * 100 for d in self.daily_returns]
        corrected_cumulative_pct = [c * 100 for c in corrected_cumulative]
        raw_cumulative_pct = [d.raw_cumulative * 100 for d in self.daily_returns]
        
        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('GOOG Phase 1 累计收益率修正分析', fontsize=16, fontweight='bold')
        
        # 子图1: 日收益率
        ax1.bar(dates, daily_returns_pct, alpha=0.7, color='steelblue')
        ax1.set_title('日收益率 (%)', fontsize=12, fontweight='bold')
        ax1.set_ylabel('收益率 (%)')
        ax1.grid(True, alpha=0.3)
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax1.tick_params(axis='x', rotation=45)
        
        # 子图2: 累计收益率对比
        ax2.plot(dates, raw_cumulative_pct, label='原始累计收益率', color='red', alpha=0.7, linestyle='--')
        ax2.plot(dates, corrected_cumulative_pct, label='修正累计收益率', color='green', linewidth=2)
        ax2.set_title('累计收益率对比 (%)', fontsize=12, fontweight='bold')
        ax2.set_ylabel('累计收益率 (%)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax2.tick_params(axis='x', rotation=45)
        
        # 子图3: 净值曲线
        net_values = [d.net_value for d in self.daily_returns]
        ax3.plot(dates, net_values, color='purple', linewidth=2)
        ax3.set_title('净值曲线 ($)', fontsize=12, fontweight='bold')
        ax3.set_ylabel('净值 ($)')
        ax3.grid(True, alpha=0.3)
        ax3.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax3.tick_params(axis='x', rotation=45)
        
        # 子图4: 滚动夏普比率
        ax4.plot(dates, rolling_sharpe, color='orange', linewidth=2)
        ax4.set_title('5日滚动夏普比率', fontsize=12, fontweight='bold')
        ax4.set_ylabel('夏普比率')
        ax4.grid(True, alpha=0.3)
        ax4.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        ax4.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        
        # 保存图表
        output_path = Path(output_dir) / f"GOOG_cumulative_return_analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
        output_path.parent.mkdir(exist_ok=True)
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 图表已保存到: {output_path}")
        return str(output_path)

def main():
    """主函数"""
    log_file = "/Users/<USER>/Code/Multi_Agent_Optimization/test_results/GOOG_phase1.md"
    
    print("🚀 GOOG Phase 1 累计收益率修正工具启动")
    print(f"📂 日志文件: {log_file}")
    
    # 创建修正器
    corrector = CumulativeReturnCorrector(log_file)
    
    # 提取数据
    corrector.extract_data_from_log()
    
    # 生成报告
    report = corrector.generate_analysis_report()
    
    # 保存报告
    report_path = "test_results/GOOG_cumulative_return_report.md"
    Path(report_path).parent.mkdir(exist_ok=True)
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"📝 分析报告已保存到: {report_path}")
    
    # 创建可视化
    chart_path = corrector.create_visualization()
    
    print("🎉 累计收益率修正分析完成！")
    print(f"📊 查看报告: {report_path}")
    print(f"📈 查看图表: {chart_path}")

if __name__ == "__main__":
    main()