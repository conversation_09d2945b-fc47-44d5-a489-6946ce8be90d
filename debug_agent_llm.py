#!/usr/bin/env python3
"""
Debug script to investigate agent <PERSON><PERSON> calling issue
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from dotenv import load_dotenv
load_dotenv()

from contribution_assessment.llm_interface import LLMInterface
from agents.agent_factory import AgentFactory
import logging

def debug_agent_llm_setup():
    """Debug agent LLM setup and calling mechanism"""
    print("=" * 60)
    print("🔍 Debug Agent LLM Setup")
    print("=" * 60)
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    # Test different LLM providers
    providers = ["zhipuai", "mock"]
    
    for provider in providers:
        print(f"\n🧪 Testing provider: {provider}")
        print("-" * 40)
        
        try:
            # Create LLM interface
            llm_interface = LLMInterface(provider=provider, logger=logger)
            print(f"✅ LLM Interface created: {llm_interface}")
            print(f"   Provider: {llm_interface.provider}")
            print(f"   Client: {llm_interface.client}")
            
            # Create agent factory
            agent_factory = AgentFactory(
                llm_interface=llm_interface,
                logger=logger,
                opro_enabled=False
            )
            
            # Create TRA agent
            tra_agent = agent_factory.create_agent("TRA")
            print(f"✅ TRA Agent created: {tra_agent}")
            print(f"   Agent ID: {tra_agent.agent_id}")
            print(f"   LLM Interface: {tra_agent.llm_interface}")
            print(f"   Has LLM: {tra_agent.llm_interface is not None}")
            
            if tra_agent.llm_interface:
                print(f"   LLM Provider: {tra_agent.llm_interface.provider}")
                print(f"   LLM Client: {tra_agent.llm_interface.client}")
            
            # Test a simple state
            test_state = {
                "current_date": "2025-01-01",
                "cash": 1000000,
                "positions": {},
                "analyst_outputs": {"NAA": {"analysis": "test"}},
                "outlook_outputs": {"BOA": {"outlook": "bullish"}},
                "trading_day_info": {"is_trading_day": True, "has_price_data": True}
            }
            
            print(f"\n🔄 Testing agent process method...")
            try:
                result = tra_agent.process(test_state, is_full_coalition=True)
                print(f"✅ Agent process result: {result}")
                action = result.get("action", "unknown")
                print(f"   Trading action: {action}")
                if action == "hold":
                    print("⚠️  Agent returned HOLD action!")
                else:
                    print(f"✅ Agent returned action: {action}")
            except Exception as e:
                print(f"❌ Agent process failed: {e}")
            
        except Exception as e:
            print(f"❌ Provider {provider} test failed: {e}")
    
    # Test environment variables
    print(f"\n🔧 Environment Variables:")
    print(f"   ZHIPUAI_API_KEY: {'SET' if os.getenv('ZHIPUAI_API_KEY') else 'NOT SET'}")
    if os.getenv('ZHIPUAI_API_KEY'):
        key = os.getenv('ZHIPUAI_API_KEY')
        print(f"   Key preview: {key[:10]}...")

def debug_weekly_io_data():
    """Debug weekly IO data attributes"""
    print(f"\n🔍 Debug Weekly IO Data:")
    print("-" * 40)
    
    try:
        llm_interface = LLMInterface(provider="mock")
        agent_factory = AgentFactory(llm_interface=llm_interface, opro_enabled=False)
        tra_agent = agent_factory.create_agent("TRA")
        
        print(f"TRA Agent type: {type(tra_agent)}")
        print(f"Has weekly_io_data: {hasattr(tra_agent, 'weekly_io_data')}")
        
        if hasattr(tra_agent, 'weekly_io_data'):
            print(f"weekly_io_data value: {tra_agent.weekly_io_data}")
        else:
            print("❌ Missing weekly_io_data attribute!")
            
        # Check if it's an OPRO agent
        print(f"Is OPRO agent: {hasattr(tra_agent, 'record_weekly_io')}")
        
    except Exception as e:
        print(f"❌ Weekly IO debug failed: {e}")

if __name__ == "__main__":
    debug_agent_llm_setup()
    debug_weekly_io_data()