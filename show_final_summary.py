#!/usr/bin/env python3
"""
批量分析结果汇总展示
"""

import json
import os

def load_summary_data():
    """加载汇总数据"""
    summary_file = "/Users/<USER>/Code/Multi_Agent_Optimization/batch_analysis_results/all_stocks_summary.json"
    
    with open(summary_file, 'r', encoding='utf-8') as f:
        return json.load(f)

def print_final_summary():
    """打印最终汇总结果"""
    
    data = load_summary_data()
    
    print("🏆 四大科技股17周投资表现最终总结")
    print("="*60)
    
    # 计算每个股票的关键指标
    results = []
    
    for stock, stock_data in data.items():
        weekly_returns = [w['周总收益率'] for w in stock_data['weekly_data']]
        
        # 计算总收益率
        total_return = stock_data['total_return']
        
        # 计算年化收益率
        periods = len(weekly_returns)
        cumulative = 1 + total_return
        annualized_return = (cumulative ** (52/periods)) - 1
        
        # 计算胜率
        profit_weeks = len([r for r in weekly_returns if r > 0])
        win_rate = profit_weeks / len(weekly_returns)
        
        # 计算最大回撤
        cumulative_values = []
        cum = 1.0
        for r in weekly_returns:
            cum *= (1 + r)
            cumulative_values.append(cum)
        
        max_drawdown = 0
        peak = cumulative_values[0]
        for value in cumulative_values:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        
        results.append({
            'stock': stock,
            'total_return': total_return,
            'annualized_return': annualized_return,
            'win_rate': win_rate,
            'max_weekly': max(weekly_returns),
            'min_weekly': min(weekly_returns),
            'max_drawdown': max_drawdown,
            'volatility': sum([(r - sum(weekly_returns)/len(weekly_returns))**2 for r in weekly_returns]) / len(weekly_returns)
        })
    
    # 按总收益率排序
    results.sort(key=lambda x: x['total_return'], reverse=True)
    
    print(f"{'排名':>2} {'股票':>6} {'总收益率':>8} {'年化收益':>8} {'胜率':>6} {'最大回撤':>8} {'波动率':>8}")
    print("-" * 60)
    
    for i, result in enumerate(results, 1):
        emoji = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else "💧"
        
        print(f"{emoji} {result['stock']:>6} "
              f"{result['total_return']*100:>7.2f}% "
              f"{result['annualized_return']*100:>7.2f}% "
              f"{result['win_rate']*100:>5.1f}% "
              f"{result['max_drawdown']*100:>7.2f}% "
              f"{result['volatility']*10000:>7.2f}")
    
    print("\n📊 关键发现:")
    print("-" * 60)
    
    best_performer = results[0]
    worst_performer = results[-1]
    
    print(f"🏆 最佳表现: {best_performer['stock']} (+{best_performer['total_return']*100:.2f}%)")
    print(f"📉 最差表现: {worst_performer['stock']} ({worst_performer['total_return']*100:.2f}%)")
    
    # 计算平均表现
    avg_return = sum([r['total_return'] for r in results]) / len(results)
    print(f"📈 平均表现: {avg_return*100:.2f}%")
    
    # 找出胜率最高的
    best_winrate = max(results, key=lambda x: x['win_rate'])
    print(f"🎯 最高胜率: {best_winrate['stock']} ({best_winrate['win_rate']*100:.1f}%)")
    
    # 最稳定的（最小波动率）
    most_stable = min(results, key=lambda x: x['volatility'])
    print(f"🛡️  最稳定股票: {most_stable['stock']} (波动率: {most_stable['volatility']*10000:.2f})")
    
    print(f"\n💡 投资建议:")
    print("-" * 60)
    if best_performer['total_return'] > 0:
        print(f"✅ {best_performer['stock']} 表现最佳，适合成长型投资者")
    else:
        print("⚠️  所有股票均为负收益，需谨慎投资")
    
    if most_stable['stock'] != best_performer['stock']:
        print(f"✅ {most_stable['stock']} 波动最小，适合保守型投资者")
    
    positive_returns = [r for r in results if r['total_return'] > 0]
    if len(positive_returns) > 1:
        print(f"📈 {len(positive_returns)}/{len(results)} 股票实现正收益，可考虑分散投资")
    
    print(f"\n📁 详细报告和图表请查看:")
    print("   📊 /Users/<USER>/Code/Multi_Agent_Optimization/batch_analysis_results/")

if __name__ == "__main__":
    print_final_summary()
