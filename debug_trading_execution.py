#!/usr/bin/env python3
"""
调试交易执行问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_trading_env import StockTradingEnv

def debug_trading_execution():
    """调试交易执行逻辑"""
    print("🔍 调试交易执行问题...")

    try:
        config = {
            "start_date": "2025-01-02",
            "end_date": "2025-01-06",
            "stocks": ["AAPL"],
            "starting_cash": 1000000,
            "coalition_size": 7
        }

        env = StockTradingEnv(config)
        state, info = env.reset()

        print(f"初始状态:")
        print(f"  净值: ${env.net_worth:.2f}")
        print(f"  持仓: {env.positions}")
        print(f"  累计收益率: {env.cumulative_return:.6f}")
        print(f"  当前日索引: {env.current_day_index}")
        print(f"  总交易日数: {env.total_days}")

        # 获取第一天的价格
        current_prices = {}
        for stock in env.stocks:
            if env.current_day_index < len(env.price_data[stock]):
                current_prices[stock] = env.price_data[stock].iloc[env.current_day_index]["close"]
                print(f"  {stock} 价格数据长度: {len(env.price_data[stock])}")

        print(f"第一天价格: {current_prices}")

        # 执行买入操作
        action = {"AAPL": 1.0}  # 全仓买入
        print(f"执行交易动作: {action}")

        # 记录交易前的状态
        pre_positions = env.positions.copy()
        pre_cumulative_return = env.cumulative_return

        state, reward, done, info = env.step(action)

        print(f"交易后状态:")
        print(f"  净值: ${env.net_worth:.2f}")
        print(f"  持仓: {env.positions}")
        print(f"  累计收益率: {env.cumulative_return:.6f}")
        print(f"  日收益率: {reward:.6f}")
        print(f"  当前日索引: {env.current_day_index}")

        # 检查持仓是否正确设置
        if env.positions.get("AAPL", 0) > 0:
            print(f"✅ 持仓设置成功: {env.positions['AAPL']} 股")

            # 计算预期持仓价值
            price = current_prices["AAPL"]
            expected_shares = int(env.starting_cash * 0.99 / price)
            expected_value = expected_shares * price

            print(f"预期买入股数: {expected_shares}")
            print(f"预期持仓价值: ${expected_value:.2f}")

            # 检查收益率计算
            print(f"\n🔍 收益率计算分析:")
            print(f"  交易前累计收益率: {pre_cumulative_return:.6f}")
            print(f"  交易后累计收益率: {env.cumulative_return:.6f}")

            # 获取下一天价格用于收益率计算
            if env.current_day_index < len(env.price_data["AAPL"]):
                next_price = env.price_data["AAPL"].iloc[env.current_day_index]["close"]
                print(f"  下一天价格: ${next_price:.2f}")

                # 计算预期收益率
                if price > 0 and next_price > 0:
                    expected_return = (next_price / price) - 1
                    print(f"  预期日收益率: {expected_return:.6f}")
                    print(f"  实际日收益率: {reward:.6f}")

                    if abs(expected_return - reward) < 0.000001:
                        print("✅ 收益率计算正确")
                    else:
                        print("❌ 收益率计算有误")
            else:
                print("  ⚠️ 没有下一天价格数据")

        else:
            print("❌ 持仓设置失败")
            print(f"  交易前持仓: {pre_positions}")
            print(f"  交易后持仓: {env.positions}")

        return True

    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_zero_return_case():
    """调试零收益率的情况"""
    print("\n🔍 调试零收益率情况...")

    try:
        config = {
            "start_date": "2025-01-02",
            "end_date": "2025-01-06",
            "stocks": ["AAPL"],
            "starting_cash": 1000000,
            "coalition_size": 7
        }

        env = StockTradingEnv(config)
        state, info = env.reset()

        # 模拟第一天没有持仓的情况
        print(f"模拟第一天空仓情况:")
        print(f"  持仓: {env.positions}")

        # 获取价格
        current_prices = {}
        for stock in env.stocks:
            if env.current_day_index < len(env.price_data[stock]):
                current_prices[stock] = env.price_data[stock].iloc[env.current_day_index]["close"]

        # 移动到下一天但不执行交易
        env.current_day_index += 1
        if env.current_day_index < env.total_days:
            env.current_date = env.trading_days[env.current_day_index]

        # 获取下一天价格
        new_day_prices = {}
        if env.current_day_index < len(env.price_data["AAPL"]):
            new_day_prices["AAPL"] = env.price_data["AAPL"].iloc[env.current_day_index]["close"]

        print(f"当前价格: {current_prices}")
        print(f"下一天价格: {new_day_prices}")

        # 计算收益率（空仓情况）
        daily_return = env._calculate_daily_return_from_price_change_with_new_prices(current_prices, new_day_prices)
        print(f"空仓时的日收益率: {daily_return:.6f}")

        # 现在设置持仓并重新计算
        env.positions["AAPL"] = 4459  # 设置持仓
        daily_return_with_position = env._calculate_daily_return_from_price_change_with_new_prices(current_prices, new_day_prices)
        print(f"有持仓时的日收益率: {daily_return_with_position:.6f}")

        return True

    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_step_by_step():
    """逐步调试step方法的执行过程"""
    print("\n🔍 逐步调试step方法执行过程...")

    try:
        config = {
            "start_date": "2025-01-02",
            "end_date": "2025-01-06",
            "stocks": ["AAPL"],
            "starting_cash": 1000000,
            "coalition_size": 7
        }

        env = StockTradingEnv(config)
        state, info = env.reset()

        print(f"步骤1 - 初始状态:")
        print(f"  持仓: {env.positions}")
        print(f"  当前日索引: {env.current_day_index}")

        # 获取当前价格（模拟step方法中的逻辑）
        current_prices = {}
        for stock in env.stocks:
            if env.current_day_index < len(env.price_data[stock]):
                current_prices[stock] = env.price_data[stock].iloc[env.current_day_index]["close"]

        print(f"步骤2 - 当前价格: {current_prices}")

        # 执行交易（模拟step方法中的交易逻辑）
        action = {"AAPL": 1.0}
        stock = "AAPL"
        price = current_prices[stock]

        print(f"步骤3 - 执行买入操作:")
        print(f"  交易前持仓: {env.positions}")

        # 模拟买入逻辑
        current_portfolio_value = env.starting_cash * (1 + env.cumulative_return)
        affordable_amount = current_portfolio_value * 0.99
        shares_to_buy = int(affordable_amount / price)
        env.positions[stock] = shares_to_buy

        print(f"  交易后持仓: {env.positions}")
        print(f"  买入股数: {shares_to_buy}")

        # 移动到下一天（模拟step方法中的逻辑）
        print(f"步骤4 - 移动到下一天:")
        print(f"  移动前日索引: {env.current_day_index}")

        env.current_day_index += 1
        if env.current_day_index < env.total_days:
            env.current_date = env.trading_days[env.current_day_index]

        print(f"  移动后日索引: {env.current_day_index}")

        # 获取新一天的价格
        new_day_prices = {}
        if env.current_day_index < len(env.price_data[stock]):
            new_day_prices[stock] = env.price_data[stock].iloc[env.current_day_index]["close"]

        print(f"步骤5 - 新一天价格: {new_day_prices}")

        # 计算收益率
        print(f"步骤6 - 计算收益率:")
        print(f"  持仓状态: {env.positions}")

        daily_return = env._calculate_daily_return_from_price_change_with_new_prices(current_prices, new_day_prices)
        print(f"  计算的日收益率: {daily_return:.6f}")

        # 手动验证收益率计算
        position = env.positions.get(stock, 0)
        print(f"  验证 - 持仓数量: {position}")

        if position > 0:
            previous_price = current_prices.get(stock, 0)
            current_price = new_day_prices.get(stock, 0)
            if previous_price > 0 and current_price > 0:
                price_return = (current_price / previous_price) - 1
                expected_return = price_return / len(env.stocks)
                print(f"  验证 - 价格变动: ${previous_price:.2f} -> ${current_price:.2f}")
                print(f"  验证 - 价格收益率: {price_return:.6f}")
                print(f"  验证 - 预期日收益率: {expected_return:.6f}")
        else:
            print(f"  验证 - 空仓，收益率为0")

        return True

    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_simplified_opro_service():
    """测试SimplifiedOPROService的状态构建"""
    print("\n🔍 测试SimplifiedOPROService状态构建...")

    try:
        from contribution_assessment.services.simplified_opro_service import SimplifiedOPROService
        from stock_trading_env import StockTradingEnv

        config = {
            "start_date": "2025-01-02",
            "end_date": "2025-01-06",
            "stocks": ["NVDA"],
            "symbol": "NVDA",
            "starting_cash": 1000000,
            "coalition_size": 7
        }

        env = StockTradingEnv(config)
        full_state, info = env.reset()

        print(f"完整环境状态中的股票字段:")
        print(f"  symbol: {full_state.get('symbol')}")
        print(f"  symbols: {full_state.get('symbols')}")

        # 创建SimplifiedOPROService实例
        opro_service = SimplifiedOPROService({})

        # 测试TRA的状态构建
        tra_state = opro_service._build_agent_input_state(full_state, "TRA")

        print(f"TRA智能体接收到的状态字段:")
        print(f"  symbol: {tra_state.get('symbol')}")
        print(f"  symbols: {tra_state.get('symbols')}")
        print(f"  所有字段: {list(tra_state.keys())}")

        # 模拟TRA智能体的股票代码解析
        symbol = (tra_state.get("symbol") or
                 tra_state.get("stock") or
                 tra_state.get("ticker") or
                 (tra_state.get("stocks", [None])[0] if tra_state.get("stocks") else None) or
                 "AAPL")  # 最后的默认值

        print(f"TRA智能体解析的股票代码: {symbol}")

        return symbol == "NVDA"

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_tra_state_fields():
    """测试TRA智能体接收到的状态字段"""
    print("\n🔍 测试TRA智能体状态字段...")

    try:
        from agents.trader_agent import TraderAgent
        from stock_trading_env import StockTradingEnv

        config = {
            "start_date": "2025-01-02",
            "end_date": "2025-01-06",
            "stocks": ["NVDA"],
            "symbol": "NVDA",
            "starting_cash": 1000000,
            "coalition_size": 7
        }

        env = StockTradingEnv(config)
        state, info = env.reset()

        print(f"环境状态中的股票字段:")
        print(f"  symbol: {state.get('symbol')}")
        print(f"  symbols: {state.get('symbols')}")
        print(f"  stocks: {state.get('stocks')}")

        # 模拟TRA智能体处理
        tra = TraderAgent()

        # 模拟TRA的process方法中的逻辑
        symbol = (state.get("symbol") or
                 state.get("stock") or
                 state.get("ticker") or
                 (state.get("stocks", [None])[0] if state.get("stocks") else None) or
                 "AAPL")  # 最后的默认值

        print(f"TRA智能体解析的股票代码: {symbol}")

        # 测试交易动作生成
        mock_result = {"action": "buy"}
        if mock_result.get("action", "hold").lower() == "buy":
            trading_actions = {symbol: 1.0}
        else:
            trading_actions = {"__HOLD__": 0.0}

        print(f"生成的交易动作: {trading_actions}")

        return symbol == "NVDA"

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_nvda_trading():
    """测试NVDA交易的情况"""
    print("\n🔍 测试NVDA交易...")

    try:
        config = {
            "start_date": "2025-01-02",
            "end_date": "2025-01-06",
            "stocks": ["NVDA"],
            "symbol": "NVDA",
            "starting_cash": 1000000,
            "coalition_size": 7
        }

        env = StockTradingEnv(config)

        # 确保没有portfolio_tracker（模拟你的环境）
        env._portfolio_tracker = None

        state, info = env.reset()

        print(f"环境设置:")
        print(f"  portfolio_tracker: {getattr(env, '_portfolio_tracker', 'None')}")
        print(f"  初始持仓: {env.positions}")
        print(f"  初始累计收益率: {env.cumulative_return:.6f}")

        # 执行买入操作
        action = {"NVDA": 1.0}
        print(f"执行交易动作: {action}")

        state, reward, done, info = env.step(action)

        print(f"交易后状态:")
        print(f"  持仓: {env.positions}")
        print(f"  累计收益率: {env.cumulative_return:.6f}")
        print(f"  日收益率: {reward:.6f}")
        print(f"  净值: ${env.net_worth:.2f}")

        # 检查是否有持仓但收益率为0的情况
        if env.positions.get("NVDA", 0) > 0 and reward == 0.0:
            print("❌ 发现问题：有持仓但收益率为0")

            # 深入调试收益率计算
            print("\n🔍 深入调试收益率计算:")

            # 获取价格数据
            current_day_index = env.current_day_index
            print(f"  当前日索引: {current_day_index}")
            print(f"  价格数据长度: {len(env.price_data['AAPL'])}")

            if current_day_index > 0 and current_day_index < len(env.price_data['NVDA']):
                prev_price = env.price_data['NVDA'].iloc[current_day_index - 1]['close']
                curr_price = env.price_data['NVDA'].iloc[current_day_index]['close']
                print(f"  前一日价格: ${prev_price:.2f}")
                print(f"  当前价格: ${curr_price:.2f}")

                expected_return = (curr_price / prev_price) - 1
                print(f"  预期收益率: {expected_return:.6f}")

                # 检查持仓状态
                position = env.positions.get("NVDA", 0)
                print(f"  持仓数量: {position}")

                if position > 0:
                    print("  ✅ 有持仓，应该有收益率")
                else:
                    print("  ❌ 无持仓，收益率为0是正确的")
        else:
            print("✅ 交易执行正常")

        return True

    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    debug_trading_execution()
    debug_zero_return_case()
    debug_step_by_step()
    test_simplified_opro_service()
    test_tra_state_fields()
    debug_nvda_trading()
