"""
数据传输对象(DTO)模块

定义系统中各个阶段和服务之间传递的数据结构，包括：
- 评估请求和结果
- 各阶段的结果DTO
- 数据验证和序列化功能
"""

from .assessment_dto import AssessmentRequest, AssessmentResult
from .phase_results_dto import CoalitionResult, SimulationResult, ShapleyResult, OPROResult
from .validation import validate_dto, ValidationError
from .serialization import serialize_dto, deserialize_dto

__all__ = [
    'AssessmentRequest', 'AssessmentResult',
    'CoalitionResult', 'SimulationResult', 'ShapleyResult', 'OPROResult',
    'validate_dto', 'ValidationError',
    'serialize_dto', 'deserialize_dto'
]