"""
数据传输对象序列化模块

提供DTO的序列化和反序列化功能，支持JSON格式
"""

import json
import pickle
from typing import Any, Dict, Type, TypeVar, Union
from dataclasses import asdict, is_dataclass
from datetime import datetime
from decimal import Decimal
import logging

T = TypeVar('T')

logger = logging.getLogger(__name__)


class SerializationError(Exception):
    """序列化异常"""
    pass


def serialize_dto(dto_instance: Any, format: str = 'json') -> Union[str, bytes]:
    """
    序列化DTO实例
    
    Args:
        dto_instance: 要序列化的DTO实例
        format: 序列化格式 ('json' 或 'pickle')
        
    Returns:
        序列化后的数据
        
    Raises:
        SerializationError: 序列化失败时抛出
    """
    try:
        if format.lower() == 'json':
            return _serialize_to_json(dto_instance)
        elif format.lower() == 'pickle':
            return _serialize_to_pickle(dto_instance)
        else:
            raise SerializationError(f"Unsupported serialization format: {format}")
    except Exception as e:
        logger.error(f"Serialization failed: {str(e)}")
        raise SerializationError(f"Failed to serialize DTO: {str(e)}")


def deserialize_dto(data: Union[str, bytes], dto_class: Type[T], format: str = 'json') -> T:
    """
    反序列化为DTO实例
    
    Args:
        data: 序列化的数据
        dto_class: 目标DTO类
        format: 序列化格式 ('json' 或 'pickle')
        
    Returns:
        反序列化的DTO实例
        
    Raises:
        SerializationError: 反序列化失败时抛出
    """
    try:
        if format.lower() == 'json':
            return _deserialize_from_json(data, dto_class)
        elif format.lower() == 'pickle':
            return _deserialize_from_pickle(data, dto_class)
        else:
            raise SerializationError(f"Unsupported deserialization format: {format}")
    except Exception as e:
        logger.error(f"Deserialization failed: {str(e)}")
        raise SerializationError(f"Failed to deserialize DTO: {str(e)}")


def _serialize_to_json(dto_instance: Any) -> str:
    """序列化为JSON字符串"""
    if not is_dataclass(dto_instance):
        raise SerializationError("Object is not a dataclass")
    
    # 转换为字典
    data_dict = asdict(dto_instance)
    
    # 处理特殊类型
    processed_dict = _process_for_json_serialization(data_dict)
    
    # 添加类型信息
    processed_dict['__class__'] = dto_instance.__class__.__name__
    processed_dict['__module__'] = dto_instance.__class__.__module__
    
    return json.dumps(processed_dict, ensure_ascii=False, indent=2)


def _serialize_to_pickle(dto_instance: Any) -> bytes:
    """序列化为pickle字节"""
    return pickle.dumps(dto_instance)


def _deserialize_from_json(json_data: str, dto_class: Type[T]) -> T:
    """从JSON字符串反序列化"""
    try:
        data_dict = json.loads(json_data)
    except json.JSONDecodeError as e:
        raise SerializationError(f"Invalid JSON data: {str(e)}")
    
    # 验证类型信息
    if '__class__' in data_dict:
        class_name = data_dict.pop('__class__')
        module_name = data_dict.pop('__module__', None)
        
        if class_name != dto_class.__name__:
            logger.warning(f"Class name mismatch: expected {dto_class.__name__}, got {class_name}")
    
    # 处理特殊类型
    processed_dict = _process_for_json_deserialization(data_dict)
    
    # 创建实例
    try:
        return dto_class(**processed_dict)
    except TypeError as e:
        raise SerializationError(f"Failed to create {dto_class.__name__} instance: {str(e)}")


def _deserialize_from_pickle(pickle_data: bytes, dto_class: Type[T]) -> T:
    """从pickle字节反序列化"""
    try:
        instance = pickle.loads(pickle_data)
        if not isinstance(instance, dto_class):
            raise SerializationError(f"Deserialized object is not of type {dto_class.__name__}")
        return instance
    except pickle.PickleError as e:
        raise SerializationError(f"Pickle deserialization failed: {str(e)}")


def _process_for_json_serialization(obj: Any) -> Any:
    """
    处理对象以便JSON序列化
    
    处理datetime、set、frozenset等不能直接JSON序列化的类型
    """
    if isinstance(obj, dict):
        return {key: _process_for_json_serialization(value) for key, value in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [_process_for_json_serialization(item) for item in obj]
    elif isinstance(obj, set):
        return {
            '__type__': 'set',
            '__value__': [_process_for_json_serialization(item) for item in obj]
        }
    elif isinstance(obj, frozenset):
        return {
            '__type__': 'frozenset',
            '__value__': [_process_for_json_serialization(item) for item in obj]
        }
    elif isinstance(obj, datetime):
        return {
            '__type__': 'datetime',
            '__value__': obj.isoformat()
        }
    elif isinstance(obj, Decimal):
        return {
            '__type__': 'decimal',
            '__value__': str(obj)
        }
    elif hasattr(obj, '__dict__') and not isinstance(obj, (str, int, float, bool)):
        # 处理嵌套的dataclass或其他对象
        if is_dataclass(obj):
            return _process_for_json_serialization(asdict(obj))
        else:
            return _process_for_json_serialization(obj.__dict__)
    else:
        return obj


def _process_for_json_deserialization(obj: Any) -> Any:
    """
    处理JSON反序列化后的对象
    
    恢复datetime、set、frozenset等类型
    """
    if isinstance(obj, dict):
        # 检查是否是特殊类型标记
        if '__type__' in obj and '__value__' in obj:
            type_name = obj['__type__']
            value = obj['__value__']
            
            if type_name == 'set':
                return set(_process_for_json_deserialization(item) for item in value)
            elif type_name == 'frozenset':
                return frozenset(_process_for_json_deserialization(item) for item in value)
            elif type_name == 'datetime':
                return datetime.fromisoformat(value)
            elif type_name == 'decimal':
                return Decimal(value)
            else:
                logger.warning(f"Unknown special type: {type_name}")
                return obj
        else:
            return {key: _process_for_json_deserialization(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [_process_for_json_deserialization(item) for item in obj]
    else:
        return obj


def dto_to_dict(dto_instance: Any, include_metadata: bool = False) -> Dict[str, Any]:
    """
    将DTO转换为字典
    
    Args:
        dto_instance: DTO实例
        include_metadata: 是否包含元数据信息
        
    Returns:
        字典表示
    """
    if not is_dataclass(dto_instance):
        raise SerializationError("Object is not a dataclass")
    
    result = asdict(dto_instance)
    
    if include_metadata:
        result['__metadata__'] = {
            'class_name': dto_instance.__class__.__name__,
            'module_name': dto_instance.__class__.__module__,
            'serialization_timestamp': datetime.now().isoformat()
        }
    
    return result


def dict_to_dto(data_dict: Dict[str, Any], dto_class: Type[T]) -> T:
    """
    从字典创建DTO实例
    
    Args:
        data_dict: 数据字典
        dto_class: 目标DTO类
        
    Returns:
        DTO实例
    """
    # 移除元数据
    clean_dict = {k: v for k, v in data_dict.items() if not k.startswith('__')}
    
    try:
        return dto_class(**clean_dict)
    except TypeError as e:
        raise SerializationError(f"Failed to create {dto_class.__name__} from dict: {str(e)}")


def validate_serialization_roundtrip(dto_instance: Any, format: str = 'json') -> bool:
    """
    验证序列化往返是否保持数据完整性
    
    Args:
        dto_instance: 要测试的DTO实例
        format: 序列化格式
        
    Returns:
        bool: 往返测试是否成功
    """
    try:
        # 序列化
        serialized = serialize_dto(dto_instance, format)
        
        # 反序列化
        deserialized = deserialize_dto(serialized, type(dto_instance), format)
        
        # 比较（简单比较，可能需要根据具体需求调整）
        if format == 'json':
            # JSON序列化可能会丢失一些类型信息，所以比较字典表示
            original_dict = dto_to_dict(dto_instance)
            deserialized_dict = dto_to_dict(deserialized)
            return original_dict == deserialized_dict
        else:
            # pickle应该完全保持对象状态
            return dto_instance == deserialized
            
    except Exception as e:
        logger.error(f"Serialization roundtrip test failed: {str(e)}")
        return False