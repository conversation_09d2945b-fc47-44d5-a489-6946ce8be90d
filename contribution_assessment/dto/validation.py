"""
数据传输对象验证模块

提供DTO的数据验证功能，包括类型检查、值范围验证等
"""

from typing import Any, Dict, List, Optional, Type, get_type_hints
from dataclasses import is_dataclass, fields
from datetime import datetime
import logging


class ValidationError(Exception):
    """数据验证异常"""
    def __init__(self, message: str, field_name: Optional[str] = None, value: Any = None):
        self.field_name = field_name
        self.value = value
        super().__init__(message)


def validate_dto(dto_instance: Any, strict: bool = True) -> bool:
    """
    验证DTO实例的数据完整性和类型正确性
    
    Args:
        dto_instance: 要验证的DTO实例
        strict: 是否启用严格模式验证
        
    Returns:
        bool: 验证是否通过
        
    Raises:
        ValidationError: 验证失败时抛出
    """
    if not is_dataclass(dto_instance):
        raise ValidationError("Object is not a dataclass")
    
    # 获取类型提示
    type_hints = get_type_hints(type(dto_instance))
    
    # 验证每个字段
    for field in fields(dto_instance):
        field_name = field.name
        field_value = getattr(dto_instance, field_name)
        expected_type = type_hints.get(field_name)
        
        # 跳过私有字段
        if field_name.startswith('_'):
            continue
            
        # 验证必需字段
        if field.default is None and field.default_factory is None:
            if field_value is None:
                raise ValidationError(f"Required field '{field_name}' cannot be None", field_name, field_value)
        
        # 类型验证
        if field_value is not None and expected_type:
            if not _validate_type(field_value, expected_type, field_name, strict):
                return False
    
    # 执行自定义验证
    if hasattr(dto_instance, '_validate'):
        try:
            dto_instance._validate()
        except Exception as e:
            raise ValidationError(f"Custom validation failed: {str(e)}")
    
    return True


def _validate_type(value: Any, expected_type: Type, field_name: str, strict: bool) -> bool:
    """
    验证值的类型是否符合预期
    
    Args:
        value: 要验证的值
        expected_type: 期望的类型
        field_name: 字段名
        strict: 是否严格模式
        
    Returns:
        bool: 类型验证是否通过
    """
    try:
        # 处理Optional类型
        if hasattr(expected_type, '__origin__') and expected_type.__origin__ is type(None):
            return True
            
        # 处理Union类型 (包括Optional)
        if hasattr(expected_type, '__origin__') and expected_type.__origin__ is type(Union):
            type_args = expected_type.__args__
            return any(_validate_single_type(value, arg, strict) for arg in type_args)
        
        # 处理泛型类型
        if hasattr(expected_type, '__origin__'):
            origin = expected_type.__origin__
            
            # List类型
            if origin is list:
                if not isinstance(value, list):
                    if strict:
                        raise ValidationError(f"Field '{field_name}' expected list, got {type(value).__name__}", field_name, value)
                    return False
                
                # 验证列表元素类型
                if hasattr(expected_type, '__args__') and expected_type.__args__:
                    element_type = expected_type.__args__[0]
                    for i, item in enumerate(value):
                        if not _validate_single_type(item, element_type, strict):
                            if strict:
                                raise ValidationError(f"Field '{field_name}[{i}]' type mismatch", f"{field_name}[{i}]", item)
                            return False
                return True
            
            # Dict类型
            elif origin is dict:
                if not isinstance(value, dict):
                    if strict:
                        raise ValidationError(f"Field '{field_name}' expected dict, got {type(value).__name__}", field_name, value)
                    return False
                
                # 验证字典键值类型
                if hasattr(expected_type, '__args__') and len(expected_type.__args__) >= 2:
                    key_type, value_type = expected_type.__args__[:2]
                    for k, v in value.items():
                        if not _validate_single_type(k, key_type, strict):
                            if strict:
                                raise ValidationError(f"Field '{field_name}' key type mismatch", field_name, k)
                            return False
                        if not _validate_single_type(v, value_type, strict):
                            if strict:
                                raise ValidationError(f"Field '{field_name}' value type mismatch", field_name, v)
                            return False
                return True
            
            # Set类型
            elif origin is set:
                if not isinstance(value, set):
                    if strict:
                        raise ValidationError(f"Field '{field_name}' expected set, got {type(value).__name__}", field_name, value)
                    return False
                return True
        
        # 基本类型验证
        return _validate_single_type(value, expected_type, strict)
        
    except Exception as e:
        if strict:
            raise ValidationError(f"Type validation failed for field '{field_name}': {str(e)}", field_name, value)
        return False


def _validate_single_type(value: Any, expected_type: Type, strict: bool) -> bool:
    """验证单个类型"""
    # 特殊类型处理
    if expected_type is Any:
        return True
    
    # datetime类型特殊处理
    if expected_type is datetime:
        return isinstance(value, datetime)
    
    # 基本类型检查
    try:
        return isinstance(value, expected_type)
    except TypeError:
        # 处理一些特殊情况，如泛型类型
        return True


def validate_assessment_request(request: 'AssessmentRequest') -> bool:
    """
    验证AssessmentRequest的业务逻辑
    
    Args:
        request: 评估请求对象
        
    Returns:
        bool: 验证是否通过
    """
    # 基础DTO验证
    validate_dto(request)
    
    # 业务逻辑验证
    if request.max_coalitions is not None and request.max_coalitions <= 0:
        raise ValidationError("max_coalitions must be positive", "max_coalitions", request.max_coalitions)
    
    if request.max_concurrent_api_calls <= 0:
        raise ValidationError("max_concurrent_api_calls must be positive", "max_concurrent_api_calls", request.max_concurrent_api_calls)
    
    # 日期验证
    if request.start_date and request.end_date:
        try:
            start = datetime.fromisoformat(request.start_date.replace('Z', '+00:00'))
            end = datetime.fromisoformat(request.end_date.replace('Z', '+00:00'))
            if start >= end:
                raise ValidationError("start_date must be before end_date")
        except ValueError as e:
            raise ValidationError(f"Invalid date format: {str(e)}")
    
    return True


def validate_assessment_result(result: 'AssessmentResult') -> bool:
    """
    验证AssessmentResult的数据完整性
    
    Args:
        result: 评估结果对象
        
    Returns:
        bool: 验证是否通过
    """
    # 基础DTO验证
    validate_dto(result)
    
    # 业务逻辑验证
    if result.execution_time < 0:
        raise ValidationError("execution_time cannot be negative", "execution_time", result.execution_time)
    
    # 成功状态验证
    if result.success:
        if result.error:
            raise ValidationError("Successful result should not have error message")
    else:
        if not result.error:
            raise ValidationError("Failed result must have error message")
    
    return True


# 导入Union用于类型检查
try:
    from typing import Union
except ImportError:
    # Python 3.9及以下版本的兼容性
    try:
        from typing_extensions import Union
    except ImportError:
        Union = None