"""
评估请求和结果的数据传输对象
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from datetime import datetime


@dataclass
class AssessmentRequest:
    """
    评估请求数据传输对象
    
    包含执行评估所需的所有参数和配置信息
    """
    # 核心参数
    agents: Optional[Dict[str, Any]] = None
    target_agents: Optional[List[str]] = None
    max_coalitions: Optional[int] = None
    
    # 配置信息
    config: Dict[str, Any] = field(default_factory=dict)
    
    # 时间配置
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    
    # 执行选项
    enable_concurrent_execution: bool = True
    max_concurrent_api_calls: int = 5
    
    # OPRO配置
    enable_opro: bool = False
    opro_config: Optional[Dict[str, Any]] = None
    
    # 请求元数据
    request_id: str = field(default_factory=lambda: f"req_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    timestamp: datetime = field(default_factory=datetime.now)
    
    def __post_init__(self):
        """初始化后验证"""
        if self.target_agents is None:
            self.target_agents = []
        if not self.config:
            self.config = {}


@dataclass
class AssessmentResult:
    """
    评估结果数据传输对象
    
    包含完整的评估执行结果和统计信息
    """
    # 执行状态
    success: bool
    execution_time: float
    request_id: str
    timestamp: datetime = field(default_factory=datetime.now)
    
    # 核心结果
    shapley_values: Dict[str, float] = field(default_factory=dict)
    shapley_analysis: Dict[str, Any] = field(default_factory=dict)
    periodic_shapley_results: Dict[str, Any] = field(default_factory=dict)
    
    # OPRO优化结果
    opro_optimization_results: Dict[str, Any] = field(default_factory=dict)
    
    # 各阶段结果
    phase_results: Dict[str, Any] = field(default_factory=dict)
    
    # 执行统计
    summary: Dict[str, Any] = field(default_factory=dict)
    
    # 错误信息
    error: Optional[str] = None
    error_type: Optional[str] = None
    error_context: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.summary:
            self.summary = {
                "total_execution_time": self.execution_time,
                "success": self.success,
                "timestamp": self.timestamp.isoformat(),
                "request_id": self.request_id
            }
    
    def add_phase_result(self, phase_name: str, result: Any) -> None:
        """添加阶段结果"""
        self.phase_results[phase_name] = result
    
    def set_error(self, error: Exception, context: Optional[Dict[str, Any]] = None) -> None:
        """设置错误信息"""
        self.success = False
        self.error = str(error)
        self.error_type = type(error).__name__
        self.error_context = context or {}
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """获取汇总统计信息"""
        stats = {
            "execution_summary": {
                "success": self.success,
                "execution_time": self.execution_time,
                "request_id": self.request_id,
                "timestamp": self.timestamp.isoformat()
            },
            "results_summary": {
                "shapley_agents_count": len(self.shapley_values),
                "has_periodic_results": bool(self.periodic_shapley_results),
                "has_opro_results": bool(self.opro_optimization_results),
                "phases_completed": len(self.phase_results)
            }
        }
        
        if not self.success and self.error:
            stats["error_summary"] = {
                "error_type": self.error_type,
                "error_message": self.error,
                "has_context": bool(self.error_context)
            }
        
        return stats