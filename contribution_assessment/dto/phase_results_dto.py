"""
各阶段结果的数据传输对象
"""

from dataclasses import dataclass, field
from typing import Dict, List, Set, Optional, Any, FrozenSet
from datetime import datetime


@dataclass
class CoalitionResult:
    """
    联盟生成阶段结果
    
    包含联盟生成的所有结果和统计信息
    """
    success: bool
    execution_time: float
    timestamp: datetime = field(default_factory=datetime.now)
    
    # 联盟数据
    valid_coalitions: Set[frozenset] = field(default_factory=set)
    pruned_coalitions: Set[frozenset] = field(default_factory=set)
    
    # 分析结果
    coalition_analysis: Dict[str, Any] = field(default_factory=dict)
    generation_stats: Dict[str, Any] = field(default_factory=dict)
    
    # 错误信息
    error: Optional[str] = None
    error_context: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.generation_stats:
            self.generation_stats = {
                "valid_coalitions_count": len(self.valid_coalitions),
                "pruned_coalitions_count": len(self.pruned_coalitions),
                "total_coalitions_generated": len(self.valid_coalitions) + len(self.pruned_coalitions),
                "execution_time": self.execution_time,
                "timestamp": self.timestamp.isoformat()
            }
    
    def add_coalition_analysis(self, analysis_type: str, data: Any) -> None:
        """添加联盟分析数据"""
        self.coalition_analysis[analysis_type] = data
    
    def get_coalition_summary(self) -> Dict[str, Any]:
        """获取联盟汇总信息"""
        return {
            "total_valid": len(self.valid_coalitions),
            "total_pruned": len(self.pruned_coalitions),
            "success": self.success,
            "execution_time": self.execution_time
        }


@dataclass
class SimulationResult:
    """
    交易模拟阶段结果
    
    包含所有联盟的模拟结果和性能统计
    """
    success: bool
    execution_time: float
    timestamp: datetime = field(default_factory=datetime.now)
    
    # 模拟结果数据
    coalition_values: Dict[frozenset, float] = field(default_factory=dict)
    coalition_daily_returns: Dict[frozenset, List[float]] = field(default_factory=dict)
    
    # 运行时智能体实例（修复状态传递断裂问题）
    active_agents: Dict[str, Any] = field(default_factory=dict)
    
    # 统计信息
    simulation_stats: Dict[str, Any] = field(default_factory=dict)
    simulator_stats: Dict[str, Any] = field(default_factory=dict)
    
    # 并发执行统计
    concurrent_stats: Optional[Dict[str, Any]] = None
    
    # 错误信息
    error: Optional[str] = None
    error_context: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.simulation_stats:
            self.simulation_stats = {
                "coalitions_simulated": len(self.coalition_values),
                "successful_simulations": sum(1 for v in self.coalition_values.values() if v is not None),
                "execution_time": self.execution_time,
                "timestamp": self.timestamp.isoformat()
            }
    
    def add_coalition_result(self, coalition: frozenset, value: float, daily_returns: List[float]) -> None:
        """添加联盟模拟结果"""
        self.coalition_values[coalition] = value
        self.coalition_daily_returns[coalition] = daily_returns
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能汇总"""
        values = [v for v in self.coalition_values.values() if v is not None]
        return {
            "total_coalitions": len(self.coalition_values),
            "successful_simulations": len(values),
            "average_performance": sum(values) / len(values) if values else 0.0,
            "best_performance": max(values) if values else 0.0,
            "worst_performance": min(values) if values else 0.0,
            "execution_time": self.execution_time
        }


@dataclass
class ShapleyResult:
    """
    Shapley值计算阶段结果
    
    包含周期性Shapley值计算的所有结果
    """
    success: bool
    execution_time: float
    timestamp: datetime = field(default_factory=datetime.now)
    
    # Shapley计算结果
    total_weeks: int = 0
    weekly_results: List[Dict[str, Any]] = field(default_factory=list)
    periodic_data: List[Dict[str, Any]] = field(default_factory=list)
    
    # 配置信息
    trading_days_per_week: int = 5
    compilation_mode: str = "precomputed_aggregation"
    
    # 计算统计
    calculation_stats: Dict[str, Any] = field(default_factory=dict)
    
    # 错误信息
    error: Optional[str] = None
    error_context: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.calculation_stats:
            self.calculation_stats = {
                "total_weeks_processed": self.total_weeks,
                "weekly_results_count": len(self.weekly_results),
                "periodic_data_count": len(self.periodic_data),
                "compilation_mode": self.compilation_mode,
                "execution_time": self.execution_time,
                "timestamp": self.timestamp.isoformat()
            }
    
    def add_weekly_result(self, week_data: Dict[str, Any]) -> None:
        """添加周结果"""
        self.weekly_results.append(week_data)
    
    def add_periodic_data(self, period_data: Dict[str, Any]) -> None:
        """添加周期数据"""
        self.periodic_data.append(period_data)
    
    def get_shapley_summary(self) -> Dict[str, Any]:
        """获取Shapley值汇总"""
        return {
            "total_weeks": self.total_weeks,
            "compilation_mode": self.compilation_mode,
            "trading_days_per_week": self.trading_days_per_week,
            "results_available": len(self.weekly_results) > 0,
            "execution_time": self.execution_time,
            "success": self.success
        }


@dataclass
class OPROResult:
    """
    OPRO优化阶段结果
    
    包含智能体提示词优化的结果
    """
    success: bool
    execution_time: float
    timestamp: datetime = field(default_factory=datetime.now)
    
    # OPRO配置
    enabled: bool = False
    
    # 优化结果
    optimization_result: Optional[Dict[str, Any]] = None
    updated_agents: List[str] = field(default_factory=list)
    
    # 优化统计
    optimization_stats: Dict[str, Any] = field(default_factory=dict)
    
    # 历史得分更新
    historical_scores_updated: bool = False
    
    # 错误信息
    error: Optional[str] = None
    error_context: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.optimization_stats:
            self.optimization_stats = {
                "enabled": self.enabled,
                "agents_updated_count": len(self.updated_agents),
                "optimization_successful": self.success and self.optimization_result is not None,
                "historical_scores_updated": self.historical_scores_updated,
                "execution_time": self.execution_time,
                "timestamp": self.timestamp.isoformat()
            }
    
    def set_optimization_result(self, result: Dict[str, Any], updated_agents: List[str]) -> None:
        """设置优化结果"""
        self.optimization_result = result
        self.updated_agents = updated_agents
        self.optimization_stats["agents_updated_count"] = len(updated_agents)
        self.optimization_stats["optimization_successful"] = True
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """获取优化汇总"""
        return {
            "enabled": self.enabled,
            "success": self.success,
            "agents_updated": len(self.updated_agents),
            "has_results": self.optimization_result is not None,
            "execution_time": self.execution_time
        }