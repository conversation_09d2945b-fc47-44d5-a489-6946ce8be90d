"""
多智能体贡献分配算法模块

本模块实现了基于Shapley值的多智能体贡献度评估系统，用于量化
多智能体交易系统中各个智能体对整体性能的贡献。

主要组件:
- CoalitionManager: 联盟管理模块，生成和剪枝智能体联盟
- TradingSimulator: 交易模拟引擎，为不同联盟运行交易模拟
- ShapleyCalculator: Shapley值计算器，计算各智能体的贡献度
- ContributionAssessor: 主协调器，统筹整个贡献度评估流程

使用示例:
    from contribution_assessment import ContributionAssessor
    
    assessor = ContributionAssessor()
    shapley_values = assessor.run()
    print(f"各智能体贡献度: {shapley_values}")
"""

__version__ = "1.0.0"
__author__ = "Multi-Agent Trading System Team"

# 导入主要类，方便外部使用
from .coalition_manager import CoalitionManager
from .trading_simulator import TradingSimulator
from .shapley_calculator import ShapleyCalculator
# 使用重构版本作为默认版本
from .refactored_assessor import RefactoredContributionAssessor as ContributionAssessor
from .weekly_optimization_manager import WeeklyOptimizationManager, WeeklyOptimizationConfig

__all__ = [
    "CoalitionManager",
    "TradingSimulator",
    "ShapleyCalculator",
    "ContributionAssessor",
    "WeeklyOptimizationManager",
    "WeeklyOptimizationConfig",
]
