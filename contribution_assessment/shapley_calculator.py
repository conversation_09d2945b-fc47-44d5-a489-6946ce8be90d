"""
Shapley值计算模块 (Shapley Value Calculator)

本模块实现了标准的Shapley值计算算法，用于量化多智能体交易系统中
各个智能体对整体性能的边际贡献。基于合作博弈论的Shapley值理论，
为每个智能体分配公平的贡献度评分。

主要功能：
1. 接收联盟特征函数值字典
2. 为被剪枝的联盟赋予零值
3. 应用标准Shapley值数学公式计算各智能体贡献
4. 返回智能体ID到Shapley值的映射
"""

import math
import itertools
from typing import Dict, Any, List, Set, Optional, Union
from datetime import datetime
import logging
import time


class ShapleyCalculator:
    """
    Shapley值计算器
    
    实现标准的Shapley值计算算法，支持大规模联盟的高效计算。
    基于合作博弈论中的Shapley值公理，确保计算结果的公平性和唯一性。
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化Shapley值计算器
        
        参数:
            logger: 日志记录器，如果为None则创建默认记录器
        """
        self.logger = logger or self._create_default_logger()
        
        # 计算统计信息
        self._stats = {
            "total_calculations": 0,
            "total_agents": 0,
            "total_coalitions": 0,
            "calculation_time": 0.0,
            "last_calculation": None
        }
        
        self.logger.info("Shapley值计算器初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.ShapleyCalculator")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def calculate(self, 
                 agents: Union[List[str], Set[str]], 
                 coalition_values: Dict[frozenset, float]) -> Dict[str, float]:
        """
        计算所有智能体的Shapley值
        
        这是核心方法，实现标准的Shapley值计算公式：
        μ_i = Σ_{S ⊆ V \\{i}} [|S|!(|V| - |S| - 1)!] / |V|! × [v(S ∪ {i}) - v(S)]
        
        参数:
            agents: 所有智能体的列表或集合
            coalition_values: 联盟特征函数值字典，键为frozenset，值为夏普比率
            
        返回:
            智能体ID到Shapley值的映射字典
        """
        start_time = time.time()
        
        # 转换为列表以确保顺序一致性
        agent_list = list(agents) if isinstance(agents, set) else agents
        n = len(agent_list)
        
        self.logger.info(f"开始计算 {n} 个智能体的Shapley值")
        self.logger.info(f"已提供 {len(coalition_values)} 个联盟的特征函数值")
        
        # 验证输入
        if n == 0:
            self.logger.warning("智能体列表为空")
            return {}
        
        if not coalition_values:
            self.logger.warning("联盟特征函数值为空")
            return {agent: 0.0 for agent in agent_list}
        
        # 生成所有可能的联盟（用于补全缺失的联盟值）
        all_coalitions = self._generate_all_coalitions(agent_list)
        
        # 补全缺失的联盟值（被剪枝的联盟赋值为0）
        complete_coalition_values = self._complete_coalition_values(
            all_coalitions, coalition_values
        )
        
        # 预计算权重系数
        weights = self._precompute_weights(n)
        
        # 计算每个智能体的Shapley值
        shapley_values = {}
        
        for i, agent in enumerate(agent_list):
            # self.logger.debug(f"计算智能体 {agent} 的Shapley值 ({i+1}/{n})")

            shapley_value = self._calculate_agent_shapley_value(
                agent, agent_list, complete_coalition_values, weights
            )

            shapley_values[agent] = shapley_value

            # self.logger.debug(f"智能体 {agent} 的Shapley值: {shapley_value:.6f}")
        
        # 更新统计信息
        calculation_time = time.time() - start_time
        self._update_stats(n, len(complete_coalition_values), calculation_time)
        
        # 验证Shapley值的效率性公理（所有值之和应等于大联盟的值）
        self._verify_efficiency_axiom(shapley_values, complete_coalition_values, agent_list)
        
        self.logger.info(f"Shapley值计算完成，耗时 {calculation_time:.3f}s")
        
        return shapley_values
    
    def _generate_all_coalitions(self, agents: List[str]) -> Set[frozenset]:
        """
        生成所有可能的智能体联盟
        
        参数:
            agents: 智能体列表
            
        返回:
            所有可能联盟的集合
        """
        all_coalitions = set()
        
        # 生成所有子集（从空集到全集）
        for r in range(len(agents) + 1):
            for coalition in itertools.combinations(agents, r):
                all_coalitions.add(frozenset(coalition))

        # self.logger.debug(f"生成了 {len(all_coalitions)} 个可能的联盟")
        return all_coalitions
    
    def _complete_coalition_values(self, 
                                 all_coalitions: Set[frozenset], 
                                 provided_values: Dict[frozenset, float]) -> Dict[frozenset, float]:
        """
        补全缺失的联盟特征函数值
        
        为所有被剪枝的联盟（未在provided_values中的联盟）赋值为0。
        这符合我们的剪枝策略：被剪枝的联盟无法产生有效的交易收益。
        
        参数:
            all_coalitions: 所有可能的联盟
            provided_values: 已提供的联盟特征函数值
            
        返回:
            完整的联盟特征函数值字典
        """
        complete_values = {}
        
        provided_count = 0
        missing_count = 0
        
        for coalition in all_coalitions:
            if coalition in provided_values:
                complete_values[coalition] = provided_values[coalition]
                provided_count += 1
            else:
                # 被剪枝的联盟赋值为0
                complete_values[coalition] = 0.0
                missing_count += 1
        
        self.logger.info(f"联盟值补全完成: 已提供 {provided_count} 个，补全 {missing_count} 个")
        return complete_values
    
    def _precompute_weights(self, n: int) -> List[float]:
        """
        预计算Shapley值公式中的权重系数
        
        权重公式: w(|S|) = |S|! × (n - |S| - 1)! / n!
        其中 |S| 是联盟S的大小，n是总智能体数量
        
        参数:
            n: 智能体总数
            
        返回:
            权重系数列表，索引为联盟大小
        """
        if n == 0:
            return []
        
        weights = [0.0] * n
        n_factorial = math.factorial(n)
        
        for s_size in range(n):
            s_factorial = math.factorial(s_size)
            n_minus_s_minus_1_factorial = math.factorial(n - s_size - 1)
            weights[s_size] = (s_factorial * n_minus_s_minus_1_factorial) / n_factorial

        # self.logger.debug(f"预计算权重完成: {weights}")
        return weights
    
    def _calculate_agent_shapley_value(self, 
                                     agent: str, 
                                     all_agents: List[str], 
                                     coalition_values: Dict[frozenset, float], 
                                     weights: List[float]) -> float:
        """
        计算单个智能体的Shapley值
        
        应用Shapley值公式：
        μ_i = Σ_{S ⊆ V \\{i}} w(|S|) × [v(S ∪ {i}) - v(S)]
        
        参数:
            agent: 目标智能体ID
            all_agents: 所有智能体列表
            coalition_values: 完整的联盟特征函数值字典
            weights: 预计算的权重系数
            
        返回:
            智能体的Shapley值
        """
        shapley_value = 0.0
        
        # 获取不包含当前智能体的其他智能体
        other_agents = [a for a in all_agents if a != agent]
        
        # 遍历所有不包含当前智能体的联盟S
        for r in range(len(other_agents) + 1):
            for coalition_tuple in itertools.combinations(other_agents, r):
                S = frozenset(coalition_tuple)
                S_with_agent = S.union({agent})
                
                # 获取特征函数值
                v_S = coalition_values.get(S, 0.0)
                v_S_with_agent = coalition_values.get(S_with_agent, 0.0)
                
                # 计算边际贡献
                marginal_contribution = v_S_with_agent - v_S
                
                # 获取权重
                weight = weights[len(S)]
                
                # 累加到Shapley值
                shapley_value += weight * marginal_contribution
        
        return shapley_value
    
    def _verify_efficiency_axiom(self, 
                               shapley_values: Dict[str, float], 
                               coalition_values: Dict[frozenset, float], 
                               agents: List[str]) -> None:
        """
        验证Shapley值的效率性公理
        
        效率性公理要求：所有智能体的Shapley值之和等于大联盟的特征函数值
        即：Σ_i μ_i = v(N)，其中N是所有智能体的集合
        
        参数:
            shapley_values: 计算得到的Shapley值
            coalition_values: 联盟特征函数值
            agents: 智能体列表
        """
        # 计算Shapley值总和
        shapley_sum = sum(shapley_values.values())
        
        # 获取大联盟（所有智能体）的特征函数值
        grand_coalition = frozenset(agents)
        grand_coalition_value = coalition_values.get(grand_coalition, 0.0)
        
        # 计算差异
        difference = abs(shapley_sum - grand_coalition_value)
        tolerance = 1e-10  # 数值计算容差
        
        if difference > tolerance:
            self.logger.warning(
                f"效率性公理验证失败: Shapley值总和 {shapley_sum:.6f} "
                f"!= 大联盟值 {grand_coalition_value:.6f} (差异: {difference:.6f})"
            )
        else:
            # self.logger.debug(
            #     f"效率性公理验证通过: Shapley值总和 {shapley_sum:.6f} "
            #     f"= 大联盟值 {grand_coalition_value:.6f}"
            # )
            pass
    
    def _update_stats(self, num_agents: int, num_coalitions: int, calculation_time: float) -> None:
        """更新计算统计信息"""
        self._stats["total_calculations"] += 1
        self._stats["total_agents"] = num_agents
        self._stats["total_coalitions"] = num_coalitions
        self._stats["calculation_time"] = calculation_time
        self._stats["last_calculation"] = datetime.now()
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取计算统计信息
        
        返回:
            包含统计信息的字典
        """
        return self._stats.copy()
    
    def analyze_shapley_values(self, shapley_values: Dict[str, float]) -> Dict[str, Any]:
        """
        分析Shapley值的分布特征
        
        参数:
            shapley_values: Shapley值字典
            
        返回:
            包含分析结果的字典
        """
        if not shapley_values:
            return {
                "total_agents": 0,
                "total_value": 0.0,
                "mean_value": 0.0,
                "max_contributor": None,
                "min_contributor": None,
                "positive_contributors": 0,
                "negative_contributors": 0,
                "zero_contributors": 0
            }
        
        values = list(shapley_values.values())
        
        # 基本统计
        total_value = sum(values)
        mean_value = total_value / len(values)
        
        # 找出最大和最小贡献者
        max_contributor = max(shapley_values.items(), key=lambda x: x[1])
        min_contributor = min(shapley_values.items(), key=lambda x: x[1])
        
        # 统计贡献类型
        positive_contributors = sum(1 for v in values if v > 0)
        negative_contributors = sum(1 for v in values if v < 0)
        zero_contributors = sum(1 for v in values if abs(v) < 1e-10)
        
        analysis = {
            "total_agents": len(shapley_values),
            "total_value": total_value,
            "mean_value": mean_value,
            "max_contributor": max_contributor,
            "min_contributor": min_contributor,
            "positive_contributors": positive_contributors,
            "negative_contributors": negative_contributors,
            "zero_contributors": zero_contributors,
            "value_distribution": {
                "max": max(values),
                "min": min(values),
                "std": (sum((v - mean_value) ** 2 for v in values) / len(values)) ** 0.5
            }
        }
        
        return analysis
