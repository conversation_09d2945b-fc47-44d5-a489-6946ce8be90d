"""
配置迁移工具

用于将旧版本的配置格式迁移到新的分层配置结构。
"""

import yaml
import json
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class ConfigMigrator:
    """配置迁移工具"""
    
    def __init__(self):
        # 旧配置键到新配置键的映射
        self.migration_mapping = {
            # 评估系统配置
            'max_coalitions': 'assessment.max_coalitions',
            'enable_detailed_logging': 'assessment.enable_detailed_logging',
            'request_timeout': 'assessment.request_timeout',
            
            # 联盟生成配置
            'coalition_max_size': 'coalition.max_size',
            'pruning_threshold': 'coalition.pruning_threshold',
            'enable_coalition_caching': 'coalition.enable_caching',
            'coalition_generation_strategy': 'coalition.generation_strategy',
            
            # 交易模拟配置
            'simulation_timeout': 'simulation.timeout_seconds',
            'max_concurrent_simulations': 'simulation.max_concurrent',
            'simulation_detailed_logging': 'simulation.detailed_logging',
            'cache_simulation_results': 'simulation.cache_results',
            'retry_failed_simulations': 'simulation.retry_failed_simulations',
            'max_simulation_retries': 'simulation.max_retries',
            
            # Shapley值计算配置
            'shapley_precision': 'shapley.precision',
            'shapley_calculation_mode': 'shapley.calculation_mode',
            'shapley_sampling_size': 'shapley.sampling_size',
            'enable_shapley_caching': 'shapley.enable_caching',
            
            # OPRO优化配置
            'opro_enabled': 'opro.enabled',
            'opro_max_iterations': 'opro.max_iterations',
            'opro_improvement_threshold': 'opro.improvement_threshold',
            'opro_candidate_count': 'opro.candidate_count',
            'opro_optimization_strategy': 'opro.optimization_strategy',
            
            # 日志配置
            'log_level': 'infrastructure.logging.level',
            'log_format': 'infrastructure.logging.format',
            'log_output': 'infrastructure.logging.output',
            'log_file_path': 'infrastructure.logging.file_path',
            
            # 事件总线配置
            'async_event_processing': 'infrastructure.event_bus.async_processing',
            'event_queue_size': 'infrastructure.event_bus.max_queue_size',
            
            # 监控配置
            'enable_monitoring': 'infrastructure.monitoring.enabled',
            'metrics_interval': 'infrastructure.monitoring.metrics_interval'
        }
        
        # 默认值映射
        self.default_values = {
            'assessment.max_coalitions': 100,
            'assessment.enable_detailed_logging': False,
            'assessment.request_timeout': 3600,
            
            'coalition.max_size': 5,
            'coalition.pruning_threshold': 0.1,
            'coalition.enable_caching': True,
            'coalition.generation_strategy': 'exhaustive',
            
            'simulation.timeout_seconds': 300,
            'simulation.max_concurrent': 5,
            'simulation.detailed_logging': False,
            'simulation.cache_results': True,
            'simulation.retry_failed_simulations': True,
            'simulation.max_retries': 3,
            
            'shapley.precision': 6,
            'shapley.calculation_mode': 'exact',
            'shapley.sampling_size': 1000,
            'shapley.enable_caching': True,
            
            'opro.enabled': False,
            'opro.max_iterations': 5,
            'opro.improvement_threshold': 0.01,
            'opro.candidate_count': 1,
            'opro.optimization_strategy': 'performance_based',
            
            'infrastructure.logging.level': 'INFO',
            'infrastructure.logging.format': 'structured',
            'infrastructure.logging.output': 'console',
            
            'infrastructure.event_bus.async_processing': True,
            'infrastructure.event_bus.max_queue_size': 1000,
            
            'infrastructure.monitoring.enabled': True,
            'infrastructure.monitoring.metrics_interval': 30
        }
    
    def migrate_config(self, old_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        迁移配置格式
        
        Args:
            old_config: 旧格式的配置字典
            
        Returns:
            Dict[str, Any]: 新格式的配置字典
        """
        new_config = {}
        
        # 迁移已知的配置项
        for old_key, new_key in self.migration_mapping.items():
            if old_key in old_config:
                self._set_nested_value(new_config, new_key, old_config[old_key])
                logger.info(f"迁移配置: {old_key} -> {new_key}")
        
        # 处理未映射的配置项
        unmapped_keys = set(old_config.keys()) - set(self.migration_mapping.keys())
        if unmapped_keys:
            logger.warning(f"未映射的配置项: {unmapped_keys}")
            # 将未映射的配置项放到custom节点下
            if unmapped_keys:
                new_config['custom'] = {key: old_config[key] for key in unmapped_keys}
        
        # 添加默认值
        self._add_default_values(new_config)
        
        return new_config
    
    def migrate_config_file(self, input_path: Union[str, Path], output_path: Union[str, Path]) -> bool:
        """
        迁移配置文件
        
        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            
        Returns:
            bool: 迁移是否成功
        """
        try:
            input_path = Path(input_path)
            output_path = Path(output_path)
            
            # 读取旧配置文件
            old_config = self._load_config_file(input_path)
            
            # 迁移配置
            new_config = self.migrate_config(old_config)
            
            # 保存新配置文件
            self._save_config_file(new_config, output_path)
            
            logger.info(f"配置文件迁移成功: {input_path} -> {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"配置文件迁移失败: {e}")
            return False
    
    def validate_migrated_config(self, config: Dict[str, Any]) -> List[str]:
        """
        验证迁移后的配置
        
        Args:
            config: 迁移后的配置字典
            
        Returns:
            List[str]: 验证错误列表
        """
        errors = []
        
        # 检查必需的配置节点
        required_sections = ['assessment', 'coalition', 'simulation', 'shapley', 'opro', 'infrastructure']
        for section in required_sections:
            if section not in config:
                errors.append(f"缺少必需的配置节点: {section}")
        
        # 检查数值范围
        validations = [
            ('assessment.max_coalitions', lambda x: isinstance(x, int) and x > 0),
            ('coalition.max_size', lambda x: isinstance(x, int) and 2 <= x <= 10),
            ('coalition.pruning_threshold', lambda x: isinstance(x, (int, float)) and 0 <= x <= 1),
            ('simulation.max_concurrent', lambda x: isinstance(x, int) and x > 0),
            ('simulation.timeout_seconds', lambda x: isinstance(x, int) and x > 0),
            ('shapley.precision', lambda x: isinstance(x, int) and 2 <= x <= 10),
            ('opro.improvement_threshold', lambda x: isinstance(x, (int, float)) and 0 < x < 1)
        ]
        
        for key_path, validator in validations:
            value = self._get_nested_value(config, key_path)
            if value is not None and not validator(value):
                errors.append(f"配置值无效: {key_path} = {value}")
        
        # 检查枚举值
        enum_validations = [
            ('coalition.generation_strategy', ['exhaustive', 'random', 'heuristic']),
            ('shapley.calculation_mode', ['exact', 'sampling', 'approximation']),
            ('opro.optimization_strategy', ['performance_based', 'diversity_based', 'hybrid']),
            ('infrastructure.logging.level', ['DEBUG', 'INFO', 'WARNING', 'ERROR']),
            ('infrastructure.logging.format', ['structured', 'simple']),
            ('infrastructure.logging.output', ['console', 'file', 'both'])
        ]
        
        for key_path, valid_values in enum_validations:
            value = self._get_nested_value(config, key_path)
            if value is not None and value not in valid_values:
                errors.append(f"配置值不在有效范围内: {key_path} = {value}, 有效值: {valid_values}")
        
        return errors
    
    def generate_migration_report(self, old_config: Dict[str, Any], new_config: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成迁移报告
        
        Args:
            old_config: 旧配置
            new_config: 新配置
            
        Returns:
            Dict[str, Any]: 迁移报告
        """
        report = {
            'migration_summary': {
                'old_config_keys': len(old_config),
                'new_config_keys': self._count_nested_keys(new_config),
                'mapped_keys': 0,
                'unmapped_keys': 0,
                'added_defaults': 0
            },
            'mapped_configurations': [],
            'unmapped_configurations': [],
            'added_defaults': [],
            'validation_errors': []
        }
        
        # 统计映射的配置
        for old_key, new_key in self.migration_mapping.items():
            if old_key in old_config:
                report['mapped_configurations'].append({
                    'old_key': old_key,
                    'new_key': new_key,
                    'value': old_config[old_key]
                })
                report['migration_summary']['mapped_keys'] += 1
        
        # 统计未映射的配置
        unmapped_keys = set(old_config.keys()) - set(self.migration_mapping.keys())
        for key in unmapped_keys:
            report['unmapped_configurations'].append({
                'key': key,
                'value': old_config[key]
            })
            report['migration_summary']['unmapped_keys'] += 1
        
        # 统计添加的默认值
        for key_path, default_value in self.default_values.items():
            if self._get_nested_value(new_config, key_path) == default_value:
                # 检查是否是新添加的默认值
                old_key = self._find_old_key_for_new_key(key_path)
                if not old_key or old_key not in old_config:
                    report['added_defaults'].append({
                        'key': key_path,
                        'value': default_value
                    })
                    report['migration_summary']['added_defaults'] += 1
        
        # 验证配置
        report['validation_errors'] = self.validate_migrated_config(new_config)
        
        return report
    
    def _set_nested_value(self, config: Dict, key_path: str, value: Any):
        """设置嵌套配置值"""
        keys = key_path.split('.')
        current = config
        
        for key in keys[:-1]:
            if key not in current:
                current[key] = {}
            current = current[key]
        
        current[keys[-1]] = value
    
    def _get_nested_value(self, config: Dict, key_path: str) -> Any:
        """获取嵌套配置值"""
        keys = key_path.split('.')
        current = config
        
        try:
            for key in keys:
                current = current[key]
            return current
        except (KeyError, TypeError):
            return None
    
    def _add_default_values(self, config: Dict[str, Any]):
        """添加默认值"""
        for key_path, default_value in self.default_values.items():
            if self._get_nested_value(config, key_path) is None:
                self._set_nested_value(config, key_path, default_value)
    
    def _load_config_file(self, file_path: Path) -> Dict[str, Any]:
        """加载配置文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            if file_path.suffix.lower() in ['.yaml', '.yml']:
                return yaml.safe_load(f) or {}
            elif file_path.suffix.lower() == '.json':
                return json.load(f)
            else:
                raise ValueError(f"不支持的文件格式: {file_path.suffix}")
    
    def _save_config_file(self, config: Dict[str, Any], file_path: Path):
        """保存配置文件"""
        file_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(file_path, 'w', encoding='utf-8') as f:
            if file_path.suffix.lower() in ['.yaml', '.yml']:
                yaml.dump(config, f, default_flow_style=False, allow_unicode=True, indent=2)
            elif file_path.suffix.lower() == '.json':
                json.dump(config, f, indent=2, ensure_ascii=False)
            else:
                raise ValueError(f"不支持的文件格式: {file_path.suffix}")
    
    def _count_nested_keys(self, config: Dict[str, Any]) -> int:
        """计算嵌套字典中的键数量"""
        count = 0
        for key, value in config.items():
            count += 1
            if isinstance(value, dict):
                count += self._count_nested_keys(value)
        return count
    
    def _find_old_key_for_new_key(self, new_key: str) -> Optional[str]:
        """根据新键查找对应的旧键"""
        for old_key, mapped_new_key in self.migration_mapping.items():
            if mapped_new_key == new_key:
                return old_key
        return None


def main():
    """命令行工具入口"""
    import argparse
    
    parser = argparse.ArgumentParser(description='配置迁移工具')
    parser.add_argument('input', help='输入配置文件路径')
    parser.add_argument('output', help='输出配置文件路径')
    parser.add_argument('--report', help='生成迁移报告文件路径')
    parser.add_argument('--validate', action='store_true', help='验证迁移后的配置')
    
    args = parser.parse_args()
    
    # 配置日志
    logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
    
    migrator = ConfigMigrator()
    
    # 执行迁移
    success = migrator.migrate_config_file(args.input, args.output)
    
    if success:
        print(f"✓ 配置迁移成功: {args.input} -> {args.output}")
        
        # 生成报告
        if args.report:
            old_config = migrator._load_config_file(Path(args.input))
            new_config = migrator._load_config_file(Path(args.output))
            report = migrator.generate_migration_report(old_config, new_config)
            
            with open(args.report, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            print(f"✓ 迁移报告已生成: {args.report}")
        
        # 验证配置
        if args.validate:
            new_config = migrator._load_config_file(Path(args.output))
            errors = migrator.validate_migrated_config(new_config)
            if errors:
                print("⚠ 配置验证发现问题:")
                for error in errors:
                    print(f"  - {error}")
            else:
                print("✓ 配置验证通过")
    else:
        print(f"✗ 配置迁移失败")
        exit(1)


if __name__ == '__main__':
    main()
