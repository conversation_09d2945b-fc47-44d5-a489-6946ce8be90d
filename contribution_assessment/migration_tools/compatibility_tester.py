"""
兼容性测试工具

提供新旧实现之间的详细兼容性测试，包括：
- API接口兼容性验证
- 返回值格式对比
- 配置参数兼容性检查
- 异常处理一致性验证
- 性能对比分析
"""

import time
import json
import logging
import traceback
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple, Callable
from dataclasses import dataclass
import inspect
import difflib

# 导入新旧实现
from ..assessor import ContributionAssessor as OriginalAssessor
from ..refactored_assessor import RefactoredContributionAssessor


@dataclass
class CompatibilityTestCase:
    """兼容性测试用例"""
    name: str
    description: str
    config: Dict[str, Any]
    agents: Dict[str, Any]
    target_agents: List[str]
    max_coalitions: Optional[int] = None
    expected_success: bool = True


@dataclass
class CompatibilityTestResult:
    """兼容性测试结果"""
    test_case_name: str
    success: bool
    original_result: Optional[Dict[str, Any]] = None
    refactored_result: Optional[Dict[str, Any]] = None
    original_error: Optional[str] = None
    refactored_error: Optional[str] = None
    compatibility_score: float = 0.0
    differences: List[str] = None
    performance_comparison: Optional[Dict[str, float]] = None


class CompatibilityTester:
    """兼容性测试器"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.test_results: List[CompatibilityTestResult] = []
        
    def create_standard_test_cases(self) -> List[CompatibilityTestCase]:
        """创建标准测试用例"""
        base_agents = {
            "NAA": {"type": "analyst", "name": "News Analyst"},
            "TAA": {"type": "analyst", "name": "Technical Analyst"},
            "FAA": {"type": "analyst", "name": "Fundamental Analyst"},
            "TRA": {"type": "trader", "name": "Trading Agent"}
        }
        
        test_cases = [
            # 基本功能测试
            CompatibilityTestCase(
                name="basic_functionality",
                description="基本功能兼容性测试",
                config={
                    "start_date": "2024-01-01",
                    "end_date": "2024-01-02",
                    "stocks": ["AAPL"],
                    "starting_cash": 1000000,
                    "simulation_days": 2,
                    "trading_days_per_week": 5
                },
                agents=base_agents,
                target_agents=["NAA", "TAA"],
                max_coalitions=5
            ),
            
            # 扩展配置测试
            CompatibilityTestCase(
                name="extended_configuration",
                description="扩展配置兼容性测试",
                config={
                    "start_date": "2024-01-01",
                    "end_date": "2024-01-03",
                    "stocks": ["AAPL", "GOOGL"],
                    "starting_cash": 1000000,
                    "risk_free_rate": 0.02,
                    "simulation_days": 3,
                    "trading_days_per_week": 5,
                    "enable_concurrent_execution": False,
                    "max_concurrent_api_calls": 1
                },
                agents=base_agents,
                target_agents=["NAA", "TAA", "FAA", "TRA"],
                max_coalitions=10
            ),
            
            # 最小配置测试
            CompatibilityTestCase(
                name="minimal_configuration",
                description="最小配置兼容性测试",
                config={
                    "start_date": "2024-01-01",
                    "end_date": "2024-01-01",
                    "stocks": ["AAPL"],
                    "starting_cash": 100000
                },
                agents={"NAA": {"type": "analyst"}},
                target_agents=["NAA"],
                max_coalitions=3
            ),
            
            # 错误配置测试
            CompatibilityTestCase(
                name="invalid_configuration",
                description="无效配置处理测试",
                config={
                    "start_date": "invalid-date",
                    "end_date": "2024-01-02",
                    "stocks": [],
                    "starting_cash": -1000
                },
                agents=base_agents,
                target_agents=["NAA"],
                max_coalitions=5,
                expected_success=False
            )
        ]
        
        return test_cases
    
    def run_compatibility_test(self, test_case: CompatibilityTestCase) -> CompatibilityTestResult:
        """运行单个兼容性测试"""
        self.logger.info(f"Running compatibility test: {test_case.name}")
        
        result = CompatibilityTestResult(
            test_case_name=test_case.name,
            success=False,
            differences=[]
        )
        
        try:
            # 测试原始实现
            original_start_time = time.time()
            original_result, original_error = self._run_original_implementation(test_case)
            original_execution_time = time.time() - original_start_time
            
            # 测试重构实现
            refactored_start_time = time.time()
            refactored_result, refactored_error = self._run_refactored_implementation(test_case)
            refactored_execution_time = time.time() - refactored_start_time
            
            # 记录结果
            result.original_result = original_result
            result.refactored_result = refactored_result
            result.original_error = original_error
            result.refactored_error = refactored_error
            result.performance_comparison = {
                "original_time": original_execution_time,
                "refactored_time": refactored_execution_time,
                "time_ratio": refactored_execution_time / max(original_execution_time, 0.001)
            }
            
            # 分析兼容性
            result.compatibility_score, result.differences = self._analyze_compatibility(
                original_result, refactored_result, original_error, refactored_error
            )
            
            # 判断测试是否成功
            if test_case.expected_success:
                # 期望成功的测试
                result.success = (
                    result.compatibility_score >= 0.8 and  # 兼容性分数至少80%
                    (original_result is not None) == (refactored_result is not None)  # 成功/失败状态一致
                )
            else:
                # 期望失败的测试
                result.success = (
                    (original_error is not None) and (refactored_error is not None)  # 都应该失败
                )
            
        except Exception as e:
            self.logger.error(f"Compatibility test {test_case.name} crashed: {e}")
            result.differences.append(f"Test execution error: {str(e)}")
        
        return result
    
    def _run_original_implementation(self, test_case: CompatibilityTestCase) -> Tuple[Optional[Dict], Optional[str]]:
        """运行原始实现"""
        try:
            assessor = OriginalAssessor(
                config=test_case.config,
                agents=test_case.agents,
                logger=self.logger,
                llm_provider=None,
                enable_opro=False
            )
            
            result = assessor.run(
                agents=test_case.agents,
                target_agents=test_case.target_agents,
                max_coalitions=test_case.max_coalitions
            )
            
            return result, None
            
        except Exception as e:
            return None, str(e)
    
    def _run_refactored_implementation(self, test_case: CompatibilityTestCase) -> Tuple[Optional[Dict], Optional[str]]:
        """运行重构实现"""
        try:
            assessor = RefactoredContributionAssessor(
                config=test_case.config,
                agents=test_case.agents,
                logger=self.logger,
                llm_provider=None,
                enable_opro=False
            )
            
            result = assessor.run(
                agents=test_case.agents,
                target_agents=test_case.target_agents,
                max_coalitions=test_case.max_coalitions
            )
            
            return result, None
            
        except Exception as e:
            return None, str(e)
    
    def _analyze_compatibility(self, 
                             original_result: Optional[Dict], 
                             refactored_result: Optional[Dict],
                             original_error: Optional[str],
                             refactored_error: Optional[str]) -> Tuple[float, List[str]]:
        """分析兼容性"""
        differences = []
        compatibility_score = 0.0
        
        # 如果都有错误，检查错误类型是否相似
        if original_error and refactored_error:
            if type(original_error) == type(refactored_error):
                compatibility_score = 0.8  # 错误类型一致
            else:
                compatibility_score = 0.5  # 都有错误但类型不同
                differences.append(f"Error type mismatch: {type(original_error)} vs {type(refactored_error)}")
            return compatibility_score, differences
        
        # 如果一个成功一个失败
        if (original_result is None) != (refactored_result is None):
            differences.append("Success/failure status mismatch")
            return 0.2, differences
        
        # 如果都没有结果
        if original_result is None and refactored_result is None:
            return 0.0, ["Both implementations failed"]
        
        # 比较结果结构
        if original_result and refactored_result:
            score_components = []
            
            # 检查必需的键
            required_keys = ["success", "execution_time", "shapley_values", "shapley_analysis"]
            for key in required_keys:
                if key in original_result and key in refactored_result:
                    score_components.append(1.0)
                elif key in original_result or key in refactored_result:
                    score_components.append(0.5)
                    differences.append(f"Key presence mismatch: {key}")
                else:
                    score_components.append(0.0)
                    differences.append(f"Key missing in both: {key}")
            
            # 检查数据类型
            for key in required_keys:
                if key in original_result and key in refactored_result:
                    if type(original_result[key]) == type(refactored_result[key]):
                        score_components.append(1.0)
                    else:
                        score_components.append(0.5)
                        differences.append(f"Type mismatch for {key}: {type(original_result[key])} vs {type(refactored_result[key])}")
            
            # 检查Shapley值的结构
            if "shapley_values" in original_result and "shapley_values" in refactored_result:
                orig_shapley = original_result["shapley_values"]
                refact_shapley = refactored_result["shapley_values"]
                
                if isinstance(orig_shapley, dict) and isinstance(refact_shapley, dict):
                    # 检查智能体键的一致性
                    orig_agents = set(orig_shapley.keys())
                    refact_agents = set(refact_shapley.keys())
                    
                    if orig_agents == refact_agents:
                        score_components.append(1.0)
                    else:
                        score_components.append(0.7)
                        differences.append(f"Shapley agents mismatch: {orig_agents} vs {refact_agents}")
            
            compatibility_score = sum(score_components) / max(len(score_components), 1)
        
        return compatibility_score, differences
    
    def run_all_tests(self, test_cases: Optional[List[CompatibilityTestCase]] = None) -> Dict[str, Any]:
        """运行所有兼容性测试"""
        if test_cases is None:
            test_cases = self.create_standard_test_cases()
        
        self.logger.info(f"Running {len(test_cases)} compatibility tests...")
        
        self.test_results = []
        start_time = time.time()
        
        for test_case in test_cases:
            result = self.run_compatibility_test(test_case)
            self.test_results.append(result)
        
        total_time = time.time() - start_time
        
        # 生成汇总报告
        summary = self._generate_summary_report(total_time)
        
        self.logger.info(f"Compatibility testing completed in {total_time:.2f}s")
        self.logger.info(f"Overall compatibility score: {summary['overall_compatibility_score']:.2f}")
        
        return summary
    
    def _generate_summary_report(self, total_time: float) -> Dict[str, Any]:
        """生成汇总报告"""
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r.success])
        
        overall_compatibility_score = sum(r.compatibility_score for r in self.test_results) / max(total_tests, 1)
        
        summary = {
            "timestamp": datetime.now().isoformat(),
            "total_execution_time": total_time,
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": passed_tests / max(total_tests, 1) * 100,
            "overall_compatibility_score": overall_compatibility_score,
            "test_results": [
                {
                    "name": r.test_case_name,
                    "success": r.success,
                    "compatibility_score": r.compatibility_score,
                    "differences_count": len(r.differences or []),
                    "performance_ratio": r.performance_comparison.get("time_ratio", 1.0) if r.performance_comparison else 1.0
                }
                for r in self.test_results
            ],
            "detailed_results": [
                {
                    "name": r.test_case_name,
                    "success": r.success,
                    "compatibility_score": r.compatibility_score,
                    "differences": r.differences,
                    "performance_comparison": r.performance_comparison,
                    "original_error": r.original_error,
                    "refactored_error": r.refactored_error
                }
                for r in self.test_results
            ]
        }
        
        return summary
