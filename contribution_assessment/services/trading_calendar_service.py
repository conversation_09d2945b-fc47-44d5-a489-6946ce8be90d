"""
交易日历服务实现

提供交易日历管理和周期划分的具体实现，支持数据库集成和多种划分策略
"""

import logging
import sqlite3
from datetime import datetime, date, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field

from ..interfaces.trading_calendar import ITradingCalendar, TradingCalendarError, DateValidationError, WeekDivisionError


@dataclass
class TradingCalendarConfig:
    """交易日历配置"""
    database_path: str = "data/tickers"
    default_stock: str = "AAPL"
    weekend_days: List[int] = field(default_factory=lambda: [5, 6])  # 周六、周日
    custom_holidays: List[str] = field(default_factory=list)
    enable_database_fallback: bool = True
    cache_size: int = 1000


class TradingCalendarService(ITradingCalendar):
    """
    交易日历服务实现
    
    提供完整的交易日历管理功能，包括数据库集成、周期划分和假期管理
    """
    
    def __init__(self, 
                 config: Optional[TradingCalendarConfig] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化交易日历服务
        
        Args:
            config: 日历配置
            logger: 日志记录器
        """
        self.config = config or TradingCalendarConfig()
        self.logger = logger or self._create_default_logger()
        
        # 缓存
        self._trading_days_cache: Dict[str, List[str]] = {}
        self._holidays_cache: Dict[int, List[str]] = {}
        self._custom_holidays = set(self.config.custom_holidays)
        
        # 统计信息
        self._stats = {
            "cache_hits": 0,
            "cache_misses": 0,
            "database_queries": 0,
            "fallback_calculations": 0
        }
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.TradingCalendarService")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def get_trading_days(self, 
                        start_date: str, 
                        end_date: str, 
                        use_database: bool = True,
                        stocks: Optional[List[str]] = None) -> List[str]:
        """获取指定日期范围内的交易日"""
        # 验证日期范围
        if not self.validate_date_range(start_date, end_date):
            raise DateValidationError(f"无效的日期范围: {start_date} 到 {end_date}")
        
        # 生成缓存键
        cache_key = f"{start_date}_{end_date}_{use_database}_{stocks}"
        
        # 检查缓存
        if cache_key in self._trading_days_cache:
            self._stats["cache_hits"] += 1
            return self._trading_days_cache[cache_key]
        
        self._stats["cache_misses"] += 1
        
        try:
            if use_database:
                trading_days = self._get_trading_days_from_database(start_date, end_date, stocks)
            else:
                trading_days = self._get_trading_days_by_calculation(start_date, end_date)
            
            # 缓存结果
            if len(self._trading_days_cache) < self.config.cache_size:
                self._trading_days_cache[cache_key] = trading_days
            
            return trading_days
            
        except Exception as e:
            if self.config.enable_database_fallback:
                self.logger.warning(f"数据库查询失败，使用计算方式: {e}")
                return self._get_trading_days_by_calculation(start_date, end_date)
            else:
                raise TradingCalendarError(f"获取交易日失败: {e}")
    
    def _get_trading_days_from_database(self, 
                                      start_date: str, 
                                      end_date: str, 
                                      stocks: Optional[List[str]]) -> List[str]:
        """从数据库获取交易日"""
        self._stats["database_queries"] += 1
        
        stock_codes = stocks or [self.config.default_stock]
        trading_days = set()
        
        for stock in stock_codes:
            try:
                db_path = f"{self.config.database_path}/{stock}/{stock}_data.db"
                with sqlite3.connect(db_path) as conn:
                    cursor = conn.cursor()
                    
                    # 查询交易日
                    cursor.execute("""
                        SELECT DISTINCT date 
                        FROM daily_data 
                        WHERE date >= ? AND date <= ?
                        ORDER BY date
                    """, (start_date, end_date))
                    
                    stock_trading_days = [row[0] for row in cursor.fetchall()]
                    trading_days.update(stock_trading_days)
                    
            except sqlite3.Error as e:
                self.logger.warning(f"查询股票 {stock} 的交易日失败: {e}")
                continue
        
        if not trading_days:
            raise TradingCalendarError("从数据库未找到任何交易日数据")
        
        return sorted(list(trading_days))
    
    def _get_trading_days_by_calculation(self, start_date: str, end_date: str) -> List[str]:
        """通过计算获取交易日"""
        self._stats["fallback_calculations"] += 1
        
        trading_days = []
        current_date = datetime.strptime(start_date, "%Y-%m-%d").date()
        end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
        
        while current_date <= end_date_obj:
            # 检查是否为工作日且不是假期
            if (current_date.weekday() not in self.config.weekend_days and 
                not self._is_holiday(current_date)):
                trading_days.append(current_date.strftime("%Y-%m-%d"))
            
            current_date += timedelta(days=1)
        
        return trading_days
    
    def _is_holiday(self, date_obj: date) -> bool:
        """检查是否为假期"""
        date_str = date_obj.strftime("%Y-%m-%d")
        
        # 检查自定义假期
        if date_str in self._custom_holidays:
            return True
        
        # 检查美国主要节假日
        return self._is_us_holiday(date_obj)
    
    def _is_us_holiday(self, date_obj: date) -> bool:
        """检查是否为美国假期"""
        year = date_obj.year
        month = date_obj.month
        day = date_obj.day
        
        # 新年 (1月1日)
        if month == 1 and day == 1:
            return True
        
        # 马丁·路德·金纪念日 (1月第三个星期一)
        if month == 1 and self._is_nth_weekday(date_obj, 0, 3):
            return True
        
        # 总统节 (2月第三个星期一)
        if month == 2 and self._is_nth_weekday(date_obj, 0, 3):
            return True
        
        # 阵亡将士纪念日 (5月最后一个星期一)
        if month == 5 and self._is_last_weekday(date_obj, 0):
            return True
        
        # 独立日 (7月4日)
        if month == 7 and day == 4:
            return True
        
        # 劳动节 (9月第一个星期一)
        if month == 9 and self._is_nth_weekday(date_obj, 0, 1):
            return True
        
        # 感恩节 (11月第四个星期四)
        if month == 11 and self._is_nth_weekday(date_obj, 3, 4):
            return True
        
        # 圣诞节 (12月25日)
        if month == 12 and day == 25:
            return True
        
        return False
    
    def _is_nth_weekday(self, date_obj: date, weekday: int, nth: int) -> bool:
        """检查日期是否为某月第n个指定工作日"""
        first_day = date_obj.replace(day=1)
        first_weekday = first_day.weekday()
        
        # 计算第n个指定工作日
        days_to_add = (weekday - first_weekday) % 7 + (nth - 1) * 7
        target_date = first_day + timedelta(days=days_to_add)
        
        return date_obj == target_date
    
    def _is_last_weekday(self, date_obj: date, weekday: int) -> bool:
        """检查日期是否为某月最后一个指定工作日"""
        # 找到下个月第一天
        if date_obj.month == 12:
            next_month = date_obj.replace(year=date_obj.year + 1, month=1, day=1)
        else:
            next_month = date_obj.replace(month=date_obj.month + 1, day=1)
        
        # 回退到上个月最后一天
        last_day = next_month - timedelta(days=1)
        
        # 找到最后一个指定工作日
        while last_day.weekday() != weekday:
            last_day -= timedelta(days=1)
        
        return date_obj == last_day
    
    def get_trading_weeks(self, 
                         trading_days: List[str],
                         strategy: str = "adaptive",
                         days_per_week: int = 5) -> List[Dict[str, Any]]:
        """将交易日划分为周期"""
        if not trading_days:
            raise WeekDivisionError("交易日列表为空")
        
        if strategy == "adaptive":
            return self._get_adaptive_weeks(trading_days, days_per_week)
        elif strategy == "fixed":
            return self._get_fixed_weeks(trading_days, days_per_week)
        elif strategy == "calendar":
            return self._get_calendar_weeks(trading_days)
        else:
            raise WeekDivisionError(f"不支持的划分策略: {strategy}")
    
    def _get_adaptive_weeks(self, trading_days: List[str], days_per_week: int) -> List[Dict[str, Any]]:
        """自适应周期划分"""
        weeks = []
        current_week = []
        week_number = 1
        
        for day in trading_days:
            current_week.append(day)
            
            # 如果当前周达到目标天数，创建新周
            if len(current_week) >= days_per_week:
                weeks.append({
                    "week_number": week_number,
                    "start_date": current_week[0],
                    "end_date": current_week[-1],
                    "trading_days": current_week.copy(),
                    "actual_days": len(current_week)
                })
                current_week = []
                week_number += 1
        
        # 处理剩余的天数
        if current_week:
            weeks.append({
                "week_number": week_number,
                "start_date": current_week[0],
                "end_date": current_week[-1],
                "trading_days": current_week.copy(),
                "actual_days": len(current_week)
            })
        
        return weeks
    
    def _get_fixed_weeks(self, trading_days: List[str], days_per_week: int) -> List[Dict[str, Any]]:
        """固定长度周期划分"""
        weeks = []
        week_number = 1
        
        for i in range(0, len(trading_days), days_per_week):
            week_days = trading_days[i:i + days_per_week]
            
            # 只有满足最少天数要求的周期才会被包含
            if len(week_days) >= max(2, days_per_week // 2):
                weeks.append({
                    "week_number": week_number,
                    "start_date": week_days[0],
                    "end_date": week_days[-1],
                    "trading_days": week_days,
                    "actual_days": len(week_days)
                })
                week_number += 1
        
        return weeks
    
    def _get_calendar_weeks(self, trading_days: List[str]) -> List[Dict[str, Any]]:
        """自然周划分"""
        weeks = []
        current_week = []
        week_number = 1
        current_week_start = None
        
        for day in trading_days:
            day_obj = datetime.strptime(day, "%Y-%m-%d").date()
            
            # 如果是周一或第一天，开始新周
            if day_obj.weekday() == 0 or current_week_start is None:
                if current_week:
                    # 保存前一周
                    weeks.append({
                        "week_number": week_number,
                        "start_date": current_week[0],
                        "end_date": current_week[-1],
                        "trading_days": current_week.copy(),
                        "actual_days": len(current_week)
                    })
                    week_number += 1
                
                current_week = [day]
                current_week_start = day_obj
            else:
                current_week.append(day)
        
        # 处理最后一周
        if current_week:
            weeks.append({
                "week_number": week_number,
                "start_date": current_week[0],
                "end_date": current_week[-1],
                "trading_days": current_week.copy(),
                "actual_days": len(current_week)
            })
        
        return weeks
    
    def is_trading_day(self, date_str: str) -> bool:
        """检查指定日期是否为交易日"""
        try:
            trading_days = self.get_trading_days(date_str, date_str)
            return date_str in trading_days
        except Exception:
            return False
    
    def get_next_trading_day(self, date_str: str) -> Optional[str]:
        """获取下一个交易日"""
        try:
            current_date = datetime.strptime(date_str, "%Y-%m-%d").date()
            
            # 向后查找10天
            for i in range(1, 11):
                next_date = current_date + timedelta(days=i)
                next_date_str = next_date.strftime("%Y-%m-%d")
                
                if self.is_trading_day(next_date_str):
                    return next_date_str
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取下一个交易日失败: {e}")
            return None
    
    def get_previous_trading_day(self, date_str: str) -> Optional[str]:
        """获取前一个交易日"""
        try:
            current_date = datetime.strptime(date_str, "%Y-%m-%d").date()
            
            # 向前查找10天
            for i in range(1, 11):
                prev_date = current_date - timedelta(days=i)
                prev_date_str = prev_date.strftime("%Y-%m-%d")
                
                if self.is_trading_day(prev_date_str):
                    return prev_date_str
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取前一个交易日失败: {e}")
            return None
    
    def get_trading_days_in_period(self, start_date: str, end_date: str) -> int:
        """获取指定时期内的交易日数量"""
        try:
            trading_days = self.get_trading_days(start_date, end_date)
            return len(trading_days)
        except Exception:
            return 0
    
    def validate_date_range(self, start_date: str, end_date: str) -> bool:
        """验证日期范围的有效性"""
        try:
            start_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
            end_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
            
            # 检查日期顺序
            if start_obj > end_obj:
                raise DateValidationError("开始日期不能晚于结束日期")
            
            # 检查日期范围是否合理（不超过10年）
            if (end_obj - start_obj).days > 365 * 10:
                raise DateValidationError("日期范围过大（超过10年）")
            
            return True
            
        except ValueError as e:
            raise DateValidationError(f"日期格式错误: {e}")
    
    def get_week_boundaries(self, trading_days: List[str], week_number: int) -> Dict[str, Any]:
        """获取指定周的边界信息"""
        weeks = self.get_trading_weeks(trading_days)
        
        for week in weeks:
            if week["week_number"] == week_number:
                return {
                    "week_number": week_number,
                    "start_date": week["start_date"],
                    "end_date": week["end_date"],
                    "trading_days": week["trading_days"],
                    "actual_days": week["actual_days"],
                    "start_index": trading_days.index(week["start_date"]),
                    "end_index": trading_days.index(week["end_date"])
                }
        
        raise WeekDivisionError(f"未找到第 {week_number} 周")
    
    def get_holidays(self, year: int) -> List[str]:
        """获取指定年份的假期列表"""
        if year in self._holidays_cache:
            return self._holidays_cache[year]
        
        holidays = []
        start_date = date(year, 1, 1)
        end_date = date(year, 12, 31)
        
        current_date = start_date
        while current_date <= end_date:
            if self._is_holiday(current_date):
                holidays.append(current_date.strftime("%Y-%m-%d"))
            current_date += timedelta(days=1)
        
        # 缓存结果
        self._holidays_cache[year] = holidays
        return holidays
    
    def add_custom_holiday(self, date_str: str, description: str) -> None:
        """添加自定义假期"""
        try:
            # 验证日期格式
            datetime.strptime(date_str, "%Y-%m-%d")
            self._custom_holidays.add(date_str)
            self.logger.info(f"添加自定义假期: {date_str} ({description})")
        except ValueError:
            raise DateValidationError(f"无效的日期格式: {date_str}")
    
    def remove_custom_holiday(self, date_str: str) -> None:
        """移除自定义假期"""
        self._custom_holidays.discard(date_str)
        self.logger.info(f"移除自定义假期: {date_str}")
    
    def get_calendar_statistics(self) -> Dict[str, Any]:
        """获取日历统计信息"""
        return {
            "cache_stats": self._stats.copy(),
            "custom_holidays_count": len(self._custom_holidays),
            "cached_trading_days": len(self._trading_days_cache),
            "cached_holidays": len(self._holidays_cache),
            "configuration": {
                "database_path": self.config.database_path,
                "default_stock": self.config.default_stock,
                "weekend_days": self.config.weekend_days,
                "enable_database_fallback": self.config.enable_database_fallback,
                "cache_size": self.config.cache_size
            }
        }
    
    def clear_cache(self) -> None:
        """清空缓存"""
        self._trading_days_cache.clear()
        self._holidays_cache.clear()
        self._stats = {
            "cache_hits": 0,
            "cache_misses": 0,
            "database_queries": 0,
            "fallback_calculations": 0
        }
        self.logger.info("缓存已清空")
    
    def get_custom_holidays(self) -> List[str]:
        """获取自定义假期列表"""
        return sorted(list(self._custom_holidays))
    
    def set_custom_holidays(self, holidays: List[str]) -> None:
        """设置自定义假期列表"""
        self._custom_holidays = set(holidays)
        self.logger.info(f"设置了 {len(holidays)} 个自定义假期")