"""
智能体创建服务

负责智能体的创建和管理，实现低耦合的智能体创建逻辑
"""

import logging
from typing import Dict, Any, Optional, List
from abc import ABC, abstractmethod

from ..interfaces.agent_creation_service import IAgentCreationService
from ..llm_interface import LLMInterface


class AgentCreationService(IAgentCreationService):
    """
    智能体创建服务实现
    
    通过依赖注入和工厂模式创建智能体，避免直接依赖具体实现
    """
    
    def __init__(self,
                 llm_interface: LLMInterface,
                 logger: Optional[logging.Logger] = None,
                 opro_enabled: bool = False):
        """
        初始化智能体创建服务

        Args:
            llm_interface: LLM接口实例
            logger: 日志记录器
            opro_enabled: 是否启用OPRO优化功能
        """
        self.llm_interface = llm_interface
        self.logger = logger or self._create_default_logger()
        self.opro_enabled = opro_enabled
        
        # 智能体配置
        self.default_agent_ids = ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]
        
        # 统计信息
        self.creation_stats = {
            "total_created": 0,
            "successful_created": 0,
            "failed_created": 0
        }
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.AgentCreationService")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def create_default_agents(self) -> Dict[str, Any]:
        """
        创建默认智能体集合
        
        Returns:
            智能体字典 {agent_id: agent_instance}
        """
        return self.create_agents_subset(self.default_agent_ids)
    
    def create_agents_subset(self, agent_ids: List[str]) -> Dict[str, Any]:
        """
        创建指定的智能体子集
        
        Args:
            agent_ids: 要创建的智能体ID列表
            
        Returns:
            智能体字典
        """
        agents = {}
        
        for agent_id in agent_ids:
            try:
                agent = self.create_single_agent(agent_id)
                if agent:
                    agents[agent_id] = agent
                    self.creation_stats["successful_created"] += 1
                    self.logger.info(f"✅ 智能体 {agent_id} 创建成功")
                else:
                    self.creation_stats["failed_created"] += 1
                    self.logger.error(f"❌ 智能体 {agent_id} 创建失败")
                    
            except Exception as e:
                self.creation_stats["failed_created"] += 1
                self.logger.error(f"❌ 智能体 {agent_id} 创建异常: {e}")
        
        self.creation_stats["total_created"] += len(agent_ids)
        self.logger.info(f"智能体创建完成，成功: {len(agents)}/{len(agent_ids)}")
        
        return agents
    
    def create_single_agent(self, agent_id: str) -> Optional[Any]:
        """
        创建单个智能体
        
        Args:
            agent_id: 智能体ID
            
        Returns:
            智能体实例或None
        """
        try:
            # 使用智能体工厂创建
            agent_factory = self._get_agent_factory()
            return agent_factory.create_agent(agent_id)
            
        except Exception as e:
            self.logger.error(f"创建智能体 {agent_id} 失败: {e}")
            return None
    
    def _get_agent_factory(self):
        """
        获取智能体工厂实例

        封装智能体工厂的获取逻辑，便于测试和扩展
        """
        from agents.agent_factory import AgentFactory
        return AgentFactory(
            llm_interface=self.llm_interface,
            logger=self.logger,
            opro_enabled=self.opro_enabled
        )
    
    def get_available_agent_ids(self) -> List[str]:
        """
        获取可用的智能体ID列表
        
        Returns:
            智能体ID列表
        """
        return self.default_agent_ids.copy()
    
    def validate_agent_id(self, agent_id: str) -> bool:
        """
        验证智能体ID是否有效
        
        Args:
            agent_id: 智能体ID
            
        Returns:
            是否有效
        """
        return agent_id in self.default_agent_ids
    
    def get_creation_stats(self) -> Dict[str, int]:
        """
        获取创建统计信息
        
        Returns:
            统计信息字典
        """
        return self.creation_stats.copy()
    
    def reset_stats(self) -> None:
        """重置统计信息"""
        self.creation_stats = {
            "total_created": 0,
            "successful_created": 0,
            "failed_created": 0
        }


class MockAgentCreationService(IAgentCreationService):
    """
    模拟智能体创建服务
    
    用于测试和开发环境
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.default_agent_ids = ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"]
    
    def create_default_agents(self) -> Dict[str, Any]:
        """创建模拟智能体"""
        agents = {}
        for agent_id in self.default_agent_ids:
            agents[agent_id] = MockAgent(agent_id)
        
        self.logger.info(f"创建了 {len(agents)} 个模拟智能体")
        return agents
    
    def create_agents_subset(self, agent_ids: List[str]) -> Dict[str, Any]:
        """创建指定的模拟智能体子集"""
        agents = {}
        for agent_id in agent_ids:
            if agent_id in self.default_agent_ids:
                agents[agent_id] = MockAgent(agent_id)
        return agents
    
    def create_single_agent(self, agent_id: str) -> Optional[Any]:
        """创建单个模拟智能体"""
        if agent_id in self.default_agent_ids:
            return MockAgent(agent_id)
        return None
    
    def get_available_agent_ids(self) -> List[str]:
        """获取可用的智能体ID列表"""
        return self.default_agent_ids.copy()
    
    def validate_agent_id(self, agent_id: str) -> bool:
        """验证智能体ID"""
        return agent_id in self.default_agent_ids
    
    def get_creation_stats(self) -> Dict[str, int]:
        """获取创建统计"""
        return {"total_created": 0, "successful_created": 0, "failed_created": 0}
    
    def reset_stats(self) -> None:
        """重置统计"""
        pass


class MockAgent:
    """模拟智能体类"""
    
    def __init__(self, agent_id: str):
        self.agent_id = agent_id
        self.name = f"Mock{agent_id}"
    
    def __repr__(self):
        return f"MockAgent({self.agent_id})"
