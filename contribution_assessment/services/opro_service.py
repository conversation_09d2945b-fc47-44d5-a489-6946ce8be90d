"""
OPRO优化服务实现

负责智能体提示词优化的服务，包括：
- 提示词优化
- 性能评估
- A/B测试
- 历史管理
"""

import time
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime

from ..interfaces.opro_service import IOPROService, OptimizationError, PromptValidationError
from ..dto.phase_results_dto import OPROResult
from .historical_score_service import HistoricalScoreService
from .prompt_template_service import PromptTemplateService


class OPROService(IOPROService):
    """
    OPRO优化服务实现
    
    负责智能体提示词的优化和管理，采用可插拔的设计模式
    支持启用/禁用状态管理和与现有OPRO组件的兼容性
    """
    
    def __init__(self,
                 opro_optimizer: Optional[Any] = None,
                 historical_score_manager: Optional[Any] = None,
                 enabled: bool = False,
                 logger: Optional[logging.Logger] = None,
                 config: Optional[Dict[str, Any]] = None,
                 historical_score_service: Optional[HistoricalScoreService] = None,
                 prompt_template_service: Optional[PromptTemplateService] = None):
        """
        初始化OPRO服务
        
        Args:
            opro_optimizer: OPRO优化器实例（可选）
            historical_score_manager: 历史得分管理器实例（可选）
            enabled: 服务是否启用
            logger: 日志记录器
            config: 服务配置
            historical_score_service: 历史得分服务实例（可选）
            prompt_template_service: 提示词模板服务实例（可选）
        """
        self.opro_optimizer = opro_optimizer
        self.historical_score_manager = historical_score_manager
        self.enabled = enabled
        self.logger = logger or logging.getLogger(__name__)
        self.config = config or {}
        
        # 新增服务集成
        self.historical_score_service = historical_score_service
        self.prompt_template_service = prompt_template_service
        
        # 内部状态
        self._optimization_history: List[Dict[str, Any]] = []
        self._prompt_templates: Dict[str, str] = {}
        self._agent_versions: Dict[str, List[str]] = {}
        
        if self.enabled:
            self.logger.info("OPRO服务已启用")
        else:
            self.logger.info("OPRO服务已禁用")
    
    def optimize_agents(self,
                       shapley_values: Dict[str, float],
                       agents: Dict[str, Any],
                       config: Optional[Dict[str, Any]] = None) -> OPROResult:
        """
        基于Shapley值优化智能体
        
        Args:
            shapley_values: 智能体Shapley值
            agents: 智能体配置
            config: 优化配置参数
            
        Returns:
            OPROResult: 优化结果
        """
        start_time = time.time()
        
        try:
            if not self.enabled:
                self.logger.info("OPRO优化未启用，跳过优化")
                return OPROResult(
                    success=True,
                    enabled=False,
                    execution_time=time.time() - start_time
                )
            
            # 检查必要的组件
            if not self.opro_optimizer:
                self.logger.warning("OPRO优化器未配置，无法进行优化")
                return OPROResult(
                    success=False,
                    enabled=True,
                    error="OPRO优化器未配置",
                    execution_time=time.time() - start_time
                )
            
            # 更新历史得分
            if self.historical_score_service and shapley_values:
                self._update_historical_scores_via_service(shapley_values)
            elif self.historical_score_manager and shapley_values:
                self._update_historical_scores(shapley_values)
            
            # 识别需要优化的智能体
            target_agents = self._identify_optimization_targets(shapley_values, agents)
            
            if not target_agents:
                self.logger.info("未识别到需要优化的智能体")
                return OPROResult(
                    success=True,
                    enabled=True,
                    execution_time=time.time() - start_time
                )
            
            # 执行优化，传递运行时智能体实例
            optimization_result = self._run_optimization_cycle(target_agents, agents, config)
            
            # 记录优化历史
            self._record_optimization_history(optimization_result, target_agents)
            
            execution_time = time.time() - start_time
            
            result = OPROResult(
                success=optimization_result.get("success", False),
                enabled=True,
                execution_time=execution_time,
                historical_scores_updated=bool(self.historical_score_manager)
            )
            
            if optimization_result.get("success", False):
                result.set_optimization_result(optimization_result, target_agents)
                self.logger.info(f"✅ OPRO优化完成，优化了 {len(target_agents)} 个智能体，耗时: {execution_time:.2f}s")
            else:
                result.error = optimization_result.get("error", "优化失败")
                self.logger.warning(f"⚠️ OPRO优化部分失败: {result.error}，耗时: {execution_time:.2f}s")
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"❌ OPRO优化失败: {e}，耗时: {execution_time:.2f}s")
            return OPROResult(
                success=False,
                enabled=self.enabled,
                error=str(e),
                execution_time=execution_time
            )
    
    def optimize_single_agent(self,
                             agent_name: str,
                             current_prompt: str,
                             performance_score: float,
                             failure_cases: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """
        优化单个智能体的提示词
        
        Args:
            agent_name: 智能体名称
            current_prompt: 当前提示词
            performance_score: 性能评分
            failure_cases: 失败案例（可选）
            
        Returns:
            Dict[str, Any]: 优化结果
        """
        try:
            if not self.enabled:
                return {
                    "success": False,
                    "error": "OPRO服务未启用"
                }
            
            if not self.opro_optimizer:
                return {
                    "success": False,
                    "error": "OPRO优化器未配置"
                }
            
            # 调用优化器进行单个智能体优化
            optimization_result = self.opro_optimizer.optimize_single_agent(
                agent_name=agent_name,
                current_prompt=current_prompt,
                performance_score=performance_score,
                failure_cases=failure_cases or []
            )
            
            # 更新提示词模板
            if optimization_result.get("success", False):
                new_prompt = optimization_result.get("optimized_prompt", current_prompt)
                if self.prompt_template_service:
                    self.prompt_template_service.save_template(
                        agent_name=agent_name,
                        content=new_prompt,
                        metadata={
                            "optimization_timestamp": datetime.now().isoformat(),
                            "previous_score": performance_score,
                            "optimization_source": "single_agent_optimization"
                        },
                        make_active=True
                    )
                else:
                    self._update_prompt_template(agent_name, new_prompt)
            
            return optimization_result
            
        except Exception as e:
            self.logger.error(f"单个智能体优化失败 ({agent_name}): {e}")
            raise OptimizationError(f"优化智能体 {agent_name} 失败: {str(e)}")
    
    def get_optimization_history(self,
                               agent_name: Optional[str] = None,
                               limit: int = 100) -> List[Dict[str, Any]]:
        """
        获取优化历史记录
        
        Args:
            agent_name: 特定智能体（可选）
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 优化历史列表
        """
        try:
            history = self._optimization_history
            
            if agent_name:
                history = [h for h in history if h.get("agent_name") == agent_name]
            
            # 按时间戳排序，最新的在前
            history.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
            
            return history[:limit]
            
        except Exception as e:
            self.logger.error(f"获取优化历史失败: {e}")
            return []
    
    def validate_prompt(self,
                       prompt: str,
                       agent_type: str) -> Dict[str, Any]:
        """
        验证提示词的有效性
        
        Args:
            prompt: 要验证的提示词
            agent_type: 智能体类型
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        try:
            if not self.enabled or not self.opro_optimizer:
                # 简化验证逻辑作为后备
                if not prompt or not prompt.strip():
                    return {"valid": False, "error": "提示词不能为空"}
                return {"valid": True, "length": len(prompt)}
            
            # 委托给OPROOptimizer进行详细验证
            return self.opro_optimizer.evaluate_prompt_quality(prompt, agent_type)
            
        except Exception as e:
            self.logger.error(f"提示词验证失败: {e}")
            raise PromptValidationError(f"提示词验证失败: {str(e)}")
    

    def optimize_all_agents(self, agent_ids: List[str], current_prompts: Dict[str, str]) -> Dict[str, Any]:
        """
        优化所有指定的智能体（兼容性方法）

        Args:
            agent_ids: 要优化的智能体ID列表
            current_prompts: 当前提示词映射

        Returns:
            Dict[str, Any]: 优化结果
        """
        try:
            if not self.enabled:
                return {
                    "success": False,
                    "error": "OPRO服务未启用",
                    "optimized_agents": []
                }
            
            if not self.opro_optimizer:
                return {
                    "success": False,
                    "error": "OPRO优化器未配置",
                    "optimized_agents": []
                }

            # 委托给OPROOptimizer进行批量优化
            return self.opro_optimizer.optimize_all_agents(agent_ids, current_prompts)

        except Exception as e:
            self.logger.error(f"批量优化失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "optimized_agents": []
            }

    def get_stats(self) -> Dict[str, Any]:
        """
        获取OPRO服务统计信息（兼容性方法）

        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            base_stats = {
                "enabled": self.enabled,
                "service_type": "OPROService",
                "available_templates": len(self._prompt_templates)
            }
            
            # 如果有OPROOptimizer，获取其详细统计信息
            if self.opro_optimizer and hasattr(self.opro_optimizer, 'get_optimization_stats'):
                optimizer_stats = self.opro_optimizer.get_optimization_stats()
                base_stats.update(optimizer_stats)
            
            return base_stats
            
        except Exception as e:
            self.logger.error(f"获取统计信息失败: {e}")
            return {
                "enabled": self.enabled,
                "error": str(e)
            }

    # 私有方法
    
    def _update_historical_scores(self, shapley_values: Dict[str, float]) -> None:
        """更新历史得分"""
        if self.historical_score_manager:
            try:
                for agent_name, score in shapley_values.items():
                    self.historical_score_manager.update_score(agent_name, score)
            except Exception as e:
                self.logger.error(f"更新历史得分失败: {e}")
    
    def _update_historical_scores_via_service(self, shapley_values: Dict[str, float]) -> None:
        """通过历史得分服务更新得分"""
        if self.historical_score_service:
            try:
                # 构造周期性结果格式
                periodic_result = {
                    "success": True,
                    "periodic_data": [{
                        "week_data": [{
                            "week": 1,
                            "shapley_values": shapley_values,
                            "end_date": datetime.now().strftime("%Y-%m-%d")
                        }]
                    }]
                }
                
                self.historical_score_service.update_scores_from_periodic_results(periodic_result)
            except Exception as e:
                self.logger.error(f"通过服务更新历史得分失败: {e}")
    
    def _identify_optimization_targets(self,
                                     shapley_values: Dict[str, float],
                                     agents: Dict[str, Any]) -> List[str]:
        """识别需要优化的智能体"""
        if not shapley_values:
            return []
        
        # 找出表现最差的智能体
        sorted_agents = sorted(shapley_values.items(), key=lambda x: x[1])
        
        # 只优化表现最差的智能体
        if sorted_agents:
            worst_agent = sorted_agents[0][0]
            if worst_agent in agents:
                return [worst_agent]
        
        return []
    
    def _run_optimization_cycle(self,
                              target_agents: List[str],
                              agents: Dict[str, Any],
                              config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """运行优化循环"""
        try:
            if not self.opro_optimizer:
                return {
                    "success": False,
                    "error": "OPRO优化器未配置"
                }
            
            # 从运行时智能体实例中获取当前提示词
            current_prompts = self._extract_prompts_from_agents(target_agents, agents)
            
            # 调用优化器，传递运行时提示词
            if hasattr(self.opro_optimizer, 'optimize_all_agents'):
                # 使用新的接口方法
                result = self.opro_optimizer.optimize_all_agents(
                    agent_ids=target_agents,
                    current_prompts=current_prompts
                )
            else:
                # 回退到旧的接口方法
                result = self.opro_optimizer.run_optimization_cycle(
                    target_agents=target_agents,
                    force_optimization=config.get("force_optimization", False) if config else False
                )
            
            return result
            
        except Exception as e:
            self.logger.error(f"优化循环失败: {e}")
            return {
                "success": False,
                "error": str(e)
            }
    
    def _record_optimization_history(self,
                                   optimization_result: Dict[str, Any],
                                   target_agents: List[str]) -> None:
        """记录优化历史"""
        try:
            record = {
                "timestamp": datetime.now().isoformat(),
                "target_agents": target_agents,
                "success": optimization_result.get("success", False),
                "result": optimization_result,
                "action": "optimization"
            }
            
            self._optimization_history.append(record)
            
            # 限制历史记录数量
            if len(self._optimization_history) > 1000:
                self._optimization_history = self._optimization_history[-500:]
                
        except Exception as e:
            self.logger.error(f"记录优化历史失败: {e}")
    
    def _extract_prompts_from_agents(self, target_agents: List[str], agents: Dict[str, Any]) -> Dict[str, str]:
        """
        从运行时智能体实例中提取当前提示词
        
        Args:
            target_agents: 目标智能体列表
            agents: 运行时智能体实例字典
            
        Returns:
            Dict[str, str]: 智能体ID到提示词的映射
        """
        current_prompts = {}
        
        for agent_id in target_agents:
            if agent_id in agents:
                agent = agents[agent_id]
                try:
                    # 尝试从智能体实例中获取提示词
                    if hasattr(agent, 'get_prompt_template'):
                        current_prompts[agent_id] = agent.get_prompt_template()
                    elif hasattr(agent, 'prompt_template'):
                        current_prompts[agent_id] = agent.prompt_template
                    elif hasattr(agent, 'prompt'):
                        current_prompts[agent_id] = agent.prompt
                    else:
                        # 如果无法获取提示词，使用默认值
                        current_prompts[agent_id] = f"默认提示词 - {agent_id}"
                        self.logger.warning(f"无法从智能体 {agent_id} 获取提示词，使用默认值")
                except Exception as e:
                    current_prompts[agent_id] = f"默认提示词 - {agent_id}"
                    self.logger.error(f"从智能体 {agent_id} 提取提示词失败: {e}")
            else:
                # 智能体不存在，使用默认提示词
                current_prompts[agent_id] = f"默认提示词 - {agent_id}"
                self.logger.warning(f"智能体 {agent_id} 不存在于运行时实例中，使用默认值")
        
        return current_prompts
    
    def _update_prompt_template(self, agent_name: str, new_prompt: str) -> None:
        """更新提示词模板"""
        try:
            self._prompt_templates[agent_name] = new_prompt
            
            # 更新版本历史
            if agent_name not in self._agent_versions:
                self._agent_versions[agent_name] = []
            
            version_id = f"v{len(self._agent_versions[agent_name]) + 1}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            self._agent_versions[agent_name].append(version_id)
            
            # 限制版本历史数量
            if len(self._agent_versions[agent_name]) > 50:
                self._agent_versions[agent_name] = self._agent_versions[agent_name][-25:]
                
        except Exception as e:
            self.logger.error(f"更新提示词模板失败 ({agent_name}): {e}")