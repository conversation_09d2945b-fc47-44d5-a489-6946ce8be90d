"""
历史得分服务

封装历史得分管理的服务层，提供统一的历史数据管理接口
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import json

from ..historical_score_manager import HistoricalScoreManager


class HistoricalScoreService:
    """
    历史得分服务
    
    封装历史得分管理器，提供智能体性能趋势分析和优化建议生成功能
    """
    
    def __init__(self,
                 historical_score_manager: Optional[HistoricalScoreManager] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化历史得分服务
        
        Args:
            historical_score_manager: 历史得分管理器实例
            logger: 日志记录器
        """
        self.historical_score_manager = historical_score_manager
        self.logger = logger or logging.getLogger(__name__)
        
        # 内部缓存
        self._trend_cache: Dict[str, Dict[str, Any]] = {}
        self._cache_timestamp: Optional[datetime] = None
        self._cache_ttl = 300  # 5分钟缓存
    
    def update_scores_from_periodic_results(self, periodic_shapley_result: Dict[str, Any]) -> bool:
        """
        从周期性Shapley结果更新历史得分
        
        Args:
            periodic_shapley_result: 周期性Shapley值计算结果
            
        Returns:
            bool: 是否更新成功
        """
        try:
            if not self.historical_score_manager:
                self.logger.warning("历史得分管理器未配置，跳过更新")
                return False
            
            if not periodic_shapley_result.get("success", False):
                self.logger.warning("周期性Shapley值计算失败，无法更新历史得分")
                return False
            
            # 提取周期性数据
            periodic_data = periodic_shapley_result.get("periodic_data", [])
            if not periodic_data:
                self.logger.warning("周期性Shapley结果中没有找到periodic_data")
                return False
            
            # 更新每个智能体的历史得分
            updated_agents = []
            for period_data in periodic_data:
                week_data = period_data.get("week_data", [])
                for week_info in week_data:
                    shapley_values = week_info.get("shapley_values", {})
                    week_number = week_info.get("week", 0)
                    
                    for agent_name, score in shapley_values.items():
                        try:
                            # 构建评估日期
                            evaluation_date = week_info.get("end_date", datetime.now().strftime("%Y-%m-%d"))
                            
                            # 更新历史得分
                            self.historical_score_manager.update_agent_score(
                                agent_id=agent_name,
                                shapley_score=score,
                                evaluation_date=evaluation_date,
                                metadata={
                                    "week_number": week_number,
                                    "period_type": "weekly",
                                    "source": "periodic_shapley_calculation"
                                }
                            )
                            
                            if agent_name not in updated_agents:
                                updated_agents.append(agent_name)
                                
                        except Exception as e:
                            self.logger.error(f"更新智能体 {agent_name} 历史得分失败: {e}")
                            continue
            
            self.logger.info(f"成功更新 {len(updated_agents)} 个智能体的历史得分")
            
            # 清除缓存
            self._clear_cache()
            
            return True
            
        except Exception as e:
            self.logger.error(f"从周期性结果更新历史得分失败: {e}")
            return False
    
    def identify_worst_performing_agent(self,
                                      periodic_shapley_result: Dict[str, Any],
                                      target_agents: List[str]) -> Optional[str]:
        """
        从周期性Shapley值结果中识别表现最差的智能体
        
        Args:
            periodic_shapley_result: 周期性Shapley值计算结果
            target_agents: 目标智能体列表
            
        Returns:
            Optional[str]: 表现最差的智能体ID，如果无法确定则返回None
        """
        try:
            # 获取周期性结果数据
            periodic_data = periodic_shapley_result.get("periodic_data", [])
            if not periodic_data:
                self.logger.warning("周期性Shapley结果中没有找到periodic_data")
                return None
            
            # 计算每个智能体的平均得分
            agent_scores = {}
            agent_counts = {}
            
            for period_data in periodic_data:
                week_data = period_data.get("week_data", [])
                for week_info in week_data:
                    shapley_values = week_info.get("shapley_values", {})
                    
                    for agent_name, score in shapley_values.items():
                        if agent_name in target_agents:
                            if agent_name not in agent_scores:
                                agent_scores[agent_name] = 0.0
                                agent_counts[agent_name] = 0
                            
                            agent_scores[agent_name] += score
                            agent_counts[agent_name] += 1
            
            # 计算平均得分
            average_scores = {}
            for agent_name in agent_scores:
                if agent_counts[agent_name] > 0:
                    average_scores[agent_name] = agent_scores[agent_name] / agent_counts[agent_name]
            
            if not average_scores:
                self.logger.warning("无法计算任何智能体的平均得分")
                return None
            
            # 找出表现最差的智能体
            worst_agent = min(average_scores.items(), key=lambda x: x[1])[0]
            worst_score = average_scores[worst_agent]
            
            self.logger.info(f"识别出表现最差的智能体: {worst_agent} (平均得分: {worst_score:.4f})")
            
            return worst_agent
            
        except Exception as e:
            self.logger.error(f"识别表现最差智能体失败: {e}")
            return None
    
    def get_agent_performance_trend(self, agent_name: str, days: int = 30) -> Dict[str, Any]:
        """
        获取智能体性能趋势分析
        
        Args:
            agent_name: 智能体名称
            days: 分析天数
            
        Returns:
            Dict[str, Any]: 性能趋势分析结果
        """
        try:
            # 检查缓存
            if self._is_cache_valid() and agent_name in self._trend_cache:
                return self._trend_cache[agent_name]
            
            if not self.historical_score_manager:
                return {"error": "历史得分管理器未配置"}
            
            # 获取历史数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            
            history = self.historical_score_manager.get_agent_history(
                agent_name, 
                start_date.strftime("%Y-%m-%d"),
                end_date.strftime("%Y-%m-%d")
            )
            
            if not history:
                return {
                    "agent_name": agent_name,
                    "trend": "no_data",
                    "scores": [],
                    "average_score": 0.0,
                    "trend_direction": "unknown"
                }
            
            # 分析趋势
            scores = [item.get("shapley_score", 0.0) for item in history]
            dates = [item.get("evaluation_date", "") for item in history]
            
            # 计算趋势方向
            trend_direction = self._calculate_trend_direction(scores)
            
            # 计算性能指标
            average_score = sum(scores) / len(scores) if scores else 0.0
            max_score = max(scores) if scores else 0.0
            min_score = min(scores) if scores else 0.0
            
            # 计算变化率
            score_change = 0.0
            if len(scores) >= 2:
                score_change = ((scores[-1] - scores[0]) / abs(scores[0])) * 100 if scores[0] != 0 else 0.0
            
            result = {
                "agent_name": agent_name,
                "trend": trend_direction,
                "scores": scores,
                "dates": dates,
                "average_score": average_score,
                "max_score": max_score,
                "min_score": min_score,
                "score_change_percent": score_change,
                "trend_direction": trend_direction,
                "data_points": len(scores),
                "analysis_period_days": days
            }
            
            # 更新缓存
            self._trend_cache[agent_name] = result
            self._cache_timestamp = datetime.now()
            
            return result
            
        except Exception as e:
            self.logger.error(f"获取智能体性能趋势失败 ({agent_name}): {e}")
            return {
                "agent_name": agent_name,
                "error": str(e),
                "trend": "error"
            }
    
    def generate_optimization_suggestions(self,
                                        agent_name: str,
                                        current_performance: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        生成优化建议
        
        Args:
            agent_name: 智能体名称
            current_performance: 当前性能数据
            
        Returns:
            List[Dict[str, Any]]: 优化建议列表
        """
        try:
            suggestions = []
            
            # 获取性能趋势
            trend_analysis = self.get_agent_performance_trend(agent_name)
            
            # 基于趋势生成建议
            if trend_analysis.get("trend_direction") == "declining":
                suggestions.append({
                    "type": "performance_decline",
                    "priority": "high",
                    "suggestion": "性能持续下降，建议立即优化",
                    "details": f"过去{trend_analysis.get('analysis_period_days', 30)}天性能下降{abs(trend_analysis.get('score_change_percent', 0)):.1f}%",
                    "action": "immediate_optimization"
                })
            
            # 基于历史表现生成建议
            if not self.historical_score_manager:
                return suggestions
            
            # 获取历史优化记录
            optimization_history = self.historical_score_manager.get_optimization_history(agent_name, limit=5)
            
            if len(optimization_history) >= 3:
                # 分析优化频率
                recent_optimizations = [opt for opt in optimization_history 
                                      if self._is_recent_optimization(opt.get("optimization_date", ""))]
                
                if len(recent_optimizations) >= 2:
                    suggestions.append({
                        "type": "optimization_frequency",
                        "priority": "medium",
                        "suggestion": "优化频率较高，建议分析根本原因",
                        "details": f"近期已进行{len(recent_optimizations)}次优化",
                        "action": "analyze_root_cause"
                    })
            
            # 基于当前性能数据生成建议
            current_score = current_performance.get("shapley_score", 0.0)
            average_score = trend_analysis.get("average_score", 0.0)
            
            if current_score < average_score * 0.8:
                suggestions.append({
                    "type": "performance_below_average",
                    "priority": "high",
                    "suggestion": "当前性能低于历史平均水平",
                    "details": f"当前得分{current_score:.4f}，历史平均{average_score:.4f}",
                    "action": "targeted_optimization"
                })
            
            return suggestions
            
        except Exception as e:
            self.logger.error(f"生成优化建议失败 ({agent_name}): {e}")
            return [{
                "type": "error",
                "priority": "low",
                "suggestion": "无法生成优化建议",
                "details": str(e),
                "action": "check_system"
            }]
    
    def get_ab_test_comparison_data(self,
                                  agent_name: str,
                                  optimization_id: str) -> Dict[str, Any]:
        """
        获取A/B测试比较数据
        
        Args:
            agent_name: 智能体名称
            optimization_id: 优化ID
            
        Returns:
            Dict[str, Any]: A/B测试比较数据
        """
        try:
            if not self.historical_score_manager:
                return {"error": "历史得分管理器未配置"}
            
            # 获取优化记录
            optimization_record = self.historical_score_manager.get_optimization_record(optimization_id)
            
            if not optimization_record:
                return {"error": "优化记录不存在"}
            
            # 获取优化前后的性能数据
            optimization_date = optimization_record.get("optimization_date", "")
            
            # 获取优化前的性能
            before_performance = self.historical_score_manager.get_agent_scores_before_date(
                agent_name, optimization_date, limit=10
            )
            
            # 获取优化后的性能
            after_performance = self.historical_score_manager.get_agent_scores_after_date(
                agent_name, optimization_date, limit=10
            )
            
            # 计算比较统计
            before_scores = [item.get("shapley_score", 0.0) for item in before_performance]
            after_scores = [item.get("shapley_score", 0.0) for item in after_performance]
            
            before_avg = sum(before_scores) / len(before_scores) if before_scores else 0.0
            after_avg = sum(after_scores) / len(after_scores) if after_scores else 0.0
            
            improvement = after_avg - before_avg
            improvement_percent = (improvement / before_avg * 100) if before_avg != 0 else 0.0
            
            return {
                "agent_name": agent_name,
                "optimization_id": optimization_id,
                "optimization_date": optimization_date,
                "before_performance": {
                    "scores": before_scores,
                    "average": before_avg,
                    "count": len(before_scores)
                },
                "after_performance": {
                    "scores": after_scores,
                    "average": after_avg,
                    "count": len(after_scores)
                },
                "improvement": improvement,
                "improvement_percent": improvement_percent,
                "is_improvement": improvement > 0,
                "significance": "significant" if abs(improvement_percent) > 5 else "marginal"
            }
            
        except Exception as e:
            self.logger.error(f"获取A/B测试比较数据失败 ({agent_name}): {e}")
            return {"error": str(e)}
    
    # 私有方法
    
    def _calculate_trend_direction(self, scores: List[float]) -> str:
        """计算趋势方向"""
        if len(scores) < 2:
            return "unknown"
        
        # 简单线性回归计算趋势
        n = len(scores)
        x = list(range(n))
        
        # 计算斜率
        x_mean = sum(x) / n
        y_mean = sum(scores) / n
        
        numerator = sum((x[i] - x_mean) * (scores[i] - y_mean) for i in range(n))
        denominator = sum((x[i] - x_mean) ** 2 for i in range(n))
        
        if denominator == 0:
            return "stable"
        
        slope = numerator / denominator
        
        if slope > 0.01:
            return "improving"
        elif slope < -0.01:
            return "declining"
        else:
            return "stable"
    
    def _is_recent_optimization(self, optimization_date: str, days: int = 30) -> bool:
        """检查是否为近期优化"""
        try:
            opt_date = datetime.strptime(optimization_date, "%Y-%m-%d")
            cutoff_date = datetime.now() - timedelta(days=days)
            return opt_date > cutoff_date
        except:
            return False
    
    def _is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        if not self._cache_timestamp:
            return False
        
        return (datetime.now() - self._cache_timestamp).total_seconds() < self._cache_ttl
    
    def _clear_cache(self) -> None:
        """清除缓存"""
        self._trend_cache.clear()
        self._cache_timestamp = None