"""
周期性优化服务实现

提供具体的周期性优化功能，通过依赖注入的方式使用其他服务
"""

import logging
import time
from typing import Dict, List, Optional, Any
from datetime import datetime

from ..interfaces.weekly_optimization_service import (
    IWeeklyOptimizationService, 
    WeeklyOptimizationConfig, 
    WeeklyOptimizationResult,
    WeeklyOptimizationError,
    OptimizationConfigurationError,
    OptimizationExecutionError
)
from ..interfaces.assessment_service import IAssessmentService
from ..interfaces.phase_coordinator import IPhaseCoordinator
from ..dto.assessment_dto import AssessmentRequest, AssessmentResult
from ..weekly_optimization_manager import WeeklyOptimizationManager
from ..llm_interface import LLMInterface


class WeeklyOptimizationService(IWeeklyOptimizationService):
    """
    周期性优化服务实现
    
    通过依赖注入的方式协调各个服务，提供周期性优化功能
    """
    
    def __init__(self,
                 phase_coordinator: IPhaseCoordinator,
                 llm_interface: Optional[LLMInterface] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化周期性优化服务
        
        Args:
            phase_coordinator: 阶段协调器
            llm_interface: LLM接口（可选）
            logger: 日志记录器（可选）
        """
        self.phase_coordinator = phase_coordinator
        self.llm_interface = llm_interface
        self.logger = logger or logging.getLogger(__name__)
        
        # 内部状态
        self._optimization_manager: Optional[WeeklyOptimizationManager] = None
        self._is_initialized = False
        self._current_config: Optional[WeeklyOptimizationConfig] = None
        self._performance_metrics = {
            'total_optimizations': 0,
            'successful_optimizations': 0,
            'failed_optimizations': 0,
            'total_execution_time': 0.0,
            'last_optimization_time': None
        }
    
    def run_weekly_optimization(self,
                              request: AssessmentRequest,
                              config: WeeklyOptimizationConfig) -> WeeklyOptimizationResult:
        """
        执行周期性优化
        
        Args:
            request: 评估请求
            config: 周期性优化配置
            
        Returns:
            WeeklyOptimizationResult: 优化结果
        """
        start_time = time.time()
        self._performance_metrics['total_optimizations'] += 1
        
        try:
            self.logger.info("开始执行周期性优化")
            
            # 验证配置
            self._validate_config(config)
            self._current_config = config
            
            # 检查是否需要LLM接口
            if not self.llm_interface:
                raise OptimizationConfigurationError("LLM接口未配置，无法执行周期性优化")
            
            # 创建或更新优化管理器
            if not self._optimization_manager:
                self._create_optimization_manager(config)
            
            # 准备智能体和配置
            agents = request.agents or {}
            target_agents = request.target_agents or list(agents.keys())
            system_config = request.config or {}
            
            # 初始化模拟
            start_date = system_config.get("start_date", "2025-01-01")
            end_date = system_config.get("end_date", "2025-04-30")
            
            success = self._optimization_manager.initialize_simulation(
                start_date=start_date,
                end_date=end_date,
                agents=agents,
                system_config=system_config
            )
            
            if not success:
                raise OptimizationExecutionError("周期性优化管理器初始化失败")
            
            self.logger.info(f"周期性优化配置: 频率={config.optimization_frequency}天, 目标智能体={target_agents}")
            
            # 执行优化 - 通过PhaseCoordinator协调
            result = self._execute_optimization_through_coordinator(
                request=request,
                config=config,
                start_date=start_date,
                end_date=end_date
            )
            
            # 更新性能指标
            execution_time = time.time() - start_time
            self._performance_metrics['successful_optimizations'] += 1
            self._performance_metrics['total_execution_time'] += execution_time
            self._performance_metrics['last_optimization_time'] = datetime.now().isoformat()
            
            result.execution_time = execution_time
            
            self.logger.info(f"周期性优化完成，耗时: {execution_time:.2f}秒")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self._performance_metrics['failed_optimizations'] += 1
            self._performance_metrics['total_execution_time'] += execution_time
            
            self.logger.error(f"周期性优化失败: {e}")
            
            return WeeklyOptimizationResult(
                success=False,
                error=str(e),
                execution_time=execution_time
            )
        finally:
            # 清理资源
            if self._optimization_manager:
                self._optimization_manager.cleanup()
    
    def initialize_optimization(self,
                              start_date: str,
                              end_date: str,
                              agents: Dict[str, Any],
                              system_config: Dict[str, Any]) -> bool:
        """
        初始化优化环境
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            agents: 智能体字典
            system_config: 系统配置
            
        Returns:
            bool: 是否初始化成功
        """
        try:
            self.logger.info(f"初始化优化环境: {start_date} 至 {end_date}")
            
            if self._optimization_manager:
                return self._optimization_manager.initialize_simulation(
                    start_date=start_date,
                    end_date=end_date,
                    agents=agents,
                    system_config=system_config
                )
            
            self._is_initialized = True
            return True
            
        except Exception as e:
            self.logger.error(f"优化环境初始化失败: {e}")
            return False
    
    def get_optimization_status(self) -> Dict[str, Any]:
        """
        获取优化状态
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        return {
            'is_initialized': self._is_initialized,
            'has_optimization_manager': self._optimization_manager is not None,
            'current_config': self._current_config.to_dict() if self._current_config else None,
            'performance_metrics': self._performance_metrics.copy(),
            'service_health': self.health_check()
        }
    
    def cancel_optimization(self) -> bool:
        """
        取消正在进行的优化
        
        Returns:
            bool: 是否成功取消
        """
        try:
            if self._optimization_manager:
                self._optimization_manager.cleanup()
            self.logger.info("优化已取消")
            return True
        except Exception as e:
            self.logger.error(f"取消优化失败: {e}")
            return False
    
    def cleanup(self) -> None:
        """
        清理资源
        """
        try:
            if self._optimization_manager:
                self._optimization_manager.cleanup()
                self._optimization_manager = None
            
            self._is_initialized = False
            self._current_config = None
            
            self.logger.info("周期性优化服务资源清理完成")
            
        except Exception as e:
            self.logger.error(f"资源清理失败: {e}")
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        获取性能指标
        
        Returns:
            Dict[str, Any]: 性能指标
        """
        metrics = self._performance_metrics.copy()
        
        # 计算成功率
        total = metrics['total_optimizations']
        if total > 0:
            metrics['success_rate'] = (metrics['successful_optimizations'] / total) * 100
            metrics['average_execution_time'] = metrics['total_execution_time'] / total
        else:
            metrics['success_rate'] = 0.0
            metrics['average_execution_time'] = 0.0
        
        return metrics
    
    def configure_service(self, config: Dict[str, Any]) -> bool:
        """
        配置服务
        
        Args:
            config: 配置参数
            
        Returns:
            bool: 是否配置成功
        """
        try:
            # 更新LLM接口配置
            if 'llm_provider' in config and not self.llm_interface:
                self.llm_interface = LLMInterface(
                    provider=config['llm_provider'],
                    logger=self.logger
                )
            
            self.logger.info("周期性优化服务配置更新成功")
            return True
            
        except Exception as e:
            self.logger.error(f"服务配置失败: {e}")
            return False
    
    def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            Dict[str, Any]: 健康状态信息
        """
        health_status = {
            'service_name': 'WeeklyOptimizationService',
            'status': 'healthy',
            'timestamp': datetime.now().isoformat(),
            'components': {}
        }
        
        # 检查PhaseCoordinator
        try:
            if hasattr(self.phase_coordinator, 'health_check'):
                health_status['components']['phase_coordinator'] = self.phase_coordinator.health_check()
            else:
                health_status['components']['phase_coordinator'] = {'status': 'available'}
        except Exception as e:
            health_status['components']['phase_coordinator'] = {'status': 'error', 'error': str(e)}
            health_status['status'] = 'degraded'
        
        # 检查LLM接口
        if self.llm_interface:
            health_status['components']['llm_interface'] = {'status': 'available'}
        else:
            health_status['components']['llm_interface'] = {'status': 'not_configured'}
        
        # 检查优化管理器
        if self._optimization_manager:
            health_status['components']['optimization_manager'] = {'status': 'available'}
        else:
            health_status['components']['optimization_manager'] = {'status': 'not_initialized'}
        
        return health_status

    def _validate_config(self, config: WeeklyOptimizationConfig) -> None:
        """
        验证配置参数

        Args:
            config: 优化配置

        Raises:
            OptimizationConfigurationError: 配置无效时抛出
        """
        if config.optimization_frequency <= 0:
            raise OptimizationConfigurationError("优化频率必须大于0")

        if config.min_days_for_optimization < 0:
            raise OptimizationConfigurationError("最少运行天数不能为负数")

        if config.max_agents_per_cycle <= 0:
            raise OptimizationConfigurationError("每周期最多优化智能体数量必须大于0")

        if config.historical_window_weeks <= 0:
            raise OptimizationConfigurationError("历史数据窗口必须大于0")

    def _create_optimization_manager(self, config: WeeklyOptimizationConfig) -> None:
        """
        创建优化管理器

        Args:
            config: 优化配置
        """
        try:
            # 转换配置为WeeklyOptimizationManager所需的格式
            from ..weekly_optimization_manager import WeeklyOptimizationConfig as ManagerConfig

            manager_config = ManagerConfig(
                optimization_frequency=config.optimization_frequency,
                min_days_for_optimization=config.min_days_for_optimization,
                max_optimization_iterations=config.max_optimization_iterations,
                performance_degradation_threshold=config.performance_degradation_threshold,
                optimize_worst_performers=config.optimize_worst_performers,
                max_agents_per_cycle=config.max_agents_per_cycle,
                historical_window_weeks=config.historical_window_weeks,
                verbose_logging=config.verbose_logging
            )

            self._optimization_manager = WeeklyOptimizationManager(
                config=manager_config,
                llm_interface=self.llm_interface,
                logger=self.logger,
                base_results_path="results"
            )

            self.logger.info("优化管理器创建成功")

        except Exception as e:
            raise OptimizationConfigurationError(f"创建优化管理器失败: {e}")

    def _execute_optimization_through_coordinator(self,
                                                request: AssessmentRequest,
                                                config: WeeklyOptimizationConfig,
                                                start_date: str,
                                                end_date: str) -> WeeklyOptimizationResult:
        """
        通过PhaseCoordinator执行优化

        Args:
            request: 评估请求
            config: 优化配置
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            WeeklyOptimizationResult: 优化结果
        """
        try:
            self.logger.info(f"通过PhaseCoordinator执行周期性优化: {start_date} 至 {end_date}")

            # 执行标准评估流程
            assessment_result = self.phase_coordinator.execute_assessment_workflow(request)

            # 转换为周期性优化结果格式
            if assessment_result.success:
                return WeeklyOptimizationResult(
                    success=True,
                    total_weeks=1,  # 简化版本，通过PhaseCoordinator执行一次完整评估
                    weekly_results=[{
                        'success': assessment_result.success,
                        'shapley_values': assessment_result.shapley_values,
                        'shapley_analysis': assessment_result.shapley_analysis,
                        'periodic_shapley_results': assessment_result.periodic_shapley_results,
                        'request_id': assessment_result.request_id,
                        'error': assessment_result.error
                    }],
                    final_shapley_values=assessment_result.shapley_values,
                    optimization_summary={
                        'optimized_agents': request.target_agents or [],
                        'optimization_method': 'phase_coordinator_integration',
                        'period': f"{start_date} to {end_date}",
                        'config': config.to_dict()
                    },
                    performance_metrics=assessment_result.shapley_analysis,
                    message="周期性优化通过PhaseCoordinator执行"
                )
            else:
                return WeeklyOptimizationResult(
                    success=False,
                    error=assessment_result.error or "评估执行失败",
                    total_weeks=0
                )

        except Exception as e:
            self.logger.error(f"通过PhaseCoordinator执行优化失败: {e}")
            return WeeklyOptimizationResult(
                success=False,
                error=str(e),
                total_weeks=0
            )
