"""
Shapley值计算服务实现

提供周期性Shapley值计算和贡献度分析的服务，支持三种计算模式：
1. 预计算结果汇总模式
2. 管理器结果汇总模式  
3. 降级计算模式

主要功能：
- 周期性Shapley值计算
- 多种计算模式支持
- 交易日历集成
- 结果存储和序列化
- 贡献度趋势分析
"""

import json
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Any, FrozenSet
from dataclasses import dataclass

from ..interfaces.shapley_service import IShapleyService, ShapleyCalculationError, DataValidationError
from ..interfaces.trading_calendar import ITradingCalendar
from ..dto.phase_results_dto import ShapleyResult
from ..shapley_calculator import ShapleyCalculator


@dataclass
class ShapleyServiceConfig:
    """Shapley服务配置"""
    trading_days_per_week: int = 5
    results_dir: str = "results/periodic_shapley"
    enable_trend_analysis: bool = True
    minimum_week_days: int = 2
    risk_free_rate: float = 0.0
    annualization_factor: float = 252.0


class ShapleyService(IShapleyService):
    """
    Shapley值计算服务
    
    负责周期性Shapley值计算和贡献度分析的专门服务。
    支持多种计算模式，与交易日历集成，提供完整的结果存储和序列化。
    """
    
    def __init__(self, 
                 shapley_calculator: ShapleyCalculator,
                 trading_calendar: Optional[ITradingCalendar] = None,
                 config: Optional[ShapleyServiceConfig] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化Shapley服务
        
        Args:
            shapley_calculator: Shapley计算器实例
            trading_calendar: 交易日历服务（可选）
            config: 服务配置
            logger: 日志记录器
        """
        self.shapley_calculator = shapley_calculator
        self.trading_calendar = trading_calendar
        self.config = config or ShapleyServiceConfig()
        self.logger = logger or self._create_default_logger()
        
        # 周期性结果缓存
        self._periodic_shapley_results: List[Dict[str, Any]] = []
        self._periodic_shapley_manager = None
        
        # 确保结果目录存在
        os.makedirs(self.config.results_dir, exist_ok=True)
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.ShapleyService")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def calculate_periodic_shapley(self,
                                 target_agents: List[str],
                                 coalition_daily_returns: Dict[frozenset, List[float]],
                                 system_config: Optional[Dict[str, Any]] = None) -> ShapleyResult:
        """
        计算周期性Shapley值 - 主入口方法
        
        优先使用预先计算的周期性结果，如果没有则降级到其他计算模式。
        
        Args:
            target_agents: 目标智能体列表
            coalition_daily_returns: 联盟每日收益数据
            system_config: 系统配置参数
            
        Returns:
            ShapleyResult: 周期性Shapley计算结果
        """
        start_time = datetime.now()
        
        try:
            self.logger.info("开始周期性Shapley值计算阶段...")
            
            # 更新配置
            if system_config:
                self._update_config_from_system(system_config)
            
            # 1. 检查预计算结果
            if self._has_precomputed_periodic_results():
                self.logger.info("🔄 发现预先计算的周期性Shapley值结果，使用汇总模式")
                result = self._compile_precomputed_periodic_results(target_agents)
                compilation_mode = "precomputed_results"
            
            # 2. 检查管理器结果
            elif self._has_manager_results():
                self.logger.info("🔄 从周期性Shapley管理器获取结果，使用汇总模式")
                result = self._compile_manager_periodic_results(target_agents)
                compilation_mode = "manager_results"
            
            # 3. 降级到实时计算
            else:
                self.logger.info("⚠️ 没有预先计算的结果，降级到传统计算模式")
                result = self._calculate_periodic_shapley_fallback(
                    target_agents, coalition_daily_returns
                )
                compilation_mode = "fallback_calculation"
            
            # 计算执行时间
            execution_time = (datetime.now() - start_time).total_seconds()
            
            # 构建ShapleyResult
            shapley_result = ShapleyResult(
                success=result["success"],
                execution_time=execution_time,
                total_weeks=result["total_weeks"],
                weekly_results=result["weekly_results"],
                periodic_data=result["periodic_data"],
                trading_days_per_week=result["trading_days_per_week"],
                compilation_mode=compilation_mode,
                calculation_stats={
                    "execution_time": execution_time,
                    "compilation_mode": compilation_mode,
                    "total_weeks_processed": result["total_weeks"],
                    "successful_weeks": len([w for w in result["weekly_results"] if w.get("success", False)])
                }
            )
            
            # 保存结果
            self.save_shapley_results(shapley_result)
            
            self.logger.info(f"✅ 周期性Shapley值计算完成: {result['total_weeks']} 个周期")
            return shapley_result
            
        except Exception as e:
            execution_time = (datetime.now() - start_time).total_seconds()
            self.logger.error(f"周期性Shapley值计算失败: {e}")
            
            return ShapleyResult(
                success=False,
                execution_time=execution_time,
                error=str(e),
                error_context={"target_agents": target_agents, "compilation_mode": "error"}
            )
    
    def _update_config_from_system(self, system_config: Dict[str, Any]) -> None:
        """从系统配置更新服务配置"""
        if "trading_days_per_week" in system_config:
            self.config.trading_days_per_week = system_config["trading_days_per_week"]
        if "risk_free_rate" in system_config:
            self.config.risk_free_rate = system_config["risk_free_rate"]
    
    def _has_precomputed_periodic_results(self) -> bool:
        """检查是否有预先计算的周期性结果"""
        return (len(self._periodic_shapley_results) > 0)
    
    def _has_manager_results(self) -> bool:
        """检查管理器是否有结果"""
        return (self._periodic_shapley_manager is not None and 
                hasattr(self._periodic_shapley_manager, 'weekly_results') and
                self._periodic_shapley_manager.weekly_results)
    
    def _compile_precomputed_periodic_results(self, target_agents: List[str]) -> Dict[str, Any]:
        """
        汇总预先计算的周期性Shapley值结果
        
        Args:
            target_agents: 目标智能体列表
            
        Returns:
            汇总的周期性Shapley值计算结果
        """
        try:
            self.logger.info(f"📊 汇总 {len(self._periodic_shapley_results)} 个预先计算的周期性结果")
            
            # 转换预先计算的结果为标准格式
            weekly_shapley_results = []
            for i, result in enumerate(self._periodic_shapley_results):
                if hasattr(result, 'to_dict'):
                    # 如果是WeeklyShapleyResult对象
                    weekly_result = result.to_dict()
                elif isinstance(result, dict):
                    # 如果已经是字典格式
                    weekly_result = result.copy()
                else:
                    self.logger.warning(f"未知的结果格式: {type(result)}")
                    continue
                
                # 确保包含必要的字段
                if 'week' not in weekly_result:
                    weekly_result['week'] = i + 1
                if 'success' not in weekly_result:
                    weekly_result['success'] = True
                    
                weekly_shapley_results.append(weekly_result)
            
            # 记录汇总结果
            self._log_periodic_shapley_summary(weekly_shapley_results, target_agents)
            
            total_weeks = len(weekly_shapley_results)
            self.logger.info(f"✅ 周期性Shapley值汇总完成: 汇总了 {total_weeks} 个周期")
            
            return {
                "success": True,
                "total_weeks": total_weeks,
                "weekly_results": weekly_shapley_results,
                "periodic_data": weekly_shapley_results,
                "trading_days_per_week": self.config.trading_days_per_week,
                "compilation_mode": "precomputed_results"
            }
            
        except Exception as e:
            self.logger.error(f"汇总预先计算结果失败: {e}")
            raise ShapleyCalculationError(f"预计算结果汇总失败: {e}")
    
    def _compile_manager_periodic_results(self, target_agents: List[str]) -> Dict[str, Any]:
        """
        从周期性Shapley管理器汇总结果
        
        Args:
            target_agents: 目标智能体列表
            
        Returns:
            汇总的周期性Shapley值计算结果
        """
        try:
            manager_results = self._periodic_shapley_manager.get_all_results()
            self.logger.info(f"📊 从管理器汇总 {len(manager_results)} 个周期性结果")
            
            # 转换管理器结果为标准格式
            weekly_shapley_results = []
            for result in manager_results:
                weekly_result = result.to_dict()
                weekly_shapley_results.append(weekly_result)
            
            # 记录汇总结果
            self._log_periodic_shapley_summary(weekly_shapley_results, target_agents)
            
            total_weeks = len(weekly_shapley_results)
            self.logger.info(f"✅ 周期性Shapley值汇总完成: 汇总了 {total_weeks} 个周期")
            
            return {
                "success": True,
                "total_weeks": total_weeks,
                "weekly_results": weekly_shapley_results,
                "periodic_data": weekly_shapley_results,
                "trading_days_per_week": self.config.trading_days_per_week,
                "compilation_mode": "manager_results"
            }
            
        except Exception as e:
            self.logger.error(f"从管理器汇总结果失败: {e}")
            raise ShapleyCalculationError(f"管理器结果汇总失败: {e}")
    
    def _calculate_periodic_shapley_fallback(self, 
                                           target_agents: List[str],
                                           coalition_daily_returns: Dict[frozenset, List[float]]) -> Dict[str, Any]:
        """
        降级的周期性Shapley值计算方法
        
        Args:
            target_agents: 目标智能体列表
            coalition_daily_returns: 联盟每日收益数据
            
        Returns:
            周期性Shapley值计算结果
        """
        self.logger.info("🔄 使用降级模式进行周期性Shapley值计算")
        
        # 获取实际交易日数据
        actual_trading_days = self._get_actual_trading_days()
        
        if not actual_trading_days:
            # 降级到基于数据长度的计算
            self.logger.warning("无法获取交易日数据，使用数据长度进行计算")
            return self._calculate_with_data_length(target_agents, coalition_daily_returns)
        else:
            # 使用实际交易日进行计算
            return self._calculate_with_trading_calendar(target_agents, coalition_daily_returns, actual_trading_days)
    
    def _get_actual_trading_days(self) -> List[str]:
        """获取实际交易日数据"""
        try:
            if self.trading_calendar:
                # 从系统配置获取日期范围
                start_date = "2024-01-01"  # 应该从配置获取
                end_date = "2024-12-31"    # 应该从配置获取
                
                return self.trading_calendar.get_trading_days(
                    start_date=start_date,
                    end_date=end_date,
                    use_database=True,
                    stocks=["AAPL"]  # 应该从配置获取
                )
            else:
                # 没有交易日历服务，返回空列表触发降级
                return []
        except Exception as e:
            self.logger.error(f"获取交易日数据失败: {e}")
            return []
    
    def _calculate_with_data_length(self, 
                                  target_agents: List[str],
                                  coalition_daily_returns: Dict[frozenset, List[float]]) -> Dict[str, Any]:
        """基于数据长度计算周期性Shapley值"""
        # 找到实际数据的最大长度
        actual_data_days = 0
        if coalition_daily_returns:
            max_data_length = max(len(returns) for returns in coalition_daily_returns.values() if returns)
            actual_data_days = max_data_length
        
        if actual_data_days == 0:
            actual_data_days = 20  # 默认值
        
        total_weeks = (actual_data_days + self.config.trading_days_per_week - 1) // self.config.trading_days_per_week
        self.logger.info(f"降级模式: 实际数据天数: {actual_data_days}, 计算周期数: {total_weeks}")
        
        # 使用简单的数学划分
        weekly_shapley_results = self._calculate_shapley_with_simple_division(
            target_agents, coalition_daily_returns, actual_data_days, total_weeks
        )
        
        return {
            "success": True,
            "total_weeks": total_weeks,
            "weekly_results": weekly_shapley_results,
            "periodic_data": weekly_shapley_results,
            "trading_days_per_week": self.config.trading_days_per_week,
            "compilation_mode": "fallback_calculation"
        }
    
    def _calculate_with_trading_calendar(self, 
                                       target_agents: List[str],
                                       coalition_daily_returns: Dict[frozenset, List[float]],
                                       actual_trading_days: List[str]) -> Dict[str, Any]:
        """基于交易日历计算周期性Shapley值"""
        try:
            if self.trading_calendar:
                # 使用依赖注入的交易日历服务
                trading_weeks = self.trading_calendar.get_trading_weeks(
                    actual_trading_days, 
                    strategy="adaptive", 
                    days_per_week=self.config.trading_days_per_week
                )
            else:
                # 回退到原有的导入方式
                from utils.trading_calendar import TradingCalendar
                calendar = TradingCalendar(logger=self.logger)
                trading_weeks = calendar.get_trading_weeks(
                    actual_trading_days, 
                    strategy="adaptive", 
                    days_per_week=self.config.trading_days_per_week
                )
            
            self.logger.info(f"基于实际交易日划分为 {len(trading_weeks)} 周")
            
            # 使用实际交易日周划分计算Shapley值
            weekly_shapley_results = self._calculate_shapley_with_trading_calendar(
                target_agents, coalition_daily_returns, trading_weeks, actual_trading_days
            )
            
            return {
                "success": True,
                "total_weeks": len(trading_weeks),
                "weekly_results": weekly_shapley_results,
                "periodic_data": weekly_shapley_results,
                "trading_days_per_week": self.config.trading_days_per_week,
                "compilation_mode": "trading_calendar_calculation"
            }
            
        except Exception as e:
            self.logger.error(f"交易日历计算失败: {e}")
            # 回退到数据长度计算
            return self._calculate_with_data_length(target_agents, coalition_daily_returns)
    
    def _calculate_shapley_with_simple_division(self,
                                              target_agents: List[str],
                                              coalition_daily_returns: Dict[frozenset, List[float]],
                                              actual_data_days: int,
                                              total_weeks: int) -> List[Dict[str, Any]]:
        """使用简单数学划分计算周期性Shapley值"""
        weekly_shapley_results = []
        
        for week in range(total_weeks):
            start_day = week * self.config.trading_days_per_week
            end_day = min(start_day + self.config.trading_days_per_week, actual_data_days)
            
            # 检查当前周期是否有足够的数据
            week_duration = end_day - start_day
            if week_duration < self.config.minimum_week_days:
                self.logger.warning(f"第 {week + 1} 周数据不足（仅 {week_duration} 天），跳过Shapley值计算")
                continue
            
            self.logger.info(f"计算第 {week + 1} 周 (第 {start_day + 1}-{end_day} 天) 的Shapley值")
            
            # 为当前周期构建联盟特征函数值
            weekly_coalition_values = {}
            
            for coalition, daily_returns in coalition_daily_returns.items():
                if len(daily_returns) >= end_day:
                    # 提取当前周期的收益数据
                    week_returns = daily_returns[start_day:end_day]
                    
                    # 计算当前周期的夏普比率
                    weekly_sharpe = self._calculate_weekly_sharpe_ratio(week_returns)
                    weekly_coalition_values[coalition] = weekly_sharpe
                else:
                    # 如果数据不足，赋值为0
                    weekly_coalition_values[coalition] = 0.0
            
            # 计算当前周期的Shapley值
            try:
                weekly_shapley_values = self.shapley_calculator.calculate(
                    target_agents, weekly_coalition_values
                )
                
                weekly_result = {
                    "week": week + 1,
                    "trading_days": f"{start_day + 1}-{end_day}",
                    "shapley_values": weekly_shapley_values,
                    "coalition_values": weekly_coalition_values,
                    "success": True
                }
                
                # 打印当前周期结果
                self.logger.info(f"第 {week + 1} 周Shapley值计算完成:")
                for agent, value in weekly_shapley_values.items():
                    self.logger.info(f"  {agent}: {value:.6f}")
                
            except Exception as e:
                self.logger.error(f"第 {week + 1} 周Shapley值计算失败: {e}")
                weekly_result = {
                    "week": week + 1,
                    "trading_days": f"{start_day + 1}-{end_day}",
                    "shapley_values": {agent: 0.0 for agent in target_agents},
                    "coalition_values": weekly_coalition_values,
                    "success": False,
                    "error": str(e)
                }
            
            weekly_shapley_results.append(weekly_result)
        
        return weekly_shapley_results
    
    def _calculate_shapley_with_trading_calendar(self,
                                               target_agents: List[str],
                                               coalition_daily_returns: Dict[frozenset, List[float]],
                                               trading_weeks: List[Dict[str, Any]],
                                               actual_trading_days: List[str]) -> List[Dict[str, Any]]:
        """使用实际交易日历计算周期性Shapley值"""
        weekly_shapley_results = []
        
        for week_info in trading_weeks:
            week_number = week_info['week_number']
            week_trading_days = week_info['trading_days']
            
            # 检查当前周期是否有足够的数据
            if len(week_trading_days) < self.config.minimum_week_days:
                self.logger.warning(f"第 {week_number} 周数据不足（仅 {len(week_trading_days)} 天），跳过Shapley值计算")
                continue
            
            self.logger.info(f"计算第 {week_number} 周 ({week_info['start_date']} 到 {week_info['end_date']}) 的Shapley值")
            
            # 计算该周在数据中的索引范围
            start_index = None
            end_index = None
            
            # 找到该周第一天和最后一天在实际交易日列表中的索引
            for i, trading_day in enumerate(actual_trading_days):
                if trading_day == week_info['start_date'] and start_index is None:
                    start_index = i
                if trading_day == week_info['end_date']:
                    end_index = i + 1  # +1 因为切片是左闭右开
            
            if start_index is None or end_index is None:
                self.logger.warning(f"第 {week_number} 周无法找到对应的数据索引，跳过")
                continue
            
            # 为当前周期构建联盟特征函数值
            weekly_coalition_values = {}
            
            for coalition, daily_returns in coalition_daily_returns.items():
                if len(daily_returns) >= end_index:
                    # 提取当前周期的收益数据
                    week_returns = daily_returns[start_index:end_index]
                    
                    # 计算当前周期的夏普比率
                    weekly_sharpe = self._calculate_weekly_sharpe_ratio(week_returns)
                    weekly_coalition_values[coalition] = weekly_sharpe
                else:
                    # 如果数据不足，赋值为0
                    weekly_coalition_values[coalition] = 0.0
            
            # 计算当前周期的Shapley值
            try:
                weekly_shapley_values = self.shapley_calculator.calculate(
                    target_agents, weekly_coalition_values
                )
                
                weekly_result = {
                    "week": week_number,
                    "trading_days": f"{week_info['start_date']} 到 {week_info['end_date']}",
                    "actual_trading_days": week_trading_days,
                    "shapley_values": weekly_shapley_values,
                    "coalition_values": weekly_coalition_values,
                    "success": True
                }
                
                # 打印当前周期结果
                self.logger.info(f"第 {week_number} 周Shapley值计算完成:")
                for agent, value in weekly_shapley_values.items():
                    self.logger.info(f"  {agent}: {value:.6f}")
                
            except Exception as e:
                self.logger.error(f"第 {week_number} 周Shapley值计算失败: {e}")
                weekly_result = {
                    "week": week_number,
                    "trading_days": f"{week_info['start_date']} 到 {week_info['end_date']}",
                    "actual_trading_days": week_trading_days,
                    "shapley_values": {agent: 0.0 for agent in target_agents},
                    "coalition_values": weekly_coalition_values,
                    "success": False,
                    "error": str(e)
                }
            
            weekly_shapley_results.append(weekly_result)
        
        return weekly_shapley_results
    
    def _calculate_weekly_sharpe_ratio(self, returns: List[float]) -> float:
        """计算周期收益的夏普比率"""
        if not returns or len(returns) == 0:
            return 0.0
        
        import numpy as np
        
        returns_array = np.array(returns)
        
        # 计算平均收益率
        mean_return = np.mean(returns_array)
        
        # 计算收益率标准差
        std_return = np.std(returns_array, ddof=1) if len(returns_array) > 1 else 0.0
        
        # 计算夏普比率
        if std_return == 0:
            return 0.0
        
        sharpe_ratio = (mean_return - self.config.risk_free_rate) / std_return
        
        # 年化处理
        sharpe_ratio_annualized = sharpe_ratio * np.sqrt(self.config.annualization_factor)
        
        return sharpe_ratio_annualized
    
    def _log_periodic_shapley_summary(self, 
                                    weekly_shapley_results: List[Dict[str, Any]], 
                                    target_agents: List[str]) -> None:
        """记录周期性Shapley值计算结果汇总"""
        if not weekly_shapley_results:
            self.logger.warning("没有周期性Shapley值结果可以汇总")
            return
        
        self.logger.info("=" * 70)
        self.logger.info("周期性Shapley值计算结果汇总")
        self.logger.info("=" * 70)
        
        successful_weeks = [w for w in weekly_shapley_results if w.get("success", False)]
        failed_weeks = [w for w in weekly_shapley_results if not w.get("success", False)]
        
        self.logger.info(f"总周数: {len(weekly_shapley_results)}")
        self.logger.info(f"成功计算: {len(successful_weeks)} 周")
        self.logger.info(f"计算失败: {len(failed_weeks)} 周")
        
        if successful_weeks:
            # 获取最新周期的贡献度
            latest_week = successful_weeks[-1]
            current_shapley_values = latest_week.get("shapley_values", {})
            
            self.logger.info("\\n各智能体当前周期贡献度:")
            
            # 显示当前周期的Shapley值
            agent_current_contributions = {}
            for agent in target_agents:
                if agent in current_shapley_values:
                    contribution = current_shapley_values[agent]
                    agent_current_contributions[agent] = contribution if contribution is not None else 0.0
                else:
                    agent_current_contributions[agent] = 0.0
                
                self.logger.info(f"  {agent}: {agent_current_contributions[agent]:.6f}")
            
            # 识别表现最好和最差的智能体
            if agent_current_contributions:
                best_agent = max(agent_current_contributions.items(), key=lambda x: x[1])
                worst_agent = min(agent_current_contributions.items(), key=lambda x: x[1])
                
                self.logger.info(f"\\n表现最佳: {best_agent[0]} ({best_agent[1]:.6f})")
                self.logger.info(f"表现最差: {worst_agent[0]} ({worst_agent[1]:.6f})")
            
            # 分析贡献度趋势
            if self.config.enable_trend_analysis:
                self._analyze_contribution_trends(successful_weeks, target_agents)
        
        if failed_weeks:
            self.logger.warning(f"\\n计算失败的周次:")
            for week_result in failed_weeks:
                week_num = week_result.get("week", "未知")
                error = week_result.get("error", "未知错误")
                self.logger.warning(f"  第 {week_num} 周: {error}")
        
        self.logger.info("=" * 70)
    
    def _analyze_contribution_trends(self, 
                                   successful_weeks: List[Dict[str, Any]], 
                                   target_agents: List[str]) -> None:
        """分析智能体贡献度趋势"""
        if len(successful_weeks) < 2:
            self.logger.info("周数不足，无法分析趋势")
            return
        
        self.logger.info("\\n贡献度趋势分析:")
        
        for agent in target_agents:
            contributions = []
            weeks = []
            
            for week_result in successful_weeks:
                if agent in week_result.get("shapley_values", {}):
                    contribution = week_result["shapley_values"][agent]
                    if contribution is not None:
                        contributions.append(contribution)
                        weeks.append(week_result.get("week", len(contributions)))
            
            if len(contributions) >= 2:
                # 简单的趋势分析
                first_half_avg = sum(contributions[:len(contributions)//2]) / (len(contributions)//2)
                second_half_avg = sum(contributions[len(contributions)//2:]) / (len(contributions) - len(contributions)//2)
                
                trend = "上升" if second_half_avg > first_half_avg else "下降" if second_half_avg < first_half_avg else "稳定"
                trend_magnitude = abs(second_half_avg - first_half_avg)
                
                self.logger.info(f"  {agent}: {trend} (变化幅度: {trend_magnitude:.6f})")
            else:
                self.logger.info(f"  {agent}: 数据不足")
    
    # 实现IShapleyService接口的其他方法
    def calculate_shapley_values(self,
                               agents: List[str],
                               coalition_values: Dict[FrozenSet[str], float]) -> Dict[str, float]:
        """计算智能体的Shapley值"""
        try:
            return self.shapley_calculator.calculate(agents, coalition_values)
        except Exception as e:
            raise ShapleyCalculationError(f"Shapley值计算失败: {e}")
    
    def analyze_contributions(self,
                            shapley_values: Dict[str, float],
                            historical_data: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """分析智能体贡献度"""
        analysis = {
            "current_contributions": shapley_values,
            "total_agents": len(shapley_values),
            "average_contribution": sum(shapley_values.values()) / len(shapley_values) if shapley_values else 0.0,
            "best_performer": max(shapley_values.items(), key=lambda x: x[1]) if shapley_values else None,
            "worst_performer": min(shapley_values.items(), key=lambda x: x[1]) if shapley_values else None,
            "contribution_variance": self._calculate_variance(list(shapley_values.values()))
        }
        
        if historical_data:
            analysis["historical_trends"] = self._analyze_historical_trends(shapley_values, historical_data)
        
        return analysis
    
    def _calculate_variance(self, values: List[float]) -> float:
        """计算方差"""
        if len(values) < 2:
            return 0.0
        
        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / (len(values) - 1)
        return variance
    
    def _analyze_historical_trends(self, 
                                 current_shapley: Dict[str, float], 
                                 historical_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析历史趋势"""
        trends = {}
        
        for agent in current_shapley:
            historical_values = []
            for data in historical_data:
                if agent in data.get("shapley_values", {}):
                    historical_values.append(data["shapley_values"][agent])
            
            if len(historical_values) >= 2:
                # 简单趋势分析
                recent_avg = sum(historical_values[-3:]) / min(3, len(historical_values))
                earlier_avg = sum(historical_values[:3]) / min(3, len(historical_values))
                
                trend_direction = "improving" if recent_avg > earlier_avg else "declining" if recent_avg < earlier_avg else "stable"
                trend_magnitude = abs(recent_avg - earlier_avg)
                
                trends[agent] = {
                    "direction": trend_direction,
                    "magnitude": trend_magnitude,
                    "recent_average": recent_avg,
                    "historical_average": sum(historical_values) / len(historical_values)
                }
        
        return trends
    
    def get_marginal_contributions(self,
                                 agent: str,
                                 coalition_values: Dict[FrozenSet[str], float]) -> List[Dict[str, Any]]:
        """获取智能体的边际贡献"""
        marginal_contributions = []
        
        for coalition in coalition_values:
            if agent in coalition:
                # 计算不包含该智能体的联盟价值
                without_agent = coalition - {agent}
                
                coalition_value = coalition_values.get(coalition, 0.0)
                without_agent_value = coalition_values.get(without_agent, 0.0)
                
                marginal_contribution = coalition_value - without_agent_value
                
                marginal_contributions.append({
                    "coalition": sorted(list(coalition)),
                    "coalition_value": coalition_value,
                    "without_agent_value": without_agent_value,
                    "marginal_contribution": marginal_contribution
                })
        
        return marginal_contributions
    
    def validate_coalition_values(self, coalition_values: Dict[FrozenSet[str], float]) -> bool:
        """验证联盟价值数据的完整性"""
        if not coalition_values:
            raise DataValidationError("联盟价值数据为空")
        
        # 检查空联盟
        empty_coalition = frozenset()
        if empty_coalition not in coalition_values:
            self.logger.warning("缺少空联盟数据")
        
        # 检查数据类型
        for coalition, value in coalition_values.items():
            if not isinstance(coalition, frozenset):
                raise DataValidationError(f"联盟键必须是frozenset类型: {type(coalition)}")
            if not isinstance(value, (int, float)):
                raise DataValidationError(f"联盟价值必须是数值类型: {type(value)}")
        
        return True
    
    def get_shapley_statistics(self, shapley_values: Dict[str, float]) -> Dict[str, Any]:
        """获取Shapley值的统计信息"""
        if not shapley_values:
            return {"error": "Shapley值数据为空"}
        
        values = list(shapley_values.values())
        
        return {
            "total_agents": len(shapley_values),
            "sum_contributions": sum(values),
            "average_contribution": sum(values) / len(values),
            "max_contribution": max(values),
            "min_contribution": min(values),
            "contribution_range": max(values) - min(values),
            "variance": self._calculate_variance(values),
            "standard_deviation": self._calculate_variance(values) ** 0.5,
            "agent_rankings": sorted(shapley_values.items(), key=lambda x: x[1], reverse=True)
        }
    
    def save_shapley_results(self, results: ShapleyResult, storage_path: Optional[str] = None) -> bool:
        """保存Shapley计算结果"""
        try:
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"periodic_shapley_{timestamp}.json"
            
            if storage_path:
                filepath = os.path.join(storage_path, filename)
            else:
                filepath = os.path.join(self.config.results_dir, filename)
            
            # 确保目录存在
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            # 转换为可序列化格式
            serializable_data = {
                "success": results.success,
                "execution_time": results.execution_time,
                "timestamp": results.timestamp.isoformat(),
                "total_weeks": results.total_weeks,
                "weekly_results": self._make_json_serializable(results.weekly_results),
                "periodic_data": self._make_json_serializable(results.periodic_data),
                "trading_days_per_week": results.trading_days_per_week,
                "compilation_mode": results.compilation_mode,
                "calculation_stats": results.calculation_stats,
                "error": results.error,
                "error_context": results.error_context
            }
            
            # 保存结果
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(serializable_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Shapley计算结果已保存至: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"保存Shapley计算结果失败: {e}")
            return False
    
    def _make_json_serializable(self, obj):
        """将包含frozenset键的对象转换为JSON可序列化格式"""
        if isinstance(obj, dict):
            serializable_dict = {}
            for key, value in obj.items():
                # 处理frozenset键
                if isinstance(key, frozenset):
                    # 将frozenset转换为排序后的元组的字符串表示
                    str_key = str(sorted(list(key)))
                else:
                    str_key = str(key)
                
                serializable_dict[str_key] = self._make_json_serializable(value)
            return serializable_dict
        elif isinstance(obj, list):
            return [self._make_json_serializable(item) for item in obj]
        elif isinstance(obj, frozenset):
            return sorted(list(obj))
        else:
            return obj
    
    def load_historical_shapley(self,
                              agent: Optional[str] = None,
                              time_range: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """加载历史Shapley数据"""
        historical_data = []
        
        try:
            # 扫描结果目录
            if not os.path.exists(self.config.results_dir):
                return historical_data
            
            for filename in os.listdir(self.config.results_dir):
                if filename.endswith('.json') and filename.startswith('periodic_shapley_'):
                    filepath = os.path.join(self.config.results_dir, filename)
                    
                    try:
                        with open(filepath, 'r', encoding='utf-8') as f:
                            data = json.load(f)
                        
                        # 如果指定了特定智能体，筛选相关数据
                        if agent:
                            filtered_data = self._filter_agent_data(data, agent)
                            if filtered_data:
                                historical_data.append(filtered_data)
                        else:
                            historical_data.append(data)
                    
                    except Exception as e:
                        self.logger.warning(f"加载历史数据文件失败 {filename}: {e}")
                        continue
            
            # 按时间排序
            historical_data.sort(key=lambda x: x.get('timestamp', ''))
            
            # 应用时间范围筛选
            if time_range:
                historical_data = self._filter_by_time_range(historical_data, time_range)
            
            return historical_data
            
        except Exception as e:
            self.logger.error(f"加载历史Shapley数据失败: {e}")
            return []
    
    def _filter_agent_data(self, data: Dict[str, Any], agent: str) -> Optional[Dict[str, Any]]:
        """筛选特定智能体的数据"""
        if 'weekly_results' not in data:
            return None
        
        filtered_results = []
        for week_result in data['weekly_results']:
            if agent in week_result.get('shapley_values', {}):
                filtered_results.append({
                    'week': week_result.get('week'),
                    'shapley_value': week_result['shapley_values'][agent],
                    'success': week_result.get('success', False)
                })
        
        if filtered_results:
            return {
                'agent': agent,
                'timestamp': data.get('timestamp'),
                'total_weeks': len(filtered_results),
                'weekly_results': filtered_results
            }
        
        return None
    
    def _filter_by_time_range(self, data: List[Dict[str, Any]], time_range: Dict[str, Any]) -> List[Dict[str, Any]]:
        """按时间范围筛选数据"""
        if 'start_time' not in time_range and 'end_time' not in time_range:
            return data
        
        filtered_data = []
        for item in data:
            timestamp = item.get('timestamp', '')
            if timestamp:
                # 简单的时间比较，实际应用中可能需要更复杂的逻辑
                if time_range.get('start_time', '') <= timestamp <= time_range.get('end_time', '9999-12-31'):
                    filtered_data.append(item)
        
        return filtered_data
    
    def set_periodic_shapley_manager(self, manager) -> None:
        """设置周期性Shapley管理器"""
        self._periodic_shapley_manager = manager
    
    def add_periodic_result(self, result: Dict[str, Any]) -> None:
        """添加周期性结果"""
        self._periodic_shapley_results.append(result)
    
    def clear_periodic_results(self) -> None:
        """清空周期性结果"""
        self._periodic_shapley_results.clear()
    
    def get_periodic_results(self) -> List[Dict[str, Any]]:
        """获取周期性结果"""
        return self._periodic_shapley_results.copy()