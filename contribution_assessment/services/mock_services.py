"""
模拟服务实现

为PhaseCoordinator提供模拟服务实现，用于重构版本的正常运行。
这些服务实现了接口但调用原始ContributionAssessor的相应方法。

Features:
- 实现所有必需的服务接口
- 桥接到原始ContributionAssessor的实现
- 支持四阶段执行流程
- 高内聚低耦合的设计
"""

import logging
import time
from typing import Dict, List, Optional, Any
from datetime import datetime

from ..dto.phase_results_dto import CoalitionResult, SimulationResult, ShapleyResult, OPROResult
from ..interfaces.coalition_service import ICoalitionService
from ..interfaces.simulation_service import ISimulationService
from ..interfaces.shapley_service import IShapleyService
from ..interfaces.opro_service import IOPROService
from ..interfaces.state_service import IStateManager, StateType
from ..infrastructure.event_bus import IEventBus, Event, EventFilter


class MockCoalitionService(ICoalitionService):
    """模拟联盟生成服务"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        
    def generate_coalitions(self, 
                          agents: Dict[str, Any],
                          max_coalitions: Optional[int] = None,
                          pruning_enabled: bool = True,
                          ablation_mode: bool = False) -> CoalitionResult:
        """生成智能体联盟"""
        self.logger.info(f"🔄 阶段1: 联盟生成 - 处理 {len(agents)} 个智能体")
        
        try:
            # 模拟联盟生成过程
            from ..coalition_manager import CoalitionManager
            
            coalition_manager = CoalitionManager()
            agent_ids = list(agents.keys())
            
            # 生成联盟
            if len(agent_ids) > 0:
                if ablation_mode:
                    # 消融实验模式：仅生成完整联盟
                    self.logger.info("🧪 Mock消融实验模式：仅生成完整联盟")
                    complete_coalition = frozenset(agent_ids)
                    coalitions = [complete_coalition]
                else:
                    # 正常模式：使用简单的子集生成
                    coalitions = coalition_manager._generate_all_subsets(agent_ids)
                    # 转换为frozenset格式
                    coalitions = [frozenset(c) for c in coalitions if c]  # 排除空集
                    
                    # 限制联盟数量
                    if max_coalitions and len(coalitions) > max_coalitions:
                        coalitions = coalitions[:max_coalitions]
            else:
                coalitions = []
            
            self.logger.info(f"✅ 阶段1完成: 生成了 {len(coalitions)} 个联盟")
            
            return CoalitionResult(
                success=True,
                execution_time=0.5,
                valid_coalitions=set(coalitions),
                pruned_coalitions=set(),
                generation_stats={"agents": len(agents), "coalitions": len(coalitions)}
            )
            
        except Exception as e:
            self.logger.error(f"❌ 阶段1失败: 联盟生成错误 - {str(e)}")
            return CoalitionResult(
                success=False,
                execution_time=0.1,
                valid_coalitions=set(),
                pruned_coalitions=set(),
                error=str(e)
            )

    def validate_coalition(self, coalition: frozenset) -> bool:
        """验证联盟的有效性"""
        return len(coalition) > 0

    def get_coalition_analysis(self, coalitions: set) -> Dict[str, Any]:
        """分析联盟集合的统计信息"""
        return {
            "total_coalitions": len(coalitions),
            "average_size": sum(len(c) for c in coalitions) / len(coalitions) if coalitions else 0,
            "max_size": max(len(c) for c in coalitions) if coalitions else 0,
            "min_size": min(len(c) for c in coalitions) if coalitions else 0
        }

    def prune_coalitions(self, coalitions: set, criteria: Optional[Dict[str, Any]] = None) -> set:
        """根据指定条件剪枝联盟"""
        return coalitions  # 简单实现：不进行剪枝

    def get_coalition_dependencies(self, coalition: frozenset) -> Dict[str, List[str]]:
        """获取联盟内智能体的依赖关系"""
        return {agent: [] for agent in coalition}  # 简单实现：无依赖关系

    def estimate_coalition_complexity(self, coalition: frozenset) -> float:
        """估算联盟的复杂度"""
        return float(len(coalition))  # 简单实现：复杂度等于联盟大小


class MockSimulationService(ISimulationService):
    """模拟交易模拟服务"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        
    def simulate_coalitions_batch(self,
                                coalitions: List[frozenset],
                                agents: Dict[str, Any],
                                config: Dict[str, Any],
                                max_concurrent: Optional[int] = None) -> SimulationResult:
        """批量模拟联盟交易"""
        self.logger.info(f"🔄 阶段2: 交易模拟 - 模拟 {len(coalitions)} 个联盟")
        
        try:
            from ..trading_simulator import TradingSimulator
            
            # 创建交易模拟器
            simulator = TradingSimulator(
                base_config=config,
                logger=self.logger
            )
            
            # 模拟每个联盟
            coalition_values = {}
            daily_returns = {}
            
            for coalition in coalitions:
                # 模拟交易
                result = simulator.simulate_coalition(
                    coalition=coalition,
                    agents=agents
                )
                
                coalition_key = frozenset(coalition)
                coalition_values[coalition_key] = result.get("total_return", 0.0)
                daily_returns[coalition_key] = result.get("daily_returns", [])
            
            self.logger.info(f"✅ 阶段2完成: 模拟了 {len(coalition_values)} 个联盟")
            
            return SimulationResult(
                success=True,
                execution_time=2.0,
                coalition_values=coalition_values,
                coalition_daily_returns=daily_returns,
                simulation_stats={
                    "total_coalitions": len(coalitions),
                    "successful_simulations": len(coalition_values),
                    "failed_simulations": len(coalitions) - len(coalition_values)
                }
            )
            
        except Exception as e:
            self.logger.error(f"❌ 阶段2失败: 交易模拟错误 - {str(e)}")
            return SimulationResult(
                success=False,
                execution_time=0.5,
                coalition_values={},
                coalition_daily_returns={},
                error=str(e)
            )

    def simulate_coalition(self, coalition: frozenset, agents: Dict[str, Any], config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """模拟单个联盟的交易表现"""
        return {
            "coalition": list(coalition),
            "sharpe_ratio": 1.5,
            "total_return": 0.1,
            "volatility": 0.15,
            "max_drawdown": -0.05
        }

    def calculate_performance_metrics(self, daily_returns: List[float], benchmark_returns: Optional[List[float]] = None) -> Dict[str, float]:
        """计算性能指标"""
        if not daily_returns:
            return {"sharpe_ratio": 0.0, "total_return": 0.0, "volatility": 0.0}

        import numpy as np
        returns_array = np.array(daily_returns)
        return {
            "sharpe_ratio": np.mean(returns_array) / np.std(returns_array) if np.std(returns_array) > 0 else 0.0,
            "total_return": np.sum(returns_array),
            "volatility": np.std(returns_array),
            "max_drawdown": np.min(np.cumsum(returns_array))
        }

    def validate_simulation_config(self, config: Dict[str, Any]) -> bool:
        """验证模拟配置的有效性"""
        return True  # 简单实现：总是有效

    def get_simulation_status(self, simulation_id: str) -> Dict[str, Any]:
        """获取模拟执行状态"""
        return {"simulation_id": simulation_id, "status": "completed", "progress": 1.0}

    def cancel_simulation(self, simulation_id: str) -> bool:
        """取消正在执行的模拟"""
        return True  # 简单实现：总是成功

    def get_simulation_history(self, coalition: Optional[frozenset] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取模拟历史记录"""
        return []  # 简单实现：返回空历史


class MockShapleyService(IShapleyService):
    """模拟Shapley值计算服务"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        
    def calculate_periodic_shapley(self,
                                 coalition_values: Dict[frozenset, float],
                                 config: Dict[str, Any]) -> ShapleyResult:
        """计算周期性Shapley值"""
        self.logger.info(f"🔄 阶段3: Shapley值计算 - 处理 {len(coalition_values)} 个联盟结果")
        
        try:
            from ..shapley_calculator import ShapleyCalculator
            
            # 创建Shapley计算器
            calculator = ShapleyCalculator(
                logger=self.logger
            )
            
            # 计算Shapley值
            agent_list = list(set().union(*coalition_values.keys())) if coalition_values else []
            shapley_values = calculator.calculate(
                agents=agent_list,
                coalition_values=coalition_values
            )
            
            # 构造周期性结果
            result = {
                "total_weeks": 1,
                "weekly_results": [{
                    "week": 1,
                    "shapley_values": shapley_values,
                    "analysis": {"total_agents": len(agent_list)}
                }],
                "periodic_data": [{"week": 1, "shapley_values": shapley_values}],
                "compilation_mode": "weekly"
            }
            
            self.logger.info(f"✅ 阶段3完成: 计算了 {result.get('total_weeks', 0)} 周的Shapley值")
            
            return ShapleyResult(
                success=True,
                execution_time=1.0,
                weekly_results=result.get("weekly_results", []),
                total_weeks=result.get("total_weeks", 0),
                periodic_data=result.get("periodic_data", {}),
                compilation_mode=result.get("compilation_mode", "weekly")
            )
            
        except Exception as e:
            self.logger.error(f"❌ 阶段3失败: Shapley值计算错误 - {str(e)}")
            return ShapleyResult(
                success=False,
                execution_time=0.2,
                weekly_results=[],
                total_weeks=0,
                periodic_data={},
                error=str(e)
            )

    def calculate_shapley_values(self, agents: List[str], coalition_values: Dict[frozenset, float]) -> Dict[str, float]:
        """计算智能体的Shapley值"""
        from ..shapley_calculator import ShapleyCalculator
        calculator = ShapleyCalculator()
        return calculator.calculate(agents=agents, coalition_values=coalition_values)

    def validate_coalition_values(self, coalition_values: Dict[frozenset, float]) -> bool:
        """验证联盟价值数据的有效性"""
        return len(coalition_values) > 0

    def get_shapley_statistics(self, shapley_values: Dict[str, float]) -> Dict[str, Any]:
        """获取Shapley值的统计信息"""
        if not shapley_values:
            return {}
        values = list(shapley_values.values())
        return {
            "mean": sum(values) / len(values),
            "max": max(values),
            "min": min(values),
            "total_agents": len(shapley_values)
        }

    def save_shapley_results(self, results: ShapleyResult, storage_path: Optional[str] = None) -> bool:
        """保存Shapley计算结果"""
        return True  # 简单实现：总是成功

    def load_historical_shapley(self, agent: Optional[str] = None, time_range: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """加载历史Shapley数据"""
        return []  # 简单实现：返回空历史

    def analyze_contributions(self, shapley_values: Dict[str, float], coalition_values: Dict[frozenset, float]) -> Dict[str, Any]:
        """分析智能体贡献"""
        if not shapley_values:
            return {}
        return {
            "top_contributor": max(shapley_values.items(), key=lambda x: x[1])[0],
            "bottom_contributor": min(shapley_values.items(), key=lambda x: x[1])[0],
            "contribution_variance": max(shapley_values.values()) - min(shapley_values.values())
        }

    def get_marginal_contributions(self, agent: str, coalition_values: Dict[frozenset, float]) -> Dict[str, float]:
        """获取智能体的边际贡献"""
        return {f"marginal_to_{i}": 0.01 * i for i in range(3)}  # 简单实现


class MockOPROService(IOPROService):
    """模拟OPRO优化服务"""
    
    def __init__(self, logger: Optional[logging.Logger] = None, enabled: bool = False):
        self.logger = logger or logging.getLogger(__name__)
        self.enabled = enabled
        
    def optimize_agents(self,
                       shapley_values: Dict[str, float],
                       agents: Dict[str, Any],
                       config: Dict[str, Any]) -> OPROResult:
        """优化智能体"""
        self.logger.info(f"🔄 阶段4: OPRO优化 - 启用状态: {self.enabled}")
        
        if not self.enabled:
            self.logger.info("⏭️ 阶段4跳过: OPRO优化未启用")
            return OPROResult(
                success=True,
                execution_time=0.1,
                enabled=False,
                optimization_result={},
                updated_agents=[],
                optimization_stats={}
            )
        
        try:
            # 找出表现最差的智能体
            if not shapley_values:
                self.logger.warning("⚠️ 无Shapley值数据，跳过OPRO优化")
                result = OPROResult(
                    success=True,
                    execution_time=0.1,
                    enabled=True,
                    optimization_result={},
                    updated_agents=[],
                    optimization_stats={}
                )
                result.optimization_stats["message"] = "No Shapley values available for optimization"
                return result
            
            # 排序找出最差的智能体
            sorted_agents = sorted(shapley_values.items(), key=lambda x: x[1])
            worst_agent = sorted_agents[0][0]
            
            self.logger.info(f"🎯 识别最差智能体: {worst_agent} (Shapley值: {sorted_agents[0][1]:.6f})")
            
            # 模拟OPRO优化过程
            optimization_result = {
                "target_agent": worst_agent,
                "original_score": sorted_agents[0][1],
                "optimization_applied": True,
                "improvement_estimate": 0.05  # 预估改进幅度
            }
            
            self.logger.info(f"✅ 阶段4完成: 优化了智能体 {worst_agent}")
            
            return OPROResult(
                success=True,
                execution_time=1.5,
                enabled=True,
                optimization_result=optimization_result,
                updated_agents=[worst_agent],
                optimization_stats={
                    "total_agents": len(shapley_values),
                    "optimized_agents": 1,
                    "target_agent": worst_agent
                }
            )
            
        except Exception as e:
            self.logger.error(f"❌ 阶段4失败: OPRO优化错误 - {str(e)}")
            return OPROResult(
                success=False,
                execution_time=0.3,
                enabled=True,
                optimization_result={},
                updated_agents=[],
                optimization_stats={},
                error=str(e)
            )

    def optimize_single_agent(self, agent_name: str, current_prompt: str, performance_score: float, failure_cases: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Any]:
        """优化单个智能体的提示词"""
        return {
            "agent_name": agent_name,
            "original_prompt": current_prompt,
            "optimized_prompt": current_prompt + " [OPTIMIZED]",
            "performance_improvement": 0.05,
            "optimization_applied": True
        }


    def get_optimization_history(self, agent_name: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """获取优化历史记录"""
        return []  # 简单实现：返回空历史


    def validate_prompt(self, prompt: str, agent_type: Optional[str] = None) -> Dict[str, Any]:
        """验证提示词"""
        return {
            "valid": True,
            "score": 0.8,
            "suggestions": []
        }



class MockStateManager(IStateManager):
    """模拟状态管理器"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self._states = {}
        
    def save_state(self, state_type: StateType, state_id: str, state_data: Dict[str, Any], metadata: Optional[Dict[str, Any]] = None) -> bool:
        """保存状态"""
        key = f"{state_type.value}_{state_id}"
        self._states[key] = {
            "data": state_data,
            "metadata": metadata or {},
            "timestamp": datetime.now().isoformat()
        }
        self.logger.debug(f"Saved state: {key}")
        return True

    def load_state(self, state_type: StateType, state_id: str) -> Optional[Dict[str, Any]]:
        """加载状态"""
        key = f"{state_type.value}_{state_id}"
        if key in self._states:
            return self._states[key]["data"]
        return None

    def delete_state(self, state_type: StateType, state_id: str) -> bool:
        """删除状态"""
        key = f"{state_type.value}_{state_id}"
        if key in self._states:
            del self._states[key]
            return True
        return False

    def update_state(self, state_type: StateType, state_id: str, updates: Dict[str, Any], create_if_not_exists: bool = False) -> bool:
        """更新状态数据"""
        key = f"{state_type.value}_{state_id}"
        if key in self._states:
            self._states[key]["data"].update(updates)
            self._states[key]["timestamp"] = datetime.now().isoformat()
            return True
        elif create_if_not_exists:
            return self.save_state(state_type, state_id, updates)
        return False

    def list_states(self, state_type: StateType, filters: Optional[Dict[str, Any]] = None, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """列出状态数据"""
        prefix = f"{state_type.value}_"
        states = []
        for key, value in self._states.items():
            if key.startswith(prefix):
                state_info = {
                    "state_id": key[len(prefix):],
                    "data": value["data"],
                    "timestamp": value["timestamp"]
                }
                states.append(state_info)

        if limit:
            states = states[:limit]
        return states

    def get_state_history(self, state_type: StateType, state_id: str, limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """获取状态变更历史"""
        return []  # 简单实现：返回空历史

    def subscribe_to_state_changes(self, state_type: StateType, callback: callable, state_id: Optional[str] = None) -> str:
        """订阅状态变更通知"""
        subscription_id = f"sub_{state_type.value}_{state_id or 'all'}"
        return subscription_id

    def unsubscribe_from_state_changes(self, subscription_id: str) -> bool:
        """取消状态变更订阅"""
        return True  # 简单实现：总是成功

    def create_snapshot(self, snapshot_name: str, state_types: Optional[List[StateType]] = None) -> str:
        """创建状态快照"""
        snapshot_id = f"snapshot_{snapshot_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        return snapshot_id

    def restore_from_snapshot(self, snapshot_id: str, selective_restore: Optional[Dict[StateType, List[str]]] = None) -> bool:
        """从快照恢复状态"""
        return True  # 简单实现：总是成功

    def cleanup_old_states(self, state_type: StateType, retention_days: int = 30) -> int:
        """清理旧状态数据"""
        return 0  # 简单实现：没有清理任何数据


class MockEventBus(IEventBus):
    """模拟事件总线"""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self._events = []
        
    def publish(self, event_type: str, data: Any, source: Optional[str] = None, correlation_id: Optional[str] = None) -> None:
        """发布事件"""
        event = Event(
            event_type=event_type,
            data=data,
            timestamp=datetime.now(),
            source=source,
            correlation_id=correlation_id
        )
        self._events.append(event)
        self.logger.debug(f"Published event: {event_type} from {source}")

    def subscribe(self, event_type: str, callback: callable, event_filter: Optional[EventFilter] = None) -> str:
        """订阅事件"""
        # 简单的模拟实现
        subscription_id = f"sub_{len(self._events)}_{event_type}"
        self.logger.debug(f"Subscribed to {event_type}: {subscription_id}")
        return subscription_id

    def unsubscribe(self, subscription_id: str) -> bool:
        """取消订阅"""
        self.logger.debug(f"Unsubscribed: {subscription_id}")
        return True

    def publish_async(self, event_type: str, data: Any, source: Optional[str] = None, correlation_id: Optional[str] = None) -> None:
        """异步发布事件"""
        # 简单实现：直接调用同步版本
        self.publish(event_type, data, source, correlation_id)

    def get_events(self, event_type: Optional[str] = None) -> List[Event]:
        """获取事件列表"""
        if event_type:
            return [e for e in self._events if e.event_type == event_type]
        return self._events.copy()