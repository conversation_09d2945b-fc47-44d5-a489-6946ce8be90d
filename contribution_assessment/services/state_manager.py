"""
状态管理服务实现

提供系统状态的管理和持久化功能
"""

import json
import os
import sqlite3
import threading
from typing import Dict, List, Optional, Any, Union
from datetime import datetime, timedelta
from pathlib import Path
import logging

from ..interfaces.state_service import IStateManager, StateType
from ..interfaces.state_service import StateStorageError, StateLoadError, StateUpdateError, SnapshotError
from ..infrastructure.event_bus import IEventBus
from ..infrastructure.configuration_manager import IConfigurationManager

logger = logging.getLogger(__name__)


class StateManager(IStateManager):
    """
    状态管理服务实现
    
    使用SQLite作为后端存储，支持状态持久化、变更跟踪和事件发布
    """
    
    def __init__(
        self,
        storage_path: Optional[str] = None,
        event_bus: Optional[IEventBus] = None,
        config_manager: Optional[IConfigurationManager] = None
    ):
        """
        初始化状态管理器
        
        Args:
            storage_path: 存储路径
            event_bus: 事件总线实例
            config_manager: 配置管理器实例
        """
        self.storage_path = storage_path or "data/state_manager.db"
        self.event_bus = event_bus
        self.config_manager = config_manager
        
        # 确保存储目录存在
        Path(self.storage_path).parent.mkdir(parents=True, exist_ok=True)
        
        # 线程锁，确保并发安全
        self._lock = threading.RLock()
        
        # 订阅管理
        self._subscriptions = {}
        self._subscription_counter = 0
        
        # 初始化数据库
        self._init_database()
        
        logger.info(f"StateManager initialized with storage: {self.storage_path}")
    
    def _init_database(self) -> None:
        """初始化数据库表结构"""
        try:
            with sqlite3.connect(self.storage_path) as conn:
                cursor = conn.cursor()
                
                # 状态数据表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS states (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        state_type TEXT NOT NULL,
                        state_id TEXT NOT NULL,
                        state_data TEXT NOT NULL,
                        metadata TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        UNIQUE(state_type, state_id)
                    )
                ''')
                
                # 状态历史表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS state_history (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        state_type TEXT NOT NULL,
                        state_id TEXT NOT NULL,
                        state_data TEXT NOT NULL,
                        metadata TEXT,
                        operation TEXT NOT NULL,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 快照表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS snapshots (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        snapshot_id TEXT UNIQUE NOT NULL,
                        snapshot_name TEXT NOT NULL,
                        snapshot_data TEXT NOT NULL,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_states_type_id ON states(state_type, state_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_history_type_id ON state_history(state_type, state_id)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_history_timestamp ON state_history(timestamp)')
                
                conn.commit()
                logger.debug("Database initialized successfully")
                
        except Exception as e:
            logger.error(f"Failed to initialize database: {str(e)}")
            raise StateStorageError(f"Database initialization failed: {str(e)}")
    
    def save_state(
        self,
        state_type: StateType,
        state_id: str,
        state_data: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """保存状态数据"""
        try:
            with self._lock:
                state_data_json = json.dumps(state_data, ensure_ascii=False, default=str)
                metadata_json = json.dumps(metadata or {}, ensure_ascii=False, default=str)
                
                with sqlite3.connect(self.storage_path) as conn:
                    cursor = conn.cursor()
                    
                    # 检查是否已存在
                    cursor.execute(
                        'SELECT id FROM states WHERE state_type = ? AND state_id = ?',
                        (state_type.value, state_id)
                    )
                    existing = cursor.fetchone()
                    
                    if existing:
                        # 更新现有记录
                        cursor.execute('''
                            UPDATE states 
                            SET state_data = ?, metadata = ?, updated_at = CURRENT_TIMESTAMP
                            WHERE state_type = ? AND state_id = ?
                        ''', (state_data_json, metadata_json, state_type.value, state_id))
                        operation = "update"
                    else:
                        # 插入新记录
                        cursor.execute('''
                            INSERT INTO states (state_type, state_id, state_data, metadata)
                            VALUES (?, ?, ?, ?)
                        ''', (state_type.value, state_id, state_data_json, metadata_json))
                        operation = "create"
                    
                    # 记录历史
                    cursor.execute('''
                        INSERT INTO state_history (state_type, state_id, state_data, metadata, operation)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (state_type.value, state_id, state_data_json, metadata_json, operation))
                    
                    conn.commit()
                
                # 发布状态变更事件
                self._publish_state_change_event(state_type, state_id, operation, state_data)
                
                logger.debug(f"State saved: {state_type.value}/{state_id}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to save state {state_type.value}/{state_id}: {str(e)}")
            raise StateStorageError(f"Failed to save state: {str(e)}")
    
    def load_state(
        self,
        state_type: StateType,
        state_id: str
    ) -> Optional[Dict[str, Any]]:
        """加载状态数据"""
        try:
            with self._lock:
                with sqlite3.connect(self.storage_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        SELECT state_data, metadata, updated_at 
                        FROM states 
                        WHERE state_type = ? AND state_id = ?
                    ''', (state_type.value, state_id))
                    
                    result = cursor.fetchone()
                    if result:
                        state_data = json.loads(result[0])
                        metadata = json.loads(result[1]) if result[1] else {}
                        
                        # 添加元数据信息
                        state_data['_metadata'] = {
                            **metadata,
                            'updated_at': result[2],
                            'state_type': state_type.value,
                            'state_id': state_id
                        }
                        
                        logger.debug(f"State loaded: {state_type.value}/{state_id}")
                        return state_data
                    
                    return None
                    
        except Exception as e:
            logger.error(f"Failed to load state {state_type.value}/{state_id}: {str(e)}")
            raise StateLoadError(f"Failed to load state: {str(e)}")
    
    def update_state(
        self,
        state_type: StateType,
        state_id: str,
        updates: Dict[str, Any],
        create_if_not_exists: bool = False
    ) -> bool:
        """更新状态数据"""
        try:
            with self._lock:
                # 加载现有状态
                current_state = self.load_state(state_type, state_id)
                
                if current_state is None:
                    if create_if_not_exists:
                        # 创建新状态
                        return self.save_state(state_type, state_id, updates)
                    else:
                        raise StateUpdateError(f"State {state_type.value}/{state_id} does not exist")
                
                # 移除元数据
                if '_metadata' in current_state:
                    del current_state['_metadata']
                
                # 应用更新
                current_state.update(updates)
                
                # 保存更新后的状态
                return self.save_state(state_type, state_id, current_state)
                
        except Exception as e:
            logger.error(f"Failed to update state {state_type.value}/{state_id}: {str(e)}")
            raise StateUpdateError(f"Failed to update state: {str(e)}")
    
    def delete_state(
        self,
        state_type: StateType,
        state_id: str
    ) -> bool:
        """删除状态数据"""
        try:
            with self._lock:
                with sqlite3.connect(self.storage_path) as conn:
                    cursor = conn.cursor()
                    
                    # 检查是否存在
                    cursor.execute(
                        'SELECT state_data FROM states WHERE state_type = ? AND state_id = ?',
                        (state_type.value, state_id)
                    )
                    existing = cursor.fetchone()
                    
                    if not existing:
                        return False
                    
                    # 记录删除历史
                    cursor.execute('''
                        INSERT INTO state_history (state_type, state_id, state_data, metadata, operation)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (state_type.value, state_id, existing[0], '{}', 'delete'))
                    
                    # 删除状态
                    cursor.execute(
                        'DELETE FROM states WHERE state_type = ? AND state_id = ?',
                        (state_type.value, state_id)
                    )
                    
                    conn.commit()
                
                # 发布删除事件
                self._publish_state_change_event(state_type, state_id, "delete", {})
                
                logger.debug(f"State deleted: {state_type.value}/{state_id}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to delete state {state_type.value}/{state_id}: {str(e)}")
            return False
    
    def list_states(
        self,
        state_type: StateType,
        filters: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """列出状态数据"""
        try:
            with self._lock:
                with sqlite3.connect(self.storage_path) as conn:
                    cursor = conn.cursor()
                    
                    query = '''
                        SELECT state_id, state_data, metadata, created_at, updated_at
                        FROM states 
                        WHERE state_type = ?
                    '''
                    params = [state_type.value]
                    
                    # 应用过滤条件
                    if filters:
                        # 这里可以根据需要扩展过滤逻辑
                        pass
                    
                    query += ' ORDER BY updated_at DESC'
                    
                    if limit:
                        query += ' LIMIT ?'
                        params.append(limit)
                    
                    cursor.execute(query, params)
                    results = []
                    
                    for row in cursor.fetchall():
                        state_data = json.loads(row[1])
                        metadata = json.loads(row[2]) if row[2] else {}
                        
                        results.append({
                            'state_id': row[0],
                            'state_data': state_data,
                            'metadata': metadata,
                            'created_at': row[3],
                            'updated_at': row[4]
                        })
                    
                    return results
                    
        except Exception as e:
            logger.error(f"Failed to list states for {state_type.value}: {str(e)}")
            return []
    
    def get_state_history(
        self,
        state_type: StateType,
        state_id: str,
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """获取状态变更历史"""
        try:
            with self._lock:
                with sqlite3.connect(self.storage_path) as conn:
                    cursor = conn.cursor()
                    
                    query = '''
                        SELECT state_data, metadata, operation, timestamp
                        FROM state_history 
                        WHERE state_type = ? AND state_id = ?
                        ORDER BY timestamp DESC
                    '''
                    params = [state_type.value, state_id]
                    
                    if limit:
                        query += ' LIMIT ?'
                        params.append(limit)
                    
                    cursor.execute(query, params)
                    results = []
                    
                    for row in cursor.fetchall():
                        state_data = json.loads(row[0])
                        metadata = json.loads(row[1]) if row[1] else {}
                        
                        results.append({
                            'state_data': state_data,
                            'metadata': metadata,
                            'operation': row[2],
                            'timestamp': row[3]
                        })
                    
                    return results
                    
        except Exception as e:
            logger.error(f"Failed to get state history for {state_type.value}/{state_id}: {str(e)}")
            return []
    
    def subscribe_to_state_changes(
        self,
        state_type: StateType,
        callback: callable,
        state_id: Optional[str] = None
    ) -> str:
        """订阅状态变更通知"""
        with self._lock:
            self._subscription_counter += 1
            subscription_id = f"sub_{self._subscription_counter}_{datetime.now().timestamp()}"
            
            self._subscriptions[subscription_id] = {
                'state_type': state_type,
                'state_id': state_id,
                'callback': callback,
                'created_at': datetime.now()
            }
            
            logger.debug(f"Subscription created: {subscription_id} for {state_type.value}")
            return subscription_id
    
    def unsubscribe_from_state_changes(self, subscription_id: str) -> bool:
        """取消状态变更订阅"""
        with self._lock:
            if subscription_id in self._subscriptions:
                del self._subscriptions[subscription_id]
                logger.debug(f"Subscription removed: {subscription_id}")
                return True
            return False
    
    def create_snapshot(
        self,
        snapshot_name: str,
        state_types: Optional[List[StateType]] = None
    ) -> str:
        """创建状态快照"""
        try:
            with self._lock:
                snapshot_id = f"snap_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hash(snapshot_name) % 10000}"
                
                # 收集要快照的状态
                snapshot_data = {}
                
                if state_types is None:
                    state_types = list(StateType)
                
                for state_type in state_types:
                    states = self.list_states(state_type)
                    snapshot_data[state_type.value] = states
                
                # 保存快照
                with sqlite3.connect(self.storage_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        INSERT INTO snapshots (snapshot_id, snapshot_name, snapshot_data)
                        VALUES (?, ?, ?)
                    ''', (snapshot_id, snapshot_name, json.dumps(snapshot_data, ensure_ascii=False, default=str)))
                    conn.commit()
                
                logger.info(f"Snapshot created: {snapshot_id} ({snapshot_name})")
                return snapshot_id
                
        except Exception as e:
            logger.error(f"Failed to create snapshot {snapshot_name}: {str(e)}")
            raise SnapshotError(f"Failed to create snapshot: {str(e)}")
    
    def restore_from_snapshot(
        self,
        snapshot_id: str,
        selective_restore: Optional[Dict[StateType, List[str]]] = None
    ) -> bool:
        """从快照恢复状态"""
        try:
            with self._lock:
                # 加载快照数据
                with sqlite3.connect(self.storage_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute(
                        'SELECT snapshot_data FROM snapshots WHERE snapshot_id = ?',
                        (snapshot_id,)
                    )
                    result = cursor.fetchone()
                    
                    if not result:
                        raise SnapshotError(f"Snapshot {snapshot_id} not found")
                    
                    snapshot_data = json.loads(result[0])
                
                # 恢复状态
                for state_type_str, states in snapshot_data.items():
                    state_type = StateType(state_type_str)
                    
                    # 检查选择性恢复
                    if selective_restore and state_type in selective_restore:
                        target_state_ids = selective_restore[state_type]
                        states = [s for s in states if s['state_id'] in target_state_ids]
                    
                    for state_info in states:
                        self.save_state(
                            state_type,
                            state_info['state_id'],
                            state_info['state_data'],
                            state_info['metadata']
                        )
                
                logger.info(f"Restored from snapshot: {snapshot_id}")
                return True
                
        except Exception as e:
            logger.error(f"Failed to restore from snapshot {snapshot_id}: {str(e)}")
            raise SnapshotError(f"Failed to restore from snapshot: {str(e)}")
    
    def cleanup_old_states(
        self,
        state_type: StateType,
        retention_days: int = 30
    ) -> int:
        """清理旧状态数据"""
        try:
            with self._lock:
                cutoff_date = datetime.now() - timedelta(days=retention_days)
                
                with sqlite3.connect(self.storage_path) as conn:
                    cursor = conn.cursor()
                    
                    # 清理旧的历史记录
                    cursor.execute('''
                        DELETE FROM state_history 
                        WHERE state_type = ? AND timestamp < ?
                    ''', (state_type.value, cutoff_date.isoformat()))
                    
                    deleted_count = cursor.rowcount
                    conn.commit()
                
                logger.info(f"Cleaned up {deleted_count} old state records for {state_type.value}")
                return deleted_count
                
        except Exception as e:
            logger.error(f"Failed to cleanup old states for {state_type.value}: {str(e)}")
            return 0
    
    def _publish_state_change_event(
        self,
        state_type: StateType,
        state_id: str,
        operation: str,
        state_data: Dict[str, Any]
    ) -> None:
        """发布状态变更事件"""
        try:
            # 通知订阅者
            for subscription_id, subscription in self._subscriptions.items():
                if (subscription['state_type'] == state_type and 
                    (subscription['state_id'] is None or subscription['state_id'] == state_id)):
                    
                    try:
                        subscription['callback']({
                            'state_type': state_type.value,
                            'state_id': state_id,
                            'operation': operation,
                            'state_data': state_data,
                            'timestamp': datetime.now().isoformat()
                        })
                    except Exception as e:
                        logger.error(f"Error in subscription callback {subscription_id}: {str(e)}")
            
            # 通过事件总线发布
            if self.event_bus:
                self.event_bus.publish('state_changed', {
                    'state_type': state_type.value,
                    'state_id': state_id,
                    'operation': operation,
                    'timestamp': datetime.now().isoformat()
                })
                
        except Exception as e:
            logger.error(f"Failed to publish state change event: {str(e)}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取状态管理器统计信息"""
        try:
            with self._lock:
                with sqlite3.connect(self.storage_path) as conn:
                    cursor = conn.cursor()
                    
                    # 统计各类型状态数量
                    cursor.execute('''
                        SELECT state_type, COUNT(*) 
                        FROM states 
                        GROUP BY state_type
                    ''')
                    state_counts = dict(cursor.fetchall())
                    
                    # 统计历史记录数量
                    cursor.execute('SELECT COUNT(*) FROM state_history')
                    history_count = cursor.fetchone()[0]
                    
                    # 统计快照数量
                    cursor.execute('SELECT COUNT(*) FROM snapshots')
                    snapshot_count = cursor.fetchone()[0]
                    
                    return {
                        'state_counts': state_counts,
                        'history_records': history_count,
                        'snapshots': snapshot_count,
                        'active_subscriptions': len(self._subscriptions),
                        'storage_path': self.storage_path
                    }
                    
        except Exception as e:
            logger.error(f"Failed to get statistics: {str(e)}")
            return {}