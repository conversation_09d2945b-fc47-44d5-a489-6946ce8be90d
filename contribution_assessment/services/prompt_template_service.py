"""
提示词模板管理服务

负责管理智能体提示词模板的存储、版本控制和更新
"""

import json
import os
import logging
from typing import Dict, List, Optional, Any
from datetime import datetime
import hashlib
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class PromptTemplate:
    """提示词模板数据结构"""
    agent_name: str
    template_id: str
    content: str
    version: str
    created_at: datetime
    is_active: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        if isinstance(self.created_at, str):
            self.created_at = datetime.fromisoformat(self.created_at)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "agent_name": self.agent_name,
            "template_id": self.template_id,
            "content": self.content,
            "version": self.version,
            "created_at": self.created_at.isoformat(),
            "is_active": self.is_active,
            "metadata": self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "PromptTemplate":
        """从字典创建实例"""
        return cls(
            agent_name=data["agent_name"],
            template_id=data["template_id"],
            content=data["content"],
            version=data["version"],
            created_at=datetime.fromisoformat(data["created_at"]),
            is_active=data.get("is_active", True),
            metadata=data.get("metadata", {})
        )


class PromptTemplateService:
    """
    提示词模板管理服务
    
    负责提示词模板的存储、版本控制、更新和检索
    支持固定模板和可优化部分的分离
    """
    
    def __init__(self,
                 templates_dir: str = "data/prompt_templates",
                 logger: Optional[logging.Logger] = None):
        """
        初始化提示词模板服务
        
        Args:
            templates_dir: 模板存储目录
            logger: 日志记录器
        """
        self.templates_dir = Path(templates_dir)
        self.logger = logger or logging.getLogger(__name__)
        
        # 创建模板目录
        self.templates_dir.mkdir(parents=True, exist_ok=True)
        
        # 内存中的模板缓存
        self._templates_cache: Dict[str, List[PromptTemplate]] = {}
        self._active_templates: Dict[str, PromptTemplate] = {}
        
        # 固定模板和可优化部分的分离
        self._fixed_templates: Dict[str, str] = {}
        self._optimizable_parts: Dict[str, str] = {}
        
        # 加载现有模板
        self._load_templates()
        
        self.logger.info(f"提示词模板服务初始化完成，模板目录: {self.templates_dir}")
    
    def get_template(self, agent_name: str, version: Optional[str] = None) -> Optional[PromptTemplate]:
        """
        获取指定智能体的提示词模板
        
        Args:
            agent_name: 智能体名称
            version: 指定版本（可选，默认获取活跃版本）
            
        Returns:
            Optional[PromptTemplate]: 提示词模板，如果不存在则返回None
        """
        try:
            if version is None:
                # 获取活跃版本
                return self._active_templates.get(agent_name)
            else:
                # 获取指定版本
                agent_templates = self._templates_cache.get(agent_name, [])
                for template in agent_templates:
                    if template.version == version:
                        return template
                return None
                
        except Exception as e:
            self.logger.error(f"获取提示词模板失败 ({agent_name}): {e}")
            return None
    
    def save_template(self,
                     agent_name: str,
                     content: str,
                     metadata: Optional[Dict[str, Any]] = None,
                     make_active: bool = True) -> Optional[str]:
        """
        保存新的提示词模板
        
        Args:
            agent_name: 智能体名称
            content: 模板内容
            metadata: 元数据
            make_active: 是否设置为活跃版本
            
        Returns:
            Optional[str]: 新模板的ID，如果保存失败则返回None
        """
        try:
            # 生成模板ID和版本
            template_id = self._generate_template_id(agent_name, content)
            version = self._generate_version(agent_name)
            
            # 创建模板对象
            template = PromptTemplate(
                agent_name=agent_name,
                template_id=template_id,
                content=content,
                version=version,
                created_at=datetime.now(),
                is_active=make_active,
                metadata=metadata or {}
            )
            
            # 保存到文件
            self._save_template_to_file(template)
            
            # 更新缓存
            if agent_name not in self._templates_cache:
                self._templates_cache[agent_name] = []
            
            self._templates_cache[agent_name].append(template)
            
            # 如果设置为活跃版本，更新活跃模板
            if make_active:
                # 将之前的活跃模板设为非活跃
                if agent_name in self._active_templates:
                    old_template = self._active_templates[agent_name]
                    old_template.is_active = False
                    self._save_template_to_file(old_template)
                
                self._active_templates[agent_name] = template
            
            self.logger.info(f"保存提示词模板成功 ({agent_name}): {template_id}")
            return template_id
            
        except Exception as e:
            self.logger.error(f"保存提示词模板失败 ({agent_name}): {e}")
            return None
    
    def update_template(self,
                       agent_name: str,
                       template_id: str,
                       content: str,
                       metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        更新现有提示词模板
        
        Args:
            agent_name: 智能体名称
            template_id: 模板ID
            content: 新的模板内容
            metadata: 新的元数据
            
        Returns:
            bool: 是否更新成功
        """
        try:
            # 查找现有模板
            template = None
            agent_templates = self._templates_cache.get(agent_name, [])
            
            for t in agent_templates:
                if t.template_id == template_id:
                    template = t
                    break
            
            if not template:
                self.logger.warning(f"模板不存在 ({agent_name}): {template_id}")
                return False
            
            # 更新模板内容
            template.content = content
            if metadata:
                template.metadata.update(metadata)
            
            # 保存到文件
            self._save_template_to_file(template)
            
            # 如果是活跃模板，更新活跃缓存
            if template.is_active:
                self._active_templates[agent_name] = template
            
            self.logger.info(f"更新提示词模板成功 ({agent_name}): {template_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"更新提示词模板失败 ({agent_name}): {e}")
            return False
    
    def set_active_template(self, agent_name: str, template_id: str) -> bool:
        """
        设置活跃模板
        
        Args:
            agent_name: 智能体名称
            template_id: 模板ID
            
        Returns:
            bool: 是否设置成功
        """
        try:
            # 查找目标模板
            target_template = None
            agent_templates = self._templates_cache.get(agent_name, [])
            
            for template in agent_templates:
                if template.template_id == template_id:
                    target_template = template
                    break
            
            if not target_template:
                self.logger.warning(f"模板不存在 ({agent_name}): {template_id}")
                return False
            
            # 将当前活跃模板设为非活跃
            if agent_name in self._active_templates:
                old_template = self._active_templates[agent_name]
                old_template.is_active = False
                self._save_template_to_file(old_template)
            
            # 设置新的活跃模板
            target_template.is_active = True
            self._save_template_to_file(target_template)
            self._active_templates[agent_name] = target_template
            
            self.logger.info(f"设置活跃模板成功 ({agent_name}): {template_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"设置活跃模板失败 ({agent_name}): {e}")
            return False
    
    def get_template_history(self, agent_name: str, limit: int = 10) -> List[PromptTemplate]:
        """
        获取智能体的模板历史
        
        Args:
            agent_name: 智能体名称
            limit: 返回数量限制
            
        Returns:
            List[PromptTemplate]: 模板历史列表
        """
        try:
            agent_templates = self._templates_cache.get(agent_name, [])
            
            # 按创建时间倒序排列
            sorted_templates = sorted(agent_templates, key=lambda x: x.created_at, reverse=True)
            
            return sorted_templates[:limit]
            
        except Exception as e:
            self.logger.error(f"获取模板历史失败 ({agent_name}): {e}")
            return []
    
    def rollback_template(self, agent_name: str, target_version: str) -> bool:
        """
        回滚模板到指定版本
        
        Args:
            agent_name: 智能体名称
            target_version: 目标版本
            
        Returns:
            bool: 是否回滚成功
        """
        try:
            # 查找目标版本模板
            target_template = None
            agent_templates = self._templates_cache.get(agent_name, [])
            
            for template in agent_templates:
                if template.version == target_version:
                    target_template = template
                    break
            
            if not target_template:
                self.logger.warning(f"目标版本不存在 ({agent_name}): {target_version}")
                return False
            
            # 创建新的模板（基于目标版本）
            new_template_id = self.save_template(
                agent_name=agent_name,
                content=target_template.content,
                metadata={
                    **target_template.metadata,
                    "rollback_from": target_version,
                    "rollback_timestamp": datetime.now().isoformat()
                },
                make_active=True
            )
            
            if new_template_id:
                self.logger.info(f"回滚模板成功 ({agent_name}): {target_version} -> {new_template_id}")
                return True
            else:
                return False
                
        except Exception as e:
            self.logger.error(f"回滚模板失败 ({agent_name}): {e}")
            return False
    
    def separate_fixed_and_optimizable_parts(self,
                                           agent_name: str,
                                           fixed_part: str,
                                           optimizable_part: str) -> bool:
        """
        分离固定模板和可优化部分
        
        Args:
            agent_name: 智能体名称
            fixed_part: 固定模板部分
            optimizable_part: 可优化部分
            
        Returns:
            bool: 是否分离成功
        """
        try:
            self._fixed_templates[agent_name] = fixed_part
            self._optimizable_parts[agent_name] = optimizable_part
            
            # 保存到文件
            self._save_template_separation(agent_name, fixed_part, optimizable_part)
            
            self.logger.info(f"分离固定和可优化部分成功 ({agent_name})")
            return True
            
        except Exception as e:
            self.logger.error(f"分离固定和可优化部分失败 ({agent_name}): {e}")
            return False
    
    def get_combined_template(self, agent_name: str) -> Optional[str]:
        """
        获取组合后的完整模板
        
        Args:
            agent_name: 智能体名称
            
        Returns:
            Optional[str]: 组合后的模板内容
        """
        try:
            fixed_part = self._fixed_templates.get(agent_name, "")
            optimizable_part = self._optimizable_parts.get(agent_name, "")
            
            if fixed_part and optimizable_part:
                # 简单组合，实际可能需要更复杂的模板渲染
                return f"{fixed_part}\n\n{optimizable_part}"
            
            # 如果没有分离，返回活跃模板
            active_template = self._active_templates.get(agent_name)
            if active_template:
                return active_template.content
            
            return None
            
        except Exception as e:
            self.logger.error(f"获取组合模板失败 ({agent_name}): {e}")
            return None
    
    def list_agents_with_templates(self) -> List[str]:
        """
        列出所有有模板的智能体
        
        Returns:
            List[str]: 智能体名称列表
        """
        return list(self._templates_cache.keys())
    
    def get_template_stats(self) -> Dict[str, Any]:
        """
        获取模板统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        try:
            stats = {
                "total_agents": len(self._templates_cache),
                "total_templates": sum(len(templates) for templates in self._templates_cache.values()),
                "active_templates": len(self._active_templates),
                "agents_with_separation": len(self._fixed_templates),
                "agents_with_templates": list(self._templates_cache.keys())
            }
            
            # 每个智能体的模板数量
            agent_template_counts = {}
            for agent_name, templates in self._templates_cache.items():
                agent_template_counts[agent_name] = len(templates)
            
            stats["template_counts_by_agent"] = agent_template_counts
            
            return stats
            
        except Exception as e:
            self.logger.error(f"获取模板统计失败: {e}")
            return {"error": str(e)}
    
    # 私有方法
    
    def _load_templates(self) -> None:
        """加载现有模板"""
        try:
            # 加载标准模板
            templates_pattern = self.templates_dir / "*.json"
            for template_file in self.templates_dir.glob("*.json"):
                try:
                    with open(template_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    template = PromptTemplate.from_dict(data)
                    
                    # 添加到缓存
                    if template.agent_name not in self._templates_cache:
                        self._templates_cache[template.agent_name] = []
                    
                    self._templates_cache[template.agent_name].append(template)
                    
                    # 如果是活跃模板，添加到活跃缓存
                    if template.is_active:
                        self._active_templates[template.agent_name] = template
                        
                except Exception as e:
                    self.logger.error(f"加载模板文件失败 ({template_file}): {e}")
                    continue
            
            # 加载模板分离文件
            separation_file = self.templates_dir / "template_separation.json"
            if separation_file.exists():
                try:
                    with open(separation_file, 'r', encoding='utf-8') as f:
                        separation_data = json.load(f)
                    
                    for agent_name, parts in separation_data.items():
                        self._fixed_templates[agent_name] = parts.get("fixed_part", "")
                        self._optimizable_parts[agent_name] = parts.get("optimizable_part", "")
                        
                except Exception as e:
                    self.logger.error(f"加载模板分离文件失败: {e}")
            
            self.logger.info(f"加载模板完成，共 {len(self._templates_cache)} 个智能体")
            
        except Exception as e:
            self.logger.error(f"加载模板失败: {e}")
    
    def _save_template_to_file(self, template: PromptTemplate) -> None:
        """保存模板到文件"""
        try:
            filename = f"{template.agent_name}_{template.template_id}.json"
            filepath = self.templates_dir / filename
            
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(template.to_dict(), f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"保存模板文件失败: {e}")
            raise
    
    def _save_template_separation(self, agent_name: str, fixed_part: str, optimizable_part: str) -> None:
        """保存模板分离信息"""
        try:
            separation_file = self.templates_dir / "template_separation.json"
            
            # 加载现有数据
            separation_data = {}
            if separation_file.exists():
                with open(separation_file, 'r', encoding='utf-8') as f:
                    separation_data = json.load(f)
            
            # 更新数据
            separation_data[agent_name] = {
                "fixed_part": fixed_part,
                "optimizable_part": optimizable_part,
                "updated_at": datetime.now().isoformat()
            }
            
            # 保存数据
            with open(separation_file, 'w', encoding='utf-8') as f:
                json.dump(separation_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            self.logger.error(f"保存模板分离信息失败: {e}")
            raise
    
    def _generate_template_id(self, agent_name: str, content: str) -> str:
        """生成模板ID"""
        # 使用内容的哈希值作为ID
        content_hash = hashlib.md5(content.encode('utf-8')).hexdigest()[:8]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        return f"{agent_name}_{timestamp}_{content_hash}"
    
    def _generate_version(self, agent_name: str) -> str:
        """生成版本号"""
        agent_templates = self._templates_cache.get(agent_name, [])
        version_number = len(agent_templates) + 1
        return f"v{version_number:03d}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"