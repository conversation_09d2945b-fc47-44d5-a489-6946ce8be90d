"""
历史得分管理器 (Historical Score Manager)

本模块负责管理智能体的历史Shapley值得分，为OPRO优化提供反馈数据。
主要功能包括：
1. 读取和解析周期性Shapley值结果
2. 维护提示词与性能的映射关系
3. 提供历史得分趋势分析
4. 存储和检索优化结果
"""

import os
import json
import glob
import logging
import sqlite3
import hashlib
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import pandas as pd
import numpy as np
from collections import defaultdict, deque

class HistoricalScoreManager:
    """
    历史得分管理器
    
    管理智能体的历史Shapley值得分，维护提示词优化的反馈循环，
    提供历史数据分析和趋势预测功能。
    """
    
    def __init__(self, 
                 results_base_path: str = "results/periodic_shapley",
                 db_path: str = "results/opro_optimization.db",
                 logger: Optional[logging.Logger] = None):
        """
        初始化历史得分管理器
        
        参数:
            results_base_path: 周期性Shapley结果文件的基础路径
            db_path: SQLite数据库路径，用于存储优化历史
            logger: 日志记录器
        """
        self.results_base_path = results_base_path
        self.db_path = db_path
        self.logger = logger or self._create_default_logger()
        
        # 缓存数据
        self._shapley_cache = {}
        self._cache_timestamp = None
        self._cache_ttl = 300  # 缓存5分钟
        
        # 初始化数据库
        self._init_database()
        
        # 加载最新数据
        self._load_latest_data()
        
        self.logger.info("历史得分管理器初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.HistoricalScoreManager")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _init_database(self):
        """初始化SQLite数据库"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # 创建优化历史表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS optimization_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    agent_id TEXT NOT NULL,
                    prompt_hash TEXT NOT NULL,
                    prompt_text TEXT NOT NULL,
                    shapley_score REAL,
                    optimization_date TEXT NOT NULL,
                    evaluation_date TEXT,
                    is_active BOOLEAN DEFAULT FALSE,
                    metadata TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建周期性得分表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS weekly_scores (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    agent_id TEXT NOT NULL,
                    week_number INTEGER NOT NULL,
                    shapley_score REAL NOT NULL,
                    trading_days TEXT NOT NULL,
                    result_file TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(agent_id, week_number, result_file)
                )
            """)
            
            # 创建决策快照表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS decision_snapshots (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    snapshot_id TEXT UNIQUE NOT NULL,
                    agent_id TEXT NOT NULL,
                    trade_date TEXT NOT NULL,
                    symbol TEXT NOT NULL,
                    coalition_id TEXT NOT NULL,
                    evaluation_run_id TEXT NOT NULL,
                    
                    -- 智能体上下文
                    prompt_used TEXT,
                    agent_output TEXT,
                    parsed_decision TEXT,
                    confidence_score REAL,
                    reasoning TEXT,
                    execution_time REAL,
                    model_used TEXT,
                    temperature REAL,
                    
                    -- 市场上下文
                    opening_price REAL,
                    closing_price REAL,
                    high_price REAL,
                    low_price REAL,
                    volume INTEGER,
                    price_change REAL,
                    price_change_percent REAL,
                    
                    -- 性能影响
                    decision_outcome TEXT,
                    optimal_decision TEXT,
                    actual_return REAL,
                    optimal_return REAL,
                    opportunity_cost REAL,
                    risk_exposure REAL,
                    sharpe_impact REAL,
                    coalition_performance_impact REAL,
                    shapley_contribution REAL,
                    
                    -- 失败分类
                    is_failure_case BOOLEAN DEFAULT FALSE,
                    failure_category TEXT,
                    failure_severity REAL DEFAULT 0.0,
                    
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建失败案例摘要表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS failure_case_summaries (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    case_id TEXT UNIQUE NOT NULL,
                    agent_id TEXT NOT NULL,
                    trade_date TEXT NOT NULL,
                    failure_category TEXT NOT NULL,
                    failure_severity REAL NOT NULL,
                    
                    -- 决策上下文摘要
                    market_context TEXT,
                    decision_made TEXT,
                    optimal_decision TEXT,
                    reasoning_provided TEXT,
                    
                    -- 失败分析
                    opportunity_cost REAL,
                    failure_description TEXT,
                    key_lessons TEXT,  -- JSON格式存储列表
                    
                    -- 元数据
                    confidence_score REAL,
                    execution_time REAL,
                    similar_cases_count INTEGER DEFAULT 1,
                    
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建OPRO优化记录表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS opro_optimization_runs (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    run_id TEXT UNIQUE NOT NULL,
                    agent_id TEXT NOT NULL,
                    optimization_type TEXT DEFAULT 'evidence_based',
                    
                    -- 输入数据
                    original_prompt TEXT,
                    failure_cases_count INTEGER DEFAULT 0,
                    failure_patterns TEXT,
                    
                    -- 输出结果
                    optimized_prompt TEXT,
                    estimated_improvement REAL,
                    candidates_generated INTEGER,
                    candidates_evaluated INTEGER,
                    
                    -- 运行信息
                    optimization_time REAL,
                    success BOOLEAN DEFAULT FALSE,
                    error_message TEXT,
                    
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_optimization_agent ON optimization_history(agent_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_optimization_date ON optimization_history(optimization_date)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_weekly_agent ON weekly_scores(agent_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_weekly_week ON weekly_scores(week_number)")
            
            # 决策快照相关索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_snapshots_agent ON decision_snapshots(agent_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_snapshots_date ON decision_snapshots(trade_date)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_snapshots_coalition ON decision_snapshots(coalition_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_snapshots_failure ON decision_snapshots(is_failure_case)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_snapshots_severity ON decision_snapshots(failure_severity)")
            
            # 失败案例摘要索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_failures_agent ON failure_case_summaries(agent_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_failures_category ON failure_case_summaries(failure_category)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_failures_severity ON failure_case_summaries(failure_severity)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_failures_date ON failure_case_summaries(trade_date)")
            
            # OPRO优化记录索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_opro_runs_agent ON opro_optimization_runs(agent_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_opro_runs_type ON opro_optimization_runs(optimization_type)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_opro_runs_success ON opro_optimization_runs(success)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_opro_runs_date ON opro_optimization_runs(created_at)")
            
            conn.commit()
        
        self.logger.info("数据库初始化完成")
    
    def _load_latest_data(self):
        """加载最新的Shapley值数据"""
        try:
            self._refresh_shapley_cache()
            self.logger.info("最新Shapley数据加载完成")
        except Exception as e:
            self.logger.error(f"加载最新数据失败: {e}")
    
    def _refresh_shapley_cache(self):
        """刷新Shapley值缓存"""
        current_time = datetime.now()
        
        # 检查缓存是否需要刷新
        if (self._cache_timestamp and 
            (current_time - self._cache_timestamp).seconds < self._cache_ttl):
            return

        # self.logger.debug("刷新Shapley值缓存...")
        
        # 清空缓存
        self._shapley_cache = defaultdict(list)
        
        # 获取所有结果文件
        pattern = os.path.join(self.results_base_path, "periodic_shapley_*.json")
        result_files = glob.glob(pattern)
        
        if not result_files:
            self.logger.warning(f"未找到Shapley结果文件: {pattern}")
            return
        
        # 按时间排序文件
        result_files.sort(key=lambda x: os.path.getmtime(x), reverse=True)
        
        # 处理最近的文件
        for file_path in result_files[:10]:  # 只处理最近10个文件
            try:
                self._process_shapley_file(file_path)
            except Exception as e:
                self.logger.error(f"处理文件 {file_path} 失败: {e}")
                continue

        self._cache_timestamp = current_time
        # self.logger.debug(f"缓存刷新完成，共加载 {len(self._shapley_cache)} 个智能体的数据")
    
    def _process_shapley_file(self, file_path: str):
        """处理单个Shapley结果文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if not isinstance(data, list):
                self.logger.warning(f"文件 {file_path} 格式不正确")
                return
            
            file_basename = os.path.basename(file_path)
            
            # 提取文件中的时间戳
            timestamp_str = file_basename.replace("periodic_shapley_", "").replace(".json", "")
            try:
                file_datetime = datetime.strptime(timestamp_str, "%Y%m%d_%H%M%S")
            except ValueError:
                file_datetime = datetime.fromtimestamp(os.path.getmtime(file_path))
            
            # 处理每周的数据
            for week_data in data:
                if not isinstance(week_data, dict) or not week_data.get("success", False):
                    continue
                
                shapley_values = week_data.get("shapley_values", {})
                week_number = week_data.get("week", 0)
                trading_days = week_data.get("trading_days", "")
                
                # 存储到缓存
                for agent_id, score in shapley_values.items():
                    if isinstance(score, (int, float)):
                        self._shapley_cache[agent_id].append({
                            "score": float(score),
                            "week": week_number,
                            "trading_days": trading_days,
                            "file_path": file_path,
                            "file_datetime": file_datetime,
                            "timestamp": file_datetime.isoformat()
                        })
                
                # 存储到数据库
                self._store_weekly_scores_to_db(shapley_values, week_number, trading_days, file_basename)
            
        except Exception as e:
            self.logger.error(f"处理Shapley文件 {file_path} 时出错: {e}")
    
    def _store_weekly_scores_to_db(self, shapley_values: Dict[str, float], 
                                  week_number: int, trading_days: str, file_name: str):
        """将周期性得分存储到数据库"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                for agent_id, score in shapley_values.items():
                    cursor.execute("""
                        INSERT OR IGNORE INTO weekly_scores 
                        (agent_id, week_number, shapley_score, trading_days, result_file)
                        VALUES (?, ?, ?, ?, ?)
                    """, (agent_id, week_number, float(score), trading_days, file_name))
                
                conn.commit()
                
        except Exception as e:
            self.logger.error(f"存储周期性得分到数据库失败: {e}")
    
    def get_agent_score_history(self, agent_id: str, weeks: int = 10) -> List[Dict[str, Any]]:
        """
        获取指定智能体的历史得分
        
        参数:
            agent_id: 智能体ID
            weeks: 返回最近几周的数据
            
        返回:
            历史得分列表，按时间排序
        """
        self._refresh_shapley_cache()
        
        if agent_id not in self._shapley_cache:
            self.logger.warning(f"未找到智能体 {agent_id} 的历史数据")
            return []
        
        # 获取该智能体的所有数据
        agent_data = self._shapley_cache[agent_id]
        
        # 按时间排序
        sorted_data = sorted(agent_data, key=lambda x: x["file_datetime"], reverse=True)
        
        # 限制返回数量
        limited_data = sorted_data[:weeks]

        # self.logger.debug(f"获取智能体 {agent_id} 最近 {len(limited_data)} 周的历史数据")

        return limited_data
    
    def get_agent_optimization_history(self, agent_id: str, weeks: int = 10) -> List[Dict[str, Any]]:
        """
        获取智能体的优化历史（包含提示词信息）
        
        参数:
            agent_id: 智能体ID
            weeks: 考虑的历史周数
            
        返回:
            优化历史列表
        """
        
        # 从数据库获取优化历史
        optimization_history = self._get_optimization_history_from_db(agent_id, weeks)
        
        # 如果数据库中没有足够的历史，使用默认提示词和历史得分
        if len(optimization_history) < 2:
            score_history = self.get_agent_score_history(agent_id, weeks)
            
            # 创建默认的优化历史
            default_history = []
            for i, score_data in enumerate(score_history):
                default_history.append({
                    "prompt": f"默认提示词 {i+1} - {agent_id}",
                    "score": score_data["score"],
                    "timestamp": score_data["timestamp"],
                    "week": score_data["week"],
                    "is_default": True,
                    "source": "historical_scores"
                })
            
            return default_history
        
        return optimization_history
    
    def _get_optimization_history_from_db(self, agent_id: str, weeks: int) -> List[Dict[str, Any]]:
        """从数据库获取优化历史"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取优化历史
                cursor.execute("""
                    SELECT prompt_text, shapley_score, optimization_date, 
                           evaluation_date, metadata
                    FROM optimization_history 
                    WHERE agent_id = ? AND shapley_score IS NOT NULL
                    ORDER BY optimization_date DESC
                    LIMIT ?
                """, (agent_id, weeks))
                
                rows = cursor.fetchall()
                
                optimization_history = []
                for row in rows:
                    prompt_text, score, opt_date, eval_date, metadata_str = row
                    
                    metadata = {}
                    if metadata_str:
                        try:
                            metadata = json.loads(metadata_str)
                        except json.JSONDecodeError:
                            pass
                    
                    optimization_history.append({
                        "prompt": prompt_text,
                        "score": score,
                        "optimization_date": opt_date,
                        "evaluation_date": eval_date,
                        "timestamp": eval_date or opt_date,
                        "metadata": metadata,
                        "is_default": False,
                        "source": "optimization_history"
                    })
                
                return optimization_history
                
        except Exception as e:
            self.logger.error(f"从数据库获取优化历史失败: {e}")
            return []
    
    def store_optimization_result(self, 
                                agent_id: str, 
                                prompt: str, 
                                estimated_score: Optional[float] = None,
                                metadata: Optional[Dict] = None) -> str:
        """
        存储优化结果
        
        参数:
            agent_id: 智能体ID
            prompt: 优化后的提示词
            estimated_score: 估算得分
            metadata: 额外的元数据
            
        返回:
            提示词哈希值
        """
        prompt_hash = self._hash_prompt(prompt)
        current_time = datetime.now().isoformat()
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 先设置其他提示词为非活跃状态
                cursor.execute("""
                    UPDATE optimization_history 
                    SET is_active = FALSE 
                    WHERE agent_id = ?
                """, (agent_id,))
                
                # 插入新的优化结果
                cursor.execute("""
                    INSERT INTO optimization_history 
                    (agent_id, prompt_hash, prompt_text, shapley_score, 
                     optimization_date, is_active, metadata)
                    VALUES (?, ?, ?, ?, ?, TRUE, ?)
                """, (agent_id, prompt_hash, prompt, estimated_score, 
                     current_time, json.dumps(metadata) if metadata else None))
                
                conn.commit()
                
            self.logger.info(f"优化结果已存储: {agent_id} -> {prompt_hash[:8]}...")
            
        except Exception as e:
            self.logger.error(f"存储优化结果失败: {e}")
        
        return prompt_hash
    
    def update_actual_score(self, 
                          agent_id: str, 
                          prompt_hash: str, 
                          actual_score: float,
                          evaluation_date: Optional[str] = None) -> bool:
        """
        更新实际得分
        
        参数:
            agent_id: 智能体ID
            prompt_hash: 提示词哈希值
            actual_score: 实际Shapley得分
            evaluation_date: 评估日期
            
        返回:
            是否更新成功
        """
        evaluation_date = evaluation_date or datetime.now().isoformat()
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    UPDATE optimization_history 
                    SET shapley_score = ?, evaluation_date = ?
                    WHERE agent_id = ? AND prompt_hash = ?
                """, (actual_score, evaluation_date, agent_id, prompt_hash))
                
                rows_affected = cursor.rowcount
                conn.commit()
                
                if rows_affected > 0:
                    self.logger.info(f"更新实际得分成功: {agent_id} {prompt_hash[:8]} -> {actual_score:.6f}")
                    return True
                else:
                    self.logger.warning(f"未找到匹配的优化记录: {agent_id} {prompt_hash[:8]}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"更新实际得分失败: {e}")
            return False
    
    def get_active_prompts(self) -> Dict[str, str]:
        """
        获取当前活跃的提示词
        
        返回:
            {agent_id: prompt_text} 字典
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT agent_id, prompt_text 
                    FROM optimization_history 
                    WHERE is_active = TRUE
                """)
                
                rows = cursor.fetchall()
                return {agent_id: prompt_text for agent_id, prompt_text in rows}
                
        except Exception as e:
            self.logger.error(f"获取活跃提示词失败: {e}")
            return {}
    
    def get_score_trends(self, agent_id: str, weeks: int = 20) -> Dict[str, Any]:
        """
        分析智能体得分趋势
        
        参数:
            agent_id: 智能体ID
            weeks: 分析的历史周数
            
        返回:
            趋势分析结果
        """
        score_history = self.get_agent_score_history(agent_id, weeks)
        
        if len(score_history) < 2:
            return {
                "trend": "insufficient_data",
                "slope": 0.0,
                "r_squared": 0.0,
                "recent_average": 0.0,
                "historical_average": 0.0,
                "volatility": 0.0
            }
        
        # 提取得分和时间
        scores = [data["score"] for data in score_history]
        scores.reverse()  # 按时间正序排列
        
        # 计算趋势
        x = np.arange(len(scores))
        if len(scores) > 1:
            # 线性回归计算趋势
            slope, intercept = np.polyfit(x, scores, 1)
            r_squared = np.corrcoef(x, scores)[0, 1] ** 2 if len(scores) > 2 else 0.0
        else:
            slope = 0.0
            r_squared = 0.0
        
        # 计算其他统计指标
        recent_scores = scores[-min(5, len(scores)):]  # 最近5周
        historical_scores = scores[:-min(5, len(scores))] if len(scores) > 5 else scores
        
        recent_average = np.mean(recent_scores)
        historical_average = np.mean(historical_scores) if historical_scores else recent_average
        volatility = np.std(scores) if len(scores) > 1 else 0.0
        
        # 确定趋势方向
        if slope > 0.01:
            trend = "improving"
        elif slope < -0.01:
            trend = "declining"
        else:
            trend = "stable"
        
        return {
            "trend": trend,
            "slope": slope,
            "r_squared": r_squared,
            "recent_average": recent_average,
            "historical_average": historical_average,
            "volatility": volatility,
            "data_points": len(scores),
            "score_range": {
                "min": min(scores),
                "max": max(scores),
                "latest": scores[-1] if scores else 0.0
            }
        }
    
    def get_cross_agent_comparison(self, weeks: int = 10) -> Dict[str, Any]:
        """
        获取跨智能体的性能比较
        
        参数:
            weeks: 比较的历史周数
            
        返回:
            比较结果
        """
        self._refresh_shapley_cache()
        
        agent_stats = {}
        
        # 计算每个智能体的统计信息
        for agent_id in self._shapley_cache.keys():
            score_history = self.get_agent_score_history(agent_id, weeks)
            scores = [data["score"] for data in score_history]
            
            if scores:
                agent_stats[agent_id] = {
                    "average_score": np.mean(scores),
                    "max_score": max(scores),
                    "min_score": min(scores),
                    "volatility": np.std(scores),
                    "data_points": len(scores),
                    "latest_score": scores[0] if scores else 0.0,  # 最新的在前面
                    "trend": self.get_score_trends(agent_id, weeks)["trend"]
                }
        
        # 排序智能体
        if agent_stats:
            sorted_agents = sorted(
                agent_stats.items(),
                key=lambda x: x[1]["average_score"],
                reverse=True
            )
            
            best_agent = sorted_agents[0]
            worst_agent = sorted_agents[-1]
            
            return {
                "agent_stats": agent_stats,
                "ranking": [agent for agent, _ in sorted_agents],
                "best_performer": {
                    "agent_id": best_agent[0],
                    "average_score": best_agent[1]["average_score"]
                },
                "worst_performer": {
                    "agent_id": worst_agent[0],
                    "average_score": worst_agent[1]["average_score"]
                },
                "overall_stats": {
                    "total_agents": len(agent_stats),
                    "average_score_across_agents": np.mean([stats["average_score"] for stats in agent_stats.values()]),
                    "score_dispersion": np.std([stats["average_score"] for stats in agent_stats.values()])
                }
            }
        
        return {"agent_stats": {}, "ranking": [], "overall_stats": {}}
    
    def get_optimization_effectiveness(self) -> Dict[str, Any]:
        """
        分析优化效果
        
        返回:
            优化效果分析结果
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取所有有实际得分的优化记录
                cursor.execute("""
                    SELECT agent_id, shapley_score, optimization_date, evaluation_date
                    FROM optimization_history 
                    WHERE shapley_score IS NOT NULL
                    ORDER BY agent_id, optimization_date
                """)
                
                rows = cursor.fetchall()
                
                if not rows:
                    return {"effectiveness": "no_data", "improvements": {}}
                
                # 按智能体分组分析
                agent_improvements = defaultdict(list)
                for agent_id, score, opt_date, eval_date in rows:
                    agent_improvements[agent_id].append({
                        "score": score,
                        "optimization_date": opt_date,
                        "evaluation_date": eval_date
                    })
                
                # 分析每个智能体的改进情况
                effectiveness_stats = {}
                for agent_id, improvements in agent_improvements.items():
                    if len(improvements) < 2:
                        continue
                    
                    scores = [imp["score"] for imp in improvements]
                    
                    # 计算改进趋势
                    first_score = scores[0]
                    latest_score = scores[-1]
                    improvement = latest_score - first_score
                    improvement_pct = (improvement / abs(first_score)) * 100 if first_score != 0 else 0
                    
                    effectiveness_stats[agent_id] = {
                        "total_optimizations": len(improvements),
                        "first_score": first_score,
                        "latest_score": latest_score,
                        "absolute_improvement": improvement,
                        "percentage_improvement": improvement_pct,
                        "best_score": max(scores),
                        "worst_score": min(scores),
                        "average_score": np.mean(scores)
                    }
                
                # 计算总体效果
                if effectiveness_stats:
                    total_improvements = sum(stats["absolute_improvement"] for stats in effectiveness_stats.values())
                    positive_improvements = sum(1 for stats in effectiveness_stats.values() if stats["absolute_improvement"] > 0)
                    
                    overall_effectiveness = {
                        "total_agents_optimized": len(effectiveness_stats),
                        "agents_with_positive_improvement": positive_improvements,
                        "improvement_success_rate": positive_improvements / len(effectiveness_stats) * 100,
                        "total_absolute_improvement": total_improvements,
                        "average_improvement_per_agent": total_improvements / len(effectiveness_stats)
                    }
                else:
                    overall_effectiveness = {}
                
                return {
                    "effectiveness": "analyzed",
                    "agent_improvements": effectiveness_stats,
                    "overall_effectiveness": overall_effectiveness
                }
                
        except Exception as e:
            self.logger.error(f"分析优化效果失败: {e}")
            return {"effectiveness": "error", "error": str(e)}
    
    def _hash_prompt(self, prompt: str) -> str:
        """生成提示词哈希值"""
        return hashlib.md5(prompt.encode('utf-8')).hexdigest()
    
    def cleanup_old_data(self, days_to_keep: int = 90):
        """
        清理旧数据
        
        参数:
            days_to_keep: 保留的天数
        """
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        cutoff_str = cutoff_date.isoformat()
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 清理旧的优化历史（保留活跃的）
                cursor.execute("""
                    DELETE FROM optimization_history 
                    WHERE optimization_date < ? AND is_active = FALSE
                """, (cutoff_str,))
                
                optimization_deleted = cursor.rowcount
                
                # 清理旧的周期性得分
                cursor.execute("""
                    DELETE FROM weekly_scores 
                    WHERE created_at < ?
                """, (cutoff_str,))
                
                weekly_deleted = cursor.rowcount
                conn.commit()
                
                self.logger.info(f"数据清理完成: 删除 {optimization_deleted} 条优化记录, "
                               f"{weekly_deleted} 条周期性得分记录")
                
        except Exception as e:
            self.logger.error(f"清理旧数据失败: {e}")
    
    def export_data(self, output_path: str) -> bool:
        """
        导出数据到JSON文件
        
        参数:
            output_path: 输出文件路径
            
        返回:
            是否导出成功
        """
        try:
            export_data = {
                "export_timestamp": datetime.now().isoformat(),
                "shapley_cache": dict(self._shapley_cache),
                "optimization_history": self._export_optimization_history(),
                "weekly_scores": self._export_weekly_scores()
            }
            
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"数据导出成功: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"数据导出失败: {e}")
            return False
    
    def _export_optimization_history(self) -> List[Dict]:
        """导出优化历史"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT agent_id, prompt_hash, prompt_text, shapley_score,
                           optimization_date, evaluation_date, is_active, metadata
                    FROM optimization_history
                    ORDER BY optimization_date DESC
                """)
                
                rows = cursor.fetchall()
                
                return [{
                    "agent_id": row[0],
                    "prompt_hash": row[1],
                    "prompt_text": row[2],
                    "shapley_score": row[3],
                    "optimization_date": row[4],
                    "evaluation_date": row[5],
                    "is_active": bool(row[6]),
                    "metadata": json.loads(row[7]) if row[7] else None
                } for row in rows]
                
        except Exception as e:
            self.logger.error(f"导出优化历史失败: {e}")
            return []
    
    def _export_weekly_scores(self) -> List[Dict]:
        """导出周期性得分"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT agent_id, week_number, shapley_score, trading_days, 
                           result_file, created_at
                    FROM weekly_scores
                    ORDER BY created_at DESC
                """)
                
                rows = cursor.fetchall()
                
                return [{
                    "agent_id": row[0],
                    "week_number": row[1],
                    "shapley_score": row[2],
                    "trading_days": row[3],
                    "result_file": row[4],
                    "created_at": row[5]
                } for row in rows]
                
        except Exception as e:
            self.logger.error(f"导出周期性得分失败: {e}")
            return []
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """获取汇总统计信息"""
        self._refresh_shapley_cache()
        
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 统计优化历史
                cursor.execute("SELECT COUNT(*) FROM optimization_history")
                total_optimizations = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(DISTINCT agent_id) FROM optimization_history")
                agents_optimized = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM optimization_history WHERE is_active = TRUE")
                active_prompts = cursor.fetchone()[0]
                
                cursor.execute("SELECT COUNT(*) FROM weekly_scores")
                total_weekly_scores = cursor.fetchone()[0]
                
                # 统计缓存数据
                cached_agents = len(self._shapley_cache)
                total_cached_records = sum(len(records) for records in self._shapley_cache.values())
                
                return {
                    "database_stats": {
                        "total_optimizations": total_optimizations,
                        "agents_optimized": agents_optimized,
                        "active_prompts": active_prompts,
                        "total_weekly_scores": total_weekly_scores
                    },
                    "cache_stats": {
                        "cached_agents": cached_agents,
                        "total_cached_records": total_cached_records,
                        "cache_timestamp": self._cache_timestamp.isoformat() if self._cache_timestamp else None
                    },
                    "system_info": {
                        "results_base_path": self.results_base_path,
                        "db_path": self.db_path,
                        "cache_ttl": self._cache_ttl
                    }
                }
                
        except Exception as e:
            self.logger.error(f"获取汇总统计失败: {e}")
            return {"error": str(e)}
    
    # ==================== 周期性在线优化支持方法 ====================
    
    def write_weekly_shapley_values(self, 
                                   week_number: int,
                                   shapley_values: Dict[str, float],
                                   trading_days: str,
                                   simulation_date: str = None,
                                   additional_metrics: Optional[Dict[str, Any]] = None) -> bool:
        """
        实时写入周期性Shapley值
        
        这个方法支持在模拟过程中实时写入每周的Shapley值，
        而不需要等待整个模拟完成。
        
        参数:
            week_number: 周数
            shapley_values: Shapley值字典 {agent_id: score}
            trading_days: 交易日信息
            simulation_date: 模拟日期（可选）
            additional_metrics: 额外的性能指标（可选）
            
        返回:
            是否写入成功
        """
        try:
            current_time = datetime.now()
            simulation_date = simulation_date or current_time.isoformat()
            
            # 生成文件名
            file_name = f"weekly_shapley_{week_number:03d}_{current_time.strftime('%Y%m%d_%H%M%S')}.json"
            
            # 存储到数据库
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                for agent_id, score in shapley_values.items():
                    cursor.execute("""
                        INSERT OR REPLACE INTO weekly_scores 
                        (agent_id, week_number, shapley_score, trading_days, result_file)
                        VALUES (?, ?, ?, ?, ?)
                    """, (agent_id, week_number, float(score), trading_days, file_name))
                
                conn.commit()
            
            # 更新缓存
            for agent_id, score in shapley_values.items():
                if agent_id not in self._shapley_cache:
                    self._shapley_cache[agent_id] = []
                
                # 添加到缓存（确保按时间排序）
                cache_entry = {
                    "score": float(score),
                    "week": week_number,
                    "trading_days": trading_days,
                    "file_path": file_name,
                    "file_datetime": current_time,
                    "timestamp": current_time.isoformat(),
                    "simulation_date": simulation_date
                }
                
                # 插入到正确位置以保持时间排序
                self._shapley_cache[agent_id].insert(0, cache_entry)
                
                # 限制缓存大小
                if len(self._shapley_cache[agent_id]) > 50:
                    self._shapley_cache[agent_id] = self._shapley_cache[agent_id][:50]
            
            # 可选：写入到文件系统
            if additional_metrics:
                self._write_weekly_metrics_file(week_number, shapley_values, 
                                              trading_days, additional_metrics)
            
            self.logger.info(f"✅ 第 {week_number} 周 Shapley 值已写入: {len(shapley_values)} 个智能体")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 写入第 {week_number} 周 Shapley 值失败: {e}")
            return False
    
    def write_optimization_result_realtime(self, 
                                         agent_id: str,
                                         week_number: int,
                                         original_prompt: str,
                                         optimized_prompt: str,
                                         original_score: float,
                                         estimated_improvement: float,
                                         optimization_metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        实时写入优化结果
        
        在周期性优化过程中立即写入优化结果，支持实时跟踪优化进度。
        
        参数:
            agent_id: 智能体ID
            week_number: 周数
            original_prompt: 原始提示词
            optimized_prompt: 优化后的提示词
            original_score: 原始得分
            estimated_improvement: 估计改进幅度
            optimization_metadata: 优化元数据
            
        返回:
            是否写入成功
        """
        try:
            current_time = datetime.now()
            
            # 计算提示词哈希
            prompt_hash = self._hash_prompt(optimized_prompt)
            
            # 准备元数据
            metadata = {
                "week_number": week_number,
                "original_score": original_score,
                "estimated_improvement": estimated_improvement,
                "optimization_type": "weekly_realtime",
                "original_prompt_hash": self._hash_prompt(original_prompt)
            }
            
            if optimization_metadata:
                metadata.update(optimization_metadata)
            
            # 计算预期得分
            estimated_score = original_score + estimated_improvement
            
            # 写入到数据库
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 首先设置该智能体的其他提示词为非活跃状态
                cursor.execute("""
                    UPDATE optimization_history 
                    SET is_active = FALSE 
                    WHERE agent_id = ?
                """, (agent_id,))
                
                # 插入新的优化结果
                cursor.execute("""
                    INSERT INTO optimization_history 
                    (agent_id, prompt_hash, prompt_text, shapley_score, 
                     optimization_date, is_active, metadata)
                    VALUES (?, ?, ?, ?, ?, TRUE, ?)
                """, (agent_id, prompt_hash, optimized_prompt, estimated_score, 
                     current_time.isoformat(), json.dumps(metadata)))
                
                conn.commit()
            
            self.logger.info(f"🔄 智能体 {agent_id} 第 {week_number} 周优化结果已写入")
            self.logger.info(f"    原始得分: {original_score:.6f}, 预期改进: {estimated_improvement:.6f}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 写入智能体 {agent_id} 第 {week_number} 周优化结果失败: {e}")
            return False
    
    def get_current_week_data(self, week_number: int) -> Dict[str, Any]:
        """
        获取指定周的数据
        
        参数:
            week_number: 周数
            
        返回:
            该周的数据字典
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取该周的Shapley值
                cursor.execute("""
                    SELECT agent_id, shapley_score, trading_days, result_file, created_at
                    FROM weekly_scores
                    WHERE week_number = ?
                    ORDER BY created_at DESC
                """, (week_number,))
                
                weekly_scores = cursor.fetchall()
                
                # 获取该周的优化结果
                cursor.execute("""
                    SELECT agent_id, prompt_text, shapley_score, metadata
                    FROM optimization_history
                    WHERE JSON_EXTRACT(metadata, '$.week_number') = ?
                    ORDER BY optimization_date DESC
                """, (week_number,))
                
                optimization_results = cursor.fetchall()
                
                # 组织数据
                shapley_values = {}
                trading_days = ""
                
                for agent_id, score, t_days, result_file, created_at in weekly_scores:
                    shapley_values[agent_id] = score
                    if not trading_days:
                        trading_days = t_days
                
                optimizations = {}
                for agent_id, prompt, score, metadata_str in optimization_results:
                    metadata = {}
                    if metadata_str:
                        try:
                            metadata = json.loads(metadata_str)
                        except json.JSONDecodeError:
                            pass
                    
                    optimizations[agent_id] = {
                        "prompt": prompt,
                        "score": score,
                        "metadata": metadata
                    }
                
                return {
                    "week_number": week_number,
                    "shapley_values": shapley_values,
                    "trading_days": trading_days,
                    "optimizations": optimizations,
                    "has_data": bool(weekly_scores)
                }
                
        except Exception as e:
            self.logger.error(f"获取第 {week_number} 周数据失败: {e}")
            return {
                "week_number": week_number,
                "shapley_values": {},
                "trading_days": "",
                "optimizations": {},
                "has_data": False,
                "error": str(e)
            }
    
    def update_weekly_performance_realtime(self, 
                                         agent_id: str,
                                         week_number: int,
                                         actual_score: float,
                                         performance_metrics: Optional[Dict[str, Any]] = None) -> bool:
        """
        实时更新周期性性能指标
        
        在模拟过程中更新智能体的实际性能表现，用于验证优化效果。
        
        参数:
            agent_id: 智能体ID
            week_number: 周数
            actual_score: 实际得分
            performance_metrics: 性能指标字典
            
        返回:
            是否更新成功
        """
        try:
            current_time = datetime.now()
            
            # 更新数据库中的实际得分
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 更新该周的 Shapley 值
                cursor.execute("""
                    UPDATE weekly_scores 
                    SET shapley_score = ?
                    WHERE agent_id = ? AND week_number = ?
                """, (actual_score, agent_id, week_number))
                
                # 更新优化历史中的实际得分
                cursor.execute("""
                    UPDATE optimization_history 
                    SET shapley_score = ?, evaluation_date = ?
                    WHERE agent_id = ? AND JSON_EXTRACT(metadata, '$.week_number') = ?
                """, (actual_score, current_time.isoformat(), agent_id, week_number))
                
                conn.commit()
            
            # 更新缓存
            if agent_id in self._shapley_cache:
                for cache_entry in self._shapley_cache[agent_id]:
                    if cache_entry.get("week") == week_number:
                        cache_entry["score"] = actual_score
                        cache_entry["last_updated"] = current_time.isoformat()
                        break
            
            self.logger.info(f"📊 智能体 {agent_id} 第 {week_number} 周性能已更新: {actual_score:.6f}")
            
            # 如果提供了性能指标，记录到日志
            if performance_metrics:
                self.logger.info(f"    性能指标: {performance_metrics}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 更新智能体 {agent_id} 第 {week_number} 周性能失败: {e}")
            return False
    
    def _write_weekly_metrics_file(self, 
                                 week_number: int,
                                 shapley_values: Dict[str, float],
                                 trading_days: str,
                                 additional_metrics: Dict[str, Any]):
        """
        写入周期性指标文件
        
        参数:
            week_number: 周数
            shapley_values: Shapley值字典
            trading_days: 交易日信息
            additional_metrics: 额外指标
        """
        try:
            # 确保目录存在
            os.makedirs(self.results_base_path, exist_ok=True)
            
            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            file_name = f"weekly_metrics_{week_number:03d}_{timestamp}.json"
            file_path = os.path.join(self.results_base_path, file_name)
            
            # 准备数据
            data = {
                "week_number": week_number,
                "timestamp": datetime.now().isoformat(),
                "trading_days": trading_days,
                "shapley_values": shapley_values,
                "additional_metrics": additional_metrics,
                "data_type": "weekly_realtime"
            }
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug(f"周期性指标文件已写入: {file_path}")
            
        except Exception as e:
            self.logger.error(f"写入周期性指标文件失败: {e}")
    
    def get_weekly_optimization_summary(self, weeks: int = 10) -> Dict[str, Any]:
        """
        获取周期性优化摘要
        
        参数:
            weeks: 要分析的周数
            
        返回:
            优化摘要字典
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取最近几周的数据
                cursor.execute("""
                    SELECT week_number, agent_id, shapley_score
                    FROM weekly_scores
                    WHERE week_number >= (
                        SELECT MAX(week_number) - ? + 1 FROM weekly_scores
                    )
                    ORDER BY week_number DESC, agent_id
                """, (weeks,))
                
                weekly_data = cursor.fetchall()
                
                # 获取优化统计
                cursor.execute("""
                    SELECT 
                        COUNT(*) as total_optimizations,
                        COUNT(DISTINCT agent_id) as agents_optimized,
                        AVG(JSON_EXTRACT(metadata, '$.estimated_improvement')) as avg_improvement
                    FROM optimization_history
                    WHERE JSON_EXTRACT(metadata, '$.week_number') >= (
                        SELECT MAX(week_number) - ? + 1 FROM weekly_scores
                    )
                """, (weeks,))
                
                optimization_stats = cursor.fetchone()
                
                # 组织数据
                weekly_summary = defaultdict(lambda: defaultdict(float))
                agent_performance = defaultdict(list)
                
                for week_num, agent_id, score in weekly_data:
                    weekly_summary[week_num][agent_id] = score
                    agent_performance[agent_id].append({
                        "week": week_num,
                        "score": score
                    })
                
                # 计算趋势
                agent_trends = {}
                for agent_id, scores in agent_performance.items():
                    if len(scores) >= 2:
                        score_values = [s["score"] for s in scores]
                        agent_trends[agent_id] = {
                            "latest_score": score_values[0],
                            "earliest_score": score_values[-1],
                            "improvement": score_values[0] - score_values[-1],
                            "average_score": np.mean(score_values),
                            "volatility": np.std(score_values) if len(score_values) > 1 else 0.0
                        }
                
                return {
                    "summary_period": f"最近 {weeks} 周",
                    "weekly_data": dict(weekly_summary),
                    "agent_trends": agent_trends,
                    "optimization_stats": {
                        "total_optimizations": optimization_stats[0] if optimization_stats else 0,
                        "agents_optimized": optimization_stats[1] if optimization_stats else 0,
                        "average_improvement": optimization_stats[2] if optimization_stats and optimization_stats[2] else 0.0
                    },
                    "total_weeks_analyzed": len(weekly_summary)
                }
                
        except Exception as e:
            self.logger.error(f"获取周期性优化摘要失败: {e}")
            return {"error": str(e)}
    
    def flush_realtime_data(self):
        """
        刷新实时数据
        
        强制更新缓存，确保最新的数据被读取。
        在周期性优化过程中可能需要调用此方法。
        """
        try:
            # 强制刷新缓存
            self._cache_timestamp = None
            self._refresh_shapley_cache()
            
            self.logger.info("实时数据已刷新")
            
        except Exception as e:
            self.logger.error(f"刷新实时数据失败: {e}")
    
    def get_realtime_agent_status(self, agent_id: str) -> Dict[str, Any]:
        """
        获取智能体实时状态
        
        参数:
            agent_id: 智能体ID
            
        返回:
            智能体状态字典
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取最新的Shapley值
                cursor.execute("""
                    SELECT week_number, shapley_score, created_at
                    FROM weekly_scores
                    WHERE agent_id = ?
                    ORDER BY week_number DESC
                    LIMIT 1
                """, (agent_id,))
                
                latest_score = cursor.fetchone()
                
                # 获取活跃提示词
                cursor.execute("""
                    SELECT prompt_text, shapley_score, optimization_date, metadata
                    FROM optimization_history
                    WHERE agent_id = ? AND is_active = TRUE
                    ORDER BY optimization_date DESC
                    LIMIT 1
                """, (agent_id,))
                
                active_prompt = cursor.fetchone()
                
                # 获取优化统计
                cursor.execute("""
                    SELECT COUNT(*) as total_optimizations,
                           AVG(JSON_EXTRACT(metadata, '$.estimated_improvement')) as avg_improvement
                    FROM optimization_history
                    WHERE agent_id = ?
                """, (agent_id,))
                
                optimization_stats = cursor.fetchone()
                
                status = {
                    "agent_id": agent_id,
                    "latest_performance": {},
                    "active_prompt": {},
                    "optimization_stats": {},
                    "status": "active" if latest_score else "inactive"
                }
                
                if latest_score:
                    status["latest_performance"] = {
                        "week_number": latest_score[0],
                        "shapley_score": latest_score[1],
                        "last_updated": latest_score[2]
                    }
                
                if active_prompt:
                    metadata = {}
                    if active_prompt[3]:
                        try:
                            metadata = json.loads(active_prompt[3])
                        except json.JSONDecodeError:
                            pass
                    
                    status["active_prompt"] = {
                        "prompt_text": active_prompt[0][:100] + "..." if len(active_prompt[0]) > 100 else active_prompt[0],
                        "estimated_score": active_prompt[1],
                        "optimization_date": active_prompt[2],
                        "metadata": metadata
                    }
                
                if optimization_stats:
                    status["optimization_stats"] = {
                        "total_optimizations": optimization_stats[0],
                        "average_improvement": optimization_stats[1] if optimization_stats[1] else 0.0
                    }
                
                return status
                
        except Exception as e:
            self.logger.error(f"获取智能体 {agent_id} 实时状态失败: {e}")
            return {
                "agent_id": agent_id,
                "status": "error",
                "error": str(e)
            }