"""
LLM 接口模块 (LLM Interface Module)

本模块提供一个通用的LLM接口，用于与不同的大语言模型提供商进行交互。
它抽象了不同SDK的细节，提供一个统一的 `analyze` 方法。

主要功能：
1. 支持多种LLM提供商（当前实现zhipuai，可扩展至OpenAI等）
2. 通过环境变量安全地管理API密钥
3. 提供统一的分析接口
"""

import os
import json
import logging
import random
import time
from typing import Optional, Dict, Any

# 动态导入，避免在未使用时强制要求安装
try:
    import zhipuai
except ImportError:
    zhipuai = None

try:
    import openai
except ImportError:
    openai = None

try:
    import requests
except ImportError:
    requests = None


class LLMInterface:
    """
    通用LLM接口类

    根据指定的提供商，初始化并封装LLM客户端。
    支持真实的LLM提供商（zhipuai, openai）和虚拟提供商（mock）用于测试。
    """

    def __init__(self, provider: Optional[str] = None, logger: Optional[logging.Logger] = None):
        """
        初始化LLM接口

        参数:
            provider: LLM提供商名称 (例如 "zhipuai", "openai", "lmstudio", "mock")
            logger: 日志记录器
        """
        self.logger = logger or self._create_default_logger()
        self.provider = provider
        self.client = None
        self.disable_concurrent = False  # LM Studio禁用并发标志

        if self.provider:
            self.provider = self.provider.lower()
            # LM Studio 不支持高并发，禁用并发执行
            if self.provider == "lmstudio":
                self.disable_concurrent = True
                self.logger.info("⚠️  LM Studio 模式: 已禁用并发执行以保护本地服务器")
            self._initialize_client()

    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.LLMInterface")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def _initialize_client(self) -> None:
        """根据提供商初始化LLM客户端"""
        # 简化初始化日志
        # self.logger.info(f"尝试初始化LLM提供商: {self.provider}")

        if self.provider == "zhipuai":
            if zhipuai is None:
                self.logger.warning("ZhipuAI SDK (zhipuai) 未安装。请运行 'pip install zhipuai'")
                return

            api_key = os.environ.get("ZHIPUAI_API_KEY")
            if not api_key:
                self.logger.warning("未找到 ZHIPUAI_API_KEY 环境变量。")
                return

            try:
                self.client = zhipuai.ZhipuAI(api_key=api_key)
                # 简化成功日志
                # self.logger.info("ZhipuAI 客户端初始化成功")
                pass
            except Exception as e:
                self.logger.error(f"初始化 ZhipuAI 客户端失败: {e}")

        elif self.provider == "openai":
            if openai is None:
                self.logger.warning("OpenAI SDK (openai) 未安装。请运行 'pip install openai'")
                return

            api_key = os.environ.get("OPENAI_API_KEY")
            if not api_key:
                self.logger.warning("未找到 OPENAI_API_KEY 环境变量。")
                return

            try:
                self.client = openai.OpenAI(api_key=api_key)
                # 简化成功日志
                # self.logger.info("OpenAI 客户端初始化成功")
                pass
            except Exception as e:
                self.logger.error(f"初始化 OpenAI 客户端失败: {e}")

        elif self.provider == "lmstudio":
            if requests is None:
                self.logger.warning("requests 库未安装。请运行 'pip install requests'")
                return

            try:
                # 测试LM Studio连接
                response = requests.get("http://127.0.0.1:1234/v1/models", timeout=5)
                if response.status_code == 200:
                    self.client = "lmstudio_client"  # 标识符，实际使用requests
                    self.logger.info("🏠 LM Studio 客户端初始化成功 (本地服务)")
                else:
                    self.logger.error(f"LM Studio 服务不可用，状态码: {response.status_code}")
            except Exception as e:
                self.logger.error(f"连接 LM Studio 失败: {e}")

        elif self.provider == "mock":
            # 虚拟LLM提供商，用于快速测试
            self.client = MockLLMClient(logger=self.logger)
            self.logger.info("🎭 虚拟LLM客户端初始化成功 (用于测试)")

        else:
            self.logger.error(f"不支持的LLM提供商: {self.provider}")

    def analyze(self, prompt: str, model: str, **kwargs) -> Optional[Dict[str, Any]]:
        """
        使用指定的模型进行分析
        
        参数:
            prompt: 发送给模型的提示
            model: 要使用的模型名称 (例如 "glm-4-flash")
            **kwargs: 传递给LLM API的其他参数
            
        返回:
            LLM的分析结果字典，如果失败则返回None
        """
        if not self.client:
            self.logger.error("LLM客户端未初始化，无法执行分析")
            return None
            
        try:
            # self.logger.debug(f"向模型 {model} 发送请求...")

            if self.provider == "zhipuai":
                response = self.client.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": prompt}],
                    **kwargs
                )
                # 假设返回的是JSON字符串
                content = response.choices[0].message.content
                return self._parse_json_response(content)

            elif self.provider == "openai":
                response = self.client.chat.completions.create(
                    model=model,
                    messages=[{"role": "user", "content": prompt}],
                    **kwargs
                )
                content = response.choices[0].message.content
                return self._parse_json_response(content)

            elif self.provider == "lmstudio":
                # 使用LM Studio API (OpenAI兼容)
                payload = {
                    "model": model,
                    "messages": [{"role": "user", "content": prompt}],
                    "temperature": kwargs.get("temperature", 0.0),
                    "max_tokens": kwargs.get("max_tokens", 1000),
                    "top_p": kwargs.get("top_p", 0.5)
                }
                
                response = requests.post(
                    "http://127.0.0.1:1234/v1/chat/completions",
                    json=payload,
                    timeout=kwargs.get("timeout", 30),
                    headers={"Content-Type": "application/json"}
                )
                
                if response.status_code == 200:
                    result = response.json()
                    content = result["choices"][0]["message"]["content"]
                    return self._parse_json_response(content)
                else:
                    self.logger.error(f"LM Studio API 请求失败: {response.status_code}")
                    return None

            elif self.provider == "mock":
                # 使用虚拟客户端
                return self.client.analyze(prompt, model, **kwargs)

            else:
                self.logger.error(f"提供商 {self.provider} 的分析逻辑未实现")
                return None

        except Exception as e:
            self.logger.error(f"LLM分析请求失败: {e}")
            return None

    def _parse_json_response(self, content: str) -> Optional[Dict[str, Any]]:
        """
        解析LLM返回的JSON字符串

        参数:
            content: LLM返回的内容字符串

        返回:
            解析后的字典，如果失败则返回包含原始内容的字典
        """
        try:
            # 移除代码块标记
            cleaned_content = content.strip().replace("```json", "").replace("```", "").strip()
            return json.loads(cleaned_content)
        except json.JSONDecodeError as e:
            # self.logger.debug(f"LLM响应不是JSON格式，返回文本内容: {e}")
            # 如果不是JSON格式，返回包含原始文本的字典
            return {"content": content, "type": "text_response"}


class MockLLMClient:
    """
    虚拟LLM客户端，用于快速测试

    提供预定义的响应，模拟真实LLM的行为，无需API调用
    """

    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化虚拟LLM客户端

        参数:
            logger: 日志记录器
        """
        self.logger = logger or logging.getLogger(__name__)

        # 预定义的智能体响应模板
        self.agent_responses = {
            "NAA": {  # 新闻分析师
                "sentiment": "neutral",
                "confidence": 0.7,
                "key_factors": ["市场波动", "政策影响", "行业趋势"],
                "recommendation": "持有",
                "reasoning": "基于当前新闻分析，市场情绪相对稳定"
            },
            "TAA": {  # 技术分析师
                "trend": "upward",
                "support_level": 150.0,
                "resistance_level": 180.0,
                "indicators": {
                    "rsi": 65.0,
                    "macd": "bullish",
                    "moving_average": "above"
                },
                "recommendation": "买入",
                "reasoning": "技术指标显示上升趋势"
            },
            "FAA": {  # 基本面分析师
                "valuation": "fair",
                "pe_ratio": 25.5,
                "growth_rate": 0.12,
                "financial_health": "good",
                "recommendation": "买入",
                "reasoning": "基本面数据支持当前估值"
            },
            "BOA": {  # 看涨观点
                "outlook": "bullish",
                "confidence": 0.8,
                "time_horizon": "3-6个月",
                "key_drivers": ["业绩增长", "市场扩张", "技术创新"],
                "recommendation": "强烈买入",
                "reasoning": "多重积极因素支持看涨观点"
            },
            "BeOA": {  # 看跌观点
                "outlook": "bearish",
                "confidence": 0.6,
                "time_horizon": "1-3个月",
                "risk_factors": ["市场不确定性", "竞争加剧", "监管风险"],
                "recommendation": "卖出",
                "reasoning": "短期风险因素较多"
            },
            "NOA": {  # 中性观察者
                "outlook": "neutral",
                "confidence": 0.75,
                "balanced_view": "市场存在机会和风险",
                "recommendation": "持有",
                "reasoning": "综合考虑各种因素，建议保持中性立场"
            },
            "TRA": {  # 交易员
                "action": "hold",
                "stop_loss": 145.0,
                "take_profit": 185.0,
                "reasoning": "基于风险管理原则的交易决策"
            }
        }

        # 通用响应模板
        self.generic_responses = [
            {
                "analysis": "市场分析显示当前处于震荡阶段",
                "recommendation": "建议谨慎操作",
                "confidence": 0.7,
                "reasoning": "基于多维度分析得出的结论"
            },
            {
                "analysis": "技术指标显示积极信号",
                "recommendation": "可以考虑适度买入",
                "confidence": 0.8,
                "reasoning": "多个技术指标支持上涨趋势"
            },
            {
                "analysis": "基本面数据表现良好",
                "recommendation": "长期持有",
                "confidence": 0.75,
                "reasoning": "公司基本面健康，适合长期投资"
            }
        ]

    def analyze(self, prompt: str, model: str, **kwargs) -> Dict[str, Any]:
        """
        模拟LLM分析

        参数:
            prompt: 分析提示
            model: 模型名称（虚拟客户端中忽略）
            **kwargs: 其他参数

        返回:
            模拟的分析结果
        """
        # 模拟API调用延迟
        time.sleep(random.uniform(0.1, 0.5))

        # 所有mock响应都返回TRA格式的买入/卖出决策
        actions = ["buy", "sell"]
        action = random.choice(actions)
        decision_text = "买入" if action == "buy" else "卖出"
        
        response = {
            "analysis": f"Mock模式测试，**最终决策：{decision_text}**",
            "action": action
        }

        return response

    def _identify_agent_type(self, prompt: str) -> Optional[str]:
        """
        从提示中识别智能体类型

        参数:
            prompt: 分析提示

        返回:
            识别出的智能体类型，如果无法识别则返回None
        """
        prompt_lower = prompt.lower()

        # 关键词映射
        keywords = {
            "NAA": ["新闻", "news", "sentiment", "情绪", "舆情"],
            "TAA": ["技术", "technical", "图表", "指标", "趋势", "trend"],
            "FAA": ["基本面", "fundamental", "财务", "估值", "pe", "增长"],
            "BOA": ["看涨", "bullish", "乐观", "上涨", "买入"],
            "BeOA": ["看跌", "bearish", "悲观", "下跌", "卖出"],
            "NOA": ["中性", "neutral", "观察", "平衡"],
            "TRA": ["交易", "trading", "仓位", "止损", "止盈"]
        }

        for agent_type, agent_keywords in keywords.items():
            if any(keyword in prompt_lower for keyword in agent_keywords):
                return agent_type

        return None

    def _generate_random_tra_response(self) -> Dict[str, Any]:
        """
        生成TRA交易决策响应（mock模式）
        
        随机返回买入或卖出决策，用于测试系统交易功能
        只包含核心字段：analysis和action
        
        Returns:
            随机的交易决策响应字典
        """
        # 简单的随机决策：50%买入，50%卖出
        actions = ["buy", "sell"]
        action = random.choice(actions)
        
        # 根据action生成对应的最终决策声明
        decision_text = "买入" if action == "buy" else "卖出"
        
        response = {
            "analysis": f"Mock模式随机测试，**最终决策：{decision_text}**",
            "action": action
        }
        
        return response
