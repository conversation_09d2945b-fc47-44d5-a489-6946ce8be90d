"""
简单的并行LLM调用管理器

专注单一职责：管理同层智能体的并行LLM调用
"""

import logging
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime


class ParallelLLMManager:
    """
    简单的并行LLM调用管理器
    
    单一职责：将同层智能体的LLM调用并行化
    """
    
    def __init__(self, max_concurrent: int = 3, timeout: float = 120.0):
        self.max_concurrent = max_concurrent
        self.timeout = timeout
        self.executor = ThreadPoolExecutor(max_workers=max_concurrent)
    
    def call_agents_parallel(self, agents_with_states: List[Tuple[Any, Dict[str, Any]]], **kwargs) -> Dict[str, Dict[str, Any]]:
        """
        并行调用智能体
        
        Args:
            agents_with_states: [(智能体, 状态数据), ...]
            
        Returns:
            {agent_id: 调用结果, ...}
        """
        if not agents_with_states:
            return {}
        
        # 提交所有任务
        futures = {}
        for agent, state in agents_with_states:
            future = self.executor.submit(agent.process, state, **kwargs)
            futures[future] = agent.agent_id
        
        # 收集结果
        results = {}
        try:
            for future in as_completed(futures, timeout=self.timeout):
                agent_id = futures[future]
                try:
                    results[agent_id] = future.result()
                except Exception as e:
                    results[agent_id] = {
                        "agent_id": agent_id,
                        "error": str(e),
                        "success": False,
                        "timestamp": datetime.now().isoformat()
                    }
        except TimeoutError:
            # 处理超时情况：为未完成的任务创建默认结果
            for future, agent_id in futures.items():
                if agent_id not in results:
                    # 尝试取消未完成的任务
                    future.cancel()
                    results[agent_id] = {
                        "agent_id": agent_id,
                        "error": f"调用超时({self.timeout}s)",
                        "success": False,
                        "timestamp": datetime.now().isoformat()
                    }
        
        return results
    
    def shutdown(self):
        """关闭线程池"""
        self.executor.shutdown(wait=True)