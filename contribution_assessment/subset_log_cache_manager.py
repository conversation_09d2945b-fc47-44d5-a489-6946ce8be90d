"""
子集日志缓存管理器 (Subset Log Cache Manager)

本模块实现了针对智能体子集（联盟）的日志缓存机制，解决并发执行时日志条目交错的问题。
主要功能：
1. 为每个子集维护独立的日志缓存
2. 在子集执行完成后批量写入主日志
3. 保持日志条目的时间顺序和完整性
4. 支持线程安全的并发操作
"""

import threading
import time
import logging
from typing import Dict, List, Optional, Any, Set
from dataclasses import dataclass
from datetime import datetime
from collections import defaultdict, deque
import json
import os
from pathlib import Path


@dataclass
class LogEntry:
    """日志条目数据结构"""
    timestamp: datetime
    level: str
    logger_name: str
    message: str
    subset_id: str
    thread_id: int
    
    def to_log_line(self) -> str:
        """转换为标准日志行格式"""
        return f"{self.timestamp.strftime('%Y-%m-%d %H:%M:%S,%f')[:-3]} - {self.logger_name} - {self.level} - {self.message}"


class SubsetLogBuffer:
    """单个子集的日志缓冲区"""
    
    def __init__(self, subset_id: str, max_buffer_size: int = 1000):
        self.subset_id = subset_id
        self.max_buffer_size = max_buffer_size
        self.buffer: deque = deque(maxlen=max_buffer_size)
        self.lock = threading.Lock()
        self.start_time = time.time()
        self.end_time = None
        self.is_active = True
        
    def add_log_entry(self, entry: LogEntry):
        """添加日志条目到缓冲区"""
        with self.lock:
            if self.is_active:
                self.buffer.append(entry)
    
    def finalize(self):
        """完成缓冲区，设置结束时间"""
        with self.lock:
            self.end_time = time.time()
            self.is_active = False
    
    def get_sorted_entries(self) -> List[LogEntry]:
        """获取按时间排序的日志条目"""
        with self.lock:
            return sorted(list(self.buffer), key=lambda entry: entry.timestamp)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓冲区统计信息"""
        with self.lock:
            return {
                "subset_id": self.subset_id,
                "entry_count": len(self.buffer),
                "start_time": self.start_time,
                "end_time": self.end_time,
                "duration": (self.end_time - self.start_time) if self.end_time else None,
                "is_active": self.is_active
            }


class SubsetLogCacheManager:
    """子集日志缓存管理器"""
    
    def __init__(self, 
                 main_logger: logging.Logger,
                 output_file: Optional[str] = None,
                 enable_file_output: bool = True,
                 max_buffer_size: int = 1000):
        """
        初始化子集日志缓存管理器
        
        参数:
            main_logger: 主日志记录器
            output_file: 输出文件路径
            enable_file_output: 是否启用文件输出
            max_buffer_size: 每个子集的最大缓冲区大小
        """
        self.main_logger = main_logger
        self.output_file = output_file
        self.enable_file_output = enable_file_output
        self.max_buffer_size = max_buffer_size
        
        # 子集缓冲区管理
        self.subset_buffers: Dict[str, SubsetLogBuffer] = {}
        self.global_lock = threading.Lock()
        
        # 统计信息
        self.stats = {
            "total_subsets": 0,
            "active_subsets": 0,
            "completed_subsets": 0,
            "total_log_entries": 0,
            "batches_written": 0
        }
        
        # 线程本地存储，用于跟踪当前子集
        self.thread_local = threading.local()
        
        # 输出文件初始化
        if self.enable_file_output and self.output_file:
            self._ensure_output_directory()
    
    def _ensure_output_directory(self):
        """确保输出目录存在"""
        if self.output_file:
            Path(self.output_file).parent.mkdir(parents=True, exist_ok=True)
    
    def create_subset_logger(self, subset_id: str) -> logging.Logger:
        """为指定子集创建缓存日志记录器"""
        with self.global_lock:
            if subset_id not in self.subset_buffers:
                self.subset_buffers[subset_id] = SubsetLogBuffer(subset_id, self.max_buffer_size)
                self.stats["total_subsets"] += 1
                self.stats["active_subsets"] += 1
        
        # 创建自定义日志记录器
        logger = logging.getLogger(f"subset_{subset_id}")
        logger.setLevel(self.main_logger.level)
        
        # 移除所有现有的处理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # 添加缓存处理器
        cache_handler = SubsetLogCacheHandler(self, subset_id)
        logger.addHandler(cache_handler)
        logger.propagate = False  # 防止传播到父记录器
        
        return logger
    
    def finalize_subset(self, subset_id: str):
        """完成子集执行，触发日志批量写入"""
        with self.global_lock:
            if subset_id in self.subset_buffers:
                buffer = self.subset_buffers[subset_id]
                buffer.finalize()
                
                # 更新统计信息
                self.stats["active_subsets"] -= 1
                self.stats["completed_subsets"] += 1
                
                # 批量写入日志
                self._write_subset_logs(subset_id)
    
    def _write_subset_logs(self, subset_id: str):
        """批量写入子集日志到主日志"""
        if subset_id not in self.subset_buffers:
            return
        
        buffer = self.subset_buffers[subset_id]
        sorted_entries = buffer.get_sorted_entries()
        
        if not sorted_entries:
            return
        
        # 写入到主日志记录器
        for entry in sorted_entries:
            # 使用原始级别和消息写入主日志
            log_method = getattr(self.main_logger, entry.level.lower(), self.main_logger.info)
            log_method(f"[{entry.subset_id}] {entry.message}")
        
        # 写入到文件（如果启用）
        if self.enable_file_output and self.output_file:
            self._write_to_file(sorted_entries)
        
        # 更新统计信息
        self.stats["total_log_entries"] += len(sorted_entries)
        self.stats["batches_written"] += 1
        
        # 记录批量写入信息
        buffer_stats = buffer.get_stats()
        self.main_logger.info(f"📝 子集 {subset_id} 日志批量写入完成: "
                             f"{len(sorted_entries)} 条日志，"
                             f"耗时 {buffer_stats['duration']:.2f}s")
    
    def _write_to_file(self, entries: List[LogEntry]):
        """写入日志条目到文件"""
        try:
            with open(self.output_file, 'a', encoding='utf-8') as f:
                for entry in entries:
                    f.write(f"{entry.to_log_line()}\n")
        except Exception as e:
            self.main_logger.error(f"写入日志文件失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存管理器统计信息"""
        with self.global_lock:
            return {
                **self.stats,
                "active_subset_details": {
                    subset_id: buffer.get_stats() 
                    for subset_id, buffer in self.subset_buffers.items() 
                    if buffer.is_active
                }
            }
    
    def flush_all(self):
        """强制刷新所有未完成的子集日志"""
        with self.global_lock:
            for subset_id, buffer in self.subset_buffers.items():
                if buffer.is_active:
                    buffer.finalize()
                    self._write_subset_logs(subset_id)
    
    def cleanup(self):
        """清理资源"""
        self.flush_all()
        
        # 清理所有子集缓冲区
        with self.global_lock:
            self.subset_buffers.clear()
            self.stats["active_subsets"] = 0
        
        self.main_logger.info("🧹 子集日志缓存管理器清理完成")


class SubsetLogCacheHandler(logging.Handler):
    """自定义日志处理器，将日志条目缓存到子集缓冲区"""
    
    def __init__(self, cache_manager: SubsetLogCacheManager, subset_id: str):
        super().__init__()
        self.cache_manager = cache_manager
        self.subset_id = subset_id
        
    def emit(self, record: logging.LogRecord):
        """处理日志记录，添加到子集缓冲区"""
        try:
            # 创建日志条目
            entry = LogEntry(
                timestamp=datetime.fromtimestamp(record.created),
                level=record.levelname,
                logger_name=record.name,
                message=self.format(record),
                subset_id=self.subset_id,
                thread_id=threading.get_ident()
            )
            
            # 添加到对应的子集缓冲区
            if self.subset_id in self.cache_manager.subset_buffers:
                self.cache_manager.subset_buffers[self.subset_id].add_log_entry(entry)
                
        except Exception as e:
            # 处理错误时回退到主日志记录器
            self.cache_manager.main_logger.error(f"日志缓存处理失败: {e}")


class SubsetLogContextManager:
    """子集日志上下文管理器，用于自动管理子集日志生命周期"""
    
    def __init__(self, cache_manager: SubsetLogCacheManager, subset_id: str):
        self.cache_manager = cache_manager
        self.subset_id = subset_id
        self.logger = None
    
    def __enter__(self) -> logging.Logger:
        """进入上下文，创建子集日志记录器"""
        self.logger = self.cache_manager.create_subset_logger(self.subset_id)
        return self.logger
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文，完成子集日志记录"""
        if self.logger:
            self.cache_manager.finalize_subset(self.subset_id)
        
        # 如果有异常，记录到主日志
        if exc_type:
            self.cache_manager.main_logger.error(f"子集 {self.subset_id} 执行异常: {exc_val}")


# 便利函数
def create_subset_log_context(cache_manager: SubsetLogCacheManager, 
                             subset_id: str) -> SubsetLogContextManager:
    """创建子集日志上下文管理器"""
    return SubsetLogContextManager(cache_manager, subset_id)