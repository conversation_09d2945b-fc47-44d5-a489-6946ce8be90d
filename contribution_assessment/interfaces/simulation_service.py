"""
模拟服务接口定义

定义交易模拟和性能评估的服务接口
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Set, Optional, Any, FrozenSet
from ..dto.phase_results_dto import SimulationResult


class ISimulationService(ABC):
    """
    模拟服务接口
    
    负责执行交易模拟和性能评估，包括：
    - 单个联盟模拟
    - 批量联盟模拟
    - 并发执行管理
    - 性能指标计算
    """
    
    @abstractmethod
    def simulate_coalition(
        self, 
        coalition: FrozenSet[str],
        agents: Dict[str, Any],
        config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        模拟单个联盟的交易表现
        
        Args:
            coalition: 要模拟的联盟
            agents: 智能体配置
            config: 模拟配置参数
            
        Returns:
            Dict[str, Any]: 模拟结果，包含性能指标和详细数据
            
        Raises:
            SimulationError: 模拟执行失败时抛出
        """
        pass
    
    @abstractmethod
    def simulate_coalitions_batch(
        self,
        coalitions: Set[FrozenSet[str]],
        agents: Dict[str, Any],
        config: Optional[Dict[str, Any]] = None,
        max_concurrent: int = 5
    ) -> SimulationResult:
        """
        批量模拟多个联盟
        
        Args:
            coalitions: 要模拟的联盟集合
            agents: 智能体配置
            config: 模拟配置参数
            max_concurrent: 最大并发数
            
        Returns:
            SimulationResult: 批量模拟结果
            
        Raises:
            SimulationError: 批量模拟失败时抛出
        """
        pass
    
    @abstractmethod
    def calculate_performance_metrics(
        self, 
        daily_returns: List[float],
        benchmark_returns: Optional[List[float]] = None
    ) -> Dict[str, float]:
        """
        计算性能指标
        
        Args:
            daily_returns: 日收益率列表
            benchmark_returns: 基准收益率（可选）
            
        Returns:
            Dict[str, float]: 性能指标字典（包含Sharpe比率、最大回撤等）
        """
        pass
    
    @abstractmethod
    def validate_simulation_config(self, config: Dict[str, Any]) -> bool:
        """
        验证模拟配置的有效性
        
        Args:
            config: 模拟配置
            
        Returns:
            bool: 配置是否有效
            
        Raises:
            ConfigurationError: 配置无效时抛出
        """
        pass
    
    @abstractmethod
    def get_simulation_status(self, simulation_id: str) -> Dict[str, Any]:
        """
        获取模拟执行状态
        
        Args:
            simulation_id: 模拟任务ID
            
        Returns:
            Dict[str, Any]: 状态信息
        """
        pass
    
    @abstractmethod
    def cancel_simulation(self, simulation_id: str) -> bool:
        """
        取消正在执行的模拟
        
        Args:
            simulation_id: 模拟任务ID
            
        Returns:
            bool: 是否成功取消
        """
        pass
    
    @abstractmethod
    def get_simulation_history(
        self, 
        coalition: Optional[FrozenSet[str]] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        获取模拟历史记录
        
        Args:
            coalition: 特定联盟（可选）
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 历史记录列表
        """
        pass


class SimulationError(Exception):
    """模拟执行异常"""
    pass


class ConfigurationError(Exception):
    """配置异常"""
    pass


class SimulationTimeoutError(SimulationError):
    """模拟超时异常"""
    pass