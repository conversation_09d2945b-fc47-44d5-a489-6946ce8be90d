"""
交易日历服务接口定义

定义交易日历管理和周期划分的服务接口
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime, date


class ITradingCalendar(ABC):
    """
    交易日历服务接口
    
    负责管理交易日历数据，包括：
    - 交易日查询和验证
    - 周期划分和时间窗口管理
    - 节假日和特殊日期处理
    - 数据库集成支持
    """
    
    @abstractmethod
    def get_trading_days(self, 
                        start_date: str, 
                        end_date: str, 
                        use_database: bool = True,
                        stocks: Optional[List[str]] = None) -> List[str]:
        """
        获取指定日期范围内的交易日
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            use_database: 是否使用数据库查询
            stocks: 股票代码列表（可选）
            
        Returns:
            List[str]: 交易日列表 (YYYY-MM-DD格式)
            
        Raises:
            TradingCalendarError: 查询失败时抛出
        """
        pass
    
    @abstractmethod
    def get_trading_weeks(self, 
                         trading_days: List[str],
                         strategy: str = "adaptive",
                         days_per_week: int = 5) -> List[Dict[str, Any]]:
        """
        将交易日划分为周期
        
        Args:
            trading_days: 交易日列表
            strategy: 划分策略 ("adaptive", "fixed", "calendar")
            days_per_week: 每周交易日数
            
        Returns:
            List[Dict[str, Any]]: 周期信息列表
            
        Raises:
            TradingCalendarError: 划分失败时抛出
        """
        pass
    
    @abstractmethod
    def is_trading_day(self, date_str: str) -> bool:
        """
        检查指定日期是否为交易日
        
        Args:
            date_str: 日期字符串 (YYYY-MM-DD)
            
        Returns:
            bool: 是否为交易日
        """
        pass
    
    @abstractmethod
    def get_next_trading_day(self, date_str: str) -> Optional[str]:
        """
        获取下一个交易日
        
        Args:
            date_str: 当前日期 (YYYY-MM-DD)
            
        Returns:
            Optional[str]: 下一个交易日，如果没有则返回None
        """
        pass
    
    @abstractmethod
    def get_previous_trading_day(self, date_str: str) -> Optional[str]:
        """
        获取前一个交易日
        
        Args:
            date_str: 当前日期 (YYYY-MM-DD)
            
        Returns:
            Optional[str]: 前一个交易日，如果没有则返回None
        """
        pass
    
    @abstractmethod
    def get_trading_days_in_period(self, 
                                  start_date: str, 
                                  end_date: str) -> int:
        """
        获取指定时期内的交易日数量
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            int: 交易日数量
        """
        pass
    
    @abstractmethod
    def validate_date_range(self, start_date: str, end_date: str) -> bool:
        """
        验证日期范围的有效性
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            
        Returns:
            bool: 日期范围是否有效
            
        Raises:
            DateValidationError: 日期格式或范围无效时抛出
        """
        pass
    
    @abstractmethod
    def get_week_boundaries(self, 
                           trading_days: List[str],
                           week_number: int) -> Dict[str, Any]:
        """
        获取指定周的边界信息
        
        Args:
            trading_days: 交易日列表
            week_number: 周数（从1开始）
            
        Returns:
            Dict[str, Any]: 周边界信息
        """
        pass
    
    @abstractmethod
    def get_holidays(self, year: int) -> List[str]:
        """
        获取指定年份的假期列表
        
        Args:
            year: 年份
            
        Returns:
            List[str]: 假期日期列表 (YYYY-MM-DD格式)
        """
        pass
    
    @abstractmethod
    def add_custom_holiday(self, date_str: str, description: str) -> None:
        """
        添加自定义假期
        
        Args:
            date_str: 假期日期 (YYYY-MM-DD)
            description: 假期描述
        """
        pass
    
    @abstractmethod
    def remove_custom_holiday(self, date_str: str) -> None:
        """
        移除自定义假期
        
        Args:
            date_str: 假期日期 (YYYY-MM-DD)
        """
        pass
    
    @abstractmethod
    def get_calendar_statistics(self) -> Dict[str, Any]:
        """
        获取日历统计信息
        
        Returns:
            Dict[str, Any]: 统计信息
        """
        pass


class TradingCalendarError(Exception):
    """交易日历异常"""
    pass


class DateValidationError(Exception):
    """日期验证异常"""
    pass


class WeekDivisionError(Exception):
    """周期划分异常"""
    pass