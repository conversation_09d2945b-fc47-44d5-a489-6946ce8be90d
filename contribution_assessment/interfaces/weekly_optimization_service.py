"""
周期性优化服务接口定义

定义周期性优化服务的标准接口，支持在线优化和周期性评估
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime
from ..dto.assessment_dto import AssessmentRequest, AssessmentResult


class WeeklyOptimizationConfig:
    """周期性优化配置"""
    
    def __init__(self,
                 optimization_frequency: int = 5,
                 min_days_for_optimization: int = 5,
                 max_optimization_iterations: int = 3,
                 performance_degradation_threshold: float = -0.05,
                 optimize_worst_performers: bool = True,
                 max_agents_per_cycle: int = 1,
                 historical_window_weeks: int = 4,
                 verbose_logging: bool = True,
                 **kwargs):
        self.optimization_frequency = optimization_frequency
        self.min_days_for_optimization = min_days_for_optimization
        self.max_optimization_iterations = max_optimization_iterations
        self.performance_degradation_threshold = performance_degradation_threshold
        self.optimize_worst_performers = optimize_worst_performers
        self.max_agents_per_cycle = max_agents_per_cycle
        self.historical_window_weeks = historical_window_weeks
        self.verbose_logging = verbose_logging
        
        # 存储额外的配置参数
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'optimization_frequency': self.optimization_frequency,
            'min_days_for_optimization': self.min_days_for_optimization,
            'max_optimization_iterations': self.max_optimization_iterations,
            'performance_degradation_threshold': self.performance_degradation_threshold,
            'optimize_worst_performers': self.optimize_worst_performers,
            'max_agents_per_cycle': self.max_agents_per_cycle,
            'historical_window_weeks': self.historical_window_weeks,
            'verbose_logging': self.verbose_logging
        }


class WeeklyOptimizationResult:
    """周期性优化结果"""
    
    def __init__(self,
                 success: bool,
                 total_weeks: int = 0,
                 weekly_results: Optional[List[Dict[str, Any]]] = None,
                 final_shapley_values: Optional[Dict[str, float]] = None,
                 optimization_summary: Optional[Dict[str, Any]] = None,
                 agent_evolution: Optional[Dict[str, Any]] = None,
                 performance_metrics: Optional[Dict[str, Any]] = None,
                 execution_time: float = 0.0,
                 error: Optional[str] = None,
                 **kwargs):
        self.success = success
        self.total_weeks = total_weeks
        self.weekly_results = weekly_results or []
        self.final_shapley_values = final_shapley_values or {}
        self.optimization_summary = optimization_summary or {}
        self.agent_evolution = agent_evolution or {}
        self.performance_metrics = performance_metrics or {}
        self.execution_time = execution_time
        self.error = error
        
        # 存储额外的结果数据
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'success': self.success,
            'total_weeks': self.total_weeks,
            'weekly_results': self.weekly_results,
            'final_shapley_values': self.final_shapley_values,
            'optimization_summary': self.optimization_summary,
            'agent_evolution': self.agent_evolution,
            'performance_metrics': self.performance_metrics,
            'execution_time': self.execution_time,
            'error': self.error
        }


class IWeeklyOptimizationService(ABC):
    """
    周期性优化服务接口
    
    定义周期性优化服务的标准接口，支持：
    - 在线优化：在模拟过程中进行周期性优化
    - 配置驱动：通过配置参数控制优化行为
    - 状态管理：跟踪优化过程和结果
    - 错误处理：处理优化过程中的异常情况
    """
    
    @abstractmethod
    def run_weekly_optimization(self,
                              request: AssessmentRequest,
                              config: WeeklyOptimizationConfig) -> WeeklyOptimizationResult:
        """
        执行周期性优化
        
        Args:
            request: 评估请求，包含智能体、配置等信息
            config: 周期性优化配置
            
        Returns:
            WeeklyOptimizationResult: 优化结果
            
        Raises:
            WeeklyOptimizationError: 优化执行失败时抛出
        """
        pass
    
    @abstractmethod
    def initialize_optimization(self,
                              start_date: str,
                              end_date: str,
                              agents: Dict[str, Any],
                              system_config: Dict[str, Any]) -> bool:
        """
        初始化优化环境
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            agents: 智能体字典
            system_config: 系统配置
            
        Returns:
            bool: 是否初始化成功
        """
        pass
    
    @abstractmethod
    def get_optimization_status(self) -> Dict[str, Any]:
        """
        获取优化状态
        
        Returns:
            Dict[str, Any]: 状态信息
        """
        pass
    
    @abstractmethod
    def cancel_optimization(self) -> bool:
        """
        取消正在进行的优化
        
        Returns:
            bool: 是否成功取消
        """
        pass
    
    @abstractmethod
    def cleanup(self) -> None:
        """
        清理资源
        """
        pass
    
    @abstractmethod
    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        获取性能指标
        
        Returns:
            Dict[str, Any]: 性能指标
        """
        pass
    
    @abstractmethod
    def configure_service(self, config: Dict[str, Any]) -> bool:
        """
        配置服务
        
        Args:
            config: 配置参数
            
        Returns:
            bool: 是否配置成功
        """
        pass
    
    @abstractmethod
    def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            Dict[str, Any]: 健康状态信息
        """
        pass


class WeeklyOptimizationError(Exception):
    """周期性优化异常"""
    pass


class OptimizationConfigurationError(WeeklyOptimizationError):
    """优化配置异常"""
    pass


class OptimizationExecutionError(WeeklyOptimizationError):
    """优化执行异常"""
    pass


class OptimizationTimeoutError(WeeklyOptimizationError):
    """优化超时异常"""
    pass
