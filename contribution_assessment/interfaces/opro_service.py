"""
OPRO优化服务接口定义

定义智能体提示词优化的服务接口
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from ..dto.phase_results_dto import OPROResult


class IOPROService(ABC):
    """
    OPRO优化服务接口
    
    负责智能体提示词的优化和管理，包括：
    - 提示词优化
    - 性能评估
    - A/B测试
    - 历史管理
    """
    
    @abstractmethod
    def optimize_agents(
        self,
        shapley_values: Dict[str, float],
        agents: Dict[str, Any],
        config: Optional[Dict[str, Any]] = None
    ) -> OPROResult:
        """
        基于Shapley值优化智能体
        
        Args:
            shapley_values: 智能体Shapley值
            agents: 智能体配置
            config: 优化配置参数
            
        Returns:
            OPROResult: 优化结果
            
        Raises:
            OptimizationError: 优化失败时抛出
        """
        pass
    
    @abstractmethod
    def optimize_single_agent(
        self,
        agent_name: str,
        current_prompt: str,
        performance_score: float,
        failure_cases: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """
        优化单个智能体的提示词
        
        Args:
            agent_name: 智能体名称
            current_prompt: 当前提示词
            performance_score: 性能评分
            failure_cases: 失败案例（可选）
            
        Returns:
            Dict[str, Any]: 优化结果，包含新提示词和元数据
            
        Raises:
            OptimizationError: 优化失败时抛出
        """
        pass
    
    @abstractmethod
    def get_optimization_history(
        self,
        agent_name: Optional[str] = None,
        limit: int = 100
    ) -> List[Dict[str, Any]]:
        """
        获取优化历史记录
        
        Args:
            agent_name: 特定智能体（可选）
            limit: 返回记录数限制
            
        Returns:
            List[Dict[str, Any]]: 优化历史列表
        """
        pass
    
    @abstractmethod
    def validate_prompt(
        self,
        prompt: str,
        agent_type: str
    ) -> Dict[str, Any]:
        """
        验证提示词的有效性
        
        Args:
            prompt: 要验证的提示词
            agent_type: 智能体类型
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        pass
    


class OptimizationError(Exception):
    """优化异常"""
    pass


class PromptValidationError(Exception):
    """提示词验证异常"""
    pass


