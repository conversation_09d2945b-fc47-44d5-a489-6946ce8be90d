"""
接口兼容性检查工具

提供接口实现的兼容性验证功能
"""

import inspect
from typing import Type, Dict, List, Any, Optional
from abc import ABC
import logging

logger = logging.getLogger(__name__)


class CompatibilityError(Exception):
    """兼容性检查异常"""
    pass


class InterfaceCompatibilityChecker:
    """
    接口兼容性检查器
    
    验证具体实现类是否正确实现了接口契约
    """
    
    @staticmethod
    def check_implementation(
        interface_class: Type[ABC],
        implementation_class: Type,
        strict_mode: bool = True
    ) -> Dict[str, Any]:
        """
        检查实现类是否符合接口契约
        
        Args:
            interface_class: 接口类
            implementation_class: 实现类
            strict_mode: 是否启用严格模式
            
        Returns:
            Dict[str, Any]: 检查结果
            
        Raises:
            CompatibilityError: 兼容性检查失败时抛出
        """
        result = {
            "compatible": True,
            "interface": interface_class.__name__,
            "implementation": implementation_class.__name__,
            "missing_methods": [],
            "signature_mismatches": [],
            "warnings": [],
            "details": {}
        }
        
        try:
            # 检查是否继承自接口
            if not issubclass(implementation_class, interface_class):
                result["compatible"] = False
                result["warnings"].append(f"{implementation_class.__name__} does not inherit from {interface_class.__name__}")
            
            # 获取接口中的抽象方法
            interface_methods = InterfaceCompatibilityChecker._get_abstract_methods(interface_class)
            implementation_methods = InterfaceCompatibilityChecker._get_methods(implementation_class)
            
            # 检查缺失的方法
            for method_name, method_info in interface_methods.items():
                if method_name not in implementation_methods:
                    result["compatible"] = False
                    result["missing_methods"].append({
                        "method": method_name,
                        "signature": str(method_info["signature"])
                    })
                else:
                    # 检查方法签名
                    impl_method = implementation_methods[method_name]
                    signature_check = InterfaceCompatibilityChecker._check_method_signature(
                        method_info, impl_method, strict_mode
                    )
                    
                    if not signature_check["compatible"]:
                        result["compatible"] = False
                        result["signature_mismatches"].append({
                            "method": method_name,
                            "expected": str(method_info["signature"]),
                            "actual": str(impl_method["signature"]),
                            "issues": signature_check["issues"]
                        })
            
            # 检查额外的公共方法（可能的接口扩展）
            extra_methods = set(implementation_methods.keys()) - set(interface_methods.keys())
            if extra_methods:
                result["details"]["extra_methods"] = list(extra_methods)
            
            return result
            
        except Exception as e:
            logger.error(f"Compatibility check failed: {str(e)}")
            raise CompatibilityError(f"Failed to check compatibility: {str(e)}")
    
    @staticmethod
    def _get_abstract_methods(interface_class: Type[ABC]) -> Dict[str, Dict[str, Any]]:
        """获取接口中的抽象方法"""
        methods = {}
        
        for name, method in inspect.getmembers(interface_class, predicate=inspect.isfunction):
            if getattr(method, '__isabstractmethod__', False):
                methods[name] = {
                    "signature": inspect.signature(method),
                    "doc": inspect.getdoc(method),
                    "annotations": getattr(method, '__annotations__', {})
                }
        
        return methods
    
    @staticmethod
    def _get_methods(class_type: Type) -> Dict[str, Dict[str, Any]]:
        """获取类中的方法"""
        methods = {}
        
        for name, method in inspect.getmembers(class_type, predicate=inspect.ismethod):
            if not name.startswith('_'):  # 跳过私有方法
                methods[name] = {
                    "signature": inspect.signature(method),
                    "doc": inspect.getdoc(method),
                    "annotations": getattr(method, '__annotations__', {})
                }
        
        # 也检查函数（对于未实例化的类）
        for name, method in inspect.getmembers(class_type, predicate=inspect.isfunction):
            if not name.startswith('_'):
                methods[name] = {
                    "signature": inspect.signature(method),
                    "doc": inspect.getdoc(method),
                    "annotations": getattr(method, '__annotations__', {})
                }
        
        return methods
    
    @staticmethod
    def _check_method_signature(
        interface_method: Dict[str, Any],
        impl_method: Dict[str, Any],
        strict_mode: bool
    ) -> Dict[str, Any]:
        """检查方法签名兼容性"""
        result = {
            "compatible": True,
            "issues": []
        }
        
        interface_sig = interface_method["signature"]
        impl_sig = impl_method["signature"]
        
        # 检查参数数量
        interface_params = list(interface_sig.parameters.values())
        impl_params = list(impl_sig.parameters.values())
        
        if len(interface_params) != len(impl_params):
            if strict_mode:
                result["compatible"] = False
                result["issues"].append(f"Parameter count mismatch: expected {len(interface_params)}, got {len(impl_params)}")
            else:
                result["issues"].append(f"Parameter count difference (warning): expected {len(interface_params)}, got {len(impl_params)}")
        
        # 检查参数名称和类型
        for i, (interface_param, impl_param) in enumerate(zip(interface_params, impl_params)):
            if interface_param.name != impl_param.name:
                if strict_mode:
                    result["compatible"] = False
                    result["issues"].append(f"Parameter {i} name mismatch: expected '{interface_param.name}', got '{impl_param.name}'")
                else:
                    result["issues"].append(f"Parameter {i} name difference (warning): expected '{interface_param.name}', got '{impl_param.name}'")
            
            # 类型注解检查
            if interface_param.annotation != impl_param.annotation:
                if interface_param.annotation != inspect.Parameter.empty and impl_param.annotation != inspect.Parameter.empty:
                    if strict_mode:
                        result["compatible"] = False
                        result["issues"].append(f"Parameter {interface_param.name} type mismatch: expected {interface_param.annotation}, got {impl_param.annotation}")
                    else:
                        result["issues"].append(f"Parameter {interface_param.name} type difference (warning): expected {interface_param.annotation}, got {impl_param.annotation}")
        
        # 检查返回类型
        if interface_sig.return_annotation != impl_sig.return_annotation:
            if interface_sig.return_annotation != inspect.Signature.empty and impl_sig.return_annotation != inspect.Signature.empty:
                if strict_mode:
                    result["compatible"] = False
                    result["issues"].append(f"Return type mismatch: expected {interface_sig.return_annotation}, got {impl_sig.return_annotation}")
                else:
                    result["issues"].append(f"Return type difference (warning): expected {interface_sig.return_annotation}, got {impl_sig.return_annotation}")
        
        return result
    
    @staticmethod
    def generate_compatibility_report(
        interface_classes: List[Type[ABC]],
        implementation_classes: List[Type],
        output_file: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        生成兼容性检查报告
        
        Args:
            interface_classes: 接口类列表
            implementation_classes: 实现类列表
            output_file: 输出文件路径（可选）
            
        Returns:
            Dict[str, Any]: 完整的兼容性报告
        """
        report = {
            "timestamp": inspect.datetime.now().isoformat(),
            "summary": {
                "total_interfaces": len(interface_classes),
                "total_implementations": len(implementation_classes),
                "compatible_pairs": 0,
                "incompatible_pairs": 0
            },
            "results": []
        }
        
        for interface_class in interface_classes:
            for impl_class in implementation_classes:
                try:
                    # 检查是否应该配对（基于命名约定或继承关系）
                    if InterfaceCompatibilityChecker._should_check_pair(interface_class, impl_class):
                        check_result = InterfaceCompatibilityChecker.check_implementation(
                            interface_class, impl_class, strict_mode=False
                        )
                        report["results"].append(check_result)
                        
                        if check_result["compatible"]:
                            report["summary"]["compatible_pairs"] += 1
                        else:
                            report["summary"]["incompatible_pairs"] += 1
                            
                except Exception as e:
                    logger.error(f"Failed to check {interface_class.__name__} vs {impl_class.__name__}: {str(e)}")
        
        # 输出到文件
        if output_file:
            try:
                import json
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(report, f, indent=2, ensure_ascii=False)
                logger.info(f"Compatibility report saved to {output_file}")
            except Exception as e:
                logger.error(f"Failed to save report to {output_file}: {str(e)}")
        
        return report
    
    @staticmethod
    def _should_check_pair(interface_class: Type[ABC], impl_class: Type) -> bool:
        """判断是否应该检查接口和实现类的配对"""
        # 基于继承关系
        try:
            if issubclass(impl_class, interface_class):
                return True
        except TypeError:
            pass
        
        # 基于命名约定（例如 IService 对应 Service）
        interface_name = interface_class.__name__
        impl_name = impl_class.__name__
        
        if interface_name.startswith('I') and len(interface_name) > 1:
            expected_impl_name = interface_name[1:]  # 去掉 'I' 前缀
            if impl_name == expected_impl_name or impl_name.endswith(expected_impl_name):
                return True
        
        return False