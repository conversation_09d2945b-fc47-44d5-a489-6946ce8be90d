"""
Shapley值计算服务接口定义

定义Shapley值计算和周期性分析的服务接口
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Set, Optional, Any, FrozenSet
from ..dto.phase_results_dto import ShapleyResult


class IShapleyService(ABC):
    """
    Shapley值计算服务接口
    
    负责计算智能体的Shapley值和贡献分析，包括：
    - 周期性Shapley值计算
    - 贡献度分析
    - 历史数据管理
    - 性能优化
    """
    
    @abstractmethod
    def calculate_periodic_shapley(
        self,
        coalition_values: Dict[FrozenSet[str], float],
        config: Optional[Dict[str, Any]] = None
    ) -> ShapleyResult:
        """
        计算周期性Shapley值
        
        Args:
            coalition_values: 联盟价值映射
            config: 计算配置参数
            
        Returns:
            ShapleyResult: Shapley计算结果
            
        Raises:
            ShapleyCalculationError: 计算失败时抛出
        """
        pass
    
    @abstractmethod
    def calculate_shapley_values(
        self,
        agents: List[str],
        coalition_values: Dict[FrozenSet[str], float]
    ) -> Dict[str, float]:
        """
        计算智能体的Shapley值
        
        Args:
            agents: 智能体列表
            coalition_values: 联盟价值映射
            
        Returns:
            Dict[str, float]: 智能体Shapley值映射
            
        Raises:
            ShapleyCalculationError: 计算失败时抛出
        """
        pass
    
    @abstractmethod
    def analyze_contributions(
        self,
        shapley_values: Dict[str, float],
        historical_data: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """
        分析智能体贡献度
        
        Args:
            shapley_values: 当前Shapley值
            historical_data: 历史数据（可选）
            
        Returns:
            Dict[str, Any]: 贡献度分析结果
        """
        pass
    
    @abstractmethod
    def get_marginal_contributions(
        self,
        agent: str,
        coalition_values: Dict[FrozenSet[str], float]
    ) -> List[Dict[str, Any]]:
        """
        获取智能体的边际贡献
        
        Args:
            agent: 智能体名称
            coalition_values: 联盟价值映射
            
        Returns:
            List[Dict[str, Any]]: 边际贡献列表
        """
        pass
    
    @abstractmethod
    def validate_coalition_values(
        self,
        coalition_values: Dict[FrozenSet[str], float]
    ) -> bool:
        """
        验证联盟价值数据的完整性
        
        Args:
            coalition_values: 联盟价值映射
            
        Returns:
            bool: 数据是否有效
            
        Raises:
            DataValidationError: 数据验证失败时抛出
        """
        pass
    
    @abstractmethod
    def get_shapley_statistics(
        self,
        shapley_values: Dict[str, float]
    ) -> Dict[str, Any]:
        """
        获取Shapley值的统计信息
        
        Args:
            shapley_values: Shapley值映射
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        pass
    
    @abstractmethod
    def save_shapley_results(
        self,
        results: ShapleyResult,
        storage_path: Optional[str] = None
    ) -> bool:
        """
        保存Shapley计算结果
        
        Args:
            results: Shapley计算结果
            storage_path: 存储路径（可选）
            
        Returns:
            bool: 是否保存成功
        """
        pass
    
    @abstractmethod
    def load_historical_shapley(
        self,
        agent: Optional[str] = None,
        time_range: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        加载历史Shapley数据
        
        Args:
            agent: 特定智能体（可选）
            time_range: 时间范围（可选）
            
        Returns:
            List[Dict[str, Any]]: 历史数据列表
        """
        pass


class ShapleyCalculationError(Exception):
    """Shapley计算异常"""
    pass


class DataValidationError(Exception):
    """数据验证异常"""
    pass


class ShapleyStorageError(Exception):
    """Shapley数据存储异常"""
    pass