"""
状态管理服务接口定义

定义系统状态管理和持久化的服务接口
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from enum import Enum


class StateType(Enum):
    """状态类型枚举"""
    ASSESSMENT = "assessment"
    COALITION = "coalition"
    SIMULATION = "simulation"
    SHAPLEY = "shapley"
    OPRO = "opro"
    SYSTEM = "system"


class IStateManager(ABC):
    """
    状态管理服务接口
    
    负责系统状态的管理和持久化，包括：
    - 状态存储和检索
    - 状态变更跟踪
    - 事件发布
    - 数据持久化
    """
    
    @abstractmethod
    def save_state(
        self,
        state_type: StateType,
        state_id: str,
        state_data: Dict[str, Any],
        metadata: Optional[Dict[str, Any]] = None
    ) -> bool:
        """
        保存状态数据
        
        Args:
            state_type: 状态类型
            state_id: 状态唯一标识
            state_data: 状态数据
            metadata: 元数据（可选）
            
        Returns:
            bool: 是否保存成功
            
        Raises:
            StateStorageError: 状态保存失败时抛出
        """
        pass
    
    @abstractmethod
    def load_state(
        self,
        state_type: StateType,
        state_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        加载状态数据
        
        Args:
            state_type: 状态类型
            state_id: 状态唯一标识
            
        Returns:
            Optional[Dict[str, Any]]: 状态数据，不存在时返回None
            
        Raises:
            StateLoadError: 状态加载失败时抛出
        """
        pass
    
    @abstractmethod
    def update_state(
        self,
        state_type: StateType,
        state_id: str,
        updates: Dict[str, Any],
        create_if_not_exists: bool = False
    ) -> bool:
        """
        更新状态数据
        
        Args:
            state_type: 状态类型
            state_id: 状态唯一标识
            updates: 更新数据
            create_if_not_exists: 如果不存在是否创建
            
        Returns:
            bool: 是否更新成功
            
        Raises:
            StateUpdateError: 状态更新失败时抛出
        """
        pass
    
    @abstractmethod
    def delete_state(
        self,
        state_type: StateType,
        state_id: str
    ) -> bool:
        """
        删除状态数据
        
        Args:
            state_type: 状态类型
            state_id: 状态唯一标识
            
        Returns:
            bool: 是否删除成功
        """
        pass
    
    @abstractmethod
    def list_states(
        self,
        state_type: StateType,
        filters: Optional[Dict[str, Any]] = None,
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        列出状态数据
        
        Args:
            state_type: 状态类型
            filters: 过滤条件（可选）
            limit: 返回数量限制（可选）
            
        Returns:
            List[Dict[str, Any]]: 状态数据列表
        """
        pass
    
    @abstractmethod
    def get_state_history(
        self,
        state_type: StateType,
        state_id: str,
        limit: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        获取状态变更历史
        
        Args:
            state_type: 状态类型
            state_id: 状态唯一标识
            limit: 返回数量限制（可选）
            
        Returns:
            List[Dict[str, Any]]: 状态历史列表
        """
        pass
    
    @abstractmethod
    def subscribe_to_state_changes(
        self,
        state_type: StateType,
        callback: callable,
        state_id: Optional[str] = None
    ) -> str:
        """
        订阅状态变更通知
        
        Args:
            state_type: 状态类型
            callback: 回调函数
            state_id: 特定状态ID（可选）
            
        Returns:
            str: 订阅ID
        """
        pass
    
    @abstractmethod
    def unsubscribe_from_state_changes(self, subscription_id: str) -> bool:
        """
        取消状态变更订阅
        
        Args:
            subscription_id: 订阅ID
            
        Returns:
            bool: 是否取消成功
        """
        pass
    
    @abstractmethod
    def create_snapshot(
        self,
        snapshot_name: str,
        state_types: Optional[List[StateType]] = None
    ) -> str:
        """
        创建状态快照
        
        Args:
            snapshot_name: 快照名称
            state_types: 要包含的状态类型（可选，默认全部）
            
        Returns:
            str: 快照ID
        """
        pass
    
    @abstractmethod
    def restore_from_snapshot(
        self,
        snapshot_id: str,
        selective_restore: Optional[Dict[StateType, List[str]]] = None
    ) -> bool:
        """
        从快照恢复状态
        
        Args:
            snapshot_id: 快照ID
            selective_restore: 选择性恢复配置（可选）
            
        Returns:
            bool: 是否恢复成功
        """
        pass
    
    @abstractmethod
    def cleanup_old_states(
        self,
        state_type: StateType,
        retention_days: int = 30
    ) -> int:
        """
        清理旧状态数据
        
        Args:
            state_type: 状态类型
            retention_days: 保留天数
            
        Returns:
            int: 清理的状态数量
        """
        pass


class StateStorageError(Exception):
    """状态存储异常"""
    pass


class StateLoadError(Exception):
    """状态加载异常"""
    pass


class StateUpdateError(Exception):
    """状态更新异常"""
    pass


class SnapshotError(Exception):
    """快照操作异常"""
    pass