"""
评估服务接口定义

定义主评估服务的接口，协调整个评估流程
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from ..dto.assessment_dto import AssessmentRequest, AssessmentResult


class IAssessmentService(ABC):
    """
    评估服务接口
    
    主评估服务，负责协调整个评估流程，包括：
    - 评估请求处理
    - 各阶段服务协调
    - 结果汇总和分析
    - 错误处理和恢复
    """
    
    @abstractmethod
    def run_assessment(self, request: AssessmentRequest) -> AssessmentResult:
        """
        执行完整的评估流程
        
        Args:
            request: 评估请求
            
        Returns:
            AssessmentResult: 评估结果
            
        Raises:
            AssessmentError: 评估执行失败时抛出
        """
        pass
    
    @abstractmethod
    def run_partial_assessment(
        self,
        request: AssessmentRequest,
        phases: List[str]
    ) -> AssessmentResult:
        """
        执行部分评估流程
        
        Args:
            request: 评估请求
            phases: 要执行的阶段列表
            
        Returns:
            AssessmentResult: 部分评估结果
            
        Raises:
            AssessmentError: 评估执行失败时抛出
        """
        pass
    
    @abstractmethod
    def validate_request(self, request: AssessmentRequest) -> Dict[str, Any]:
        """
        验证评估请求的有效性
        
        Args:
            request: 评估请求
            
        Returns:
            Dict[str, Any]: 验证结果
            
        Raises:
            RequestValidationError: 请求验证失败时抛出
        """
        pass
    
    @abstractmethod
    def get_assessment_status(self, request_id: str) -> Dict[str, Any]:
        """
        获取评估执行状态
        
        Args:
            request_id: 请求ID
            
        Returns:
            Dict[str, Any]: 状态信息
        """
        pass
    
    @abstractmethod
    def cancel_assessment(self, request_id: str) -> bool:
        """
        取消正在执行的评估
        
        Args:
            request_id: 请求ID
            
        Returns:
            bool: 是否成功取消
        """
        pass
    
    @abstractmethod
    def get_assessment_history(
        self,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        获取评估历史记录
        
        Args:
            limit: 返回记录数限制
            filters: 过滤条件（可选）
            
        Returns:
            List[Dict[str, Any]]: 历史记录列表
        """
        pass
    
    @abstractmethod
    def get_performance_metrics(
        self,
        time_range: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        获取评估服务的性能指标
        
        Args:
            time_range: 时间范围（可选）
            
        Returns:
            Dict[str, Any]: 性能指标
        """
        pass
    
    @abstractmethod
    def configure_service(self, config: Dict[str, Any]) -> bool:
        """
        配置评估服务
        
        Args:
            config: 配置参数
            
        Returns:
            bool: 是否配置成功
        """
        pass
    
    @abstractmethod
    def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            Dict[str, Any]: 健康状态信息
        """
        pass


class AssessmentError(Exception):
    """评估异常"""
    pass


class RequestValidationError(Exception):
    """请求验证异常"""
    pass


class AssessmentTimeoutError(AssessmentError):
    """评估超时异常"""
    pass


class ServiceUnavailableError(AssessmentError):
    """服务不可用异常"""
    pass