"""
智能体创建服务接口

定义智能体创建服务的抽象接口，实现依赖倒置原则
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List


class IAgentCreationService(ABC):
    """
    智能体创建服务接口
    
    定义智能体创建的标准接口，支持不同的实现策略
    """
    
    @abstractmethod
    def create_default_agents(self) -> Dict[str, Any]:
        """
        创建默认智能体集合
        
        Returns:
            智能体字典 {agent_id: agent_instance}
        """
        pass
    
    @abstractmethod
    def create_agents_subset(self, agent_ids: List[str]) -> Dict[str, Any]:
        """
        创建指定的智能体子集
        
        Args:
            agent_ids: 要创建的智能体ID列表
            
        Returns:
            智能体字典
        """
        pass
    
    @abstractmethod
    def create_single_agent(self, agent_id: str) -> Optional[Any]:
        """
        创建单个智能体
        
        Args:
            agent_id: 智能体ID
            
        Returns:
            智能体实例或None
        """
        pass
    
    @abstractmethod
    def get_available_agent_ids(self) -> List[str]:
        """
        获取可用的智能体ID列表
        
        Returns:
            智能体ID列表
        """
        pass
    
    @abstractmethod
    def validate_agent_id(self, agent_id: str) -> bool:
        """
        验证智能体ID是否有效
        
        Args:
            agent_id: 智能体ID
            
        Returns:
            是否有效
        """
        pass
    
    @abstractmethod
    def get_creation_stats(self) -> Dict[str, int]:
        """
        获取创建统计信息
        
        Returns:
            统计信息字典
        """
        pass
    
    @abstractmethod
    def reset_stats(self) -> None:
        """重置统计信息"""
        pass


class IAgentFactory(ABC):
    """
    智能体工厂接口
    
    定义智能体工厂的抽象接口
    """
    
    @abstractmethod
    def create_agent(self, agent_id: str) -> Any:
        """
        创建单个智能体
        
        Args:
            agent_id: 智能体ID
            
        Returns:
            智能体实例
        """
        pass
    
    @abstractmethod
    def create_all_agents(self) -> Dict[str, Any]:
        """
        创建所有智能体
        
        Returns:
            智能体字典
        """
        pass
    
    @abstractmethod
    def get_available_agents(self) -> List[str]:
        """
        获取可用的智能体ID列表
        
        Returns:
            智能体ID列表
        """
        pass


class ILLMInterface(ABC):
    """
    LLM接口抽象
    
    定义LLM接口的标准方法
    """
    
    @abstractmethod
    def analyze(self, prompt: str, model: str = None, **kwargs) -> Optional[Dict[str, Any]]:
        """
        分析请求
        
        Args:
            prompt: 提示词
            model: 模型名称
            **kwargs: 其他参数
            
        Returns:
            分析结果
        """
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """
        检查LLM接口是否可用
        
        Returns:
            是否可用
        """
        pass
