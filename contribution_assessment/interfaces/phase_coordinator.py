"""
阶段协调器接口定义

定义评估工作流协调和管理的服务接口
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from ..dto.assessment_dto import AssessmentRequest, AssessmentResult


class IPhaseCoordinator(ABC):
    """
    阶段协调器接口
    
    负责协调整个评估工作流的执行，包括：
    - 四阶段执行流程管理
    - 阶段间数据传递
    - 错误处理和恢复
    - 执行统计和监控
    """
    
    @abstractmethod
    def execute_assessment_workflow(self, request: AssessmentRequest) -> AssessmentResult:
        """
        执行完整的评估工作流
        
        Args:
            request: 评估请求
            
        Returns:
            AssessmentResult: 评估结果
            
        Raises:
            PhaseCoordinatorError: 工作流执行失败时抛出
        """
        pass
    
    @abstractmethod
    def execute_partial_workflow(
        self,
        request: AssessmentRequest,
        phases: List[str],
        intermediate_data: Optional[Dict[str, Any]] = None
    ) -> AssessmentResult:
        """
        执行部分工作流
        
        Args:
            request: 评估请求
            phases: 要执行的阶段列表
            intermediate_data: 中间数据（可选）
            
        Returns:
            AssessmentResult: 部分评估结果
            
        Raises:
            PhaseCoordinatorError: 部分工作流执行失败时抛出
        """
        pass
    
    @abstractmethod
    def get_execution_stats(self) -> Dict[str, Any]:
        """
        获取执行统计信息
        
        Returns:
            Dict[str, Any]: 执行统计数据
        """
        pass
    
    @abstractmethod
    def get_phase_config(self) -> Dict[str, Any]:
        """
        获取阶段配置信息
        
        Returns:
            Dict[str, Any]: 阶段配置
        """
        pass
    
    @abstractmethod
    def update_phase_config(self, phase_name: str, config: Dict[str, Any]) -> bool:
        """
        更新阶段配置
        
        Args:
            phase_name: 阶段名称
            config: 新配置
            
        Returns:
            bool: 是否更新成功
        """
        pass
    
    @abstractmethod
    def validate_workflow_request(self, request: AssessmentRequest) -> Dict[str, Any]:
        """
        验证工作流请求
        
        Args:
            request: 评估请求
            
        Returns:
            Dict[str, Any]: 验证结果
            
        Raises:
            RequestValidationError: 请求验证失败时抛出
        """
        pass
    
    @abstractmethod
    def get_workflow_status(self, workflow_id: str) -> Dict[str, Any]:
        """
        获取工作流执行状态
        
        Args:
            workflow_id: 工作流ID
            
        Returns:
            Dict[str, Any]: 状态信息
        """
        pass
    
    @abstractmethod
    def cancel_workflow(self, workflow_id: str) -> bool:
        """
        取消正在执行的工作流
        
        Args:
            workflow_id: 工作流ID
            
        Returns:
            bool: 是否成功取消
        """
        pass
    
    @abstractmethod
    def get_workflow_history(
        self,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        获取工作流执行历史
        
        Args:
            limit: 返回记录数限制
            filters: 过滤条件（可选）
            
        Returns:
            List[Dict[str, Any]]: 历史记录列表
        """
        pass
    
    @abstractmethod
    def pause_workflow(self, workflow_id: str) -> bool:
        """
        暂停工作流执行
        
        Args:
            workflow_id: 工作流ID
            
        Returns:
            bool: 是否成功暂停
        """
        pass
    
    @abstractmethod
    def resume_workflow(self, workflow_id: str) -> bool:
        """
        恢复工作流执行
        
        Args:
            workflow_id: 工作流ID
            
        Returns:
            bool: 是否成功恢复
        """
        pass
    
    @abstractmethod
    def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            Dict[str, Any]: 健康状态信息
        """
        pass
    
    @abstractmethod
    def get_performance_metrics(
        self,
        time_range: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        获取性能指标
        
        Args:
            time_range: 时间范围（可选）
            
        Returns:
            Dict[str, Any]: 性能指标
        """
        pass
    
    @abstractmethod
    def configure_workflow(self, config: Dict[str, Any]) -> bool:
        """
        配置工作流
        
        Args:
            config: 配置参数
            
        Returns:
            bool: 是否配置成功
        """
        pass


class PhaseCoordinatorError(Exception):
    """阶段协调器异常"""
    pass


class PhaseExecutionError(PhaseCoordinatorError):
    """阶段执行异常"""
    pass


class WorkflowValidationError(PhaseCoordinatorError):
    """工作流验证异常"""
    pass


class WorkflowTimeoutError(PhaseCoordinatorError):
    """工作流超时异常"""
    pass


class WorkflowCancellationError(PhaseCoordinatorError):
    """工作流取消异常"""
    pass