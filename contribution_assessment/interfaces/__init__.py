"""
服务接口模块

定义系统中各个服务的抽象接口，包括：
- 联盟服务接口
- 模拟服务接口
- Shapley计算服务接口
- OPRO优化服务接口
- 状态管理服务接口
- 阶段协调器接口
- 周期性优化服务接口
"""

from .coalition_service import ICoalitionService
from .simulation_service import ISimulationService
from .shapley_service import IShapleyService
from .opro_service import IOPROService
from .state_service import IStateManager
from .assessment_service import IAssessmentService
from .trading_calendar import ITradingCalendar
from .phase_coordinator import IPhaseCoordinator
from .weekly_optimization_service import IWeeklyOptimizationService, WeeklyOptimizationConfig, WeeklyOptimizationResult

__all__ = [
    'ICoalitionService',
    'ISimulationService',
    'IShapleyService',
    'IOPROService',
    'IStateManager',
    'IAssessmentService',
    'ITradingCalendar',
    'IPhaseCoordinator',
    'IWeeklyOptimizationService',
    'WeeklyOptimizationConfig',
    'WeeklyOptimizationResult'
]