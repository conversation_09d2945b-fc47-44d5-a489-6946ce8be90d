"""
配置管理器

提供集中化的配置管理功能，支持：
- 多层级配置
- 环境变量覆盖
- 配置验证和类型转换
- 配置数据模型
"""

import os
import json
import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Type, Union
from dataclasses import dataclass, field
from pathlib import Path


@dataclass
class AssessmentConfig:
    """评估配置数据模型"""
    start_date: str
    end_date: str
    stocks: List[str]
    starting_cash: int = 100000
    risk_free_rate: float = 0.02
    simulation_days: Optional[int] = None
    enable_concurrent_execution: bool = True
    max_concurrent_api_calls: int = 5
    trading_days_per_week: int = 5
    
    def __post_init__(self):
        """配置验证"""
        if self.starting_cash <= 0:
            raise ValueError("starting_cash must be positive")
        if not (0 <= self.risk_free_rate <= 1):
            raise ValueError("risk_free_rate must be between 0 and 1")
        if self.max_concurrent_api_calls <= 0:
            raise ValueError("max_concurrent_api_calls must be positive")
        if not (1 <= self.trading_days_per_week <= 7):
            raise ValueError("trading_days_per_week must be between 1 and 7")


@dataclass
class OPROConfig:
    """OPRO配置数据模型"""
    optimization_frequency: str = "weekly"
    candidates_per_generation: int = 8
    historical_weeks_to_consider: int = 4
    temperature: float = 0.7
    max_optimization_iterations: int = 10
    convergence_threshold: float = 0.01
    enable_cache: bool = True
    parallel_evaluation: bool = True
    max_workers: int = 4
    
    def __post_init__(self):
        """配置验证"""
        valid_frequencies = ["daily", "weekly", "monthly"]
        if self.optimization_frequency not in valid_frequencies:
            raise ValueError(f"optimization_frequency must be one of {valid_frequencies}")
        if self.candidates_per_generation <= 0:
            raise ValueError("candidates_per_generation must be positive")
        if self.historical_weeks_to_consider <= 0:
            raise ValueError("historical_weeks_to_consider must be positive")
        if not (0 <= self.temperature <= 2):
            raise ValueError("temperature must be between 0 and 2 (inclusive)")
        if self.max_optimization_iterations <= 0:
            raise ValueError("max_optimization_iterations must be positive")
        if not (0 < self.convergence_threshold < 1):
            raise ValueError("convergence_threshold must be between 0 and 1")
        if self.max_workers <= 0:
            raise ValueError("max_workers must be positive")


class ConfigurationError(Exception):
    """配置相关异常"""
    pass


class IConfigurationManager(ABC):
    """配置管理器接口"""
    
    @abstractmethod
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        pass
    
    @abstractmethod
    def get_typed_config(self, key: str, config_type: Type, default: Any = None) -> Any:
        """获取类型化配置"""
        pass
    
    @abstractmethod
    def set_config(self, key: str, value: Any) -> None:
        """设置配置值"""
        pass
    
    @abstractmethod
    def has_config(self, key: str) -> bool:
        """检查配置是否存在"""
        pass
    
    @abstractmethod
    def get_assessment_config(self) -> AssessmentConfig:
        """获取评估配置"""
        pass
    
    @abstractmethod
    def get_opro_config(self) -> OPROConfig:
        """获取OPRO配置"""
        pass


class ConfigurationManager(IConfigurationManager):
    """配置管理器实现"""
    
    def __init__(self, config_dict: Optional[Dict[str, Any]] = None,
                 config_file_path: Optional[str] = None,
                 enable_env_override: bool = True):
        """
        初始化配置管理器
        
        Args:
            config_dict: 配置字典
            config_file_path: 配置文件路径
            enable_env_override: 是否启用环境变量覆盖
        """
        self._config: Dict[str, Any] = {}
        self._config_dict = config_dict
        self._config_file_path = config_file_path
        self._enable_env_override = enable_env_override
        self._logger = logging.getLogger(__name__)
        
        # 加载配置
        if config_dict:
            self._config.update(config_dict)
        
        if config_file_path:
            self._load_config_file(config_file_path)
        
        # 应用环境变量覆盖
        if enable_env_override:
            self._apply_env_overrides()
    
    def _load_config_file(self, file_path: str) -> None:
        """加载配置文件"""
        try:
            path = Path(file_path)
            if not path.exists():
                self._logger.warning(f"Configuration file not found: {file_path}")
                return
            
            with open(path, 'r', encoding='utf-8') as f:
                if path.suffix.lower() == '.json':
                    file_config = json.load(f)
                else:
                    raise ConfigurationError(f"Unsupported config file format: {path.suffix}")
            
            self._config.update(file_config)
            self._logger.info(f"Loaded configuration from {file_path}")
            
        except Exception as e:
            raise ConfigurationError(f"Failed to load config file {file_path}: {e}")
    
    def _apply_env_overrides(self) -> None:
        """应用环境变量覆盖"""
        env_prefix = "ASSESSMENT_"
        
        for key, value in os.environ.items():
            if key.startswith(env_prefix):
                config_key = key[len(env_prefix):].lower().replace('_', '.')
                # 尝试类型转换
                converted_value = self._convert_env_value(value)
                self._set_nested_config(config_key, converted_value)
                self._logger.debug(f"Applied env override: {config_key} = {converted_value}")
    
    def _set_nested_config(self, key: str, value: Any) -> None:
        """设置嵌套配置（内部方法）"""
        keys = key.split('.')
        config = self._config
        
        # 创建嵌套结构
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def _convert_env_value(self, value: str) -> Any:
        """转换环境变量值的类型"""
        # 尝试转换为布尔值
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # 尝试转换为整数
        try:
            return int(value)
        except ValueError:
            pass
        
        # 尝试转换为浮点数
        try:
            return float(value)
        except ValueError:
            pass
        
        # 尝试转换为JSON
        try:
            return json.loads(value)
        except (json.JSONDecodeError, ValueError):
            pass
        
        # 返回原始字符串
        return value
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        # 支持嵌套键，如 "database.host"
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def get_typed_config(self, key: str, config_type: Type, default: Any = None) -> Any:
        """获取类型化配置"""
        value = self.get_config(key, default)
        
        if value is None:
            return default
        
        try:
            if config_type == bool:
                if isinstance(value, str):
                    return value.lower() in ('true', '1', 'yes', 'on')
                return bool(value)
            elif config_type == int:
                return int(value)
            elif config_type == float:
                return float(value)
            elif config_type == str:
                return str(value)
            elif config_type == list:
                if isinstance(value, str):
                    # 尝试解析JSON数组
                    return json.loads(value)
                return list(value)
            else:
                return config_type(value)
        except (ValueError, TypeError) as e:
            self._logger.warning(f"Failed to convert config {key} to {config_type}: {e}")
            return default
    
    def set_config(self, key: str, value: Any) -> None:
        """设置配置值"""
        keys = key.split('.')
        config = self._config

        # 创建嵌套结构
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]

        config[keys[-1]] = value
        self._logger.debug(f"Set config: {key} = {value}")

    def update_config(self, updates: Dict[str, Any]) -> None:
        """批量更新配置"""
        for key, value in updates.items():
            if isinstance(value, dict) and key in self._config and isinstance(self._config[key], dict):
                # 递归更新嵌套字典
                self._config[key].update(value)
            else:
                # 直接设置值
                self._config[key] = value
        self._logger.debug(f"Updated config with {len(updates)} items")
    
    def has_config(self, key: str) -> bool:
        """检查配置是否存在"""
        return self.get_config(key) is not None
    
    def get_assessment_config(self) -> AssessmentConfig:
        """获取评估配置"""
        try:
            config_data = {
                'start_date': self.get_config('assessment.start_date', '2024-01-01'),
                'end_date': self.get_config('assessment.end_date', '2024-12-31'),
                'stocks': self.get_config('assessment.stocks', ['AAPL', 'GOOGL', 'MSFT']),
                'starting_cash': self.get_typed_config('assessment.starting_cash', int, 100000),
                'risk_free_rate': self.get_typed_config('assessment.risk_free_rate', float, 0.02),
                'simulation_days': self.get_typed_config('assessment.simulation_days', int, None),
                'enable_concurrent_execution': self.get_typed_config('assessment.enable_concurrent_execution', bool, True),
                'max_concurrent_api_calls': self.get_typed_config('assessment.max_concurrent_api_calls', int, 60),
                'trading_days_per_week': self.get_typed_config('assessment.trading_days_per_week', int, 5)
            }
            
            return AssessmentConfig(**config_data)
            
        except Exception as e:
            raise ConfigurationError(f"Failed to create AssessmentConfig: {e}")
    
    def get_opro_config(self) -> OPROConfig:
        """获取OPRO配置"""
        try:
            config_data = {
                'optimization_frequency': self.get_config('opro.optimization_frequency', 'weekly'),
                'candidates_per_generation': self.get_typed_config('opro.candidates_per_generation', int, 8),
                'historical_weeks_to_consider': self.get_typed_config('opro.historical_weeks_to_consider', int, 4),
                'temperature': self.get_typed_config('opro.temperature', float, 0.7),
                'max_optimization_iterations': self.get_typed_config('opro.max_optimization_iterations', int, 10),
                'convergence_threshold': self.get_typed_config('opro.convergence_threshold', float, 0.01),
                'enable_cache': self.get_typed_config('opro.enable_cache', bool, True),
                'parallel_evaluation': self.get_typed_config('opro.parallel_evaluation', bool, True),
                'max_workers': self.get_typed_config('opro.max_workers', int, 4)
            }
            
            return OPROConfig(**config_data)
            
        except Exception as e:
            raise ConfigurationError(f"Failed to create OPROConfig: {e}")
    
    def get_all_configs(self) -> Dict[str, Any]:
        """获取所有配置"""
        return self._config.copy()
    
    def validate_config(self) -> List[str]:
        """验证配置，返回错误列表"""
        errors = []
        
        # 验证必需的配置项
        required_keys = [
            'start_date', 'end_date', 'stocks'
        ]
        
        for key in required_keys:
            if not self.get_config(key):
                errors.append(f"Required configuration key '{key}' is missing or empty")
        
        # 验证配置结构
        structure_errors = self._validate_config_structure()
        errors.extend(structure_errors)
        
        # 验证类型化配置
        try:
            self.get_assessment_config()
        except Exception as e:
            errors.append(f"Assessment config validation failed: {e}")
        
        try:
            self.get_opro_config()
        except Exception as e:
            errors.append(f"OPRO config validation failed: {e}")
        
        # 验证环境变量
        env_errors = self._validate_environment_variables()
        errors.extend(env_errors)
        
        return errors
    
    def _validate_config_structure(self) -> List[str]:
        """验证配置结构"""
        errors = []
        
        # 验证数据类型
        type_validations = {
            'starting_cash': (int, float),
            'risk_free_rate': (int, float),
            'max_concurrent_api_calls': int,
            'trading_days_per_week': int,
            'stocks': list,
            'enable_concurrent_execution': bool
        }
        
        for key, expected_types in type_validations.items():
            value = self.get_config(key)
            if value is not None and not isinstance(value, expected_types):
                errors.append(f"Configuration '{key}' must be of type {expected_types}, got {type(value)}")
        
        # 验证数值范围
        range_validations = {
            'starting_cash': (1, float('inf')),
            'risk_free_rate': (0, 1),
            'max_concurrent_api_calls': (1, 100),
            'trading_days_per_week': (1, 7),
            'opro.temperature': (0.1, 2.0),
            'opro.candidates_per_generation': (1, 50),
            'opro.max_optimization_iterations': (1, 100)
        }
        
        for key, (min_val, max_val) in range_validations.items():
            value = self.get_config(key)
            if value is not None and isinstance(value, (int, float)):
                if not (min_val <= value <= max_val):
                    errors.append(f"Configuration '{key}' must be between {min_val} and {max_val}, got {value}")
        
        return errors
    
    def _validate_environment_variables(self) -> List[str]:
        """验证环境变量"""
        errors = []
        
        # 检查必需的环境变量
        required_env_vars = ['ZHIPUAI_API_KEY']
        
        for env_var in required_env_vars:
            if not os.environ.get(env_var):
                errors.append(f"Required environment variable '{env_var}' is not set")
        
        # 检查可选但推荐的环境变量
        optional_env_vars = ['OPENAI_API_KEY', 'ALPHAVANTAGE_API_KEY']
        
        for env_var in optional_env_vars:
            if not os.environ.get(env_var):
                # 这些只是警告，不是错误
                pass
        
        return errors
    
    def validate_config_with_defaults(self) -> Dict[str, Any]:
        """验证配置并应用默认值"""
        validated_config = self._config.copy()
        
        # 应用默认值
        defaults = {
            'starting_cash': 100000,
            'risk_free_rate': 0.02,
            'max_concurrent_api_calls': 60,
            'trading_days_per_week': 5,
            'enable_concurrent_execution': True,
            'simulation_days': None,
            'opro.optimization_frequency': 'weekly',
            'opro.candidates_per_generation': 8,
            'opro.historical_weeks_to_consider': 4,
            'opro.temperature': 0.7,
            'opro.max_optimization_iterations': 10,
            'opro.convergence_threshold': 0.01,
            'opro.enable_cache': True,
            'opro.parallel_evaluation': True,
            'opro.max_workers': 4
        }
        
        for key, default_value in defaults.items():
            if self.get_config(key) is None:
                self.set_config(key, default_value)
        
        return validated_config
    
    def set_config(self, key: str, value: Any) -> None:
        """设置配置项"""
        keys = key.split('.')
        current = self._config
        
        # 导航到目标位置
        for k in keys[:-1]:
            if k not in current:
                current[k] = {}
            current = current[k]
        
        # 设置值
        current[keys[-1]] = value
        self._logger.debug(f"Set config {key} = {value}")
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            'config_sources': self.get_config_sources(),
            'total_keys': len(self.get_all_keys()),
            'validation_errors': self.validate_config(),
            'required_env_vars': {
                'ZHIPUAI_API_KEY': bool(os.environ.get('ZHIPUAI_API_KEY')),
                'OPENAI_API_KEY': bool(os.environ.get('OPENAI_API_KEY'))
            }
        }
    
    def get_config_sources(self) -> List[str]:
        """获取配置来源列表"""
        sources = []
        
        if self._config_file_path:
            sources.append(f"file: {self._config_file_path}")
        
        if self._config_dict:
            sources.append("dict: provided")
        
        if self._enable_env_override:
            sources.append("env: enabled")
        
        return sources
    
    def get_all_keys(self) -> List[str]:
        """获取所有配置键"""
        def extract_keys(obj, prefix=""):
            keys = []
            if isinstance(obj, dict):
                for key, value in obj.items():
                    full_key = f"{prefix}.{key}" if prefix else key
                    keys.append(full_key)
                    if isinstance(value, dict):
                        keys.extend(extract_keys(value, full_key))
            return keys
        
        return extract_keys(self._config)