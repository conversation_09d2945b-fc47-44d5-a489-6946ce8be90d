"""
统一错误处理框架

提供集中化的错误处理功能，支持：
- 异常层次结构
- 错误恢复和重试机制
- 错误上下文和追踪
- 与事件总线和日志系统集成
"""

import logging
import traceback
import time
from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Type, Union
from dataclasses import dataclass, field
from datetime import datetime
from functools import wraps

from .event_bus import IEventBus


class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """错误类别"""
    CONFIGURATION = "configuration"
    VALIDATION = "validation"
    NETWORK = "network"
    DATABASE = "database"
    BUSINESS_LOGIC = "business_logic"
    EXTERNAL_SERVICE = "external_service"
    SYSTEM = "system"
    UNKNOWN = "unknown"


@dataclass
class ErrorContext:
    """错误上下文信息"""
    operation: str
    component: str
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    request_id: Optional[str] = None
    additional_data: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)


@dataclass
class ErrorInfo:
    """错误信息"""
    error_id: str
    error_type: str
    message: str
    severity: ErrorSeverity
    category: ErrorCategory
    context: ErrorContext
    stack_trace: Optional[str] = None
    inner_error: Optional['ErrorInfo'] = None
    retry_count: int = 0
    max_retries: int = 0
    additional_data: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)


# 异常层次结构
class AssessmentError(Exception):
    """评估系统基础异常"""
    
    def __init__(self, message: str, 
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM,
                 category: ErrorCategory = ErrorCategory.UNKNOWN,
                 context: Optional[ErrorContext] = None,
                 inner_exception: Optional[Exception] = None):
        super().__init__(message)
        self.message = message
        self.severity = severity
        self.category = category
        self.context = context
        self.inner_exception = inner_exception


class ConfigurationError(AssessmentError):
    """配置错误"""
    
    def __init__(self, message: str, context: Optional[ErrorContext] = None):
        super().__init__(
            message=message,
            severity=ErrorSeverity.HIGH,
            category=ErrorCategory.CONFIGURATION,
            context=context
        )


class ValidationError(AssessmentError):
    """验证错误"""
    
    def __init__(self, message: str, field_name: Optional[str] = None, 
                 context: Optional[ErrorContext] = None):
        super().__init__(
            message=message,
            severity=ErrorSeverity.MEDIUM,
            category=ErrorCategory.VALIDATION,
            context=context
        )
        self.field_name = field_name


class NetworkError(AssessmentError):
    """网络错误"""
    
    def __init__(self, message: str, url: Optional[str] = None,
                 status_code: Optional[int] = None,
                 context: Optional[ErrorContext] = None):
        super().__init__(
            message=message,
            severity=ErrorSeverity.HIGH,
            category=ErrorCategory.NETWORK,
            context=context
        )
        self.url = url
        self.status_code = status_code


class DatabaseError(AssessmentError):
    """数据库错误"""
    
    def __init__(self, message: str, query: Optional[str] = None,
                 context: Optional[ErrorContext] = None):
        super().__init__(
            message=message,
            severity=ErrorSeverity.HIGH,
            category=ErrorCategory.DATABASE,
            context=context
        )
        self.query = query


class BusinessLogicError(AssessmentError):
    """业务逻辑错误"""
    
    def __init__(self, message: str, context: Optional[ErrorContext] = None):
        super().__init__(
            message=message,
            severity=ErrorSeverity.MEDIUM,
            category=ErrorCategory.BUSINESS_LOGIC,
            context=context
        )


class ExternalServiceError(AssessmentError):
    """外部服务错误"""
    
    def __init__(self, message: str, service_name: Optional[str] = None,
                 context: Optional[ErrorContext] = None):
        super().__init__(
            message=message,
            severity=ErrorSeverity.HIGH,
            category=ErrorCategory.EXTERNAL_SERVICE,
            context=context
        )
        self.service_name = service_name


class SystemError(AssessmentError):
    """系统错误"""
    
    def __init__(self, message: str, context: Optional[ErrorContext] = None):
        super().__init__(
            message=message,
            severity=ErrorSeverity.CRITICAL,
            category=ErrorCategory.SYSTEM,
            context=context
        )


class IErrorHandler(ABC):
    """错误处理器接口"""
    
    @abstractmethod
    def handle_error(self, error: Exception, context: Optional[ErrorContext] = None) -> ErrorInfo:
        """处理错误"""
        pass
    
    @abstractmethod
    def register_recovery_strategy(self, error_type: Type[Exception], 
                                 strategy: Callable[[Exception, ErrorContext], Any]) -> None:
        """注册错误恢复策略"""
        pass


class RetryStrategy:
    """重试策略"""
    
    def __init__(self, max_retries: int = 3, delay: float = 1.0, 
                 backoff_factor: float = 2.0, max_delay: float = 60.0):
        self.max_retries = max_retries
        self.delay = delay
        self.backoff_factor = backoff_factor
        self.max_delay = max_delay
    
    def should_retry(self, error: Exception, retry_count: int) -> bool:
        """判断是否应该重试"""
        if retry_count >= self.max_retries:
            return False
        
        # 某些错误类型不应该重试
        non_retryable_errors = (ConfigurationError, ValidationError, BusinessLogicError)
        if isinstance(error, non_retryable_errors):
            return False
        
        return True
    
    def get_delay(self, retry_count: int) -> float:
        """获取重试延迟时间"""
        delay = self.delay * (self.backoff_factor ** retry_count)
        return min(delay, self.max_delay)


class ErrorHandler(IErrorHandler):
    """错误处理器实现"""
    
    def __init__(self, logger: Optional[logging.Logger] = None,
                 event_bus: Optional[IEventBus] = None,
                 retry_strategy: Optional[RetryStrategy] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.event_bus = event_bus
        self.retry_strategy = retry_strategy or RetryStrategy()
        self._recovery_strategies: Dict[Type[Exception], Callable] = {}
        self._error_counter = 0
    
    def handle_error(self, error: Exception, context: Optional[ErrorContext] = None) -> ErrorInfo:
        """处理错误"""
        self._error_counter += 1
        error_id = f"ERR_{int(time.time())}_{self._error_counter}"
        
        # 创建错误信息
        error_info = self._create_error_info(error_id, error, context)
        
        # 记录错误日志
        self._log_error(error_info)
        
        # 发布错误事件
        if self.event_bus:
            self._publish_error_event(error_info)
        
        # 尝试错误恢复
        recovery_result = self._attempt_recovery(error, context)
        if recovery_result:
            self.logger.info(f"Error {error_id} recovered successfully")
            error_info.additional_data = {"recovery_result": recovery_result}
        
        return error_info
    
    def _create_error_info(self, error_id: str, error: Exception, 
                          context: Optional[ErrorContext]) -> ErrorInfo:
        """创建错误信息"""
        if isinstance(error, AssessmentError):
            severity = error.severity
            category = error.category
            error_context = error.context or context
        else:
            severity = ErrorSeverity.MEDIUM
            category = self._categorize_error(error)
            error_context = context
        
        return ErrorInfo(
            error_id=error_id,
            error_type=type(error).__name__,
            message=str(error),
            severity=severity,
            category=category,
            context=error_context or ErrorContext("unknown", "unknown"),
            stack_trace=traceback.format_exc(),
            inner_error=self._get_inner_error_info(error) if hasattr(error, 'inner_exception') else None
        )
    
    def _categorize_error(self, error: Exception) -> ErrorCategory:
        """自动分类错误"""
        error_type = type(error).__name__.lower()
        
        if any(keyword in error_type for keyword in ['config', 'setting']):
            return ErrorCategory.CONFIGURATION
        elif any(keyword in error_type for keyword in ['validation', 'value', 'argument']):
            return ErrorCategory.VALIDATION
        elif any(keyword in error_type for keyword in ['network', 'connection', 'timeout', 'http']):
            return ErrorCategory.NETWORK
        elif any(keyword in error_type for keyword in ['database', 'sql', 'db']):
            return ErrorCategory.DATABASE
        elif any(keyword in error_type for keyword in ['system', 'os', 'memory', 'disk']):
            return ErrorCategory.SYSTEM
        else:
            return ErrorCategory.UNKNOWN
    
    def _get_inner_error_info(self, error: AssessmentError) -> Optional[ErrorInfo]:
        """获取内部错误信息"""
        if error.inner_exception:
            return self._create_error_info(
                f"INNER_{int(time.time())}_{self._error_counter}",
                error.inner_exception,
                error.context
            )
        return None
    
    def _log_error(self, error_info: ErrorInfo) -> None:
        """记录错误日志"""
        log_level = {
            ErrorSeverity.LOW: logging.INFO,
            ErrorSeverity.MEDIUM: logging.WARNING,
            ErrorSeverity.HIGH: logging.ERROR,
            ErrorSeverity.CRITICAL: logging.CRITICAL
        }.get(error_info.severity, logging.ERROR)
        
        log_message = (
            f"[{error_info.error_id}] {error_info.error_type}: {error_info.message} "
            f"| Component: {error_info.context.component} "
            f"| Operation: {error_info.context.operation} "
            f"| Category: {error_info.category.value} "
            f"| Severity: {error_info.severity.value}"
        )
        
        self.logger.log(log_level, log_message)
        
        # 记录堆栈跟踪（仅对高严重性错误）
        if error_info.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL] and error_info.stack_trace:
            self.logger.debug(f"Stack trace for {error_info.error_id}:\n{error_info.stack_trace}")
    
    def _publish_error_event(self, error_info: ErrorInfo) -> None:
        """发布错误事件"""
        try:
            self.event_bus.publish(
                event_type="error_occurred",
                data={
                    "error_id": error_info.error_id,
                    "error_type": error_info.error_type,
                    "message": error_info.message,
                    "severity": error_info.severity.value,
                    "category": error_info.category.value,
                    "component": error_info.context.component,
                    "operation": error_info.context.operation
                },
                source="error_handler"
            )
        except Exception as e:
            self.logger.warning(f"Failed to publish error event: {e}")
    
    def _attempt_recovery(self, error: Exception, context: Optional[ErrorContext]) -> Any:
        """尝试错误恢复"""
        error_type = type(error)
        
        # 查找匹配的恢复策略
        for registered_type, strategy in self._recovery_strategies.items():
            if isinstance(error, registered_type):
                try:
                    return strategy(error, context)
                except Exception as recovery_error:
                    self.logger.warning(f"Recovery strategy failed: {recovery_error}")
        
        return None
    
    def register_recovery_strategy(self, error_type: Type[Exception], 
                                 strategy: Callable[[Exception, ErrorContext], Any]) -> None:
        """注册错误恢复策略"""
        self._recovery_strategies[error_type] = strategy
        self.logger.debug(f"Registered recovery strategy for {error_type.__name__}")
    
    def with_retry(self, func: Callable, *args, **kwargs) -> Any:
        """带重试的函数执行"""
        retry_count = 0
        last_error = None
        
        while retry_count <= self.retry_strategy.max_retries:
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_error = e
                
                if not self.retry_strategy.should_retry(e, retry_count):
                    break
                
                if retry_count < self.retry_strategy.max_retries:
                    delay = self.retry_strategy.get_delay(retry_count)
                    self.logger.info(f"Retrying in {delay}s (attempt {retry_count + 1}/{self.retry_strategy.max_retries})")
                    time.sleep(delay)
                
                retry_count += 1
        
        # 所有重试都失败了，处理最后的错误
        if last_error:
            error_info = self.handle_error(last_error)
            error_info.retry_count = retry_count
            error_info.max_retries = self.retry_strategy.max_retries
            raise last_error
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        return {
            "total_errors": self._error_counter,
            "registered_recovery_strategies": len(self._recovery_strategies),
            "retry_strategy": {
                "max_retries": self.retry_strategy.max_retries,
                "base_delay": self.retry_strategy.delay,
                "backoff_factor": self.retry_strategy.backoff_factor
            }
        }


def error_handler_decorator(error_handler: ErrorHandler, 
                          context: Optional[ErrorContext] = None):
    """错误处理装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                error_context = context or ErrorContext(
                    operation=func.__name__,
                    component=func.__module__
                )
                error_info = error_handler.handle_error(e, error_context)
                # 重新抛出异常，让调用者决定如何处理
                raise
        return wrapper
    return decorator


def retry_on_error(error_handler: ErrorHandler):
    """重试装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            return error_handler.with_retry(func, *args, **kwargs)
        return wrapper
    return decorator