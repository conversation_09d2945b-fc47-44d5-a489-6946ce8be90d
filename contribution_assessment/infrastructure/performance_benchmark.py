"""
性能基准测试系统

提供全面的性能基准测试功能，支持：
- 重构前后的性能基准对比
- 自动化的性能回归测试
- 负载测试和压力测试场景
- 性能报告和趋势分析
"""

import json
import logging
import platform
import statistics
import sys
import threading
import time
from abc import ABC, abstractmethod
from concurrent.futures import ThreadPoolExecutor, as_completed
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Tuple, Union
import tracemalloc
import psutil

from .performance_monitor import PerformanceMonitor, PerformanceMetric
from .resource_manager import ResourceManager
from .event_bus import IEventBus


@dataclass
class BenchmarkResult:
    """基准测试结果"""
    test_name: str
    execution_time: float
    memory_usage: int
    cpu_usage: float
    success: bool
    error_message: Optional[str] = None
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class BenchmarkSuite:
    """基准测试套件"""
    name: str
    description: str
    tests: List[Callable] = field(default_factory=list)
    setup_func: Optional[Callable] = None
    teardown_func: Optional[Callable] = None
    timeout: float = 300.0  # 5分钟超时
    iterations: int = 1


@dataclass
class PerformanceBaseline:
    """性能基线"""
    test_name: str
    baseline_time: float
    baseline_memory: int
    baseline_cpu: float
    timestamp: datetime
    version: str
    environment: Dict[str, Any] = field(default_factory=dict)


@dataclass
class RegressionTestResult:
    """回归测试结果"""
    test_name: str
    current_result: BenchmarkResult
    baseline: PerformanceBaseline
    time_regression: float  # 时间回归百分比
    memory_regression: float  # 内存回归百分比
    cpu_regression: float  # CPU回归百分比
    is_regression: bool
    severity: str  # low, medium, high, critical


@dataclass
class LoadTestConfig:
    """负载测试配置"""
    concurrent_users: int
    duration: float  # 秒
    ramp_up_time: float  # 秒
    target_function: Callable
    test_data_generator: Optional[Callable] = None


@dataclass
class LoadTestResult:
    """负载测试结果"""
    config: LoadTestConfig
    total_requests: int
    successful_requests: int
    failed_requests: int
    average_response_time: float
    min_response_time: float
    max_response_time: float
    percentile_95: float
    percentile_99: float
    throughput: float  # 请求/秒
    error_rate: float
    timestamp: datetime = field(default_factory=datetime.now)


class IBenchmarkRunner(ABC):
    """基准测试运行器接口"""
    
    @abstractmethod
    def run_benchmark(self, test_func: Callable, iterations: int = 1) -> BenchmarkResult:
        """运行单个基准测试"""
        pass
    
    @abstractmethod
    def run_suite(self, suite: BenchmarkSuite) -> List[BenchmarkResult]:
        """运行基准测试套件"""
        pass
    
    @abstractmethod
    def compare_with_baseline(self, results: List[BenchmarkResult], 
                            baselines: List[PerformanceBaseline]) -> List[RegressionTestResult]:
        """与基线对比"""
        pass


class PerformanceBenchmark(IBenchmarkRunner):
    """性能基准测试实现"""
    
    def __init__(self, logger: Optional[logging.Logger] = None,
                 event_bus: Optional[IEventBus] = None,
                 performance_monitor: Optional[PerformanceMonitor] = None,
                 resource_manager: Optional[ResourceManager] = None,
                 baseline_file: str = "performance_baselines.json"):
        self.logger = logger or logging.getLogger(__name__)
        self.event_bus = event_bus
        self.performance_monitor = performance_monitor
        self.resource_manager = resource_manager
        self.baseline_file = Path(baseline_file)
        
        # 回归阈值配置
        self.regression_thresholds = {
            "time": {"low": 5.0, "medium": 15.0, "high": 30.0, "critical": 50.0},
            "memory": {"low": 10.0, "medium": 25.0, "high": 50.0, "critical": 100.0},
            "cpu": {"low": 10.0, "medium": 25.0, "high": 50.0, "critical": 100.0}
        }
        
        # 系统信息
        self._system_info = self._collect_system_info()
    
    def _collect_system_info(self) -> Dict[str, Any]:
        """收集系统信息"""
        return {
            "cpu_count": psutil.cpu_count(),
            "memory_total": psutil.virtual_memory().total,
            "python_version": f"{sys.version_info.major}.{sys.version_info.minor}",
            "platform": platform.system(),
            "architecture": platform.machine()
        }
    
    def run_benchmark(self, test_func: Callable, iterations: int = 1) -> BenchmarkResult:
        """运行单个基准测试"""
        test_name = getattr(test_func, '__name__', 'unknown_test')
        
        # 启用内存跟踪
        tracemalloc.start()
        process = psutil.Process()
        
        execution_times = []
        memory_usages = []
        cpu_usages = []
        success = True
        error_message = None
        
        try:
            for i in range(iterations):
                # 清理内存
                if self.resource_manager:
                    self.resource_manager.cleanup_resources()
                
                # 记录开始状态
                start_memory = tracemalloc.get_traced_memory()[0]
                start_cpu_time = time.process_time()
                start_time = time.time()
                
                try:
                    # 执行测试函数
                    result = test_func()
                    
                    # 记录结束状态
                    end_time = time.time()
                    end_cpu_time = time.process_time()
                    end_memory = tracemalloc.get_traced_memory()[0]
                    
                    # 计算指标
                    execution_time = end_time - start_time
                    cpu_time = end_cpu_time - start_cpu_time
                    memory_delta = end_memory - start_memory
                    
                    execution_times.append(execution_time)
                    memory_usages.append(memory_delta)
                    cpu_usages.append(cpu_time)
                    
                except Exception as e:
                    success = False
                    error_message = str(e)
                    self.logger.error(f"Benchmark test {test_name} failed: {e}")
                    break
        
        finally:
            tracemalloc.stop()
        
        # 计算平均值
        avg_execution_time = statistics.mean(execution_times) if execution_times else 0.0
        avg_memory_usage = int(statistics.mean(memory_usages)) if memory_usages else 0
        avg_cpu_usage = statistics.mean(cpu_usages) if cpu_usages else 0.0
        
        result = BenchmarkResult(
            test_name=test_name,
            execution_time=avg_execution_time,
            memory_usage=avg_memory_usage,
            cpu_usage=avg_cpu_usage,
            success=success,
            error_message=error_message,
            metadata={
                "iterations": iterations,
                "execution_times": execution_times,
                "memory_usages": memory_usages,
                "cpu_usages": cpu_usages,
                "system_info": self._system_info
            }
        )
        
        # 发布基准测试事件
        if self.event_bus:
            self.event_bus.publish(
                event_type="benchmark_completed",
                data={
                    "test_name": test_name,
                    "execution_time": avg_execution_time,
                    "memory_usage": avg_memory_usage,
                    "success": success
                },
                source="performance_benchmark"
            )
        
        return result
    
    def run_suite(self, suite: BenchmarkSuite) -> List[BenchmarkResult]:
        """运行基准测试套件"""
        self.logger.info(f"Running benchmark suite: {suite.name}")
        results = []
        
        try:
            # 执行设置函数
            if suite.setup_func:
                suite.setup_func()
            
            # 运行所有测试
            for test_func in suite.tests:
                self.logger.info(f"Running test: {getattr(test_func, '__name__', 'unknown')}")
                
                try:
                    result = self.run_benchmark(test_func, suite.iterations)
                    results.append(result)
                except Exception as e:
                    self.logger.error(f"Test failed: {e}")
                    results.append(BenchmarkResult(
                        test_name=getattr(test_func, '__name__', 'unknown'),
                        execution_time=0.0,
                        memory_usage=0,
                        cpu_usage=0.0,
                        success=False,
                        error_message=str(e)
                    ))
        
        finally:
            # 执行清理函数
            if suite.teardown_func:
                try:
                    suite.teardown_func()
                except Exception as e:
                    self.logger.error(f"Teardown failed: {e}")
        
        # 发布套件完成事件
        if self.event_bus:
            self.event_bus.publish(
                event_type="benchmark_suite_completed",
                data={
                    "suite_name": suite.name,
                    "total_tests": len(suite.tests),
                    "successful_tests": sum(1 for r in results if r.success),
                    "failed_tests": sum(1 for r in results if not r.success)
                },
                source="performance_benchmark"
            )
        
        return results
    
    def compare_with_baseline(self, results: List[BenchmarkResult], 
                            baselines: List[PerformanceBaseline]) -> List[RegressionTestResult]:
        """与基线对比"""
        baseline_dict = {b.test_name: b for b in baselines}
        regression_results = []
        
        for result in results:
            if result.test_name not in baseline_dict:
                self.logger.warning(f"No baseline found for test: {result.test_name}")
                continue
            
            baseline = baseline_dict[result.test_name]
            
            # 计算回归百分比
            time_regression = self._calculate_regression(
                result.execution_time, baseline.baseline_time
            )
            memory_regression = self._calculate_regression(
                result.memory_usage, baseline.baseline_memory
            )
            cpu_regression = self._calculate_regression(
                result.cpu_usage, baseline.baseline_cpu
            )
            
            # 判断是否为回归
            is_regression = (
                time_regression > self.regression_thresholds["time"]["low"] or
                memory_regression > self.regression_thresholds["memory"]["low"] or
                cpu_regression > self.regression_thresholds["cpu"]["low"]
            )
            
            # 确定严重程度
            severity = self._determine_severity(time_regression, memory_regression, cpu_regression)
            
            regression_result = RegressionTestResult(
                test_name=result.test_name,
                current_result=result,
                baseline=baseline,
                time_regression=time_regression,
                memory_regression=memory_regression,
                cpu_regression=cpu_regression,
                is_regression=is_regression,
                severity=severity
            )
            
            regression_results.append(regression_result)
            
            # 发布回归检测事件
            if is_regression and self.event_bus:
                self.event_bus.publish(
                    event_type="performance_regression_detected",
                    data={
                        "test_name": result.test_name,
                        "severity": severity,
                        "time_regression": time_regression,
                        "memory_regression": memory_regression,
                        "cpu_regression": cpu_regression
                    },
                    source="performance_benchmark"
                )
        
        return regression_results
    
    def _calculate_regression(self, current: float, baseline: float) -> float:
        """计算回归百分比"""
        if baseline == 0:
            return 0.0
        return ((current - baseline) / baseline) * 100
    
    def _determine_severity(self, time_reg: float, memory_reg: float, cpu_reg: float) -> str:
        """确定回归严重程度"""
        max_regression = max(time_reg, memory_reg, cpu_reg)
        
        if max_regression >= self.regression_thresholds["time"]["critical"]:
            return "critical"
        elif max_regression >= self.regression_thresholds["time"]["high"]:
            return "high"
        elif max_regression >= self.regression_thresholds["time"]["medium"]:
            return "medium"
        else:
            return "low"
    
    def save_baseline(self, results: List[BenchmarkResult], version: str) -> None:
        """保存性能基线"""
        baselines = []
        
        # 加载现有基线
        if self.baseline_file.exists():
            try:
                with open(self.baseline_file, 'r') as f:
                    existing_data = json.load(f)
                    baselines = [
                        PerformanceBaseline(**item) for item in existing_data
                        if item.get('version') != version  # 替换同版本的基线
                    ]
            except Exception as e:
                self.logger.error(f"Failed to load existing baselines: {e}")
        
        # 添加新基线
        for result in results:
            if result.success:
                baseline = PerformanceBaseline(
                    test_name=result.test_name,
                    baseline_time=result.execution_time,
                    baseline_memory=result.memory_usage,
                    baseline_cpu=result.cpu_usage,
                    timestamp=result.timestamp,
                    version=version,
                    environment=self._system_info
                )
                baselines.append(baseline)
        
        # 保存基线
        try:
            baseline_data = [
                {
                    "test_name": b.test_name,
                    "baseline_time": b.baseline_time,
                    "baseline_memory": b.baseline_memory,
                    "baseline_cpu": b.baseline_cpu,
                    "timestamp": b.timestamp.isoformat(),
                    "version": b.version,
                    "environment": b.environment
                }
                for b in baselines
            ]
            
            with open(self.baseline_file, 'w') as f:
                json.dump(baseline_data, f, indent=2)
            
            self.logger.info(f"Saved {len(baseline_data)} baselines to {self.baseline_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to save baselines: {e}")
    
    def load_baselines(self, version: Optional[str] = None) -> List[PerformanceBaseline]:
        """加载性能基线"""
        if not self.baseline_file.exists():
            return []
        
        try:
            with open(self.baseline_file, 'r') as f:
                data = json.load(f)
            
            baselines = []
            for item in data:
                if version is None or item.get('version') == version:
                    baseline = PerformanceBaseline(
                        test_name=item['test_name'],
                        baseline_time=item['baseline_time'],
                        baseline_memory=item['baseline_memory'],
                        baseline_cpu=item['baseline_cpu'],
                        timestamp=datetime.fromisoformat(item['timestamp']),
                        version=item['version'],
                        environment=item.get('environment', {})
                    )
                    baselines.append(baseline)
            
            return baselines
            
        except Exception as e:
            self.logger.error(f"Failed to load baselines: {e}")
            return []
    
    def run_load_test(self, config: LoadTestConfig) -> LoadTestResult:
        """运行负载测试"""
        self.logger.info(f"Starting load test with {config.concurrent_users} concurrent users")
        
        results = []
        start_time = time.time()
        end_time = start_time + config.duration
        
        def worker():
            """工作线程函数"""
            thread_results = []
            while time.time() < end_time:
                try:
                    # 生成测试数据
                    test_data = config.test_data_generator() if config.test_data_generator else None
                    
                    # 执行测试
                    request_start = time.time()
                    if test_data:
                        config.target_function(test_data)
                    else:
                        config.target_function()
                    request_end = time.time()
                    
                    thread_results.append({
                        'success': True,
                        'response_time': request_end - request_start
                    })
                    
                except Exception as e:
                    thread_results.append({
                        'success': False,
                        'response_time': 0.0,
                        'error': str(e)
                    })
            
            return thread_results
        
        # 启动工作线程
        with ThreadPoolExecutor(max_workers=config.concurrent_users) as executor:
            # 渐进式启动（ramp-up）
            futures = []
            ramp_up_delay = config.ramp_up_time / config.concurrent_users
            
            for i in range(config.concurrent_users):
                if i > 0:
                    time.sleep(ramp_up_delay)
                future = executor.submit(worker)
                futures.append(future)
            
            # 收集结果
            for future in as_completed(futures):
                try:
                    thread_results = future.result()
                    results.extend(thread_results)
                except Exception as e:
                    self.logger.error(f"Worker thread failed: {e}")
        
        # 分析结果
        successful_requests = sum(1 for r in results if r['success'])
        failed_requests = len(results) - successful_requests
        response_times = [r['response_time'] for r in results if r['success']]
        
        if response_times:
            avg_response_time = statistics.mean(response_times)
            min_response_time = min(response_times)
            max_response_time = max(response_times)
            percentile_95 = statistics.quantiles(response_times, n=20)[18]  # 95th percentile
            percentile_99 = statistics.quantiles(response_times, n=100)[98]  # 99th percentile
        else:
            avg_response_time = min_response_time = max_response_time = 0.0
            percentile_95 = percentile_99 = 0.0
        
        actual_duration = time.time() - start_time
        throughput = len(results) / actual_duration if actual_duration > 0 else 0.0
        error_rate = (failed_requests / len(results)) * 100 if results else 0.0
        
        load_result = LoadTestResult(
            config=config,
            total_requests=len(results),
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            average_response_time=avg_response_time,
            min_response_time=min_response_time,
            max_response_time=max_response_time,
            percentile_95=percentile_95,
            percentile_99=percentile_99,
            throughput=throughput,
            error_rate=error_rate
        )
        
        # 发布负载测试事件
        if self.event_bus:
            self.event_bus.publish(
                event_type="load_test_completed",
                data={
                    "concurrent_users": config.concurrent_users,
                    "duration": config.duration,
                    "total_requests": len(results),
                    "successful_requests": successful_requests,
                    "throughput": throughput,
                    "error_rate": error_rate
                },
                source="performance_benchmark"
            )
        
        return load_result
    
    def generate_performance_report(self, results: List[BenchmarkResult],
                                  regression_results: Optional[List[RegressionTestResult]] = None,
                                  load_results: Optional[List[LoadTestResult]] = None) -> Dict[str, Any]:
        """生成性能报告"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "system_info": self._system_info,
            "benchmark_summary": {
                "total_tests": len(results),
                "successful_tests": sum(1 for r in results if r.success),
                "failed_tests": sum(1 for r in results if not r.success),
                "total_execution_time": sum(r.execution_time for r in results),
                "average_execution_time": statistics.mean([r.execution_time for r in results]) if results else 0.0,
                "total_memory_usage": sum(r.memory_usage for r in results),
                "average_memory_usage": statistics.mean([r.memory_usage for r in results]) if results else 0.0
            },
            "test_results": [
                {
                    "test_name": r.test_name,
                    "execution_time": r.execution_time,
                    "memory_usage": r.memory_usage,
                    "cpu_usage": r.cpu_usage,
                    "success": r.success,
                    "error_message": r.error_message
                }
                for r in results
            ]
        }
        
        # 添加回归测试结果
        if regression_results:
            report["regression_analysis"] = {
                "total_regressions": sum(1 for r in regression_results if r.is_regression),
                "critical_regressions": sum(1 for r in regression_results if r.severity == "critical"),
                "high_regressions": sum(1 for r in regression_results if r.severity == "high"),
                "medium_regressions": sum(1 for r in regression_results if r.severity == "medium"),
                "low_regressions": sum(1 for r in regression_results if r.severity == "low"),
                "regression_details": [
                    {
                        "test_name": r.test_name,
                        "time_regression": r.time_regression,
                        "memory_regression": r.memory_regression,
                        "cpu_regression": r.cpu_regression,
                        "severity": r.severity,
                        "is_regression": r.is_regression
                    }
                    for r in regression_results
                ]
            }
        
        # 添加负载测试结果
        if load_results:
            report["load_test_summary"] = {
                "total_load_tests": len(load_results),
                "average_throughput": statistics.mean([r.throughput for r in load_results]) if load_results else 0.0,
                "average_error_rate": statistics.mean([r.error_rate for r in load_results]) if load_results else 0.0,
                "load_test_details": [
                    {
                        "concurrent_users": r.config.concurrent_users,
                        "duration": r.config.duration,
                        "total_requests": r.total_requests,
                        "throughput": r.throughput,
                        "error_rate": r.error_rate,
                        "average_response_time": r.average_response_time,
                        "percentile_95": r.percentile_95,
                        "percentile_99": r.percentile_99
                    }
                    for r in load_results
                ]
            }
        
        return report
    
    def save_report(self, report: Dict[str, Any], filename: str) -> None:
        """保存性能报告"""
        try:
            report_path = Path(filename)
            report_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(report_path, 'w') as f:
                json.dump(report, f, indent=2)
            
            self.logger.info(f"Performance report saved to {report_path}")
            
        except Exception as e:
            self.logger.error(f"Failed to save performance report: {e}")


# 装饰器和工具函数
def benchmark_test(name: Optional[str] = None, iterations: int = 1):
    """基准测试装饰器"""
    def decorator(func: Callable) -> Callable:
        func._benchmark_name = name or func.__name__
        func._benchmark_iterations = iterations
        return func
    return decorator


def create_benchmark_suite(name: str, description: str = "") -> BenchmarkSuite:
    """创建基准测试套件"""
    return BenchmarkSuite(name=name, description=description)


def add_test_to_suite(suite: BenchmarkSuite, test_func: Callable) -> None:
    """添加测试到套件"""
    suite.tests.append(test_func)