"""
应用启动器
处理系统初始化，包括：
- 配置加载、验证和服务注册
- 启动失败的错误处理和诊断
- 优雅关闭和资源清理机制
- 环境适配和功能开关管理
"""

import logging
import sys
import traceback
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from pathlib import Path
import json
import os
from datetime import datetime

from .configuration_manager import IConfigurationManager, ConfigurationManager, ConfigurationError
from .service_factory import IServiceFactory, ServiceFactory
from .service_registry import IServiceRegistry, ServiceRegistry
from .event_bus import IEventBus, EventBus
from .feature_toggle_manager import IFeatureToggleManager, FeatureToggleManager


@dataclass
class BootstrapResult:
    """启动结果"""
    success: bool
    services: Dict[str, Any]
    config_manager: Optional[IConfigurationManager] = None
    service_factory: Optional[IServiceFactory] = None
    error: Optional[str] = None
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.warnings is None:
            self.warnings = []


class ApplicationBootstrapperError(Exception):
    """应用启动器异常"""
    pass


class ConfigurationValidationError(ApplicationBootstrapperError):
    """配置验证异常"""
    pass


class ServiceInitializationError(ApplicationBootstrapperError):
    """服务初始化异常"""
    pass


class ApplicationBootstrapper:
    """
    应用启动器
    
    负责系统的初始化和启动流程，包括配置加载、服务创建、
    依赖注入配置、错误处理和优雅关闭机制。
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化应用启动器
        
        Args:
            logger: 日志记录器
        """
        self._logger = logger or self._create_default_logger()
        self._shutdown_handlers: List[Callable] = []
        self._is_initialized = False
        self._services: Dict[str, Any] = {}
        self._config_manager: Optional[IConfigurationManager] = None
        self._service_factory: Optional[IServiceFactory] = None
        self._event_bus: Optional[IEventBus] = None
        self._feature_toggle_manager: Optional[IFeatureToggleManager] = None
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger('ApplicationBootstrapper')
        if not logger.handlers:
            handler = logging.StreamHandler(sys.stdout)
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def bootstrap(self, 
                  config_file_path: Optional[str] = None,
                  config_dict: Optional[Dict[str, Any]] = None,
                  enable_env_override: bool = True,
                  validate_config: bool = True) -> BootstrapResult:
        """
        启动应用
        
        Args:
            config_file_path: 配置文件路径
            config_dict: 配置字典
            enable_env_override: 是否启用环境变量覆盖
            validate_config: 是否验证配置
            
        Returns:
            BootstrapResult: 启动结果
        """
        bootstrap_start_time = datetime.now()
        warnings = []
        
        try:
            self._logger.info("Starting application bootstrap...")
            
            # 1. 初始化配置管理器
            self._config_manager = self._initialize_configuration_manager(
                config_file_path, config_dict, enable_env_override
            )
            
            # 2. 验证配置
            if validate_config:
                validation_errors = self._validate_configuration()
                if validation_errors:
                    raise ConfigurationValidationError(
                        f"Configuration validation failed: {validation_errors}"
                    )
            
            # 3. 初始化基础设施服务
            self._initialize_infrastructure_services()
            
            # 4. 创建服务工厂
            self._service_factory = self._create_service_factory()
            
            # 5. 创建应用服务
            self._services = self._create_application_services()
            
            # 6. 验证服务状态
            service_validation_warnings = self._validate_services()
            warnings.extend(service_validation_warnings)
            
            # 7. 运行服务健康检查
            health_warnings = self._run_health_checks()
            warnings.extend(health_warnings)
            
            # 8. 注册关闭处理程序
            self._register_shutdown_handlers()
            
            self._is_initialized = True
            bootstrap_duration = (datetime.now() - bootstrap_start_time).total_seconds()
            self._logger.info(f"Application bootstrap completed successfully in {bootstrap_duration:.2f}s")
            
            return BootstrapResult(
                success=True,
                services=self._services,
                config_manager=self._config_manager,
                service_factory=self._service_factory,
                warnings=warnings
            )
            
        except Exception as e:
            bootstrap_duration = (datetime.now() - bootstrap_start_time).total_seconds()
            self._logger.error(f"Application bootstrap failed after {bootstrap_duration:.2f}s: {e}")
            
            # 增强错误诊断
            diagnostic_info = self._create_diagnostic_info(e)
            self._logger.error(f"Diagnostic information: {diagnostic_info}")
            
            return BootstrapResult(
                success=False,
                services={},
                error=str(e),
                warnings=warnings
            )
    
    def _initialize_configuration_manager(self, 
                                        config_file_path: Optional[str],
                                        config_dict: Optional[Dict[str, Any]],
                                        enable_env_override: bool) -> IConfigurationManager:
        """初始化配置管理器"""
        self._logger.info("Initializing configuration manager...")
        
        try:
            # 如果没有提供配置文件路径，尝试查找默认配置文件
            if not config_file_path:
                config_file_path = self._find_default_config_file()
            
            config_manager = ConfigurationManager(
                config_dict=config_dict,
                config_file_path=config_file_path,
                enable_env_override=enable_env_override
            )
            
            self._logger.info("Configuration manager initialized successfully")
            return config_manager
            
        except Exception as e:
            self._logger.error(f"Failed to initialize configuration manager: {e}")
            raise ApplicationBootstrapperError(f"Configuration manager initialization failed: {e}")
    
    def _find_default_config_file(self) -> Optional[str]:
        """查找默认配置文件"""
        default_config_files = [
            'config/assessment_config.json',
            'config/opro_config.json',
            'assessment_config.json',
            'opro_config.json'
        ]
        
        for config_file in default_config_files:
            if Path(config_file).exists():
                self._logger.info(f"Found default config file: {config_file}")
                return config_file
        
        self._logger.info("No default config file found")
        return None
    
    def _validate_configuration(self) -> List[str]:
        """验证配置"""
        self._logger.info("Validating configuration...")
        
        try:
            errors = self._config_manager.validate_config()
            
            if errors:
                self._logger.warning(f"Configuration validation errors: {errors}")
            else:
                self._logger.info("Configuration validation passed")
            
            return errors
            
        except Exception as e:
            self._logger.error(f"Configuration validation failed: {e}")
            return [f"Configuration validation error: {e}"]
    
    def _initialize_infrastructure_services(self) -> None:
        """初始化基础设施服务"""
        self._logger.info("Initializing infrastructure services...")
        
        try:
            # 创建事件总线
            self._event_bus = EventBus()
            
            # 创建功能开关管理器
            self._feature_toggle_manager = FeatureToggleManager(
                config_manager=self._config_manager,
                event_bus=self._event_bus
            )
            
            # 创建服务注册表
            service_registry = ServiceRegistry()
            
            # 将基础服务存储到服务字典
            self._services['event_bus'] = self._event_bus
            self._services['feature_toggle_manager'] = self._feature_toggle_manager
            self._services['service_registry'] = service_registry
            
            self._logger.info("Infrastructure services initialized successfully")
            
        except Exception as e:
            self._logger.error(f"Failed to initialize infrastructure services: {e}")
            raise ServiceInitializationError(f"Infrastructure services initialization failed: {e}")
    
    def _create_service_factory(self) -> IServiceFactory:
        """创建服务工厂"""
        self._logger.info("Creating service factory...")
        
        try:
            service_factory = ServiceFactory(
                config_manager=self._config_manager,
                service_registry=self._services['service_registry'],
                event_bus=self._event_bus,
                logger=self._logger
            )
            
            self._logger.info("Service factory created successfully")
            return service_factory
            
        except Exception as e:
            self._logger.error(f"Failed to create service factory: {e}")
            raise ServiceInitializationError(f"Service factory creation failed: {e}")
    
    def _create_application_services(self) -> Dict[str, Any]:
        """创建应用服务"""
        self._logger.info("Creating application services...")
        
        try:
            # 获取启用的服务列表
            enabled_services = self._get_enabled_services()
            
            # 创建服务
            created_services = {}
            for service_type in enabled_services:
                if self._is_service_enabled(service_type):
                    # 跳过已经在基础设施中创建的服务
                    if service_type in self._services:
                        self._logger.info(f"Service {service_type} already exists in infrastructure services")
                        continue
                    
                    # 尝试创建服务
                    service_instance = self._create_service_by_name(service_type)
                    if service_instance:
                        created_services[service_type] = service_instance
                        self._logger.info(f"Created service: {service_type}")
                    else:
                        self._logger.warning(f"Service {service_type} creation returned None")
                else:
                    self._logger.info(f"Service {service_type} is disabled, skipping")
            
            # 合并基础设施服务和应用服务
            all_services = {**self._services, **created_services}
            
            self._logger.info(f"Application services created successfully: {list(created_services.keys())}")
            return all_services
            
        except Exception as e:
            self._logger.error(f"Failed to create application services: {e}")
            raise ServiceInitializationError(f"Application services creation failed: {e}")
    
    def _create_service_by_name(self, service_name: str) -> Any:
        """根据服务名称创建服务实例"""
        # 服务名称到类型的映射
        service_mapping = {
            'coalition_service': 'contribution_assessment.services.coalition_service.CoalitionService',
            'simulation_service': 'contribution_assessment.services.simulation_service.SimulationService',
            'shapley_service': 'contribution_assessment.services.shapley_service.ShapleyService',
            'opro_service': 'contribution_assessment.services.opro_service.OPROService',
            'state_manager': 'contribution_assessment.services.state_manager.StateManager',
            'phase_coordinator': 'contribution_assessment.services.phase_coordinator.PhaseCoordinator'
        }
        
        try:
            # 尝试从映射中获取类型
            if service_name in service_mapping:
                service_type_name = service_mapping[service_name]
                return self._service_factory.create_service(service_type_name)
            else:
                self._logger.warning(f"Unknown service type: {service_name}")
                return None
                
        except Exception as e:
            self._logger.error(f"Failed to create service {service_name}: {e}")
            return None
    
    def _get_enabled_services(self) -> List[str]:
        """获取启用的服务列表"""
        # 默认服务列表
        default_services = [
            'coalition_service',
            'simulation_service',
            'shapley_service',
            'opro_service',
            'state_manager',
            'phase_coordinator'
        ]
        
        # 从配置获取服务列表
        configured_services = self._config_manager.get_config('services.enabled', default_services)
        
        return configured_services
    
    def _is_service_enabled(self, service_type: str) -> bool:
        """检查服务是否启用"""
        # 通过功能开关管理器检查
        if self._feature_toggle_manager:
            return self._feature_toggle_manager.is_feature_enabled(f"service.{service_type}")
        
        # 默认启用
        return True
    
    def _validate_services(self) -> List[str]:
        """验证服务状态"""
        self._logger.info("Validating services...")
        warnings = []
        
        try:
            # 检查必需服务
            required_services = ['event_bus']
            
            for service_type in required_services:
                if service_type not in self._services:
                    raise ServiceInitializationError(f"Required service {service_type} is missing")
            
            # 检查可选服务
            optional_services = ['phase_coordinator', 'coalition_service', 'simulation_service']
            for service_type in optional_services:
                if service_type not in self._services:
                    warning = f"Optional service {service_type} is not available"
                    warnings.append(warning)
                    self._logger.warning(warning)
            
            self._logger.info("Services validation completed")
            return warnings
            
        except Exception as e:
            self._logger.error(f"Services validation failed: {e}")
            raise ServiceInitializationError(f"Services validation failed: {e}")
    
    def _run_health_checks(self) -> List[str]:
        """运行服务健康检查"""
        self._logger.info("Running health checks...")
        warnings = []
        
        try:
            for service_type, service_instance in self._services.items():
                if hasattr(service_instance, 'health_check'):
                    try:
                        if not service_instance.health_check():
                            warning = f"Service {service_type} health check failed"
                            warnings.append(warning)
                            self._logger.warning(warning)
                        else:
                            self._logger.debug(f"Service {service_type} health check passed")
                    except Exception as e:
                        warning = f"Service {service_type} health check error: {e}"
                        warnings.append(warning)
                        self._logger.warning(warning)
            
            self._logger.info("Health checks completed")
            return warnings
            
        except Exception as e:
            self._logger.error(f"Health checks failed: {e}")
            return [f"Health check system error: {e}"]
    
    def _create_diagnostic_info(self, error: Exception) -> Dict[str, Any]:
        """创建错误诊断信息"""
        diagnostic_info = {
            'error_type': type(error).__name__,
            'error_message': str(error),
            'timestamp': datetime.now().isoformat(),
            'python_version': sys.version,
            'platform': sys.platform,
            'initialized_services': list(self._services.keys()),
            'config_manager_available': self._config_manager is not None,
            'service_factory_available': self._service_factory is not None,
            'event_bus_available': self._event_bus is not None,
            'is_initialized': self._is_initialized
        }
        
        # 添加环境变量信息
        relevant_env_vars = [
            'ZHIPUAI_API_KEY', 'OPENAI_API_KEY', 'PYTHON_PATH',
            'PYTHONPATH', 'PATH'
        ]
        
        diagnostic_info['environment'] = {
            var: os.environ.get(var, 'NOT_SET')
            for var in relevant_env_vars
        }
        
        # 添加配置信息
        if self._config_manager:
            try:
                diagnostic_info['config_summary'] = {
                    'config_keys': list(self._config_manager.get_all_keys()),
                    'config_sources': self._config_manager.get_config_sources()
                }
            except Exception as e:
                diagnostic_info['config_summary'] = f"Error getting config info: {e}"
        
        return diagnostic_info
    
    def _register_shutdown_handlers(self) -> None:
        """注册关闭处理程序"""
        self._logger.info("Registering shutdown handlers...")
        
        try:
            # 注册服务关闭处理程序
            for service_type, service_instance in self._services.items():
                if hasattr(service_instance, 'shutdown'):
                    self._shutdown_handlers.append(
                        lambda s=service_instance: s.shutdown()
                    )
            
            # 注册资源清理处理程序
            self._shutdown_handlers.append(self._cleanup_resources)
            
            self._logger.info("Shutdown handlers registered successfully")
            
        except Exception as e:
            self._logger.error(f"Failed to register shutdown handlers: {e}")
            # 不抛出异常，因为这不是致命错误
    
    def _cleanup_resources(self) -> None:
        """清理资源"""
        self._logger.info("Cleaning up resources...")
        
        try:
            # 清理服务实例
            self._services.clear()
            
            # 重置初始化状态
            self._is_initialized = False
            
            self._logger.info("Resources cleaned up successfully")
            
        except Exception as e:
            self._logger.error(f"Failed to cleanup resources: {e}")
    
    def shutdown(self, timeout: float = 30.0) -> bool:
        """优雅关闭应用"""
        if not self._is_initialized:
            self._logger.info("Application is not initialized, nothing to shutdown")
            return True
        
        self._logger.info("Shutting down application...")
        shutdown_start_time = datetime.now()
        
        try:
            # 执行所有关闭处理程序
            for i, handler in enumerate(reversed(self._shutdown_handlers)):
                try:
                    handler_start_time = datetime.now()
                    handler()
                    handler_duration = (datetime.now() - handler_start_time).total_seconds()
                    self._logger.debug(f"Shutdown handler {i+1} completed in {handler_duration:.2f}s")
                    
                    # 检查超时
                    total_duration = (datetime.now() - shutdown_start_time).total_seconds()
                    if total_duration > timeout:
                        self._logger.warning(f"Shutdown timeout ({timeout}s) exceeded, forcing shutdown")
                        break
                        
                except Exception as e:
                    self._logger.error(f"Shutdown handler {i+1} error: {e}")
            
            # 清理状态
            self._is_initialized = False
            self._services.clear()
            self._shutdown_handlers.clear()
            
            shutdown_duration = (datetime.now() - shutdown_start_time).total_seconds()
            self._logger.info(f"Application shutdown completed in {shutdown_duration:.2f}s")
            return True
            
        except Exception as e:
            shutdown_duration = (datetime.now() - shutdown_start_time).total_seconds()
            self._logger.error(f"Application shutdown failed after {shutdown_duration:.2f}s: {e}")
            return False
    
    def force_shutdown(self) -> None:
        """强制关闭应用（无超时）"""
        self._logger.warning("Force shutdown initiated")
        
        try:
            # 强制清理所有资源
            if hasattr(self, '_services'):
                for service_name, service_instance in self._services.items():
                    if hasattr(service_instance, 'force_shutdown'):
                        try:
                            service_instance.force_shutdown()
                        except Exception as e:
                            self._logger.error(f"Force shutdown failed for service {service_name}: {e}")
            
            # 重置状态
            self._is_initialized = False
            self._services.clear()
            self._shutdown_handlers.clear()
            
            self._logger.info("Force shutdown completed")
            
        except Exception as e:
            self._logger.error(f"Force shutdown failed: {e}")
    
    def restart(self, config_file_path: Optional[str] = None,
                config_dict: Optional[Dict[str, Any]] = None) -> BootstrapResult:
        """重启应用"""
        self._logger.info("Restarting application...")
        
        # 先关闭
        shutdown_success = self.shutdown()
        if not shutdown_success:
            self._logger.warning("Shutdown failed, proceeding with restart anyway")
        
        # 重新启动
        return self.bootstrap(config_file_path=config_file_path, config_dict=config_dict)
    
    def get_service(self, service_type: str) -> Any:
        """获取服务实例"""
        if not self._is_initialized:
            raise ApplicationBootstrapperError("Application is not initialized")
        
        if service_type not in self._services:
            raise ApplicationBootstrapperError(f"Service {service_type} not found")
        
        return self._services[service_type]
    
    def get_all_services(self) -> Dict[str, Any]:
        """获取所有服务"""
        if not self._is_initialized:
            raise ApplicationBootstrapperError("Application is not initialized")
        
        return self._services.copy()
    
    def is_initialized(self) -> bool:
        """检查是否已初始化"""
        return self._is_initialized
    
    def add_shutdown_handler(self, handler: Callable) -> None:
        """添加关闭处理程序"""
        self._shutdown_handlers.append(handler)
    
    def get_config_manager(self) -> IConfigurationManager:
        """获取配置管理器"""
        if not self._config_manager:
            raise ApplicationBootstrapperError("Configuration manager is not initialized")
        return self._config_manager
    
    def get_service_factory(self) -> IServiceFactory:
        """获取服务工厂"""
        if not self._service_factory:
            raise ApplicationBootstrapperError("Service factory is not initialized")
        return self._service_factory