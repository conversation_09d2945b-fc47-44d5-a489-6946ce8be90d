"""
基础设施层组件集成示例

展示事件总线、配置管理器、服务注册表和错误处理框架如何协同工作
"""

import logging
from typing import Dict, Any

from contribution_assessment.infrastructure.event_bus import EventBus, Event
from contribution_assessment.infrastructure.configuration_manager import ConfigurationManager, AssessmentConfig
from contribution_assessment.infrastructure.service_registry import ServiceRegistry, ServiceContainer
from contribution_assessment.infrastructure.error_handler import <PERSON>rrorHandler, ErrorContext, NetworkError


# 示例服务类
class DatabaseService:
    """数据库服务示例"""
    
    def __init__(self, config_manager: ConfigurationManager, 
                 event_bus: EventBus, error_handler: ErrorHandler):
        self.config_manager = config_manager
        self.event_bus = event_bus
        self.error_handler = error_handler
        self.logger = logging.getLogger(__name__)
    
    def connect(self) -> bool:
        """连接数据库"""
        try:
            # 模拟数据库连接
            db_config = self.config_manager.get_config("database.host", "localhost")
            self.logger.info(f"Connecting to database at {db_config}")
            
            # 发布连接事件
            self.event_bus.publish("database_connected", {"host": db_config})
            
            return True
            
        except Exception as e:
            context = ErrorContext("connect", "DatabaseService")
            self.error_handler.handle_error(e, context)
            return False
    
    def query(self, sql: str) -> Dict[str, Any]:
        """执行查询"""
        try:
            # 模拟查询执行
            if "invalid" in sql.lower():
                raise NetworkError("Database connection lost")
            
            result = {"rows": [], "count": 0}
            self.event_bus.publish("query_executed", {"sql": sql, "result": result})
            
            return result
            
        except Exception as e:
            context = ErrorContext("query", "DatabaseService", additional_data={"sql": sql})
            self.error_handler.handle_error(e, context)
            raise


class NotificationService:
    """通知服务示例"""
    
    def __init__(self, event_bus: EventBus):
        self.event_bus = event_bus
        self.logger = logging.getLogger(__name__)
        
        # 订阅数据库事件
        self.event_bus.subscribe("database_connected", self.on_database_connected)
        self.event_bus.subscribe("error_occurred", self.on_error_occurred)
    
    def on_database_connected(self, event: Event):
        """处理数据库连接事件"""
        self.logger.info(f"Database connected notification: {event.data}")
    
    def on_error_occurred(self, event: Event):
        """处理错误事件"""
        self.logger.warning(f"Error notification: {event.data}")


def create_integrated_system() -> Dict[str, Any]:
    """创建集成系统示例"""
    
    # 1. 创建配置管理器
    config_data = {
        "database": {
            "host": "localhost",
            "port": 5432
        },
        "assessment": {
            "start_date": "2024-01-01",
            "end_date": "2024-12-31",
            "stocks": ["AAPL", "GOOGL"],
            "starting_cash": 100000
        }
    }
    config_manager = ConfigurationManager(config_dict=config_data)
    
    # 2. 创建事件总线
    event_bus = EventBus(max_workers=2, enable_async=True)
    
    # 3. 创建错误处理器
    logger = logging.getLogger("integrated_system")
    error_handler = ErrorHandler(logger=logger, event_bus=event_bus)
    
    # 注册网络错误恢复策略
    def network_recovery_strategy(error, context):
        logger.info("Attempting network error recovery...")
        return "recovered"
    
    error_handler.register_recovery_strategy(NetworkError, network_recovery_strategy)
    
    # 4. 创建服务容器和注册服务
    container = ServiceContainer()
    
    def configure_services(registry):
        # 注册基础设施服务
        registry.register_singleton(ConfigurationManager, instance=config_manager)
        registry.register_singleton(EventBus, instance=event_bus)
        registry.register_singleton(ErrorHandler, instance=error_handler)
        
        # 注册业务服务
        registry.register_singleton(DatabaseService, DatabaseService)
        registry.register_singleton(NotificationService, NotificationService)
    
    container.configure_services(configure_services)
    service_provider = container.build_service_provider()
    
    # 5. 获取服务并演示集成
    db_service = service_provider.get_service(DatabaseService)
    notification_service = service_provider.get_service(NotificationService)
    
    return {
        "config_manager": config_manager,
        "event_bus": event_bus,
        "error_handler": error_handler,
        "service_provider": service_provider,
        "db_service": db_service,
        "notification_service": notification_service
    }


def demonstrate_integration():
    """演示基础设施层组件集成"""
    
    # 设置日志
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger("demo")
    
    logger.info("=== 基础设施层组件集成演示 ===")
    
    # 创建集成系统
    system = create_integrated_system()
    
    config_manager = system["config_manager"]
    event_bus = system["event_bus"]
    error_handler = system["error_handler"]
    db_service = system["db_service"]
    
    # 1. 演示配置管理
    logger.info("\n1. 配置管理演示:")
    assessment_config = config_manager.get_assessment_config()
    logger.info(f"评估配置: 股票={assessment_config.stocks}, 起始资金={assessment_config.starting_cash}")
    
    # 2. 演示服务依赖注入和事件发布
    logger.info("\n2. 服务集成和事件演示:")
    db_service.connect()  # 这会触发事件，通知服务会收到
    
    # 3. 演示错误处理和恢复
    logger.info("\n3. 错误处理和恢复演示:")
    try:
        db_service.query("SELECT * FROM invalid_table")
    except NetworkError as e:
        logger.info(f"捕获到网络错误: {e}")
    
    # 4. 演示错误统计
    logger.info("\n4. 错误统计:")
    stats = error_handler.get_error_statistics()
    logger.info(f"错误统计: {stats}")
    
    # 5. 演示事件总线统计
    logger.info("\n5. 事件总线统计:")
    event_types = event_bus.get_all_event_types()
    logger.info(f"已订阅的事件类型: {event_types}")
    for event_type in event_types:
        count = event_bus.get_subscriber_count(event_type)
        logger.info(f"  {event_type}: {count} 个订阅者")
    
    # 清理
    event_bus.shutdown()
    logger.info("\n=== 演示完成 ===")


if __name__ == "__main__":
    demonstrate_integration()