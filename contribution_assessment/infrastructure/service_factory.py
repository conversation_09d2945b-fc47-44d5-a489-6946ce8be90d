"""
服务工厂

提供基于配置的服务实例化和管理功能，支持：
- 基于配置的服务创建
- 服务依赖关系的自动解析
- 单例和原型模式支持
- 服务生命周期管理
- 配置驱动的服务注册
"""

import logging
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Type, TypeVar, Callable, Union
from dataclasses import dataclass
from datetime import datetime

from .service_registry import (
    IServiceRegistry, ServiceRegistry, ServiceLifetime, 
    ServiceRegistrationError, ServiceResolutionError
)
from .configuration_manager import IConfigurationManager, ConfigurationError


T = TypeVar('T')


@dataclass
class ServiceConfiguration:
    """服务配置描述符"""
    service_type: str  # 服务类型的完整类名
    implementation_type: Optional[str] = None  # 实现类型的完整类名
    factory_method: Optional[str] = None  # 工厂方法名
    lifetime: str = "singleton"  # 生命周期：singleton, transient, scoped
    dependencies: List[str] = None  # 依赖的服务类型列表
    configuration: Dict[str, Any] = None  # 服务特定配置
    enabled: bool = True  # 是否启用此服务
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []
        if self.configuration is None:
            self.configuration = {}


class ServiceFactoryError(Exception):
    """服务工厂异常"""
    pass


class IServiceFactory(ABC):
    """服务工厂接口"""
    
    @abstractmethod
    def create_service(self, service_type: Type[T], config: Optional[ServiceConfiguration] = None) -> T:
        """创建服务实例"""
        pass
    
    @abstractmethod
    def register_service_from_config(self, config: ServiceConfiguration) -> None:
        """根据配置注册服务"""
        pass
    
    @abstractmethod
    def register_services_from_configuration(self, config_key: str = "services") -> None:
        """从配置管理器批量注册服务"""
        pass
    
    @abstractmethod
    def get_registry(self) -> IServiceRegistry:
        """获取服务注册表"""
        pass


class ServiceFactory(IServiceFactory):
    """服务工厂实现"""
    
    def __init__(self, 
                 registry: Optional[IServiceRegistry] = None,
                 config_manager: Optional[IConfigurationManager] = None,
                 event_bus: Optional[Any] = None,
                 logger: Optional[logging.Logger] = None,
                 service_registry: Optional[IServiceRegistry] = None):
        """
        初始化服务工厂
        
        Args:
            registry: 服务注册表实例 (向后兼容)
            config_manager: 配置管理器实例
            event_bus: 事件总线实例
            logger: 日志记录器实例
            service_registry: 服务注册表实例 (优先使用)
        """
        # 优先使用 service_registry 参数，向后兼容 registry 参数
        self._registry = service_registry or registry or ServiceRegistry()
        self._config_manager = config_manager
        self._event_bus = event_bus
        self._logger = logger or logging.getLogger(__name__)
        self._type_cache: Dict[str, Type] = {}
        self._factory_methods: Dict[str, Callable] = {}
        
        # 注册内置工厂方法
        self._register_builtin_factories()
    
    def _register_builtin_factories(self) -> None:
        """注册内置工厂方法"""
        # 注册常用的工厂方法
        self._factory_methods['create_logger'] = self._create_logger_factory
        self._factory_methods['create_default_service'] = self._create_default_service_factory
    
    def _create_logger_factory(self, name: str = None) -> logging.Logger:
        """创建日志记录器的工厂方法"""
        return logging.getLogger(name or __name__)
    
    def _create_default_service_factory(self, service_type: Type[T]) -> T:
        """创建默认服务实例的工厂方法"""
        return service_type()
    
    def create_service(self, service_type: Union[Type[T], str], config: Optional[ServiceConfiguration] = None) -> T:
        """创建服务实例"""
        try:
            # 如果是字符串，先解析为类型
            if isinstance(service_type, str):
                service_type = self._resolve_type(service_type)
            
            if config:
                # 使用提供的配置创建服务
                return self._create_service_with_config(service_type, config)
            else:
                # 直接从注册表获取服务
                return self._registry.get_service(service_type)
                
        except Exception as e:
            raise ServiceFactoryError(f"Failed to create service {service_type.__name__ if hasattr(service_type, '__name__') else str(service_type)}: {e}")
    
    def _create_service_with_config(self, service_type: Type[T], config: ServiceConfiguration) -> T:
        """使用配置创建服务实例"""
        # 解析实现类型
        impl_type = None
        if config.implementation_type:
            impl_type = self._resolve_type(config.implementation_type)
        
        # 解析工厂方法
        factory = None
        if config.factory_method:
            factory = self._resolve_factory_method(config.factory_method)
        
        # 解析生命周期
        lifetime = self._parse_lifetime(config.lifetime)
        
        # 注册服务
        if lifetime == ServiceLifetime.SINGLETON:
            self._registry.register_singleton(
                service_type=service_type,
                implementation_type=impl_type,
                factory=factory
            )
        elif lifetime == ServiceLifetime.TRANSIENT:
            self._registry.register_transient(
                service_type=service_type,
                implementation_type=impl_type,
                factory=factory
            )
        else:
            raise ServiceFactoryError(f"Unsupported service lifetime: {config.lifetime}")
        
        # 获取服务实例
        return self._registry.get_service(service_type)
    
    def register_service_from_config(self, config: ServiceConfiguration) -> None:
        """根据配置注册服务"""
        if not config.enabled:
            self._logger.debug(f"Service {config.service_type} is disabled, skipping registration")
            return
        
        try:
            # 解析服务类型
            service_type = self._resolve_type(config.service_type)
            
            # 解析实现类型
            impl_type = None
            if config.implementation_type:
                impl_type = self._resolve_type(config.implementation_type)
            
            # 解析工厂方法
            factory = None
            if config.factory_method:
                factory = self._resolve_factory_method(config.factory_method)
            
            # 解析生命周期
            lifetime = self._parse_lifetime(config.lifetime)
            
            # 注册服务
            if lifetime == ServiceLifetime.SINGLETON:
                self._registry.register_singleton(
                    service_type=service_type,
                    implementation_type=impl_type,
                    factory=factory
                )
            elif lifetime == ServiceLifetime.TRANSIENT:
                self._registry.register_transient(
                    service_type=service_type,
                    implementation_type=impl_type,
                    factory=factory
                )
            else:
                raise ServiceFactoryError(f"Unsupported service lifetime: {config.lifetime}")
            
            self._logger.info(f"Registered service: {config.service_type} as {config.lifetime}")
            
        except Exception as e:
            raise ServiceFactoryError(f"Failed to register service from config {config.service_type}: {e}")
    
    def register_services_from_configuration(self, config_key: str = "services") -> None:
        """从配置管理器批量注册服务"""
        if not self._config_manager:
            raise ServiceFactoryError("Configuration manager is not available")
        
        try:
            services_config = self._config_manager.get_config(config_key, {})
            
            if not isinstance(services_config, dict):
                raise ServiceFactoryError(f"Services configuration must be a dictionary, got {type(services_config)}")
            
            # 根据依赖关系排序服务注册顺序
            ordered_services = self._sort_services_by_dependencies(services_config)
            
            registered_count = 0
            for service_name in ordered_services:
                service_config_dict = services_config[service_name]
                try:
                    # 将字典转换为ServiceConfiguration对象
                    service_config = self._parse_service_config(service_name, service_config_dict)
                    self.register_service_from_config(service_config)
                    registered_count += 1
                    
                except Exception as e:
                    self._logger.error(f"Failed to register service '{service_name}': {e}")
                    # 继续处理其他服务，不中断整个注册过程
            
            self._logger.info(f"Successfully registered {registered_count} services from configuration")
            
        except Exception as e:
            raise ServiceFactoryError(f"Failed to register services from configuration: {e}")
    
    def _sort_services_by_dependencies(self, services_config: Dict[str, Any]) -> List[str]:
        """根据依赖关系对服务进行拓扑排序"""
        # 简单的拓扑排序实现
        from collections import defaultdict, deque
        
        # 构建依赖图
        graph = defaultdict(list)
        in_degree = defaultdict(int)
        
        all_services = set(services_config.keys())
        
        for service_name, config in services_config.items():
            dependencies = config.get('dependencies', [])
            in_degree[service_name] = len(dependencies)
            
            for dep in dependencies:
                if dep in all_services:
                    graph[dep].append(service_name)
        
        # 拓扑排序
        queue = deque([service for service in all_services if in_degree[service] == 0])
        result = []
        
        while queue:
            current = queue.popleft()
            result.append(current)
            
            for neighbor in graph[current]:
                in_degree[neighbor] -= 1
                if in_degree[neighbor] == 0:
                    queue.append(neighbor)
        
        # 检查是否有循环依赖
        if len(result) != len(all_services):
            remaining = all_services - set(result)
            raise ServiceFactoryError(f"Circular dependency detected among services: {remaining}")
        
        return result
    
    def _parse_service_config(self, service_name: str, config_dict: Dict[str, Any]) -> ServiceConfiguration:
        """解析服务配置字典"""
        try:
            return ServiceConfiguration(
                service_type=config_dict.get('service_type', service_name),
                implementation_type=config_dict.get('implementation_type'),
                factory_method=config_dict.get('factory_method'),
                lifetime=config_dict.get('lifetime', 'singleton'),
                dependencies=config_dict.get('dependencies', []),
                configuration=config_dict.get('configuration', {}),
                enabled=config_dict.get('enabled', True)
            )
        except Exception as e:
            raise ServiceFactoryError(f"Invalid service configuration for '{service_name}': {e}")
    
    def _resolve_type(self, type_name: str) -> Type:
        """解析类型名称为实际类型"""
        if type_name in self._type_cache:
            return self._type_cache[type_name]
        
        try:
            # 分割模块和类名
            if '.' in type_name:
                module_name, class_name = type_name.rsplit('.', 1)
            else:
                # 如果没有模块名，假设在当前模块中
                module_name = __name__
                class_name = type_name
            
            # 动态导入模块
            import importlib
            module = importlib.import_module(module_name)
            
            # 获取类
            cls = getattr(module, class_name)
            
            # 缓存类型
            self._type_cache[type_name] = cls
            
            return cls
            
        except Exception as e:
            raise ServiceFactoryError(f"Failed to resolve type '{type_name}': {e}")
    
    def _resolve_factory_method(self, factory_name: str) -> Callable:
        """解析工厂方法"""
        if factory_name in self._factory_methods:
            return self._factory_methods[factory_name]
        
        try:
            # 分割模块和方法名
            if '.' in factory_name:
                module_name, method_name = factory_name.rsplit('.', 1)
                
                # 动态导入模块
                import importlib
                module = importlib.import_module(module_name)
                
                # 获取方法
                method = getattr(module, method_name)
            else:
                # 如果没有模块名，在内置工厂方法中查找
                if hasattr(self, factory_name):
                    method = getattr(self, factory_name)
                else:
                    raise ServiceFactoryError(f"Factory method '{factory_name}' not found")
            
            # 缓存工厂方法
            self._factory_methods[factory_name] = method
            
            return method
            
        except Exception as e:
            raise ServiceFactoryError(f"Failed to resolve factory method '{factory_name}': {e}")
    
    def _parse_lifetime(self, lifetime_str: str) -> ServiceLifetime:
        """解析生命周期字符串"""
        lifetime_map = {
            'singleton': ServiceLifetime.SINGLETON,
            'transient': ServiceLifetime.TRANSIENT,
            'scoped': ServiceLifetime.SCOPED
        }
        
        lifetime = lifetime_map.get(lifetime_str.lower())
        if lifetime is None:
            raise ServiceFactoryError(f"Invalid service lifetime: {lifetime_str}")
        
        return lifetime
    
    def get_registry(self) -> IServiceRegistry:
        """获取服务注册表"""
        return self._registry
    
    def register_factory_method(self, name: str, factory: Callable) -> None:
        """注册自定义工厂方法"""
        self._factory_methods[name] = factory
        self._logger.debug(f"Registered factory method: {name}")
    
    def register_type_alias(self, alias: str, type_obj: Type) -> None:
        """注册类型别名"""
        self._type_cache[alias] = type_obj
        self._logger.debug(f"Registered type alias: {alias} -> {type_obj.__name__}")
    
    def validate_service_configuration(self, config: ServiceConfiguration) -> List[str]:
        """验证服务配置"""
        errors = []
        
        # 验证服务类型
        try:
            self._resolve_type(config.service_type)
        except Exception as e:
            errors.append(f"Invalid service_type '{config.service_type}': {e}")
        
        # 验证实现类型
        if config.implementation_type:
            try:
                self._resolve_type(config.implementation_type)
            except Exception as e:
                errors.append(f"Invalid implementation_type '{config.implementation_type}': {e}")
        
        # 验证工厂方法
        if config.factory_method:
            try:
                self._resolve_factory_method(config.factory_method)
            except Exception as e:
                errors.append(f"Invalid factory_method '{config.factory_method}': {e}")
        
        # 验证生命周期
        try:
            self._parse_lifetime(config.lifetime)
        except Exception as e:
            errors.append(f"Invalid lifetime '{config.lifetime}': {e}")
        
        # 验证依赖
        for dep in config.dependencies:
            try:
                self._resolve_type(dep)
            except Exception as e:
                errors.append(f"Invalid dependency '{dep}': {e}")
        
        return errors
    
    def create_service_with_auto_discovery(self, service_type: Type[T]) -> T:
        """使用自动发现创建服务实例"""
        try:
            # 首先尝试从注册表获取
            if self._registry.has_service(service_type):
                return self._registry.get_service(service_type)
            
            # 如果没有注册，尝试自动创建
            self._logger.info(f"Auto-discovering service: {service_type.__name__}")
            
            # 分析构造函数依赖
            dependencies = self._analyze_constructor_dependencies(service_type)
            
            # 创建依赖实例
            dependency_instances = []
            for dep_type in dependencies:
                if dep_type == logging.Logger:
                    # 特殊处理Logger
                    dependency_instances.append(self._create_logger_factory(service_type.__name__))
                elif self._registry.has_service(dep_type):
                    dependency_instances.append(self._registry.get_service(dep_type))
                else:
                    # 递归创建依赖
                    dependency_instances.append(self.create_service_with_auto_discovery(dep_type))
            
            # 创建服务实例
            instance = service_type(*dependency_instances)
            
            # 自动注册为单例
            self._registry.register_singleton(service_type, instance=instance)
            
            return instance
            
        except Exception as e:
            raise ServiceFactoryError(f"Failed to auto-discover service {service_type.__name__}: {e}")
    
    def _analyze_constructor_dependencies(self, service_type: Type) -> List[Type]:
        """分析构造函数依赖"""
        import inspect
        from typing import get_origin, get_args, Union
        
        try:
            signature = inspect.signature(service_type.__init__)
            parameters = list(signature.parameters.values())[1:]  # 跳过self
            
            dependencies = []
            for param in parameters:
                # Skip parameters with default values (they're optional)
                if param.default != inspect.Parameter.empty:
                    continue
                    
                if param.annotation != inspect.Parameter.empty:
                    annotation = param.annotation
                    
                    # Handle Optional[T] and Union[T, None] types
                    if get_origin(annotation) is Union:
                        args = get_args(annotation)
                        # Check if it's Optional (Union with None)
                        if len(args) == 2 and type(None) in args:
                            # Skip optional dependencies entirely
                            continue
                    
                    # Only add non-optional, concrete types
                    if not isinstance(annotation, str) and hasattr(annotation, '__name__'):
                        dependencies.append(annotation)
            
            return dependencies
            
        except Exception as e:
            self._logger.warning(f"Failed to analyze constructor dependencies for {service_type.__name__}: {e}")
            return []

    def get_service_configurations(self) -> List[ServiceConfiguration]:
        """获取所有已注册的服务配置"""
        # 这个方法可以用于调试和监控
        # 实际实现可能需要维护一个配置列表
        return []
    
    def clear_caches(self) -> None:
        """清除缓存（用于测试）"""
        self._type_cache.clear()
        self._factory_methods.clear()
        self._logger.debug("Cleared service factory caches")

    def configure_services(self, config: Dict[str, Any]) -> None:
        """
        根据配置字典配置服务

        Args:
            config: 配置字典
        """
        # 更新配置管理器
        if self._config_manager:
            self._config_manager.update_config(config)

        # 这里可以根据配置注册特定的服务
        # 目前保持简单，只更新配置
        self._logger.debug(f"Configured services with {len(config)} configuration items")

    def get_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        if self._config_manager:
            return self._config_manager.get_all_configs()
        return {}

    def get_config_manager(self) -> Optional[IConfigurationManager]:
        """获取配置管理器"""
        return self._config_manager

    def get_health_status(self) -> Dict[str, Any]:
        """获取服务健康状态"""
        try:
            # 检查注册表状态
            registry_healthy = self._registry is not None

            # 检查配置管理器状态
            config_healthy = self._config_manager is not None

            overall_status = "healthy" if (registry_healthy and config_healthy) else "degraded"

            return {
                "status": overall_status,
                "registry_healthy": registry_healthy,
                "config_manager_healthy": config_healthy,
                "timestamp": str(datetime.now())
            }
        except Exception as e:
            return {
                "status": "unhealthy",
                "error": str(e),
                "timestamp": str(datetime.now())
            }
    
    @staticmethod
    def create_with_defaults(config_manager: Optional[IConfigurationManager] = None) -> 'ServiceFactory':
        """创建带有默认配置的服务工厂"""
        registry = ServiceRegistry()
        logger = logging.getLogger(__name__)
        
        factory = ServiceFactory(
            registry=registry,
            config_manager=config_manager,
            logger=logger
        )
        
        # 注册一些默认服务
        registry.register_singleton(logging.Logger, factory=lambda: logger)
        registry.register_singleton(IServiceRegistry, instance=registry)
        registry.register_singleton(IServiceFactory, instance=factory)
        
        return factory