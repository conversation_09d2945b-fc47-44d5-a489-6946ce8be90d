"""
配置命令行集成

支持通过命令行参数覆盖配置项，包括：
- 环境变量覆盖
- 命令行参数覆盖
- 配置文件层次结构
"""

import argparse
import os
import sys
from typing import Dict, Any, List, Optional
from dataclasses import dataclass

from .configuration_manager import ConfigurationManager


@dataclass
class ConfigOverride:
    """配置覆盖项"""
    key: str
    value: Any
    source: str  # 'env', 'cli', 'file'
    priority: int  # 数值越高优先级越高


class ConfigCLIIntegration:
    """配置命令行集成"""
    
    def __init__(self):
        self.overrides: List[ConfigOverride] = []
    
    def add_cli_arguments(self, parser: argparse.ArgumentParser) -> None:
        """添加命令行参数"""
        # 基本配置
        parser.add_argument('--config', 
                          help='Configuration file path')
        parser.add_argument('--start-date', 
                          help='Assessment start date (YYYY-MM-DD)')
        parser.add_argument('--end-date', 
                          help='Assessment end date (YYYY-MM-DD)')
        parser.add_argument('--stocks', nargs='+', 
                          help='Stock symbols to analyze')
        
        # 交易配置
        parser.add_argument('--starting-cash', type=int,
                          help='Starting cash amount')
        parser.add_argument('--risk-free-rate', type=float,
                          help='Risk-free rate')
        parser.add_argument('--max-concurrent-api-calls', type=int,
                          help='Maximum concurrent API calls')
        parser.add_argument('--trading-days-per-week', type=int,
                          help='Trading days per week')
        
        # OPRO配置
        parser.add_argument('--opro-temperature', type=float,
                          help='OPRO temperature parameter')
        parser.add_argument('--opro-candidates', type=int,
                          help='OPRO candidates per generation')
        parser.add_argument('--opro-iterations', type=int,
                          help='OPRO max optimization iterations')
        
        # 系统配置
        parser.add_argument('--enable-concurrent-execution', action='store_true',
                          help='Enable concurrent execution')
        parser.add_argument('--disable-concurrent-execution', action='store_true',
                          help='Disable concurrent execution')
        parser.add_argument('--verbose', action='store_true',
                          help='Enable verbose logging')
        
        # 环境配置
        parser.add_argument('--llm-provider', choices=['zhipuai', 'openai'],
                          help='LLM provider to use')
        parser.add_argument('--env-file', 
                          help='Environment file path')
    
    def parse_cli_overrides(self, args: argparse.Namespace) -> List[ConfigOverride]:
        """解析命令行覆盖"""
        overrides = []
        
        # 映射命令行参数到配置键
        cli_to_config = {
            'start_date': 'start_date',
            'end_date': 'end_date',
            'stocks': 'stocks',
            'starting_cash': 'starting_cash',
            'risk_free_rate': 'risk_free_rate',
            'max_concurrent_api_calls': 'max_concurrent_api_calls',
            'trading_days_per_week': 'trading_days_per_week',
            'opro_temperature': 'opro.temperature',
            'opro_candidates': 'opro.candidates_per_generation',
            'opro_iterations': 'opro.max_optimization_iterations',
            'llm_provider': 'llm_provider'
        }
        
        for arg_name, config_key in cli_to_config.items():
            value = getattr(args, arg_name, None)
            if value is not None:
                overrides.append(ConfigOverride(
                    key=config_key,
                    value=value,
                    source='cli',
                    priority=100
                ))
        
        # 处理布尔值参数
        if args.enable_concurrent_execution:
            overrides.append(ConfigOverride(
                key='enable_concurrent_execution',
                value=True,
                source='cli',
                priority=100
            ))
        elif args.disable_concurrent_execution:
            overrides.append(ConfigOverride(
                key='enable_concurrent_execution',
                value=False,
                source='cli',
                priority=100
            ))
        
        return overrides
    
    def collect_env_overrides(self) -> List[ConfigOverride]:
        """收集环境变量覆盖"""
        overrides = []
        
        # 环境变量到配置键的映射
        env_to_config = {
            'ASSESSMENT_START_DATE': 'start_date',
            'ASSESSMENT_END_DATE': 'end_date',
            'ASSESSMENT_STOCKS': 'stocks',
            'ASSESSMENT_STARTING_CASH': 'starting_cash',
            'ASSESSMENT_RISK_FREE_RATE': 'risk_free_rate',
            'ASSESSMENT_MAX_CONCURRENT_API_CALLS': 'max_concurrent_api_calls',
            'ASSESSMENT_TRADING_DAYS_PER_WEEK': 'trading_days_per_week',
            'OPRO_TEMPERATURE': 'opro.temperature',
            'OPRO_CANDIDATES_PER_GENERATION': 'opro.candidates_per_generation',
            'OPRO_MAX_OPTIMIZATION_ITERATIONS': 'opro.max_optimization_iterations',
            'LLM_PROVIDER': 'llm_provider'
        }
        
        for env_var, config_key in env_to_config.items():
            value = os.environ.get(env_var)
            if value is not None:
                # 类型转换
                converted_value = self._convert_env_value(value, config_key)
                overrides.append(ConfigOverride(
                    key=config_key,
                    value=converted_value,
                    source='env',
                    priority=50
                ))
        
        return overrides
    
    def _convert_env_value(self, value: str, config_key: str) -> Any:
        """转换环境变量值"""
        # 根据配置键类型转换
        if config_key in ['starting_cash', 'max_concurrent_api_calls', 
                         'trading_days_per_week', 'opro.candidates_per_generation',
                         'opro.max_optimization_iterations']:
            return int(value)
        elif config_key in ['risk_free_rate', 'opro.temperature']:
            return float(value)
        elif config_key == 'stocks':
            return value.split(',')
        elif config_key == 'enable_concurrent_execution':
            return value.lower() in ('true', '1', 'yes', 'on')
        else:
            return value
    
    def apply_overrides(self, config_manager: ConfigurationManager, 
                       overrides: List[ConfigOverride]) -> None:
        """应用配置覆盖"""
        # 按优先级排序
        overrides.sort(key=lambda x: x.priority)
        
        for override in overrides:
            config_manager.set_config(override.key, override.value)
    
    def create_config_manager_with_overrides(self, 
                                           config_file_path: Optional[str] = None,
                                           config_dict: Optional[Dict[str, Any]] = None,
                                           cli_args: Optional[argparse.Namespace] = None) -> ConfigurationManager:
        """创建带有覆盖的配置管理器"""
        # 创建基础配置管理器
        config_manager = ConfigurationManager(
            config_file_path=config_file_path,
            config_dict=config_dict,
            enable_env_override=True
        )
        
        # 收集所有覆盖
        all_overrides = []
        
        # 环境变量覆盖
        all_overrides.extend(self.collect_env_overrides())
        
        # 命令行覆盖
        if cli_args:
            all_overrides.extend(self.parse_cli_overrides(cli_args))
        
        # 应用覆盖
        self.apply_overrides(config_manager, all_overrides)
        
        return config_manager
    
    def create_enhanced_parser(self) -> argparse.ArgumentParser:
        """创建增强的命令行解析器"""
        parser = argparse.ArgumentParser(
            description='Multi-Agent Trading System with Contribution Assessment',
            formatter_class=argparse.RawDescriptionHelpFormatter,
            epilog='''
Examples:
  # 使用配置文件
  python run_assessment.py --config config/assessment.json
  
  # 命令行覆盖
  python run_assessment.py --start-date 2025-01-01 --end-date 2025-01-31 --stocks AAPL GOOGL
  
  # 环境变量覆盖
  ASSESSMENT_START_DATE=2025-01-01 python run_assessment.py
  
  # 混合使用
  python run_assessment.py --config config/base.json --opro-temperature 0.8 --verbose
            '''
        )
        
        self.add_cli_arguments(parser)
        return parser
    
    def validate_overrides(self, overrides: List[ConfigOverride]) -> List[str]:
        """验证覆盖配置"""
        errors = []
        
        for override in overrides:
            # 验证配置键是否有效
            if not self._is_valid_config_key(override.key):
                errors.append(f"Invalid configuration key: {override.key}")
            
            # 验证值类型
            if not self._is_valid_config_value(override.key, override.value):
                errors.append(f"Invalid value for {override.key}: {override.value}")
        
        return errors
    
    def _is_valid_config_key(self, key: str) -> bool:
        """检查配置键是否有效"""
        valid_keys = [
            'start_date', 'end_date', 'stocks', 'starting_cash',
            'risk_free_rate', 'max_concurrent_api_calls',
            'trading_days_per_week', 'enable_concurrent_execution',
            'opro.temperature', 'opro.candidates_per_generation',
            'opro.max_optimization_iterations', 'llm_provider'
        ]
        
        return key in valid_keys
    
    def _is_valid_config_value(self, key: str, value: Any) -> bool:
        """检查配置值是否有效"""
        if key == 'stocks':
            return isinstance(value, list) and len(value) > 0
        elif key in ['starting_cash', 'max_concurrent_api_calls', 'trading_days_per_week']:
            return isinstance(value, int) and value > 0
        elif key in ['risk_free_rate', 'opro.temperature']:
            return isinstance(value, (int, float)) and 0 <= value <= 1
        elif key == 'enable_concurrent_execution':
            return isinstance(value, bool)
        elif key == 'llm_provider':
            return value in ['zhipuai', 'openai']
        
        return True
    
    def print_config_summary(self, config_manager: ConfigurationManager) -> None:
        """打印配置摘要"""
        summary = config_manager.get_config_summary()
        
        print("=" * 60)
        print("Configuration Summary")
        print("=" * 60)
        
        print(f"Configuration sources: {', '.join(summary['config_sources'])}")
        print(f"Total configuration keys: {summary['total_keys']}")
        
        if summary['validation_errors']:
            print("\nValidation Errors:")
            for error in summary['validation_errors']:
                print(f"  - {error}")
        else:
            print("\nConfiguration validation: PASSED")
        
        print("\nEnvironment Variables:")
        for var, present in summary['required_env_vars'].items():
            status = "✓" if present else "✗"
            print(f"  {status} {var}")
        
        print("=" * 60)