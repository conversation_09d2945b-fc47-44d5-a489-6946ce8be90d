"""
事件总线系统

提供解耦的事件发布-订阅机制，支持：
- 事件发布和订阅
- 事件过滤
- 异步事件处理
- 订阅管理
"""

import asyncio
import logging
import threading
from abc import ABC, abstractmethod
from collections import defaultdict
from typing import Any, Callable, Dict, List, Optional, Set
from concurrent.futures import Thread<PERSON>oolExecutor
from dataclasses import dataclass
from datetime import datetime


@dataclass
class Event:
    """事件数据结构"""
    event_type: str
    data: Any
    timestamp: datetime
    source: Optional[str] = None
    correlation_id: Optional[str] = None


class EventFilter:
    """事件过滤器"""
    
    def __init__(self, event_types: Optional[Set[str]] = None, 
                 source_filter: Optional[str] = None):
        self.event_types = event_types
        self.source_filter = source_filter
    
    def matches(self, event: Event) -> bool:
        """检查事件是否匹配过滤条件"""
        if self.event_types and event.event_type not in self.event_types:
            return False
        if self.source_filter and event.source != self.source_filter:
            return False
        return True


class IEventBus(ABC):
    """事件总线接口"""
    
    @abstractmethod
    def publish(self, event_type: str, data: Any, source: Optional[str] = None,
                correlation_id: Optional[str] = None) -> None:
        """发布事件"""
        pass
    
    @abstractmethod
    def subscribe(self, event_type: str, callback: Callable[[Event], None],
                  event_filter: Optional[EventFilter] = None) -> str:
        """订阅事件，返回订阅ID"""
        pass
    
    @abstractmethod
    def unsubscribe(self, subscription_id: str) -> bool:
        """取消订阅"""
        pass
    
    @abstractmethod
    def publish_async(self, event_type: str, data: Any, source: Optional[str] = None,
                      correlation_id: Optional[str] = None) -> None:
        """异步发布事件"""
        pass


class EventBus(IEventBus):
    """事件总线实现类"""
    
    def __init__(self, max_workers: int = 4, enable_async: bool = True):
        self._subscribers: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
        self._subscription_counter = 0
        self._subscription_lock = threading.RLock()
        self._logger = logging.getLogger(__name__)
        self._max_workers = max_workers
        self._enable_async = enable_async
        
        # 异步处理相关
        if enable_async:
            self._executor = ThreadPoolExecutor(max_workers=max_workers)
            try:
                # 尝试获取当前事件循环，如果没有则不创建队列
                loop = asyncio.get_running_loop()
                self._event_queue = asyncio.Queue()
            except RuntimeError:
                # 没有运行中的事件循环
                self._event_queue = None
        else:
            self._executor = None
            self._event_queue = None
    
    def publish(self, event_type: str, data: Any, source: Optional[str] = None,
                correlation_id: Optional[str] = None) -> None:
        """同步发布事件"""
        event = Event(
            event_type=event_type,
            data=data,
            timestamp=datetime.now(),
            source=source,
            correlation_id=correlation_id
        )
        
        self._logger.debug(f"Publishing event: {event_type} from {source}")
        
        with self._subscription_lock:
            subscribers = self._subscribers.get(event_type, [])
        
        # 同步执行所有回调
        for subscriber_info in subscribers:
            try:
                callback = subscriber_info['callback']
                event_filter = subscriber_info.get('filter')
                
                # 应用过滤器
                if event_filter and not event_filter.matches(event):
                    continue
                
                callback(event)
                
            except Exception as e:
                self._logger.error(f"Error in event callback for {event_type}: {e}")
                # 继续执行其他回调，不中断
    
    def publish_async(self, event_type: str, data: Any, source: Optional[str] = None,
                      correlation_id: Optional[str] = None) -> None:
        """异步发布事件"""
        if not self._enable_async or not self._executor:
            # 降级到同步发布
            self.publish(event_type, data, source, correlation_id)
            return
        
        event = Event(
            event_type=event_type,
            data=data,
            timestamp=datetime.now(),
            source=source,
            correlation_id=correlation_id
        )
        
        self._logger.debug(f"Publishing async event: {event_type} from {source}")
        
        # 提交到线程池异步执行
        self._executor.submit(self._process_async_event, event)
    
    def _process_async_event(self, event: Event) -> None:
        """异步处理事件"""
        with self._subscription_lock:
            subscribers = self._subscribers.get(event.event_type, [])
        
        for subscriber_info in subscribers:
            try:
                callback = subscriber_info['callback']
                event_filter = subscriber_info.get('filter')
                
                # 应用过滤器
                if event_filter and not event_filter.matches(event):
                    continue
                
                callback(event)
                
            except Exception as e:
                self._logger.error(f"Error in async event callback for {event.event_type}: {e}")
    
    def subscribe(self, event_type: str, callback: Callable[[Event], None],
                  event_filter: Optional[EventFilter] = None) -> str:
        """订阅事件"""
        with self._subscription_lock:
            self._subscription_counter += 1
            subscription_id = f"sub_{self._subscription_counter}"
            
            subscriber_info = {
                'id': subscription_id,
                'callback': callback,
                'filter': event_filter,
                'created_at': datetime.now()
            }
            
            self._subscribers[event_type].append(subscriber_info)
            
            self._logger.debug(f"Added subscription {subscription_id} for event {event_type}")
            return subscription_id
    
    def unsubscribe(self, subscription_id: str) -> bool:
        """取消订阅"""
        with self._subscription_lock:
            for event_type, subscribers in self._subscribers.items():
                for i, subscriber_info in enumerate(subscribers):
                    if subscriber_info['id'] == subscription_id:
                        del subscribers[i]
                        self._logger.debug(f"Removed subscription {subscription_id} for event {event_type}")
                        return True
            
            self._logger.warning(f"Subscription {subscription_id} not found")
            return False
    
    def get_subscriber_count(self, event_type: str) -> int:
        """获取指定事件类型的订阅者数量"""
        with self._subscription_lock:
            return len(self._subscribers.get(event_type, []))
    
    def get_all_event_types(self) -> List[str]:
        """获取所有已订阅的事件类型"""
        with self._subscription_lock:
            return list(self._subscribers.keys())
    
    def clear_all_subscriptions(self) -> None:
        """清除所有订阅"""
        with self._subscription_lock:
            self._subscribers.clear()
            self._logger.info("Cleared all event subscriptions")
    
    def shutdown(self) -> None:
        """关闭事件总线"""
        if self._executor:
            self._executor.shutdown(wait=True)
            self._logger.info("Event bus executor shutdown complete")
        
        self.clear_all_subscriptions()