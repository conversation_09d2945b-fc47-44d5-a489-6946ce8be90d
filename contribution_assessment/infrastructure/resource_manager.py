"""
资源管理器

提供内存和资源优化功能，支持：
- 对象池管理
- 缓存机制
- 内存泄漏检测和预防
- 资源使用优化
- 垃圾回收优化
"""

import gc
import logging
import threading
import time
import weakref
from abc import ABC, abstractmethod
from collections import defaultdict, OrderedDict
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from typing import Any, Callable, Dict, Generic, List, Optional, Set, TypeVar, Union
from concurrent.futures import ThreadPoolExecutor
import tracemalloc

from .event_bus import IEventBus
from .performance_monitor import IPerformanceMonitor


T = TypeVar('T')


@dataclass
class CacheStats:
    """缓存统计信息"""
    hits: int = 0
    misses: int = 0
    evictions: int = 0
    size: int = 0
    max_size: int = 0
    hit_rate: float = 0.0
    
    def update_hit_rate(self):
        """更新命中率"""
        total = self.hits + self.misses
        self.hit_rate = (self.hits / total * 100) if total > 0 else 0.0


@dataclass
class MemoryUsage:
    """内存使用信息"""
    timestamp: datetime
    allocated_bytes: int
    peak_bytes: int
    object_count: int
    gc_collections: Dict[int, int]
    tracemalloc_snapshot: Optional[Any] = None


@dataclass
class ObjectPoolStats:
    """对象池统计信息"""
    pool_name: str
    created_objects: int = 0
    reused_objects: int = 0
    active_objects: int = 0
    pool_size: int = 0
    max_pool_size: int = 0
    reuse_rate: float = 0.0
    
    def update_reuse_rate(self):
        """更新重用率"""
        total = self.created_objects + self.reused_objects
        self.reuse_rate = (self.reused_objects / total * 100) if total > 0 else 0.0


class ICache(ABC, Generic[T]):
    """缓存接口"""
    
    @abstractmethod
    def get(self, key: str) -> Optional[T]:
        """获取缓存项"""
        pass
    
    @abstractmethod
    def put(self, key: str, value: T, ttl: Optional[int] = None) -> None:
        """存储缓存项"""
        pass
    
    @abstractmethod
    def remove(self, key: str) -> bool:
        """移除缓存项"""
        pass
    
    @abstractmethod
    def clear(self) -> None:
        """清空缓存"""
        pass
    
    @abstractmethod
    def get_stats(self) -> CacheStats:
        """获取缓存统计"""
        pass


class LRUCache(ICache[T]):
    """LRU缓存实现"""
    
    def __init__(self, max_size: int = 1000, default_ttl: Optional[int] = None):
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: OrderedDict = OrderedDict()
        self._timestamps: Dict[str, datetime] = {}
        self._ttls: Dict[str, int] = {}
        self._stats = CacheStats(max_size=max_size)
        self._lock = threading.RLock()
    
    def get(self, key: str) -> Optional[T]:
        """获取缓存项"""
        with self._lock:
            # 检查是否过期
            if self._is_expired(key):
                self._remove_expired(key)
                self._stats.misses += 1
                self._stats.update_hit_rate()
                return None
            
            if key in self._cache:
                # 移动到末尾（最近使用）
                value = self._cache.pop(key)
                self._cache[key] = value
                self._stats.hits += 1
                self._stats.update_hit_rate()
                return value
            
            self._stats.misses += 1
            self._stats.update_hit_rate()
            return None
    
    def put(self, key: str, value: T, ttl: Optional[int] = None) -> None:
        """存储缓存项"""
        with self._lock:
            # 如果已存在，先移除
            if key in self._cache:
                self._cache.pop(key)
            
            # 检查是否需要驱逐
            while len(self._cache) >= self.max_size:
                self._evict_oldest()
            
            # 添加新项
            self._cache[key] = value
            self._timestamps[key] = datetime.now()
            
            # 设置TTL
            effective_ttl = ttl or self.default_ttl
            if effective_ttl:
                self._ttls[key] = effective_ttl
            
            self._stats.size = len(self._cache)
    
    def remove(self, key: str) -> bool:
        """移除缓存项"""
        with self._lock:
            if key in self._cache:
                self._cache.pop(key)
                self._timestamps.pop(key, None)
                self._ttls.pop(key, None)
                self._stats.size = len(self._cache)
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self._cache.clear()
            self._timestamps.clear()
            self._ttls.clear()
            self._stats.size = 0
    
    def get_stats(self) -> CacheStats:
        """获取缓存统计"""
        with self._lock:
            self._stats.size = len(self._cache)
            return self._stats
    
    def _is_expired(self, key: str) -> bool:
        """检查是否过期"""
        if key not in self._ttls:
            return False
        
        timestamp = self._timestamps.get(key)
        if not timestamp:
            return True
        
        ttl = self._ttls[key]
        return datetime.now() > timestamp + timedelta(seconds=ttl)
    
    def _remove_expired(self, key: str) -> None:
        """移除过期项"""
        self._cache.pop(key, None)
        self._timestamps.pop(key, None)
        self._ttls.pop(key, None)
        self._stats.size = len(self._cache)
    
    def _evict_oldest(self) -> None:
        """驱逐最旧的项"""
        if self._cache:
            oldest_key = next(iter(self._cache))
            self._cache.pop(oldest_key)
            self._timestamps.pop(oldest_key, None)
            self._ttls.pop(oldest_key, None)
            self._stats.evictions += 1
    
    def cleanup_expired(self) -> int:
        """清理过期项"""
        with self._lock:
            expired_keys = []
            for key in list(self._cache.keys()):
                if self._is_expired(key):
                    expired_keys.append(key)
            
            for key in expired_keys:
                self._remove_expired(key)
            
            return len(expired_keys)


class ObjectPool(Generic[T]):
    """对象池实现"""
    
    def __init__(self, factory: Callable[[], T], max_size: int = 100,
                 reset_func: Optional[Callable[[T], None]] = None,
                 validate_func: Optional[Callable[[T], bool]] = None):
        self.factory = factory
        self.max_size = max_size
        self.reset_func = reset_func
        self.validate_func = validate_func
        
        self._pool: List[T] = []
        self._active_objects: Set[int] = set()
        self._stats = ObjectPoolStats(
            pool_name=f"{factory.__name__ if hasattr(factory, '__name__') else 'unknown'}_pool",
            max_pool_size=max_size
        )
        self._lock = threading.RLock()
    
    def acquire(self) -> T:
        """获取对象"""
        with self._lock:
            # 尝试从池中获取
            while self._pool:
                obj = self._pool.pop()
                
                # 验证对象是否有效
                if self.validate_func and not self.validate_func(obj):
                    continue
                
                # 重置对象状态
                if self.reset_func:
                    try:
                        self.reset_func(obj)
                    except Exception:
                        continue
                
                self._active_objects.add(id(obj))
                self._stats.reused_objects += 1
                self._stats.active_objects = len(self._active_objects)
                self._stats.pool_size = len(self._pool)
                self._stats.update_reuse_rate()
                return obj
            
            # 池中没有可用对象，创建新对象
            obj = self.factory()
            self._active_objects.add(id(obj))
            self._stats.created_objects += 1
            self._stats.active_objects = len(self._active_objects)
            self._stats.update_reuse_rate()
            return obj
    
    def release(self, obj: T) -> None:
        """释放对象"""
        with self._lock:
            obj_id = id(obj)
            if obj_id not in self._active_objects:
                return  # 对象不是从此池获取的
            
            self._active_objects.remove(obj_id)
            
            # 如果池未满，将对象放回池中
            if len(self._pool) < self.max_size:
                self._pool.append(obj)
            
            self._stats.active_objects = len(self._active_objects)
            self._stats.pool_size = len(self._pool)
    
    def clear(self) -> None:
        """清空对象池"""
        with self._lock:
            self._pool.clear()
            self._active_objects.clear()
            self._stats.active_objects = 0
            self._stats.pool_size = 0
    
    def get_stats(self) -> ObjectPoolStats:
        """获取对象池统计"""
        with self._lock:
            self._stats.active_objects = len(self._active_objects)
            self._stats.pool_size = len(self._pool)
            return self._stats
    
    @contextmanager
    def get_object(self):
        """上下文管理器方式获取对象"""
        obj = self.acquire()
        try:
            yield obj
        finally:
            self.release(obj)


class MemoryLeakDetector:
    """内存泄漏检测器"""
    
    def __init__(self, logger: Optional[logging.Logger] = None,
                 event_bus: Optional[IEventBus] = None,
                 check_interval: float = 300.0,  # 5分钟
                 growth_threshold: float = 1.5):  # 50%增长阈值
        self.logger = logger or logging.getLogger(__name__)
        self.event_bus = event_bus
        self.check_interval = check_interval
        self.growth_threshold = growth_threshold
        
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._shutdown_event = threading.Event()
        
        self._memory_history: List[MemoryUsage] = []
        self._object_refs: Dict[str, Set[weakref.ref]] = defaultdict(set)
        self._baseline_memory: Optional[MemoryUsage] = None
        
        # 启用tracemalloc
        if not tracemalloc.is_tracing():
            tracemalloc.start()
    
    def start_monitoring(self) -> None:
        """开始内存泄漏监控"""
        if self._monitoring:
            return
        
        self._monitoring = True
        self._shutdown_event.clear()
        
        # 设置基线内存使用
        self._baseline_memory = self._collect_memory_usage()
        
        # 启动监控线程
        self._monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            name="MemoryLeakDetector",
            daemon=True
        )
        self._monitor_thread.start()
        
        self.logger.info("Memory leak detection started")
    
    def stop_monitoring(self) -> None:
        """停止内存泄漏监控"""
        if not self._monitoring:
            return
        
        self._monitoring = False
        self._shutdown_event.set()
        
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=5.0)
        
        self.logger.info("Memory leak detection stopped")
    
    def _monitoring_loop(self) -> None:
        """监控循环"""
        while self._monitoring and not self._shutdown_event.is_set():
            try:
                # 收集内存使用情况
                memory_usage = self._collect_memory_usage()
                self._memory_history.append(memory_usage)
                
                # 检查内存泄漏
                self._check_memory_leaks(memory_usage)
                
                # 清理旧的历史记录
                self._cleanup_history()
                
                # 强制垃圾回收
                self._force_garbage_collection()
                
            except Exception as e:
                self.logger.error(f"Error in memory leak detection: {e}")
            
            self._shutdown_event.wait(self.check_interval)
    
    def _collect_memory_usage(self) -> MemoryUsage:
        """收集内存使用情况"""
        # tracemalloc统计
        current, peak = tracemalloc.get_traced_memory()
        snapshot = tracemalloc.take_snapshot()
        
        # 垃圾回收统计
        gc_collections = {}
        for i in range(3):  # Python有3代垃圾回收
            gc_collections[i] = gc.get_count()[i]
        
        # 对象计数
        object_count = len(gc.get_objects())
        
        return MemoryUsage(
            timestamp=datetime.now(),
            allocated_bytes=current,
            peak_bytes=peak,
            object_count=object_count,
            gc_collections=gc_collections,
            tracemalloc_snapshot=snapshot
        )
    
    def _check_memory_leaks(self, current_usage: MemoryUsage) -> None:
        """检查内存泄漏"""
        if not self._baseline_memory or len(self._memory_history) < 2:
            return
        
        # 检查内存增长
        baseline_memory = self._baseline_memory.allocated_bytes
        current_memory = current_usage.allocated_bytes
        growth_ratio = current_memory / baseline_memory if baseline_memory > 0 else 1.0
        
        if growth_ratio > self.growth_threshold:
            self._report_potential_leak(current_usage, growth_ratio)
        
        # 检查对象数量增长
        baseline_objects = self._baseline_memory.object_count
        current_objects = current_usage.object_count
        object_growth_ratio = current_objects / baseline_objects if baseline_objects > 0 else 1.0
        
        if object_growth_ratio > self.growth_threshold:
            self._report_object_growth(current_usage, object_growth_ratio)
    
    def _report_potential_leak(self, usage: MemoryUsage, growth_ratio: float) -> None:
        """报告潜在的内存泄漏"""
        message = (f"Potential memory leak detected: "
                  f"Memory usage grew by {(growth_ratio - 1) * 100:.1f}% "
                  f"({usage.allocated_bytes / 1024 / 1024:.1f} MB)")
        
        self.logger.warning(message)
        
        if self.event_bus:
            self.event_bus.publish(
                event_type="memory_leak_detected",
                data={
                    "growth_ratio": growth_ratio,
                    "current_memory_mb": usage.allocated_bytes / 1024 / 1024,
                    "baseline_memory_mb": self._baseline_memory.allocated_bytes / 1024 / 1024,
                    "timestamp": usage.timestamp.isoformat()
                },
                source="memory_leak_detector"
            )
    
    def _report_object_growth(self, usage: MemoryUsage, growth_ratio: float) -> None:
        """报告对象数量增长"""
        message = (f"Object count growth detected: "
                  f"Object count grew by {(growth_ratio - 1) * 100:.1f}% "
                  f"({usage.object_count} objects)")
        
        self.logger.warning(message)
        
        if self.event_bus:
            self.event_bus.publish(
                event_type="object_growth_detected",
                data={
                    "growth_ratio": growth_ratio,
                    "current_objects": usage.object_count,
                    "baseline_objects": self._baseline_memory.object_count,
                    "timestamp": usage.timestamp.isoformat()
                },
                source="memory_leak_detector"
            )
    
    def _cleanup_history(self) -> None:
        """清理历史记录"""
        # 保留最近24小时的记录
        cutoff_time = datetime.now() - timedelta(hours=24)
        self._memory_history = [
            usage for usage in self._memory_history
            if usage.timestamp > cutoff_time
        ]
    
    def _force_garbage_collection(self) -> None:
        """强制垃圾回收"""
        collected = gc.collect()
        if collected > 0:
            self.logger.debug(f"Garbage collection freed {collected} objects")
    
    def register_object(self, obj: Any, category: str = "default") -> None:
        """注册对象用于跟踪"""
        try:
            ref = weakref.ref(obj)
            self._object_refs[category].add(ref)
        except TypeError:
            # 某些对象类型不支持弱引用，跳过
            self.logger.debug(f"Cannot create weak reference for object of type {type(obj)}")
            pass
    
    def get_memory_report(self) -> Dict[str, Any]:
        """获取内存报告"""
        if not self._memory_history:
            return {}
        
        latest = self._memory_history[-1]
        
        # 计算趋势
        if len(self._memory_history) >= 2:
            previous = self._memory_history[-2]
            memory_trend = latest.allocated_bytes - previous.allocated_bytes
            object_trend = latest.object_count - previous.object_count
        else:
            memory_trend = 0
            object_trend = 0
        
        # 统计活跃对象引用
        active_refs = {}
        for category, refs in self._object_refs.items():
            # 清理死引用
            alive_refs = {ref for ref in refs if ref() is not None}
            self._object_refs[category] = alive_refs
            active_refs[category] = len(alive_refs)
        
        return {
            "current_memory_mb": latest.allocated_bytes / 1024 / 1024,
            "peak_memory_mb": latest.peak_bytes / 1024 / 1024,
            "object_count": latest.object_count,
            "memory_trend_mb": memory_trend / 1024 / 1024,
            "object_trend": object_trend,
            "gc_collections": latest.gc_collections,
            "active_object_refs": active_refs,
            "monitoring_duration_hours": len(self._memory_history) * self.check_interval / 3600
        }


class ResourceManager:
    """资源管理器 - 统一管理缓存、对象池和内存"""
    
    def __init__(self, logger: Optional[logging.Logger] = None,
                 event_bus: Optional[IEventBus] = None,
                 performance_monitor: Optional[IPerformanceMonitor] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.event_bus = event_bus
        self.performance_monitor = performance_monitor
        
        # 组件
        self._caches: Dict[str, ICache] = {}
        self._object_pools: Dict[str, ObjectPool] = {}
        self._memory_detector = MemoryLeakDetector(logger, event_bus)
        
        # 清理任务
        self._cleanup_executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="ResourceCleanup")
        self._cleanup_interval = 300  # 5分钟
        self._last_cleanup = datetime.now()
    
    def create_cache(self, name: str, max_size: int = 1000, 
                    default_ttl: Optional[int] = None) -> ICache:
        """创建缓存"""
        cache = LRUCache(max_size=max_size, default_ttl=default_ttl)
        self._caches[name] = cache
        
        self.logger.info(f"Created cache '{name}' with max_size={max_size}")
        return cache
    
    def get_cache(self, name: str) -> Optional[ICache]:
        """获取缓存"""
        return self._caches.get(name)
    
    def create_object_pool(self, name: str, factory: Callable[[], T], 
                          max_size: int = 100,
                          reset_func: Optional[Callable[[T], None]] = None,
                          validate_func: Optional[Callable[[T], bool]] = None) -> ObjectPool[T]:
        """创建对象池"""
        pool = ObjectPool(factory, max_size, reset_func, validate_func)
        self._object_pools[name] = pool
        
        self.logger.info(f"Created object pool '{name}' with max_size={max_size}")
        return pool
    
    def get_object_pool(self, name: str) -> Optional[ObjectPool]:
        """获取对象池"""
        return self._object_pools.get(name)
    
    def start_memory_monitoring(self) -> None:
        """开始内存监控"""
        self._memory_detector.start_monitoring()
    
    def stop_memory_monitoring(self) -> None:
        """停止内存监控"""
        self._memory_detector.stop_monitoring()
    
    def cleanup_resources(self) -> None:
        """清理资源"""
        self.logger.info("Starting resource cleanup")
        
        # 清理缓存过期项
        for name, cache in self._caches.items():
            if isinstance(cache, LRUCache):
                expired_count = cache.cleanup_expired()
                if expired_count > 0:
                    self.logger.debug(f"Cleaned {expired_count} expired items from cache '{name}'")
        
        # 强制垃圾回收
        collected = gc.collect()
        if collected > 0:
            self.logger.debug(f"Garbage collection freed {collected} objects")
        
        self._last_cleanup = datetime.now()
        
        # 发布清理事件
        if self.event_bus:
            self.event_bus.publish(
                event_type="resource_cleanup_completed",
                data={"timestamp": self._last_cleanup.isoformat()},
                source="resource_manager"
            )
    
    def get_resource_summary(self) -> Dict[str, Any]:
        """获取资源摘要"""
        cache_stats = {}
        for name, cache in self._caches.items():
            cache_stats[name] = cache.get_stats().__dict__
        
        pool_stats = {}
        for name, pool in self._object_pools.items():
            pool_stats[name] = pool.get_stats().__dict__
        
        memory_report = self._memory_detector.get_memory_report()
        
        return {
            "caches": cache_stats,
            "object_pools": pool_stats,
            "memory": memory_report,
            "last_cleanup": self._last_cleanup.isoformat()
        }
    
    def auto_cleanup_if_needed(self) -> None:
        """如果需要则自动清理"""
        if datetime.now() - self._last_cleanup > timedelta(seconds=self._cleanup_interval):
            self._cleanup_executor.submit(self.cleanup_resources)
    
    def shutdown(self) -> None:
        """关闭资源管理器"""
        self.stop_memory_monitoring()
        
        # 清理所有缓存和对象池
        for cache in self._caches.values():
            cache.clear()
        
        for pool in self._object_pools.values():
            pool.clear()
        
        # 关闭清理执行器
        self._cleanup_executor.shutdown(wait=True)
        
        self.logger.info("Resource manager shutdown complete")


# 装饰器和上下文管理器
@contextmanager
def cached_result(cache: ICache, key: str, ttl: Optional[int] = None):
    """缓存结果的上下文管理器"""
    # 尝试从缓存获取
    result = cache.get(key)
    if result is not None:
        yield result
        return
    
    # 缓存未命中，需要计算结果
    class ResultCapture:
        def __init__(self):
            self.value = None
        
        def set(self, value):
            self.value = value
            cache.put(key, value, ttl)
    
    capture = ResultCapture()
    yield capture
    

def memory_optimized(func: Callable) -> Callable:
    """内存优化装饰器"""
    def wrapper(*args, **kwargs):
        # 执行前清理
        gc.collect()
        
        try:
            result = func(*args, **kwargs)
            return result
        finally:
            # 执行后清理
            gc.collect()
    
    return wrapper