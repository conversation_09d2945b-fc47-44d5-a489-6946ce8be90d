"""
性能监控系统

提供全面的性能监控功能，支持：
- 方法级别的执行时间统计
- 内存使用和资源消耗监控
- 性能指标收集和报告
- 性能告警和异常检测
- 与事件总线集成的实时监控
"""

import gc
import logging
import psutil
import threading
import time
import tracemalloc
from abc import ABC, abstractmethod
from collections import defaultdict, deque
from contextlib import contextmanager
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from functools import wraps
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union
from concurrent.futures import ThreadPoolExecutor
import weakref

from .event_bus import IEventBus


@dataclass
class PerformanceMetric:
    """性能指标数据结构"""
    name: str
    value: Union[int, float]
    unit: str
    timestamp: datetime
    component: str
    operation: Optional[str] = None
    tags: Dict[str, str] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ExecutionStats:
    """执行统计信息"""
    operation_name: str
    component: str
    total_calls: int = 0
    total_time: float = 0.0
    min_time: float = float('inf')
    max_time: float = 0.0
    avg_time: float = 0.0
    last_execution: Optional[datetime] = None
    error_count: int = 0
    success_rate: float = 100.0
    recent_times: deque = field(default_factory=lambda: deque(maxlen=100))


@dataclass
class MemorySnapshot:
    """内存快照"""
    timestamp: datetime
    rss_memory: int  # 物理内存使用 (bytes)
    vms_memory: int  # 虚拟内存使用 (bytes)
    memory_percent: float  # 内存使用百分比
    available_memory: int  # 可用内存 (bytes)
    gc_stats: Dict[str, Any]  # 垃圾回收统计
    tracemalloc_stats: Optional[Dict[str, Any]] = None  # tracemalloc统计


@dataclass
class SystemResourceSnapshot:
    """系统资源快照"""
    timestamp: datetime
    cpu_percent: float
    memory_snapshot: MemorySnapshot
    disk_io: Dict[str, Any]
    network_io: Dict[str, Any]
    thread_count: int
    file_descriptor_count: int


@dataclass
class PerformanceAlert:
    """性能告警"""
    alert_id: str
    alert_type: str
    severity: str
    message: str
    metric_name: str
    threshold_value: Union[int, float]
    actual_value: Union[int, float]
    component: str
    timestamp: datetime
    resolved: bool = False
    resolution_time: Optional[datetime] = None


class PerformanceThreshold:
    """性能阈值配置"""
    
    def __init__(self, metric_name: str, warning_threshold: Union[int, float],
                 critical_threshold: Union[int, float], 
                 comparison_type: str = "greater_than"):
        self.metric_name = metric_name
        self.warning_threshold = warning_threshold
        self.critical_threshold = critical_threshold
        self.comparison_type = comparison_type  # greater_than, less_than, equals
    
    def check_threshold(self, value: Union[int, float]) -> Optional[str]:
        """检查阈值，返回告警级别"""
        if self.comparison_type == "greater_than":
            if value >= self.critical_threshold:
                return "critical"
            elif value >= self.warning_threshold:
                return "warning"
        elif self.comparison_type == "less_than":
            if value <= self.critical_threshold:
                return "critical"
            elif value <= self.warning_threshold:
                return "warning"
        
        return None


class IPerformanceMonitor(ABC):
    """性能监控器接口"""
    
    @abstractmethod
    def start_monitoring(self) -> None:
        """开始监控"""
        pass
    
    @abstractmethod
    def stop_monitoring(self) -> None:
        """停止监控"""
        pass
    
    @abstractmethod
    def record_metric(self, metric: PerformanceMetric) -> None:
        """记录性能指标"""
        pass
    
    @abstractmethod
    def get_metrics(self, component: Optional[str] = None,
                   time_range: Optional[Tuple[datetime, datetime]] = None) -> List[PerformanceMetric]:
        """获取性能指标"""
        pass
    
    @abstractmethod
    def get_execution_stats(self, operation_name: Optional[str] = None) -> Dict[str, ExecutionStats]:
        """获取执行统计"""
        pass


class PerformanceMonitor(IPerformanceMonitor):
    """性能监控器实现"""
    
    def __init__(self, logger: Optional[logging.Logger] = None,
                 event_bus: Optional[IEventBus] = None,
                 monitoring_interval: float = 5.0,
                 enable_memory_tracking: bool = True,
                 enable_tracemalloc: bool = False,
                 max_metrics_history: int = 10000):
        self.logger = logger or logging.getLogger(__name__)
        self.event_bus = event_bus
        self.monitoring_interval = monitoring_interval
        self.enable_memory_tracking = enable_memory_tracking
        self.enable_tracemalloc = enable_tracemalloc
        self.max_metrics_history = max_metrics_history
        
        # 监控状态
        self._monitoring = False
        self._monitor_thread: Optional[threading.Thread] = None
        self._shutdown_event = threading.Event()
        
        # 数据存储
        self._metrics: deque = deque(maxlen=max_metrics_history)
        self._execution_stats: Dict[str, ExecutionStats] = {}
        self._memory_snapshots: deque = deque(maxlen=1000)
        self._resource_snapshots: deque = deque(maxlen=1000)
        
        # 告警系统
        self._thresholds: List[PerformanceThreshold] = []
        self._active_alerts: Dict[str, PerformanceAlert] = {}
        self._alert_counter = 0
        
        # 线程安全
        self._stats_lock = threading.RLock()
        self._metrics_lock = threading.RLock()
        
        # 进程信息
        self._process = psutil.Process()
        
        # 启用tracemalloc
        if self.enable_tracemalloc:
            tracemalloc.start()
        
        # 默认阈值配置
        self._setup_default_thresholds()
    
    def _setup_default_thresholds(self) -> None:
        """设置默认性能阈值"""
        default_thresholds = [
            PerformanceThreshold("execution_time", 5.0, 10.0),  # 执行时间 (秒)
            PerformanceThreshold("memory_percent", 80.0, 90.0),  # 内存使用率 (%)
            PerformanceThreshold("cpu_percent", 80.0, 95.0),     # CPU使用率 (%)
            PerformanceThreshold("error_rate", 5.0, 10.0),       # 错误率 (%)
        ]
        self._thresholds.extend(default_thresholds)
    
    def start_monitoring(self) -> None:
        """开始监控"""
        if self._monitoring:
            self.logger.warning("Performance monitoring is already running")
            return
        
        self._monitoring = True
        self._shutdown_event.clear()
        
        # 启动监控线程
        self._monitor_thread = threading.Thread(
            target=self._monitoring_loop,
            name="PerformanceMonitor",
            daemon=True
        )
        self._monitor_thread.start()
        
        self.logger.info("Performance monitoring started")
        
        # 发布监控启动事件
        if self.event_bus:
            self.event_bus.publish(
                event_type="performance_monitoring_started",
                data={"monitoring_interval": self.monitoring_interval},
                source="performance_monitor"
            )
    
    def stop_monitoring(self) -> None:
        """停止监控"""
        if not self._monitoring:
            return
        
        self._monitoring = False
        self._shutdown_event.set()
        
        # 等待监控线程结束
        if self._monitor_thread and self._monitor_thread.is_alive():
            self._monitor_thread.join(timeout=5.0)
        
        self.logger.info("Performance monitoring stopped")
        
        # 发布监控停止事件
        if self.event_bus:
            self.event_bus.publish(
                event_type="performance_monitoring_stopped",
                data={"total_metrics": len(self._metrics)},
                source="performance_monitor"
            )
    
    def _monitoring_loop(self) -> None:
        """监控循环"""
        while self._monitoring and not self._shutdown_event.is_set():
            try:
                # 收集系统资源快照
                resource_snapshot = self._collect_system_resources()
                
                with self._metrics_lock:
                    self._resource_snapshots.append(resource_snapshot)
                
                # 记录系统指标
                self._record_system_metrics(resource_snapshot)
                
                # 检查性能阈值
                self._check_performance_thresholds(resource_snapshot)
                
                # 清理过期数据
                self._cleanup_old_data()
                
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
            
            # 等待下一次监控
            self._shutdown_event.wait(self.monitoring_interval)
    
    def _collect_system_resources(self) -> SystemResourceSnapshot:
        """收集系统资源信息"""
        # CPU使用率
        cpu_percent = self._process.cpu_percent()
        
        # 内存信息
        memory_info = self._process.memory_info()
        memory_percent = self._process.memory_percent()
        system_memory = psutil.virtual_memory()
        
        # 垃圾回收统计
        gc_stats = {
            "collections": gc.get_stats(),
            "counts": gc.get_count(),
            "threshold": gc.get_threshold()
        }
        
        # tracemalloc统计
        tracemalloc_stats = None
        if self.enable_tracemalloc:
            try:
                current, peak = tracemalloc.get_traced_memory()
                tracemalloc_stats = {
                    "current": current,
                    "peak": peak,
                    "top_stats": tracemalloc.take_snapshot().statistics('lineno')[:10]
                }
            except Exception as e:
                self.logger.debug(f"Failed to get tracemalloc stats: {e}")
        
        memory_snapshot = MemorySnapshot(
            timestamp=datetime.now(),
            rss_memory=memory_info.rss,
            vms_memory=memory_info.vms,
            memory_percent=memory_percent,
            available_memory=system_memory.available,
            gc_stats=gc_stats,
            tracemalloc_stats=tracemalloc_stats
        )
        
        # IO统计
        try:
            io_counters = self._process.io_counters()
            disk_io = {
                "read_count": io_counters.read_count,
                "write_count": io_counters.write_count,
                "read_bytes": io_counters.read_bytes,
                "write_bytes": io_counters.write_bytes
            }
        except (psutil.AccessDenied, AttributeError):
            disk_io = {}
        
        try:
            net_io = psutil.net_io_counters()
            network_io = {
                "bytes_sent": net_io.bytes_sent,
                "bytes_recv": net_io.bytes_recv,
                "packets_sent": net_io.packets_sent,
                "packets_recv": net_io.packets_recv
            }
        except AttributeError:
            network_io = {}
        
        # 线程和文件描述符数量
        thread_count = self._process.num_threads()
        try:
            fd_count = self._process.num_fds()
        except (psutil.AccessDenied, AttributeError):
            fd_count = 0
        
        return SystemResourceSnapshot(
            timestamp=datetime.now(),
            cpu_percent=cpu_percent,
            memory_snapshot=memory_snapshot,
            disk_io=disk_io,
            network_io=network_io,
            thread_count=thread_count,
            file_descriptor_count=fd_count
        )
    
    def _record_system_metrics(self, snapshot: SystemResourceSnapshot) -> None:
        """记录系统指标"""
        metrics = [
            PerformanceMetric(
                name="cpu_percent",
                value=snapshot.cpu_percent,
                unit="percent",
                timestamp=snapshot.timestamp,
                component="system"
            ),
            PerformanceMetric(
                name="memory_percent",
                value=snapshot.memory_snapshot.memory_percent,
                unit="percent",
                timestamp=snapshot.timestamp,
                component="system"
            ),
            PerformanceMetric(
                name="memory_rss",
                value=snapshot.memory_snapshot.rss_memory,
                unit="bytes",
                timestamp=snapshot.timestamp,
                component="system"
            ),
            PerformanceMetric(
                name="memory_vms",
                value=snapshot.memory_snapshot.vms_memory,
                unit="bytes",
                timestamp=snapshot.timestamp,
                component="system"
            ),
            PerformanceMetric(
                name="thread_count",
                value=snapshot.thread_count,
                unit="count",
                timestamp=snapshot.timestamp,
                component="system"
            )
        ]
        
        # 添加IO指标
        if snapshot.disk_io:
            metrics.extend([
                PerformanceMetric(
                    name="disk_read_bytes",
                    value=snapshot.disk_io.get("read_bytes", 0),
                    unit="bytes",
                    timestamp=snapshot.timestamp,
                    component="system"
                ),
                PerformanceMetric(
                    name="disk_write_bytes",
                    value=snapshot.disk_io.get("write_bytes", 0),
                    unit="bytes",
                    timestamp=snapshot.timestamp,
                    component="system"
                )
            ])
        
        # 记录所有指标
        for metric in metrics:
            self.record_metric(metric)
    
    def _check_performance_thresholds(self, snapshot: SystemResourceSnapshot) -> None:
        """检查性能阈值"""
        # 检查CPU使用率
        self._check_threshold("cpu_percent", snapshot.cpu_percent, "system")
        
        # 检查内存使用率
        self._check_threshold("memory_percent", snapshot.memory_snapshot.memory_percent, "system")
        
        # 检查执行统计中的错误率
        with self._stats_lock:
            for operation_name, stats in self._execution_stats.items():
                if stats.total_calls > 0:
                    error_rate = (stats.error_count / stats.total_calls) * 100
                    self._check_threshold("error_rate", error_rate, stats.component, operation_name)
                    
                    # 检查平均执行时间
                    if stats.avg_time > 0:
                        self._check_threshold("execution_time", stats.avg_time, stats.component, operation_name)
    
    def _check_threshold(self, metric_name: str, value: Union[int, float], 
                        component: str, operation: Optional[str] = None) -> None:
        """检查单个阈值"""
        for threshold in self._thresholds:
            if threshold.metric_name == metric_name:
                alert_level = threshold.check_threshold(value)
                if alert_level:
                    alert_key = f"{component}_{metric_name}_{operation or 'global'}"
                    
                    # 检查是否已有活跃告警
                    if alert_key not in self._active_alerts:
                        self._create_alert(alert_key, alert_level, metric_name, value, 
                                         threshold, component, operation)
                else:
                    # 检查是否需要解决现有告警
                    alert_key = f"{component}_{metric_name}_{operation or 'global'}"
                    if alert_key in self._active_alerts:
                        self._resolve_alert(alert_key)
    
    def _create_alert(self, alert_key: str, severity: str, metric_name: str,
                     actual_value: Union[int, float], threshold: PerformanceThreshold,
                     component: str, operation: Optional[str] = None) -> None:
        """创建性能告警"""
        self._alert_counter += 1
        alert_id = f"PERF_ALERT_{int(time.time())}_{self._alert_counter}"
        
        threshold_value = (threshold.critical_threshold if severity == "critical" 
                          else threshold.warning_threshold)
        
        alert = PerformanceAlert(
            alert_id=alert_id,
            alert_type="performance_threshold",
            severity=severity,
            message=f"{metric_name} {threshold.comparison_type} threshold exceeded: {actual_value} > {threshold_value}",
            metric_name=metric_name,
            threshold_value=threshold_value,
            actual_value=actual_value,
            component=component,
            timestamp=datetime.now()
        )
        
        self._active_alerts[alert_key] = alert
        
        # 记录告警日志
        log_level = logging.CRITICAL if severity == "critical" else logging.WARNING
        self.logger.log(log_level, f"Performance alert: {alert.message}")
        
        # 发布告警事件
        if self.event_bus:
            self.event_bus.publish(
                event_type="performance_alert_created",
                data={
                    "alert_id": alert_id,
                    "severity": severity,
                    "metric_name": metric_name,
                    "actual_value": actual_value,
                    "threshold_value": threshold_value,
                    "component": component,
                    "operation": operation
                },
                source="performance_monitor"
            )
    
    def _resolve_alert(self, alert_key: str) -> None:
        """解决告警"""
        if alert_key in self._active_alerts:
            alert = self._active_alerts[alert_key]
            alert.resolved = True
            alert.resolution_time = datetime.now()
            
            self.logger.info(f"Performance alert resolved: {alert.alert_id}")
            
            # 发布告警解决事件
            if self.event_bus:
                self.event_bus.publish(
                    event_type="performance_alert_resolved",
                    data={
                        "alert_id": alert.alert_id,
                        "resolution_time": alert.resolution_time.isoformat()
                    },
                    source="performance_monitor"
                )
            
            # 从活跃告警中移除
            del self._active_alerts[alert_key]
    
    def _cleanup_old_data(self) -> None:
        """清理过期数据"""
        cutoff_time = datetime.now() - timedelta(hours=24)
        
        # 清理旧的内存快照
        while (self._memory_snapshots and 
               self._memory_snapshots[0].timestamp < cutoff_time):
            self._memory_snapshots.popleft()
        
        # 清理旧的资源快照
        while (self._resource_snapshots and 
               self._resource_snapshots[0].timestamp < cutoff_time):
            self._resource_snapshots.popleft()
    
    def record_metric(self, metric: PerformanceMetric) -> None:
        """记录性能指标"""
        with self._metrics_lock:
            self._metrics.append(metric)
        
        # 发布指标事件
        if self.event_bus:
            self.event_bus.publish(
                event_type="performance_metric_recorded",
                data={
                    "name": metric.name,
                    "value": metric.value,
                    "unit": metric.unit,
                    "component": metric.component,
                    "operation": metric.operation,
                    "timestamp": metric.timestamp.isoformat()
                },
                source="performance_monitor"
            )
    
    def get_metrics(self, component: Optional[str] = None,
                   time_range: Optional[Tuple[datetime, datetime]] = None) -> List[PerformanceMetric]:
        """获取性能指标"""
        with self._metrics_lock:
            metrics = list(self._metrics)
        
        # 按组件过滤
        if component:
            metrics = [m for m in metrics if m.component == component]
        
        # 按时间范围过滤
        if time_range:
            start_time, end_time = time_range
            metrics = [m for m in metrics if start_time <= m.timestamp <= end_time]
        
        return metrics
    
    def get_execution_stats(self, operation_name: Optional[str] = None) -> Dict[str, ExecutionStats]:
        """获取执行统计"""
        with self._stats_lock:
            if operation_name:
                return {operation_name: self._execution_stats.get(operation_name)}
            return dict(self._execution_stats)
    
    def record_execution(self, operation_name: str, component: str, 
                        execution_time: float, success: bool = True) -> None:
        """记录方法执行统计"""
        with self._stats_lock:
            if operation_name not in self._execution_stats:
                self._execution_stats[operation_name] = ExecutionStats(
                    operation_name=operation_name,
                    component=component
                )
            
            stats = self._execution_stats[operation_name]
            stats.total_calls += 1
            stats.total_time += execution_time
            stats.min_time = min(stats.min_time, execution_time)
            stats.max_time = max(stats.max_time, execution_time)
            stats.avg_time = stats.total_time / stats.total_calls
            stats.last_execution = datetime.now()
            stats.recent_times.append(execution_time)
            
            if not success:
                stats.error_count += 1
            
            stats.success_rate = ((stats.total_calls - stats.error_count) / stats.total_calls) * 100
        
        # 记录执行时间指标
        self.record_metric(PerformanceMetric(
            name="execution_time",
            value=execution_time,
            unit="seconds",
            timestamp=datetime.now(),
            component=component,
            operation=operation_name,
            tags={"success": str(success)}
        ))
    
    def add_threshold(self, threshold: PerformanceThreshold) -> None:
        """添加性能阈值"""
        self._thresholds.append(threshold)
        self.logger.debug(f"Added performance threshold for {threshold.metric_name}")
    
    def get_active_alerts(self) -> List[PerformanceAlert]:
        """获取活跃告警"""
        return list(self._active_alerts.values())
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        with self._stats_lock:
            total_operations = len(self._execution_stats)
            total_calls = sum(stats.total_calls for stats in self._execution_stats.values())
            total_errors = sum(stats.error_count for stats in self._execution_stats.values())
            avg_success_rate = (
                sum(stats.success_rate for stats in self._execution_stats.values()) / total_operations
                if total_operations > 0 else 100.0
            )
        
        # 最新资源快照
        latest_snapshot = self._resource_snapshots[-1] if self._resource_snapshots else None
        
        return {
            "monitoring_active": self._monitoring,
            "total_metrics": len(self._metrics),
            "total_operations": total_operations,
            "total_calls": total_calls,
            "total_errors": total_errors,
            "average_success_rate": avg_success_rate,
            "active_alerts": len(self._active_alerts),
            "current_resources": {
                "cpu_percent": latest_snapshot.cpu_percent if latest_snapshot else 0,
                "memory_percent": latest_snapshot.memory_snapshot.memory_percent if latest_snapshot else 0,
                "thread_count": latest_snapshot.thread_count if latest_snapshot else 0
            } if latest_snapshot else {}
        }


@contextmanager
def performance_timer(monitor: PerformanceMonitor, operation_name: str, component: str):
    """性能计时上下文管理器"""
    start_time = time.time()
    success = True
    
    try:
        yield
    except Exception:
        success = False
        raise
    finally:
        execution_time = time.time() - start_time
        monitor.record_execution(operation_name, component, execution_time, success)


def performance_monitor_decorator(monitor: PerformanceMonitor, component: str):
    """性能监控装饰器"""
    def decorator(func: Callable) -> Callable:
        @wraps(func)
        def wrapper(*args, **kwargs):
            with performance_timer(monitor, func.__name__, component):
                return func(*args, **kwargs)
        return wrapper
    return decorator


class PerformanceProfiler:
    """性能分析器 - 用于详细的性能分析"""
    
    def __init__(self, monitor: PerformanceMonitor):
        self.monitor = monitor
        self._profiles: Dict[str, Dict[str, Any]] = {}
    
    @contextmanager
    def profile_operation(self, operation_name: str, component: str):
        """性能分析上下文管理器"""
        # 开始内存跟踪
        if self.monitor.enable_tracemalloc:
            tracemalloc.start()
            start_memory = tracemalloc.get_traced_memory()[0]
        else:
            start_memory = 0
        
        start_time = time.time()
        start_cpu = time.process_time()
        
        try:
            yield
        finally:
            end_time = time.time()
            end_cpu = time.process_time()
            
            if self.monitor.enable_tracemalloc:
                end_memory = tracemalloc.get_traced_memory()[0]
                memory_delta = end_memory - start_memory
            else:
                memory_delta = 0
            
            # 记录详细的性能分析数据
            profile_data = {
                "wall_time": end_time - start_time,
                "cpu_time": end_cpu - start_cpu,
                "memory_delta": memory_delta,
                "timestamp": datetime.now()
            }
            
            profile_key = f"{component}.{operation_name}"
            if profile_key not in self._profiles:
                self._profiles[profile_key] = []
            
            self._profiles[profile_key].append(profile_data)
            
            # 记录到监控器
            self.monitor.record_execution(
                operation_name, component, 
                profile_data["wall_time"], True
            )
    
    def get_profile_summary(self, operation_name: Optional[str] = None) -> Dict[str, Any]:
        """获取性能分析摘要"""
        if operation_name:
            profiles = {k: v for k, v in self._profiles.items() if operation_name in k}
        else:
            profiles = self._profiles
        
        summary = {}
        for profile_key, profile_list in profiles.items():
            if not profile_list:
                continue
            
            wall_times = [p["wall_time"] for p in profile_list]
            cpu_times = [p["cpu_time"] for p in profile_list]
            memory_deltas = [p["memory_delta"] for p in profile_list]
            
            summary[profile_key] = {
                "count": len(profile_list),
                "wall_time": {
                    "avg": sum(wall_times) / len(wall_times),
                    "min": min(wall_times),
                    "max": max(wall_times),
                    "total": sum(wall_times)
                },
                "cpu_time": {
                    "avg": sum(cpu_times) / len(cpu_times),
                    "min": min(cpu_times),
                    "max": max(cpu_times),
                    "total": sum(cpu_times)
                },
                "memory_delta": {
                    "avg": sum(memory_deltas) / len(memory_deltas),
                    "min": min(memory_deltas),
                    "max": max(memory_deltas),
                    "total": sum(memory_deltas)
                }
            }
        
        return summary