"""
功能开关管理器
提供功能开关管理功能，支持：
- 配置驱动的功能开关
- 运行时动态启用/禁用功能
- 环境特定的功能配置
- 功能开关状态监控和事件发布
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Callable
from dataclasses import dataclass
from enum import Enum
from datetime import datetime

from .configuration_manager import IConfigurationManager
from .event_bus import IEventBus


class FeatureToggleError(Exception):
    """功能开关异常"""
    pass


class FeatureToggleState(Enum):
    """功能开关状态"""
    ENABLED = "enabled"
    DISABLED = "disabled"
    CONDITIONAL = "conditional"


@dataclass
class FeatureToggle:
    """功能开关数据模型"""
    name: str
    state: FeatureToggleState
    description: str = ""
    conditions: Dict[str, Any] = None
    metadata: Dict[str, Any] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        if self.conditions is None:
            self.conditions = {}
        if self.metadata is None:
            self.metadata = {}
        if self.created_at is None:
            self.created_at = datetime.now()
        if self.updated_at is None:
            self.updated_at = datetime.now()


class IFeatureToggleManager(ABC):
    """功能开关管理器接口"""
    
    @abstractmethod
    def is_feature_enabled(self, feature_name: str) -> bool:
        """检查功能是否启用"""
        pass
    
    @abstractmethod
    def enable_feature(self, feature_name: str) -> None:
        """启用功能"""
        pass
    
    @abstractmethod
    def disable_feature(self, feature_name: str) -> None:
        """禁用功能"""
        pass
    
    @abstractmethod
    def get_feature_toggle(self, feature_name: str) -> Optional[FeatureToggle]:
        """获取功能开关"""
        pass
    
    @abstractmethod
    def get_all_features(self) -> Dict[str, FeatureToggle]:
        """获取所有功能开关"""
        pass
    
    @abstractmethod
    def register_feature(self, feature: FeatureToggle) -> None:
        """注册功能开关"""
        pass


class FeatureToggleManager(IFeatureToggleManager):
    """
    功能开关管理器实现
    
    负责管理系统中的功能开关，支持配置驱动的功能控制、
    运行时动态开关、条件性功能启用和状态监控。
    """
    
    def __init__(self, 
                 config_manager: IConfigurationManager,
                 event_bus: IEventBus,
                 logger: Optional[logging.Logger] = None):
        """
        初始化功能开关管理器
        
        Args:
            config_manager: 配置管理器
            event_bus: 事件总线
            logger: 日志记录器
        """
        self._config_manager = config_manager
        self._event_bus = event_bus
        self._logger = logger or logging.getLogger(__name__)
        
        # 功能开关存储
        self._features: Dict[str, FeatureToggle] = {}
        
        # 条件评估器
        self._condition_evaluators: Dict[str, Callable[[Any], bool]] = {}
        
        # 注册默认条件评估器
        self._register_default_condition_evaluators()
        
        # 从配置加载功能开关
        self._load_features_from_config()
    
    def _register_default_condition_evaluators(self) -> None:
        """注册默认条件评估器"""
        self._condition_evaluators.update({
            'environment': self._evaluate_environment_condition,
            'config_value': self._evaluate_config_value_condition,
            'time_range': self._evaluate_time_range_condition,
            'percentage': self._evaluate_percentage_condition
        })
    
    def _load_features_from_config(self) -> None:
        """从配置加载功能开关"""
        try:
            features_config = self._config_manager.get_config('features', {})
            
            for feature_name, feature_config in features_config.items():
                try:
                    feature_toggle = self._parse_feature_config(feature_name, feature_config)
                    self._features[feature_name] = feature_toggle
                    self._logger.debug(f"Loaded feature toggle: {feature_name}")
                except Exception as e:
                    self._logger.error(f"Failed to load feature {feature_name}: {e}")
            
            self._logger.info(f"Loaded {len(self._features)} feature toggles from config")
            
        except Exception as e:
            self._logger.error(f"Failed to load features from config: {e}")
    
    def _parse_feature_config(self, feature_name: str, config: Dict[str, Any]) -> FeatureToggle:
        """解析功能配置"""
        # 默认值
        defaults = {
            'state': 'enabled',
            'description': '',
            'conditions': {},
            'metadata': {}
        }
        
        # 合并配置
        merged_config = {**defaults, **config}
        
        # 解析状态
        state_map = {
            'enabled': FeatureToggleState.ENABLED,
            'disabled': FeatureToggleState.DISABLED,
            'conditional': FeatureToggleState.CONDITIONAL
        }
        
        state = state_map.get(merged_config['state'], FeatureToggleState.ENABLED)
        
        return FeatureToggle(
            name=feature_name,
            state=state,
            description=merged_config['description'],
            conditions=merged_config['conditions'],
            metadata=merged_config['metadata']
        )
    
    def is_feature_enabled(self, feature_name: str) -> bool:
        """检查功能是否启用"""
        try:
            feature = self._features.get(feature_name)
            
            if not feature:
                # 如果功能不存在，检查配置中是否有默认值
                default_enabled = self._config_manager.get_config(f'features.{feature_name}.enabled', True)
                return default_enabled
            
            # 根据状态判断
            if feature.state == FeatureToggleState.ENABLED:
                return True
            elif feature.state == FeatureToggleState.DISABLED:
                return False
            elif feature.state == FeatureToggleState.CONDITIONAL:
                return self._evaluate_conditions(feature)
            
            return False
            
        except Exception as e:
            self._logger.error(f"Error checking feature {feature_name}: {e}")
            return False
    
    def _evaluate_conditions(self, feature: FeatureToggle) -> bool:
        """评估条件"""
        if not feature.conditions:
            return True
        
        try:
            # 评估所有条件（默认使用AND逻辑）
            for condition_type, condition_value in feature.conditions.items():
                evaluator = self._condition_evaluators.get(condition_type)
                if evaluator:
                    if not evaluator(condition_value):
                        return False
                else:
                    self._logger.warning(f"Unknown condition type: {condition_type}")
                    return False
            
            return True
            
        except Exception as e:
            self._logger.error(f"Error evaluating conditions for feature {feature.name}: {e}")
            return False
    
    def _evaluate_environment_condition(self, condition_value: Any) -> bool:
        """评估环境条件"""
        current_env = self._config_manager.get_config('environment', 'production')
        
        if isinstance(condition_value, str):
            return current_env == condition_value
        elif isinstance(condition_value, list):
            return current_env in condition_value
        
        return False
    
    def _evaluate_config_value_condition(self, condition_value: Any) -> bool:
        """评估配置值条件"""
        if not isinstance(condition_value, dict):
            return False
        
        config_key = condition_value.get('key')
        expected_value = condition_value.get('value')
        
        if not config_key:
            return False
        
        actual_value = self._config_manager.get_config(config_key)
        return actual_value == expected_value
    
    def _evaluate_time_range_condition(self, condition_value: Any) -> bool:
        """评估时间范围条件"""
        if not isinstance(condition_value, dict):
            return False
        
        start_time = condition_value.get('start')
        end_time = condition_value.get('end')
        
        if not start_time or not end_time:
            return False
        
        now = datetime.now()
        
        try:
            start_dt = datetime.fromisoformat(start_time)
            end_dt = datetime.fromisoformat(end_time)
            return start_dt <= now <= end_dt
        except Exception:
            return False
    
    def _evaluate_percentage_condition(self, condition_value: Any) -> bool:
        """评估百分比条件"""
        if not isinstance(condition_value, (int, float)):
            return False
        
        if not (0 <= condition_value <= 100):
            return False
        
        # 简单的百分比实现（实际应用中可能需要更复杂的逻辑）
        import random
        return random.randint(1, 100) <= condition_value
    
    def enable_feature(self, feature_name: str) -> None:
        """启用功能"""
        try:
            feature = self._features.get(feature_name)
            
            if not feature:
                # 创建新的功能开关
                feature = FeatureToggle(
                    name=feature_name,
                    state=FeatureToggleState.ENABLED,
                    description=f"Dynamically enabled feature: {feature_name}"
                )
                self._features[feature_name] = feature
            else:
                # 更新现有功能开关
                feature.state = FeatureToggleState.ENABLED
                feature.updated_at = datetime.now()
            
            # 发布事件
            self._event_bus.publish('feature_enabled', {
                'feature_name': feature_name,
                'feature': feature
            })
            
            self._logger.info(f"Feature enabled: {feature_name}")
            
        except Exception as e:
            self._logger.error(f"Failed to enable feature {feature_name}: {e}")
            raise FeatureToggleError(f"Failed to enable feature {feature_name}: {e}")
    
    def disable_feature(self, feature_name: str) -> None:
        """禁用功能"""
        try:
            feature = self._features.get(feature_name)
            
            if not feature:
                # 创建新的功能开关
                feature = FeatureToggle(
                    name=feature_name,
                    state=FeatureToggleState.DISABLED,
                    description=f"Dynamically disabled feature: {feature_name}"
                )
                self._features[feature_name] = feature
            else:
                # 更新现有功能开关
                feature.state = FeatureToggleState.DISABLED
                feature.updated_at = datetime.now()
            
            # 发布事件
            self._event_bus.publish('feature_disabled', {
                'feature_name': feature_name,
                'feature': feature
            })
            
            self._logger.info(f"Feature disabled: {feature_name}")
            
        except Exception as e:
            self._logger.error(f"Failed to disable feature {feature_name}: {e}")
            raise FeatureToggleError(f"Failed to disable feature {feature_name}: {e}")
    
    def get_feature_toggle(self, feature_name: str) -> Optional[FeatureToggle]:
        """获取功能开关"""
        return self._features.get(feature_name)
    
    def get_all_features(self) -> Dict[str, FeatureToggle]:
        """获取所有功能开关"""
        return self._features.copy()
    
    def register_feature(self, feature: FeatureToggle) -> None:
        """注册功能开关"""
        try:
            self._features[feature.name] = feature
            
            # 发布事件
            self._event_bus.publish('feature_registered', {
                'feature_name': feature.name,
                'feature': feature
            })
            
            self._logger.info(f"Feature registered: {feature.name}")
            
        except Exception as e:
            self._logger.error(f"Failed to register feature {feature.name}: {e}")
            raise FeatureToggleError(f"Failed to register feature {feature.name}: {e}")
    
    def register_condition_evaluator(self, condition_type: str, evaluator: Callable[[Any], bool]) -> None:
        """注册条件评估器"""
        self._condition_evaluators[condition_type] = evaluator
        self._logger.debug(f"Registered condition evaluator: {condition_type}")
    
    def get_feature_status_summary(self) -> Dict[str, Any]:
        """获取功能状态摘要"""
        summary = {
            'total_features': len(self._features),
            'enabled_features': 0,
            'disabled_features': 0,
            'conditional_features': 0,
            'features': {}
        }
        
        for name, feature in self._features.items():
            is_enabled = self.is_feature_enabled(name)
            summary['features'][name] = {
                'state': feature.state.value,
                'enabled': is_enabled,
                'description': feature.description
            }
            
            if is_enabled:
                summary['enabled_features'] += 1
            else:
                summary['disabled_features'] += 1
                
            if feature.state == FeatureToggleState.CONDITIONAL:
                summary['conditional_features'] += 1
        
        return summary