"""
OPRO服务工厂

提供OPRO服务的创建和配置管理，支持：
- 服务依赖注入
- 配置驱动创建
- 服务生命周期管理
- 与基础设施组件集成
"""

import logging
from typing import Dict, Any, Optional

from ..services.opro_service import OPROService
from ..services.historical_score_service import HistoricalScoreService
from ..services.prompt_template_service import PromptTemplateService
from ..historical_score_manager import HistoricalScoreManager
from .service_registry import ServiceRegistry
from .configuration_manager import ConfigurationManager


class OPROServiceFactory:
    """
    OPRO服务工厂
    
    负责创建和配置OPRO服务实例，管理服务依赖关系
    """
    
    def __init__(self,
                 service_registry: ServiceRegistry,
                 configuration_manager: ConfigurationManager,
                 logger: Optional[logging.Logger] = None):
        """
        初始化OPRO服务工厂
        
        Args:
            service_registry: 服务注册表
            configuration_manager: 配置管理器
            logger: 日志记录器
        """
        self.service_registry = service_registry
        self.configuration_manager = configuration_manager
        self.logger = logger or logging.getLogger(__name__)
    
    def create_opro_service(self,
                          opro_optimizer: Optional[Any] = None,
                          config_override: Optional[Dict[str, Any]] = None) -> OPROService:
        """
        创建OPRO服务实例
        
        Args:
            opro_optimizer: 外部OPRO优化器实例（可选）
            config_override: 配置覆盖（可选）
            
        Returns:
            OPROService: 配置好的OPRO服务实例
        """
        try:
            # 获取配置
            config = self._get_merged_config(config_override)
            
            # 创建或获取历史得分管理器
            historical_score_manager = self._create_historical_score_manager(config)
            
            # 创建历史得分服务
            historical_score_service = self._create_historical_score_service(
                historical_score_manager,
                config
            )
            
            # 创建提示词模板服务
            prompt_template_service = self._create_prompt_template_service(config)
            
            # 确定是否启用OPRO
            enabled = config.get("opro", {}).get("enabled", False)
            
            # 创建OPRO服务
            opro_service = OPROService(
                opro_optimizer=opro_optimizer,
                historical_score_manager=historical_score_manager,
                enabled=enabled,
                logger=self.logger,
                config=config.get("opro", {}),
                historical_score_service=historical_score_service,
                prompt_template_service=prompt_template_service
            )
            
            # 注册服务到注册表
            self.service_registry.register_instance(
                service_type=OPROService,
                instance=opro_service,
                name="opro_service"
            )
            
            self.logger.info(f"OPRO服务创建成功，启用状态: {enabled}")
            return opro_service
            
        except Exception as e:
            self.logger.error(f"创建OPRO服务失败: {e}")
            raise
    
    def create_opro_service_with_mock_dependencies(self,
                                                  enabled: bool = True,
                                                  config_override: Optional[Dict[str, Any]] = None) -> OPROService:
        """
        创建带模拟依赖的OPRO服务（用于测试）
        
        Args:
            enabled: 是否启用OPRO服务
            config_override: 配置覆盖
            
        Returns:
            OPROService: 配置好的OPRO服务实例
        """
        try:
            from unittest.mock import Mock
            
            # 创建模拟依赖
            mock_optimizer = Mock()
            mock_historical_manager = Mock()
            mock_historical_service = Mock(spec=HistoricalScoreService)
            mock_template_service = Mock(spec=PromptTemplateService)
            
            # 获取配置
            config = self._get_merged_config(config_override)
            
            # 创建OPRO服务
            opro_service = OPROService(
                opro_optimizer=mock_optimizer,
                historical_score_manager=mock_historical_manager,
                enabled=enabled,
                logger=self.logger,
                config=config.get("opro", {}),
                historical_score_service=mock_historical_service,
                prompt_template_service=mock_template_service
            )
            
            self.logger.info(f"创建模拟OPRO服务成功，启用状态: {enabled}")
            return opro_service
            
        except Exception as e:
            self.logger.error(f"创建模拟OPRO服务失败: {e}")
            raise
    
    def configure_opro_service(self,
                             service: OPROService,
                             config_update: Dict[str, Any]) -> None:
        """
        配置现有OPRO服务
        
        Args:
            service: OPRO服务实例
            config_update: 配置更新
        """
        try:
            # 更新服务配置
            service.config.update(config_update)
            
            # 更新启用状态
            if "enabled" in config_update:
                service.enabled = config_update["enabled"]
            
            self.logger.info("OPRO服务配置更新成功")
            
        except Exception as e:
            self.logger.error(f"配置OPRO服务失败: {e}")
            raise
    
    def get_opro_service_dependencies(self) -> Dict[str, Any]:
        """
        获取OPRO服务的依赖信息
        
        Returns:
            Dict[str, Any]: 依赖信息
        """
        return {
            "historical_score_manager": {
                "type": "HistoricalScoreManager",
                "required": False,
                "description": "历史得分管理器"
            },
            "historical_score_service": {
                "type": "HistoricalScoreService",
                "required": False,
                "description": "历史得分服务"
            },
            "prompt_template_service": {
                "type": "PromptTemplateService",
                "required": False,
                "description": "提示词模板服务"
            },
            "opro_optimizer": {
                "type": "OPROOptimizer",
                "required": False,
                "description": "OPRO优化器"
            },
            "configuration_manager": {
                "type": "ConfigurationManager",
                "required": True,
                "description": "配置管理器"
            },
            "logger": {
                "type": "logging.Logger",
                "required": False,
                "description": "日志记录器"
            }
        }
    
    def validate_opro_service_config(self, config: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证OPRO服务配置
        
        Args:
            config: 配置字典
            
        Returns:
            Dict[str, Any]: 验证结果
        """
        validation_result = {
            "valid": True,
            "errors": [],
            "warnings": []
        }
        
        try:
            opro_config = config.get("opro", {})
            
            # 验证必需配置
            if "enabled" not in opro_config:
                validation_result["warnings"].append("未配置OPRO启用状态，默认为禁用")
            
            # 验证可选配置
            if opro_config.get("enabled", False):
                # 如果启用，检查相关配置
                if "optimization_frequency" not in opro_config:
                    validation_result["warnings"].append("未配置优化频率")
                
                if "candidates_per_generation" not in opro_config:
                    validation_result["warnings"].append("未配置每代候选数量")
                
                # 验证历史得分配置
                historical_config = config.get("historical_score", {})
                if not historical_config.get("enabled", True):
                    validation_result["warnings"].append("历史得分管理被禁用，可能影响OPRO优化效果")
                
                # 验证提示词模板配置
                template_config = config.get("prompt_template", {})
                if not template_config.get("templates_dir"):
                    validation_result["warnings"].append("未配置提示词模板目录")
            
            # 验证数值配置
            if "temperature" in opro_config:
                temperature = opro_config["temperature"]
                if not isinstance(temperature, (int, float)) or temperature < 0 or temperature > 2:
                    validation_result["errors"].append("温度参数必须是0-2之间的数值")
            
            if "max_optimization_iterations" in opro_config:
                iterations = opro_config["max_optimization_iterations"]
                if not isinstance(iterations, int) or iterations < 1:
                    validation_result["errors"].append("最大优化迭代次数必须是正整数")
            
            if validation_result["errors"]:
                validation_result["valid"] = False
            
        except Exception as e:
            validation_result["valid"] = False
            validation_result["errors"].append(f"配置验证异常: {str(e)}")
        
        return validation_result
    
    def get_opro_service_status(self, service: OPROService) -> Dict[str, Any]:
        """
        获取OPRO服务状态
        
        Args:
            service: OPRO服务实例
            
        Returns:
            Dict[str, Any]: 服务状态信息
        """
        try:
            status = {
                "enabled": service.enabled,
                "has_optimizer": service.opro_optimizer is not None,
                "has_historical_manager": service.historical_score_manager is not None,
                "has_historical_service": service.historical_score_service is not None,
                "has_template_service": service.prompt_template_service is not None,
                "config": service.config,
                "optimization_history_count": len(service._optimization_history),
                "prompt_templates_count": len(service._prompt_templates),
                "agent_versions_count": len(service._agent_versions)
            }
            
            # 如果有模板服务，获取模板统计
            if service.prompt_template_service:
                try:
                    template_stats = service.prompt_template_service.get_template_stats()
                    status["template_stats"] = template_stats
                except Exception as e:
                    status["template_stats_error"] = str(e)
            
            return status
            
        except Exception as e:
            self.logger.error(f"获取OPRO服务状态失败: {e}")
            return {"error": str(e)}
    
    # 私有方法
    
    def _get_merged_config(self, config_override: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """获取合并后的配置"""
        base_config = self.configuration_manager.get_all_config()
        
        if config_override:
            # 深度合并配置
            merged_config = base_config.copy()
            for key, value in config_override.items():
                if key in merged_config and isinstance(merged_config[key], dict) and isinstance(value, dict):
                    merged_config[key].update(value)
                else:
                    merged_config[key] = value
            return merged_config
        
        return base_config
    
    def _create_historical_score_manager(self, config: Dict[str, Any]) -> Optional[HistoricalScoreManager]:
        """创建历史得分管理器"""
        try:
            # 尝试从服务注册表获取
            try:
                return self.service_registry.get_service(HistoricalScoreManager)
            except:
                pass
            
            # 创建新实例
            historical_config = config.get("historical_score", {})
            
            if not historical_config.get("enabled", True):
                return None
            
            return HistoricalScoreManager(
                results_base_path=historical_config.get("results_base_path", "results/periodic_shapley"),
                db_path=historical_config.get("db_path", "results/opro_optimization.db"),
                logger=self.logger
            )
            
        except Exception as e:
            self.logger.warning(f"创建历史得分管理器失败: {e}")
            return None
    
    def _create_historical_score_service(self,
                                       historical_score_manager: Optional[HistoricalScoreManager],
                                       config: Dict[str, Any]) -> Optional[HistoricalScoreService]:
        """创建历史得分服务"""
        try:
            # 尝试从服务注册表获取
            try:
                return self.service_registry.get_service(HistoricalScoreService)
            except:
                pass
            
            # 创建新实例
            if historical_score_manager:
                return HistoricalScoreService(
                    historical_score_manager=historical_score_manager,
                    logger=self.logger
                )
            
            return None
            
        except Exception as e:
            self.logger.warning(f"创建历史得分服务失败: {e}")
            return None
    
    def _create_prompt_template_service(self, config: Dict[str, Any]) -> Optional[PromptTemplateService]:
        """创建提示词模板服务"""
        try:
            # 尝试从服务注册表获取
            try:
                return self.service_registry.get_service(PromptTemplateService)
            except:
                pass
            
            # 创建新实例
            template_config = config.get("prompt_template", {})
            
            return PromptTemplateService(
                templates_dir=template_config.get("templates_dir", "data/prompt_templates"),
                logger=self.logger
            )
            
        except Exception as e:
            self.logger.warning(f"创建提示词模板服务失败: {e}")
            return None


def create_opro_service_from_config(config: Dict[str, Any],
                                   service_registry: ServiceRegistry,
                                   logger: Optional[logging.Logger] = None) -> OPROService:
    """
    从配置创建OPRO服务的便捷函数
    
    Args:
        config: 配置字典
        service_registry: 服务注册表
        logger: 日志记录器
        
    Returns:
        OPROService: 配置好的OPRO服务实例
    """
    # 创建配置管理器
    from .configuration_manager import ConfigurationManager
    config_manager = ConfigurationManager(config)
    
    # 创建服务工厂
    factory = OPROServiceFactory(
        service_registry=service_registry,
        configuration_manager=config_manager,
        logger=logger
    )
    
    # 创建服务
    return factory.create_opro_service()


def register_opro_service_with_registry(service_registry: ServiceRegistry,
                                      config: Dict[str, Any],
                                      logger: Optional[logging.Logger] = None) -> None:
    """
    向服务注册表注册OPRO服务工厂
    
    Args:
        service_registry: 服务注册表
        config: 配置字典
        logger: 日志记录器
    """
    from .configuration_manager import ConfigurationManager
    
    # 创建配置管理器
    config_manager = ConfigurationManager(config)
    
    # 注册OPRO服务工厂
    service_registry.register_factory(
        service_type=OPROServiceFactory,
        factory=lambda: OPROServiceFactory(
            service_registry=service_registry,
            configuration_manager=config_manager,
            logger=logger
        )
    )
    
    # 注册OPRO服务
    service_registry.register_factory(
        service_type=OPROService,
        factory=lambda: create_opro_service_from_config(
            config=config,
            service_registry=service_registry,
            logger=logger
        )
    )