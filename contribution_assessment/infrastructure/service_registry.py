"""
服务注册表和依赖注入

提供服务实例管理和依赖注入功能，支持：
- 单例和工厂模式的服务创建
- 循环依赖检测和解决
- 服务生命周期管理
- 依赖注入容器
- OPRO服务集成支持
"""

import inspect
import logging
import threading
from abc import ABC, abstractmethod
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set, Type, TypeVar, Union
from dataclasses import dataclass
from collections import defaultdict


class ServiceLifetime(Enum):
    """服务生命周期枚举"""
    SINGLETON = "singleton"
    TRANSIENT = "transient"
    SCOPED = "scoped"


@dataclass
class ServiceDescriptor:
    """服务描述符"""
    service_type: Type
    implementation_type: Optional[Type] = None
    factory: Optional[Callable] = None
    instance: Optional[Any] = None
    lifetime: ServiceLifetime = ServiceLifetime.SINGLETON
    dependencies: List[Type] = None
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []


class ServiceRegistrationError(Exception):
    """服务注册异常"""
    pass


class CircularDependencyError(ServiceRegistrationError):
    """循环依赖异常"""
    pass


class ServiceResolutionError(Exception):
    """服务解析异常"""
    pass


T = TypeVar('T')


class IServiceRegistry(ABC):
    """服务注册表接口"""
    
    @abstractmethod
    def register_singleton(self, service_type: Type[T], 
                          implementation_type: Optional[Type[T]] = None,
                          factory: Optional[Callable[[], T]] = None,
                          instance: Optional[T] = None) -> None:
        """注册单例服务"""
        pass
    
    @abstractmethod
    def register_transient(self, service_type: Type[T],
                          implementation_type: Optional[Type[T]] = None,
                          factory: Optional[Callable[[], T]] = None) -> None:
        """注册瞬态服务"""
        pass
    
    @abstractmethod
    def get_service(self, service_type: Type[T]) -> T:
        """获取服务实例"""
        pass
    
    @abstractmethod
    def has_service(self, service_type: Type) -> bool:
        """检查服务是否已注册"""
        pass


class ServiceRegistry(IServiceRegistry):
    """服务注册表实现"""
    
    def __init__(self):
        self._services: Dict[Type, ServiceDescriptor] = {}
        self._instances: Dict[Type, Any] = {}
        self._lock = threading.RLock()
        self._logger = logging.getLogger(__name__)
        self._resolution_stack: Set[Type] = set()
    
    def register_singleton(self, service_type: Type[T], 
                          implementation_type: Optional[Type[T]] = None,
                          factory: Optional[Callable[[], T]] = None,
                          instance: Optional[T] = None) -> None:
        """注册单例服务"""
        self._register_service(
            service_type=service_type,
            implementation_type=implementation_type,
            factory=factory,
            instance=instance,
            lifetime=ServiceLifetime.SINGLETON
        )
    
    def register_transient(self, service_type: Type[T],
                          implementation_type: Optional[Type[T]] = None,
                          factory: Optional[Callable[[], T]] = None,
                          instance: Optional[T] = None) -> None:
        """注册瞬态服务"""
        self._register_service(
            service_type=service_type,
            implementation_type=implementation_type,
            factory=factory,
            instance=instance,
            lifetime=ServiceLifetime.TRANSIENT
        )
    
    def _register_service(self, service_type: Type, 
                         implementation_type: Optional[Type] = None,
                         factory: Optional[Callable] = None,
                         instance: Optional[Any] = None,
                         lifetime: ServiceLifetime = ServiceLifetime.SINGLETON) -> None:
        """内部服务注册方法"""
        with self._lock:
            # 验证注册参数
            if sum(x is not None for x in [implementation_type, factory, instance]) != 1:
                raise ServiceRegistrationError(
                    "Must provide exactly one of: implementation_type, factory, or instance"
                )
            
            # 如果提供了实例，必须是单例
            if instance is not None and lifetime != ServiceLifetime.SINGLETON:
                raise ServiceRegistrationError(
                    "Instance registration is only supported for singleton lifetime"
                )
            
            # 分析依赖关系
            dependencies = []
            if implementation_type:
                dependencies = self._analyze_dependencies(implementation_type)
            elif factory:
                dependencies = self._analyze_dependencies(factory)
            
            descriptor = ServiceDescriptor(
                service_type=service_type,
                implementation_type=implementation_type,
                factory=factory,
                instance=instance,
                lifetime=lifetime,
                dependencies=dependencies
            )
            
            self._services[service_type] = descriptor
            
            # 如果提供了实例，直接存储
            if instance is not None:
                self._instances[service_type] = instance
            
            self._logger.debug(f"Registered service: {service_type.__name__} as {lifetime.value}")
    
    def _analyze_dependencies(self, target: Union[Type, Callable]) -> List[Type]:
        """分析依赖关系"""
        try:
            if inspect.isclass(target):
                # 分析类的构造函数
                signature = inspect.signature(target.__init__)
                parameters = list(signature.parameters.values())[1:]  # 跳过self
            else:
                # 分析函数
                signature = inspect.signature(target)
                parameters = list(signature.parameters.values())
            
            dependencies = []
            for param in parameters:
                if param.annotation != inspect.Parameter.empty:
                    # 只考虑有类型注解的参数，跳过字符串类型注解
                    if isinstance(param.annotation, str):
                        # 字符串类型注解（前向引用）暂时跳过
                        self._logger.warning(f"Skipping string annotation '{param.annotation}' for parameter '{param.name}'")
                        continue
                    dependencies.append(param.annotation)
            
            return dependencies
            
        except Exception as e:
            self._logger.warning(f"Failed to analyze dependencies for {target}: {e}")
            return []
    
    def get_service(self, service_type: Type[T]) -> T:
        """获取服务实例"""
        with self._lock:
            if service_type not in self._services:
                raise ServiceResolutionError(f"Service {service_type.__name__} is not registered")
            
            descriptor = self._services[service_type]
            
            # 检查循环依赖
            if service_type in self._resolution_stack:
                cycle = " -> ".join([t.__name__ for t in self._resolution_stack]) + f" -> {service_type.__name__}"
                raise CircularDependencyError(f"Circular dependency detected: {cycle}")
            
            # 单例模式：检查是否已有实例
            if descriptor.lifetime == ServiceLifetime.SINGLETON:
                if service_type in self._instances:
                    return self._instances[service_type]
            
            # 创建实例
            self._resolution_stack.add(service_type)
            try:
                instance = self._create_instance(descriptor)
                
                # 单例模式：缓存实例
                if descriptor.lifetime == ServiceLifetime.SINGLETON:
                    self._instances[service_type] = instance
                
                return instance
                
            finally:
                self._resolution_stack.discard(service_type)
    
    def _create_instance(self, descriptor: ServiceDescriptor) -> Any:
        """创建服务实例"""
        try:
            # 如果已有实例，直接返回
            if descriptor.instance is not None:
                return descriptor.instance
            
            # 解析依赖
            dependencies = []
            for dep_type in descriptor.dependencies:
                dep_instance = self.get_service(dep_type)
                dependencies.append(dep_instance)
            
            # 使用工厂方法创建
            if descriptor.factory:
                if dependencies:
                    return descriptor.factory(*dependencies)
                else:
                    return descriptor.factory()
            
            # 使用实现类型创建
            if descriptor.implementation_type:
                return descriptor.implementation_type(*dependencies)
            
            raise ServiceResolutionError(
                f"Cannot create instance for {descriptor.service_type.__name__}: "
                "no factory or implementation type provided"
            )
            
        except CircularDependencyError:
            # 重新抛出循环依赖异常，不包装
            raise
        except Exception as e:
            raise ServiceResolutionError(
                f"Failed to create instance of {descriptor.service_type.__name__}: {e}"
            )
    
    def has_service(self, service_type: Type) -> bool:
        """检查服务是否已注册"""
        with self._lock:
            return service_type in self._services
    
    def get_registered_services(self) -> List[Type]:
        """获取所有已注册的服务类型"""
        with self._lock:
            return list(self._services.keys())
    
    def clear_instances(self) -> None:
        """清除所有单例实例（用于测试）"""
        with self._lock:
            self._instances.clear()
            self._logger.debug("Cleared all service instances")
    
    def unregister_service(self, service_type: Type) -> bool:
        """注销服务"""
        with self._lock:
            if service_type in self._services:
                del self._services[service_type]
                if service_type in self._instances:
                    del self._instances[service_type]
                self._logger.debug(f"Unregistered service: {service_type.__name__}")
                return True
            return False
    
    def get_service_info(self, service_type: Type) -> Optional[Dict[str, Any]]:
        """获取服务信息"""
        with self._lock:
            if service_type not in self._services:
                return None
            
            descriptor = self._services[service_type]
            return {
                'service_type': descriptor.service_type.__name__,
                'implementation_type': descriptor.implementation_type.__name__ if descriptor.implementation_type else None,
                'lifetime': descriptor.lifetime.value,
                'has_instance': service_type in self._instances,
                'dependencies': [dep.__name__ for dep in descriptor.dependencies]
            }
    
    def validate_dependencies(self) -> List[str]:
        """验证所有服务的依赖关系，返回错误列表"""
        errors = []
        
        with self._lock:
            for service_type, descriptor in self._services.items():
                # 检查依赖是否都已注册
                for dep_type in descriptor.dependencies:
                    if not self.has_service(dep_type):
                        errors.append(
                            f"Service {service_type.__name__} depends on unregistered service {dep_type.__name__}"
                        )
                
                # 尝试检测循环依赖
                try:
                    self._check_circular_dependency(service_type, set())
                except CircularDependencyError as e:
                    errors.append(str(e))
        
        return errors
    
    def _check_circular_dependency(self, service_type: Type, visited: Set[Type]) -> None:
        """递归检查循环依赖"""
        if service_type in visited:
            cycle = " -> ".join([t.__name__ for t in visited]) + f" -> {service_type.__name__}"
            raise CircularDependencyError(f"Circular dependency detected: {cycle}")
        
        if service_type not in self._services:
            return
        
        visited.add(service_type)
        descriptor = self._services[service_type]
        
        for dep_type in descriptor.dependencies:
            self._check_circular_dependency(dep_type, visited.copy())


class ServiceContainer:
    """服务容器 - 提供更高级的依赖注入功能"""
    
    def __init__(self, registry: Optional[IServiceRegistry] = None):
        self.registry = registry or ServiceRegistry()
        self._logger = logging.getLogger(__name__)
    
    def configure_services(self, configuration_func: Callable[[IServiceRegistry], None]) -> None:
        """配置服务"""
        try:
            configuration_func(self.registry)
            self._logger.info("Services configured successfully")
        except Exception as e:
            self._logger.error(f"Failed to configure services: {e}")
            raise
    
    def build_service_provider(self) -> IServiceRegistry:
        """构建服务提供者"""
        # 验证依赖关系
        errors = self.registry.validate_dependencies()
        if errors:
            error_msg = "Service validation failed:\n" + "\n".join(errors)
            raise ServiceRegistrationError(error_msg)
        
        return self.registry
    
    def create_scope(self) -> 'ServiceScope':
        """创建服务作用域"""
        return ServiceScope(self.registry)


class ServiceScope:
    """服务作用域 - 管理作用域内的服务实例"""
    
    def __init__(self, registry: IServiceRegistry):
        self.registry = registry
        self._scoped_instances: Dict[Type, Any] = {}
        self._disposed = False
    
    def get_service(self, service_type: Type[T]) -> T:
        """获取作用域内的服务实例"""
        if self._disposed:
            raise ServiceResolutionError("Service scope has been disposed")
        
        # 对于作用域服务，在当前作用域内缓存实例
        if service_type in self._scoped_instances:
            return self._scoped_instances[service_type]
        
        instance = self.registry.get_service(service_type)
        self._scoped_instances[service_type] = instance
        return instance
    
    def dispose(self) -> None:
        """释放作用域"""
        if not self._disposed:
            # 释放实现了dispose方法的服务
            for instance in self._scoped_instances.values():
                if hasattr(instance, 'dispose') and callable(getattr(instance, 'dispose')):
                    try:
                        instance.dispose()
                    except Exception as e:
                        logging.getLogger(__name__).warning(f"Error disposing service: {e}")
            
            self._scoped_instances.clear()
            self._disposed = True
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.dispose()