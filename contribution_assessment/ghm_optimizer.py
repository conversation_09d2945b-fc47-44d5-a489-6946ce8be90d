"""
Generalized Hierarchical Memoization (GHM) Optimizer

本模块实现了基于分层架构的广义分层记忆化优化器，用于显著降低
多智能体系统中Shapley值计算的计算复杂度。通过利用DAG结构的
分层特性和智能体间的依赖关系，实现高效的联盟评估优化。

核心思想：
1. 将多智能体系统建模为分层DAG结构
2. 利用层间依赖关系进行智能缓存
3. 避免重复计算相同输入配置下的智能体输出

主要功能：
1. 层级结构管理和依赖关系分析
2. 基于输入配置的智能缓存机制
3. 高效的联盟评估和Shapley值计算
4. 性能基准测试和优化分析
"""

import time
import logging
from typing import Dict, List, Set, Tuple, Any, Optional, Union, FrozenSet
from dataclasses import dataclass, field
from collections import defaultdict
import itertools
from abc import ABC, abstractmethod


@dataclass
class LayerConfiguration:
    """层配置信息"""
    layer_id: int
    agents: Set[str]
    mandatory: bool = False  # 是否为强制层（必须非空）
    dependencies: Set[int] = field(default_factory=set)  # 依赖的层ID


@dataclass
class CacheKey:
    """缓存键，用于标识唯一的输入配置"""
    agent_id: str
    preceding_layer_outputs: FrozenSet[FrozenSet[str]]  # 前置层的输出组合
    
    def __hash__(self) -> int:
        return hash((self.agent_id, self.preceding_layer_outputs))
    
    def __eq__(self, other) -> bool:
        if not isinstance(other, CacheKey):
            return False
        return (self.agent_id == other.agent_id and 
                self.preceding_layer_outputs == other.preceding_layer_outputs)


@dataclass
class GHMStats:
    """GHM统计信息"""
    total_agent_executions: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    computation_time: float = 0.0
    memory_usage: int = 0
    layer_execution_counts: Dict[int, int] = field(default_factory=dict)
    
    @property
    def cache_hit_rate(self) -> float:
        total_requests = self.cache_hits + self.cache_misses
        return (self.cache_hits / total_requests * 100) if total_requests > 0 else 0.0


class GHMOptimizer:
    """
    广义分层记忆化优化器
    
    通过分层架构和智能缓存机制，显著降低多智能体系统
    中联盟评估的计算复杂度。
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化GHM优化器
        
        参数:
            logger: 日志记录器
        """
        self.logger = logger or self._create_default_logger()
        
        # 分层配置
        self.layers: Dict[int, LayerConfiguration] = {}
        self.layer_order: List[int] = []
        
        # 缓存系统
        self.computation_cache: Dict[CacheKey, Any] = {}
        self.layer_output_cache: Dict[Tuple[int, FrozenSet[str]], Set[FrozenSet[str]]] = {}
        
        # 统计信息
        self.stats = GHMStats()
        
        # 默认系统层级配置（基于CORE_SYSTEM_FRAMEWORK）
        self._setup_default_layers()
        
        self.logger.info("GHM优化器初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.GHMOptimizer")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def _setup_default_layers(self) -> None:
        """设置默认的三层架构"""
        # Layer 1: 分析层 (Analysis Layer)
        self.layers[1] = LayerConfiguration(
            layer_id=1,
            agents={"NAA", "TAA", "FAA"},
            mandatory=True,  # 分析层必须非空
            dependencies=set()  # 无依赖
        )
        
        # Layer 2: 展望层 (Outlook Layer)  
        self.layers[2] = LayerConfiguration(
            layer_id=2,
            agents={"BOA", "BeOA", "NOA"},
            mandatory=False,  # 展望层可以为空
            dependencies={1}  # 依赖分析层
        )
        
        # Layer 3: 交易层 (Trading Layer)
        self.layers[3] = LayerConfiguration(
            layer_id=3,
            agents={"TRA"},
            mandatory=True,  # 交易层必须非空
            dependencies={1, 2}  # 依赖分析层和展望层
        )
        
        self.layer_order = [1, 2, 3]
        
        self.logger.info("默认三层架构配置完成")
    
    def configure_layers(self, layer_configs: Dict[int, LayerConfiguration]) -> None:
        """
        配置自定义层级结构
        
        参数:
            layer_configs: 层配置字典
        """
        self.layers = layer_configs.copy()
        self.layer_order = sorted(layer_configs.keys())
        
        # 验证依赖关系
        for layer_id, config in self.layers.items():
            for dep_id in config.dependencies:
                if dep_id not in self.layers:
                    raise ValueError(f"层 {layer_id} 依赖的层 {dep_id} 不存在")
                if dep_id >= layer_id:
                    raise ValueError(f"层 {layer_id} 不能依赖后续层 {dep_id}")
        
        self.logger.info(f"自定义层级结构配置完成: {len(self.layers)} 层")
    
    def get_valid_coalitions_with_ghm(self, 
                                    coalitions: Set[FrozenSet[str]]) -> Set[FrozenSet[str]]:
        """
        使用GHM优化联盟验证过程
        
        参数:
            coalitions: 候选联盟集合
            
        返回:
            通过层级约束验证的有效联盟集合
        """
        valid_coalitions = set()
        
        for coalition in coalitions:
            if self._validate_coalition_layers(coalition):
                valid_coalitions.add(coalition)
        
        return valid_coalitions
    
    def _validate_coalition_layers(self, coalition: FrozenSet[str]) -> bool:
        """
        验证联盟是否满足层级约束
        
        参数:
            coalition: 待验证的联盟
            
        返回:
            是否满足层级约束
        """
        coalition_set = set(coalition)
        
        for layer_id, config in self.layers.items():
            layer_agents = coalition_set.intersection(config.agents)
            
            # 检查强制层约束
            if config.mandatory and len(layer_agents) == 0:
                return False
            
            # 检查依赖关系约束
            if len(layer_agents) > 0:  # 如果该层有智能体
                for dep_layer_id in config.dependencies:
                    dep_config = self.layers[dep_layer_id]
                    dep_agents = coalition_set.intersection(dep_config.agents)
                    if len(dep_agents) == 0:
                        return False
        
        return True
    
    def calculate_ghm_cost(self, coalition: FrozenSet[str]) -> int:
        """
        计算使用GHM的理论计算成本
        
        参数:
            coalition: 联盟
            
        返回:
            理论执行次数
        """
        total_cost = 0
        
        for layer_id in self.layer_order:
            config = self.layers[layer_id]
            layer_agents = set(coalition).intersection(config.agents)
            
            if len(layer_agents) == 0:
                continue
            
            # 计算前置层的输出组合数
            preceding_combinations = 1
            for prev_layer_id in range(1, layer_id):
                if prev_layer_id not in self.layers:
                    continue
                prev_config = self.layers[prev_layer_id]
                prev_agents = set(coalition).intersection(prev_config.agents)
                
                if len(prev_agents) > 0:
                    # 考虑强制层约束
                    combinations = 2 ** len(prev_agents)
                    if prev_config.mandatory:
                        combinations -= 1  # 排除空集
                    preceding_combinations *= combinations
            
            # 当前层的成本 = 前置组合数 × 当前层智能体数量
            layer_cost = preceding_combinations * len(layer_agents)
            total_cost += layer_cost
            
            self.logger.debug(f"层 {layer_id}: {layer_cost} 次执行 "
                            f"({preceding_combinations} 输入组合 × {len(layer_agents)} 智能体)")
        
        return total_cost
    
    def simulate_coalition_with_ghm(self, 
                                  coalition: FrozenSet[str],
                                  simulation_func: callable) -> Any:
        """
        使用GHM优化的联盟模拟
        
        参数:
            coalition: 联盟
            simulation_func: 模拟函数
            
        返回:
            模拟结果
        """
        start_time = time.time()
        
        # 验证联盟有效性
        if not self._validate_coalition_layers(coalition):
            self.logger.warning(f"联盟 {coalition} 不满足层级约束")
            return None
        
        # 执行分层模拟
        layer_outputs = {}
        
        for layer_id in self.layer_order:
            config = self.layers[layer_id]
            layer_agents = set(coalition).intersection(config.agents)
            
            if len(layer_agents) == 0:
                layer_outputs[layer_id] = set()
                continue
            
            # 获取前置层输出
            preceding_outputs = self._get_preceding_layer_outputs(layer_id, layer_outputs)
            
            # 执行当前层智能体
            layer_result = self._execute_layer_with_cache(
                layer_id, layer_agents, preceding_outputs, simulation_func
            )
            
            layer_outputs[layer_id] = layer_result
        
        # 更新统计信息
        computation_time = time.time() - start_time
        self.stats.computation_time += computation_time
        
        return layer_outputs
    
    def _get_preceding_layer_outputs(self, 
                                   layer_id: int, 
                                   layer_outputs: Dict[int, Set]) -> Set[FrozenSet[str]]:
        """获取前置层的输出组合"""
        preceding_combinations = {frozenset()}  # 从空输入开始
        
        for prev_layer_id in range(1, layer_id):
            if prev_layer_id not in layer_outputs:
                continue
            
            prev_outputs = layer_outputs[prev_layer_id]
            if not prev_outputs:
                continue
            
            # 生成所有可能的组合
            new_combinations = set()
            for existing_combo in preceding_combinations:
                for prev_output in prev_outputs:
                    new_combinations.add(existing_combo.union(prev_output))
            
            preceding_combinations = new_combinations
        
        return preceding_combinations
    
    def _execute_layer_with_cache(self, 
                                layer_id: int,
                                layer_agents: Set[str],
                                preceding_outputs: Set[FrozenSet[str]],
                                simulation_func: callable) -> Set[FrozenSet[str]]:
        """使用缓存执行层级计算"""
        results = set()
        
        for agent in layer_agents:
            for preceding_combo in preceding_outputs:
                cache_key = CacheKey(agent, frozenset(preceding_combo))
                
                if cache_key in self.computation_cache:
                    # 缓存命中
                    self.stats.cache_hits += 1
                    result = self.computation_cache[cache_key]
                else:
                    # 缓存未命中，执行计算
                    self.stats.cache_misses += 1
                    result = simulation_func(agent, preceding_combo)
                    self.computation_cache[cache_key] = result
                    self.stats.total_agent_executions += 1
                
                if result:
                    results.add(frozenset([agent]))
        
        # 更新层级执行统计
        if layer_id not in self.stats.layer_execution_counts:
            self.stats.layer_execution_counts[layer_id] = 0
        self.stats.layer_execution_counts[layer_id] += len(layer_agents) * len(preceding_outputs)
        
        return results
    
    def benchmark_vs_naive(self, 
                          coalitions: Set[FrozenSet[str]],
                          simulation_func: callable) -> Dict[str, Any]:
        """
        GHM vs 朴素方法的性能基准测试
        
        参数:
            coalitions: 测试联盟集合
            simulation_func: 模拟函数
            
        返回:
            性能比较结果
        """
        self.logger.info("开始GHM vs 朴素方法基准测试")
        
        # 重置统计信息
        self.stats = GHMStats()
        
        # GHM方法测试
        ghm_start_time = time.time()
        ghm_results = {}
        
        for coalition in coalitions:
            result = self.simulate_coalition_with_ghm(coalition, simulation_func)
            ghm_results[coalition] = result
        
        ghm_time = time.time() - ghm_start_time
        ghm_executions = self.stats.total_agent_executions
        
        # 朴素方法测试（每个联盟独立计算）
        naive_start_time = time.time()
        naive_executions = 0
        naive_results = {}
        
        for coalition in coalitions:
            # 朴素方法：每个智能体独立执行
            coalition_agents = set(coalition)
            for agent in coalition_agents:
                simulation_func(agent, frozenset())  # 简化的朴素执行
                naive_executions += 1
            naive_results[coalition] = {"executed": len(coalition_agents)}
        
        naive_time = time.time() - naive_start_time
        
        # 计算性能改进
        time_improvement = ((naive_time - ghm_time) / naive_time * 100) if naive_time > 0 else 0
        execution_improvement = ((naive_executions - ghm_executions) / naive_executions * 100) if naive_executions > 0 else 0
        
        benchmark_results = {
            "ghm_performance": {
                "execution_time": ghm_time,
                "total_executions": ghm_executions,
                "cache_hit_rate": self.stats.cache_hit_rate,
                "layer_executions": self.stats.layer_execution_counts
            },
            "naive_performance": {
                "execution_time": naive_time,
                "total_executions": naive_executions
            },
            "improvements": {
                "time_reduction_percent": time_improvement,
                "execution_reduction_percent": execution_improvement,
                "speedup_factor": naive_time / ghm_time if ghm_time > 0 else float('inf')
            },
            "test_parameters": {
                "total_coalitions": len(coalitions),
                "total_agents": len(set().union(*coalitions)) if coalitions else 0
            }
        }
        
        self.logger.info(f"基准测试完成:")
        self.logger.info(f"  - 时间改进: {time_improvement:.2f}%")
        self.logger.info(f"  - 执行次数减少: {execution_improvement:.2f}%") 
        self.logger.info(f"  - 加速比: {benchmark_results['improvements']['speedup_factor']:.2f}x")
        
        return benchmark_results
    
    def get_theoretical_complexity(self, num_agents_per_layer: List[int]) -> Dict[str, int]:
        """
        计算理论计算复杂度
        
        参数:
            num_agents_per_layer: 每层的智能体数量列表
            
        返回:
            理论复杂度分析
        """
        if len(num_agents_per_layer) != len(self.layers):
            raise ValueError("智能体数量列表长度必须等于层数")
        
        # GHM复杂度计算
        ghm_cost = 0
        for i, (layer_id, config) in enumerate(self.layers.items()):
            layer_agents = num_agents_per_layer[i]
            
            # 计算前置层组合数
            preceding_combinations = 1
            for j in range(i):
                prev_layer_agents = num_agents_per_layer[j]
                combinations = 2 ** prev_layer_agents
                if self.layers[j + 1].mandatory:  # 调整索引
                    combinations -= 1
                preceding_combinations *= combinations
            
            layer_cost = preceding_combinations * layer_agents
            ghm_cost += layer_cost
        
        # 朴素方法复杂度（2^n - 1）
        total_agents = sum(num_agents_per_layer)
        naive_cost = (2 ** total_agents - 1) * total_agents  # 每个联盟执行所有智能体
        
        return {
            "ghm_complexity": ghm_cost,
            "naive_complexity": naive_cost,
            "theoretical_improvement": ((naive_cost - ghm_cost) / naive_cost * 100) if naive_cost > 0 else 0,
            "complexity_ratio": naive_cost / ghm_cost if ghm_cost > 0 else float('inf')
        }
    
    def clear_cache(self) -> None:
        """清空缓存"""
        self.computation_cache.clear()
        self.layer_output_cache.clear()
        self.stats = GHMStats()
        self.logger.info("缓存已清空")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        return {
            "cache_size": len(self.computation_cache),
            "memory_usage_estimate": len(self.computation_cache) * 64,  # 估算字节数
            "hit_rate": self.stats.cache_hit_rate,
            "total_requests": self.stats.cache_hits + self.stats.cache_misses
        }
    
    def export_layer_config(self) -> Dict[str, Any]:
        """导出层级配置"""
        return {
            "layers": {
                layer_id: {
                    "agents": list(config.agents),
                    "mandatory": config.mandatory,
                    "dependencies": list(config.dependencies)
                }
                for layer_id, config in self.layers.items()
            },
            "layer_order": self.layer_order
        }


# 示例模拟函数
def mock_simulation_function(agent_id: str, preceding_inputs: FrozenSet[str]) -> bool:
    """
    模拟函数示例
    
    参数:
        agent_id: 智能体ID
        preceding_inputs: 前置层输入
        
    返回:
        模拟结果（简化为布尔值）
    """
    # 简化的模拟逻辑
    return len(preceding_inputs) % 2 == 0 or agent_id in ["TRA", "BOA"]


if __name__ == "__main__":
    # 使用示例
    optimizer = GHMOptimizer()
    
    # 测试联盟
    test_coalitions = {
        frozenset(["NAA", "BOA", "TRA"]),
        frozenset(["TAA", "FAA", "BeOA", "TRA"]),
        frozenset(["NAA", "TAA", "NOA", "TRA"])
    }
    
    # 运行基准测试
    results = optimizer.benchmark_vs_naive(test_coalitions, mock_simulation_function)
    print("基准测试结果:", results)
    
    # 理论复杂度分析
    complexity = optimizer.get_theoretical_complexity([3, 3, 1])  # 3-3-1架构
    print("理论复杂度分析:", complexity)