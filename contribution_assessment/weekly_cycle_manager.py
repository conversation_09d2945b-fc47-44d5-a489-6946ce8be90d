#!/usr/bin/env python3
"""
周期循环管理器 (Weekly Cycle Manager) - 清理版

实现"周 > 阶段"的层级结构，每个"周"作为一个完整的优化周期，
内部包含固定的四个阶段的优化流程。

完全使用新架构组件，无任何回退逻辑。

架构设计：
- 周 (Week): 作为顶层循环单位
- 阶段 (Phase): 每个周内部的四个执行步骤
  - 阶段1: 性能评估与问题识别
  - 阶段2: 优化策略生成与应用  
  - 阶段3: 优化效果验证
  - 阶段4: 结果固化与系统更新
"""

import os
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

# 导入新架构组件
from .refactored_assessor import RefactoredContributionAssessor
from .services.phase_coordinator import WeeklyResult as PhaseCoordinatorWeeklyResult

# 导入跨周状态管理器
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from portfolio_state_manager import PortfolioStateManager
from .dto.assessment_dto import AssessmentRequest


class WeekPhase(Enum):
    """周内阶段枚举"""
    PHASE_1_EVALUATION = "phase_1_evaluation"          # 阶段1: 性能评估与问题识别
    PHASE_2_OPTIMIZATION = "phase_2_optimization"      # 阶段2: 优化策略生成与应用
    PHASE_3_VERIFICATION = "phase_3_verification"      # 阶段3: 优化效果验证
    PHASE_4_CONSOLIDATION = "phase_4_consolidation"    # 阶段4: 结果固化与系统更新


@dataclass
class WeekPhaseResult:
    """周内阶段执行结果"""
    phase: WeekPhase
    success: bool
    execution_time: float
    data: Dict[str, Any]
    error_message: Optional[str] = None
    timestamp: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，处理枚举序列化"""
        return {
            "phase": self.phase.value,  # 转换枚举为字符串
            "success": self.success,
            "execution_time": self.execution_time,
            "data": self.data,
            "error_message": self.error_message,
            "timestamp": self.timestamp
        }


@dataclass
class WeekResult:
    """单周执行结果"""
    week_number: int
    success: bool
    total_execution_time: float
    phases: List[WeekPhaseResult]
    agents_optimized: List[str]
    performance_improvement: Dict[str, float]
    timestamp: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "week_number": self.week_number,
            "success": self.success,
            "total_execution_time": self.total_execution_time,
            "phases": [phase.to_dict() for phase in self.phases],  # 使用自定义to_dict方法
            "agents_optimized": self.agents_optimized,
            "performance_improvement": self.performance_improvement,
            "timestamp": self.timestamp
        }


@dataclass
class CycleResult:
    """多周期执行结果"""
    total_weeks: int
    successful_weeks: int
    total_execution_time: float
    weeks: List[WeekResult]
    overall_improvement: Dict[str, float]
    timestamp: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "total_weeks": self.total_weeks,
            "successful_weeks": self.successful_weeks,
            "success_rate": self.successful_weeks / max(1, self.total_weeks) * 100,
            "total_execution_time": self.total_execution_time,
            "weeks": [week.to_dict() for week in self.weeks],
            "overall_improvement": self.overall_improvement,
            "timestamp": self.timestamp
        }


class WeeklyCycleManager:
    """
    周期循环管理器
    
    负责管理多周的优化循环，每周内部执行四个阶段的优化流程。
    完全基于新架构，无回退逻辑。
    """
    
    def __init__(self, 
                 assessor: RefactoredContributionAssessor,
                 config: Dict[str, Any],
                 logger: Optional[logging.Logger] = None):
        """
        初始化周期循环管理器
        
        Args:
            assessor: 重构版本的贡献度评估器
            config: 配置字典
            logger: 日志记录器
        """
        self.assessor = assessor
        self.config = config
        self.logger = logger or logging.getLogger(__name__)
        
        # 初始化日常投资组合跟踪器
        self.portfolio_tracker = None
        # 修复配置读取逻辑，确保enable_daily_tracking能够正确继承系统级配置
        enable_daily_tracking = config.get("enable_daily_tracking", config.get("enable_portfolio_tracking", True))
        
        # 检查是否已经有现成的tracker实例
        existing_tracker = config.get("portfolio_tracker")
        if existing_tracker:
            self.portfolio_tracker = existing_tracker
            self.logger.info("📊 使用现有的投资组合跟踪器实例")
        elif enable_daily_tracking:
            try:
                sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                from portfolio_state_tracker import PortfolioStateTracker
                self.portfolio_tracker = PortfolioStateTracker()
                self.logger.info("📊 启用日常投资组合跟踪器 (纯内存模式)")
            except Exception as e:
                self.logger.error(f"❌ 投资组合跟踪器初始化失败: {e}")
                self.portfolio_tracker = None
        
        # 初始化智能体状态管理器
        self.agent_state_manager = None
        enable_agent_state_tracking = config.get("enable_state_inheritance", True)
        
        if enable_agent_state_tracking:
            try:
                sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                from state_management.agent_state_manager import AgentStateManager
                
                # 使用与投资组合跟踪器相同的数据目录
                state_dir = "data/agent_states"
                self.agent_state_manager = AgentStateManager(
                    state_dir=state_dir,
                    logger=self.logger
                )
                self.logger.info(f"🤖 启用智能体状态管理器 (状态目录: {state_dir})")
            except Exception as e:
                self.logger.error(f"❌ 智能体状态管理器初始化失败: {e}")
                self.agent_state_manager = None
        
        # 记录最终状态
        if self.portfolio_tracker:
            self.logger.info("✅ 投资组合跟踪器初始化成功")
        else:
            self.logger.warning("⚠️ 投资组合跟踪器未启用，将使用旧版状态传递机制")
            
        if self.agent_state_manager:
            self.logger.info("✅ 智能体状态管理器初始化成功")
        else:
            self.logger.warning("⚠️ 智能体状态管理器未启用，智能体状态不会跨周传递")
    
    def _get_or_restore_agents(self, week_number: int) -> Dict[str, Any]:
        """
        获取或恢复智能体实例
        
        优先级：
        1. 从状态管理器恢复上一周的智能体状态
        2. 使用assessor中已有的智能体实例
        3. 创建新的智能体实例
        
        Args:
            week_number: 当前周数
            
        Returns:
            智能体字典
        """
        agents = {}
        
        # 1. 尝试从状态管理器恢复智能体状态
        if self.agent_state_manager and week_number > 1:
            previous_week = week_number - 1
            
            if self.agent_state_manager.has_state_for_week(previous_week):
                self.logger.info(f"🔄 尝试从第{previous_week}周恢复智能体状态...")
                
                # 创建智能体创建回调，提供LLM接口和logger
                def agent_creation_callback(agent_id: str) -> Dict[str, Any]:
                    llm_interface = None
                    if hasattr(self.assessor, 'llm_provider') and self.assessor.llm_provider:
                        from ..llm_interface import LLMInterface
                        llm_interface = LLMInterface(provider=self.assessor.llm_provider, logger=self.logger)
                    
                    return {
                        "llm_interface": llm_interface,
                        "logger": self.logger
                    }
                
                restored_agents = self.agent_state_manager.restore_agents_state(
                    week_number=previous_week,
                    agent_creation_callback=agent_creation_callback
                )
                
                if restored_agents:
                    agents = restored_agents
                    # 保存恢复的智能体到assessor
                    self.assessor.agents = agents
                    self.logger.info(f"✅ 成功从第{previous_week}周恢复 {len(agents)} 个智能体状态")
                    
                    # 从恢复的智能体状态中提取投资组合状态
                    portfolio_state = self._extract_portfolio_state_from_agents(agents, previous_week)
                    if portfolio_state:
                        # 保存提取的投资组合状态供本周使用
                        PortfolioStateManager.save_week_end_state(previous_week, portfolio_state)
                        self.logger.info(f"📊 从智能体状态提取第{previous_week}周末投资组合状态")
                        self.logger.info(f"    累计收益率: {portfolio_state.get('cumulative_return', 0):.6f}")
                        self.logger.info(f"    持仓情况: {portfolio_state.get('positions', {})}")
                    
                    return agents
                else:
                    self.logger.warning(f"⚠️ 从第{previous_week}周恢复智能体状态失败")
            else:
                self.logger.info(f"📝 第{previous_week}周无智能体状态文件，将创建新智能体")
        
        # 2. 使用assessor中已有的智能体实例
        existing_agents = getattr(self.assessor, 'agents', {})
        if existing_agents:
            self.logger.info(f"🔄 使用assessor中现有的 {len(existing_agents)} 个智能体实例")
            agents = existing_agents
        
        # 3. 创建新的智能体实例
        if not agents and hasattr(self.assessor, '_create_agents'):
            self.logger.info("🆕 创建新的智能体实例...")
            agents = self.assessor._create_agents()
            # 保存新创建的智能体到assessor
            self.assessor.agents = agents
            self.logger.info(f"✅ 新创建 {len(agents)} 个智能体实例")
        
        if not agents:
            raise RuntimeError("无法获取或创建智能体实例")
        
        return agents
    
    def _extract_portfolio_state_from_agents(self, agents: Dict[str, Any], week_number: int) -> Optional[Dict[str, Any]]:
        """
        从智能体状态中提取投资组合状态
        
        Args:
            agents: 智能体字典
            week_number: 周数
            
        Returns:
            投资组合状态字典，如果提取失败则返回None
        """
        try:
            # 查找包含投资组合状态的智能体（通常是交易智能体TRA或有IO数据的智能体）
            portfolio_state = None
            
            for agent_id, agent_instance in agents.items():
                if hasattr(agent_instance, 'weekly_io_data') and agent_instance.weekly_io_data:
                    # 打印weekly_io_data的详细信息
                    io_data_count = len(agent_instance.weekly_io_data)
                    self.logger.info(f"📊 智能体 {agent_id} weekly_io_data 记录数: {io_data_count}")
                    
                    # 打印每条记录的基本信息
                    for i, io_record in enumerate(agent_instance.weekly_io_data):
                        timestamp = io_record.get('timestamp', 'N/A')
                        input_state_keys = list(io_record.get('input_state', {}).keys())
                        self.logger.info(f"  记录 {i+1}: 时间戳={timestamp}, 输入状态字段={input_state_keys}")
                    
                    # 获取最后一条IO记录（最新的状态）
                    last_io_data = agent_instance.weekly_io_data[-1]
                    
                    if 'input_state' in last_io_data:
                        input_state = last_io_data['input_state']
                        
                        # 检查是否包含完整的投资组合信息
                        required_fields = ['cumulative_return', 'positions', 'position_values']
                        if all(field in input_state for field in required_fields):
                            portfolio_state = {
                                'cumulative_return': input_state.get('cumulative_return', 0.0),
                                'weekly_return': input_state.get('weekly_return', 0.0),
                                'last_week_return': input_state.get('last_week_return', 0.0),
                                'positions': input_state.get('positions', {}),
                                'position_values': input_state.get('position_values', {}),
                                'cash': input_state.get('cash', 10000.0),  # 默认现金
                                'net_worth': sum(input_state.get('position_values', {}).values()) + input_state.get('cash', 10000.0),
                                'previous_day_return': input_state.get('previous_day_return', 0.0),
                                'current_date': input_state.get('current_date', input_state.get('date', '')),
                                'symbols': input_state.get('symbols', []),
                                'price_history': input_state.get('price_history', {})
                            }
                            
                            self.logger.info(f"📊 从智能体 {agent_id} 中提取投资组合状态")
                            self.logger.info(f"    数据来源: weekly_io_data 最后一条记录")
                            self.logger.info(f"    累计收益率: {portfolio_state['cumulative_return']:.6f}")
                            self.logger.info(f"    持仓: {portfolio_state['positions']}")
                            break
            
            if not portfolio_state:
                self.logger.warning(f"⚠️ 无法从第{week_number}周智能体状态中提取投资组合状态")
                self.logger.warning("    可能原因: 智能体没有weekly_io_data或数据不完整")
                
                # 提供详细调试信息
                for agent_id, agent_instance in agents.items():
                    if hasattr(agent_instance, 'weekly_io_data'):
                        io_data_count = len(agent_instance.weekly_io_data) if agent_instance.weekly_io_data else 0
                        self.logger.info(f"    {agent_id}: weekly_io_data 记录数 = {io_data_count}")
                        
                        # 如果有数据，打印每条记录的详细信息
                        if agent_instance.weekly_io_data:
                            for i, io_record in enumerate(agent_instance.weekly_io_data):
                                timestamp = io_record.get('timestamp', 'N/A')
                                input_state = io_record.get('input_state', {})
                                available_fields = list(input_state.keys())
                                
                                # 检查是否有投资组合相关字段
                                portfolio_fields = [f for f in available_fields if f in ['cumulative_return', 'positions', 'position_values', 'cash', 'net_worth']]
                                
                                self.logger.info(f"      记录 {i+1}: 时间={timestamp[:19] if len(timestamp) > 19 else timestamp}")
                                self.logger.info(f"      可用字段: {available_fields}")
                                self.logger.info(f"      投资组合字段: {portfolio_fields}")
                                
                                if portfolio_fields:
                                    for field in portfolio_fields:
                                        value = input_state.get(field)
                                        if field == 'cumulative_return':
                                            self.logger.info(f"        {field}: {value:.6f}" if isinstance(value, (int, float)) else f"        {field}: {value}")
                                        else:
                                            self.logger.info(f"        {field}: {value}")
                    else:
                        self.logger.info(f"    {agent_id}: 无 weekly_io_data 属性")
            
            return portfolio_state
            
        except Exception as e:
            self.logger.error(f"❌ 从智能体状态提取投资组合状态异常: {e}")
            import traceback
            self.logger.debug(f"异常堆栈: {traceback.format_exc()}")
            return None
    
    def _validate_agent_state_transfer(self, agents: Dict[str, Any], week_number: int) -> bool:
        """
        验证智能体状态传递的完整性
        
        Args:
            agents: 智能体字典
            week_number: 当前周数
            
        Returns:
            验证是否通过
        """
        if not agents:
            self.logger.error("❌ 状态传递验证失败: 无可用智能体")
            return False
        
        validation_results = {}
        all_passed = True
        
        for agent_id, agent_instance in agents.items():
            try:
                # 基础智能体状态验证
                has_analysis_count = hasattr(agent_instance, 'analysis_count')
                has_total_processing_time = hasattr(agent_instance, 'total_processing_time')
                has_last_analysis = hasattr(agent_instance, 'last_analysis')
                
                basic_validation = has_analysis_count and has_total_processing_time
                
                # OPRO相关状态验证（如果是OPRO智能体）
                opro_validation = True
                is_opro_agent = hasattr(agent_instance, 'opro_enabled')
                
                if is_opro_agent:
                    has_current_prompt = hasattr(agent_instance, 'current_prompt')
                    has_prompt_version = hasattr(agent_instance, 'prompt_version')
                    has_prompt_history = hasattr(agent_instance, 'prompt_history')
                    has_opro_stats = hasattr(agent_instance, '_opro_stats')
                    
                    opro_validation = has_current_prompt and has_prompt_version and has_prompt_history and has_opro_stats
                    
                    # 如果是从状态恢复的，验证OPRO状态是否正确恢复
                    if week_number > 1 and opro_validation:
                        analysis_count = getattr(agent_instance, 'analysis_count', 0)
                        optimization_count = getattr(agent_instance, '_opro_stats', {}).get('total_optimizations', 0)
                        
                        # 记录详细的OPRO状态
                        self.logger.debug(f"  🤖 {agent_id} OPRO状态: 分析{analysis_count}次, 优化{optimization_count}次")
                
                # 综合验证结果
                agent_validation = basic_validation and opro_validation
                validation_results[agent_id] = {
                    "basic_validation": basic_validation,
                    "opro_validation": opro_validation,
                    "is_opro_agent": is_opro_agent,
                    "overall": agent_validation
                }
                
                if not agent_validation:
                    all_passed = False
                    self.logger.warning(f"  ⚠️ {agent_id} 状态验证失败: 基础={basic_validation}, OPRO={opro_validation}")
                else:
                    self.logger.debug(f"  ✅ {agent_id} 状态验证通过")
                    
            except Exception as e:
                validation_results[agent_id] = {
                    "error": str(e),
                    "overall": False
                }
                all_passed = False
                self.logger.error(f"  ❌ {agent_id} 状态验证异常: {e}")
        
        # 输出验证摘要
        passed_count = sum(1 for result in validation_results.values() if result.get("overall", False))
        total_count = len(validation_results)
        
        if all_passed:
            self.logger.info(f"✅ 智能体状态传递验证通过: {passed_count}/{total_count} 个智能体")
        else:
            self.logger.warning(f"⚠️ 智能体状态传递验证部分失败: {passed_count}/{total_count} 个智能体通过")
        
        return all_passed
    
    def _log_agent_state_summary(self, agents: Dict[str, Any], week_number: int) -> None:
        """
        记录智能体状态摘要
        
        Args:
            agents: 智能体字典
            week_number: 当前周数
        """
        if not agents:
            self.logger.info("📊 智能体状态摘要: 无可用智能体")
            return
        
        self.logger.info(f"📊 第{week_number}周智能体状态摘要:")
        
        total_analysis = 0
        total_optimization = 0
        opro_agents = 0
        
        for agent_id, agent_instance in agents.items():
            try:
                # 基础状态
                analysis_count = getattr(agent_instance, 'analysis_count', 0)
                processing_time = getattr(agent_instance, 'total_processing_time', 0.0)
                total_analysis += analysis_count
                
                # OPRO状态
                is_opro_agent = hasattr(agent_instance, 'opro_enabled') and getattr(agent_instance, 'opro_enabled', False)
                if is_opro_agent:
                    opro_agents += 1
                    opro_stats = getattr(agent_instance, '_opro_stats', {})
                    optimization_count = opro_stats.get('total_optimizations', 0)
                    total_optimization += optimization_count
                    prompt_version = getattr(agent_instance, 'prompt_version', 'unknown')
                    
                    self.logger.info(f"  🤖 {agent_id}: 分析{analysis_count}次, 优化{optimization_count}次, 提示词v{prompt_version}, 耗时{processing_time:.2f}s")
                else:
                    self.logger.info(f"  🤖 {agent_id}: 分析{analysis_count}次, 耗时{processing_time:.2f}s (非OPRO)")
                    
            except Exception as e:
                self.logger.warning(f"  ⚠️ {agent_id}: 状态摘要生成失败 - {e}")
        
        # 总体统计
        self.logger.info(f"📈 总体统计: {len(agents)}个智能体, {total_analysis}次分析, {total_optimization}次优化, {opro_agents}个OPRO智能体")
        
        # 如果有状态管理器，记录可用的历史周数
        if self.agent_state_manager:
            available_weeks = self.agent_state_manager.get_available_weeks()
            if available_weeks:
                self.logger.info(f"💾 可用历史状态: {available_weeks[:3]}..." if len(available_weeks) > 3 else f"💾 可用历史状态: {available_weeks}")
        
        # 统计信息
        self.stats = {
            "total_cycles": 0,
            "successful_cycles": 0,
            "failed_cycles": 0,
            "total_execution_time": 0.0,
            "phases_executed": 0,
            "successful_phases": 0,
            "failed_phases": 0
        }
        
        # 初始化组件
        self._initialize_components()
    
    def _initialize_components(self):
        """初始化子组件"""
        try:
            # 使用新架构的WeeklyOptimizationService
            from .services.weekly_optimization_service import WeeklyOptimizationService
            from .llm_interface import LLMInterface
            
            # 获取PhaseCoordinator
            phase_coordinator = getattr(self.assessor, 'phase_coordinator', None)
            if not phase_coordinator:
                raise RuntimeError("无法获取PhaseCoordinator")
            
            # 创建LLM接口（如果有LLM provider）
            llm_interface = None
            if hasattr(self.assessor, 'llm_provider') and self.assessor.llm_provider:
                llm_interface = LLMInterface(provider=self.assessor.llm_provider, logger=self.logger)
            
            # 创建WeeklyOptimizationService
            self.optimization_executor = WeeklyOptimizationService(
                phase_coordinator=phase_coordinator,
                llm_interface=llm_interface,
                logger=self.logger
            )
            self.logger.info("✅ 使用WeeklyOptimizationService作为优化执行器")
            
            self.logger.info("✅ WeeklyCycleManager组件初始化成功")
            
        except Exception as e:
            self.logger.error(f"❌ WeeklyCycleManager组件初始化失败: {e}")
            raise RuntimeError(f"组件初始化失败: {e}")
    
    def run_weekly_cycles(self, 
                         num_weeks: int = 1,
                         agents_per_week: int = 2,
                         target_agents: Optional[List[str]] = None,
                         max_coalitions: Optional[int] = None,
                         weekly_config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        运行多周的优化循环
        
        Args:
            num_weeks: 运行周数
            agents_per_week: 每周优化的智能体数量
            target_agents: 目标智能体列表
            max_coalitions: 最大联盟数
            weekly_config: 周期配置
            
        Returns:
            循环执行结果字典
        """
        start_time = time.time()
        
        # 确保stats已正确初始化
        if not hasattr(self, 'stats'):
            self.logger.error("❌ WeeklyCycleManager.stats未正确初始化")
            self.stats = {
                "total_cycles": 0,
                "successful_cycles": 0,
                "failed_cycles": 0,
                "total_execution_time": 0.0,
                "phases_executed": 0,
                "successful_phases": 0,
                "failed_phases": 0
            }
        
        self.stats["total_cycles"] += 1
        
        self.logger.info(f"🚀 开始{num_weeks}周的优化循环")
        
        weeks: List[WeekResult] = []
        overall_improvement = {}
        
        try:
            for week_num in range(1, num_weeks + 1):
                self.logger.info(f"\n📅 === 第{week_num}周优化循环 ===")
                
                # 记录所有智能体的提示词状态（在周开始前）
                self._log_all_agent_prompts(week_num, detailed=True)
                
                # 执行单周优化 - 使用新的5阶段工作流
                week_result = self._execute_single_week_with_phase_coordinator(
                    week_number=week_num,
                    target_agents=target_agents,
                    max_coalitions=max_coalitions,
                    weekly_config=weekly_config
                )
                
                weeks.append(week_result)
                
                # 合并性能改进数据
                for agent, improvement in week_result.performance_improvement.items():
                    if agent not in overall_improvement:
                        overall_improvement[agent] = 0.0
                    overall_improvement[agent] += improvement
            
            # 统计成功周数
            successful_weeks = sum(1 for week in weeks if week.success)
            execution_time = time.time() - start_time
            
            # 更新统计
            if successful_weeks > 0:
                self.stats["successful_cycles"] += 1
            else:
                self.stats["failed_cycles"] += 1
            
            self.stats["total_execution_time"] += execution_time
            
            # 构建结果
            cycle_result = CycleResult(
                total_weeks=num_weeks,
                successful_weeks=successful_weeks,
                total_execution_time=execution_time,
                weeks=weeks,
                overall_improvement=overall_improvement,
                timestamp=datetime.now().isoformat()
            )
            
            # 保存结果
            self._save_cycle_result(cycle_result, weekly_config)
            
            self.logger.info(f"🏁 {num_weeks}周优化循环完成，成功{successful_weeks}周")
            
            return {
                "success": successful_weeks > 0,
                "cycle_result": cycle_result.to_dict(),
                "architecture": "weekly_cycle_manager"
            }
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.stats["failed_cycles"] += 1
            self.stats["total_execution_time"] += execution_time
            
            self.logger.error(f"❌ 周期循环执行失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "execution_time": execution_time,
                "partial_results": [week.to_dict() for week in weeks],
                "architecture": "weekly_cycle_manager"
            }
    
    def _execute_single_week(self,
                            week_number: int,
                            agents_per_week: int,
                            target_agents: Optional[List[str]],
                            max_coalitions: Optional[int],
                            weekly_config: Optional[Dict[str, Any]]) -> WeekResult:
        """
        执行单周的优化循环
        
        Args:
            week_number: 周数
            agents_per_week: 每周优化的智能体数量
            target_agents: 目标智能体列表
            max_coalitions: 最大联盟数
            weekly_config: 周期配置
            
        Returns:
            单周执行结果
        """
        start_time = time.time()
        phases: List[WeekPhaseResult] = []
        agents_optimized: List[str] = []
        performance_improvement: Dict[str, float] = {}
        
        try:
            # 阶段1: 性能评估与问题识别
            phase1_result = self._execute_phase_1_evaluation(
                week_number=week_number,
                target_agents=target_agents,
                max_coalitions=max_coalitions,
                weekly_config=weekly_config
            )
            phases.append(phase1_result)
            
            if not phase1_result.success:
                raise RuntimeError(f"阶段1失败: {phase1_result.error_message}")
            
            # 阶段2: 优化策略生成与应用
            phase2_result = self._execute_phase_2_optimization(
                evaluation_result=phase1_result.data,
                agents_per_week=agents_per_week,
                weekly_config=weekly_config
            )
            phases.append(phase2_result)
            
            if phase2_result.success:
                agents_optimized = phase2_result.data.get("optimized_agents", [])
            
            # 阶段3: 优化效果验证
            phase3_result = self._execute_phase_3_verification(
                optimization_result=phase2_result.data,
                agents_optimized=agents_optimized,
                weekly_config=weekly_config
            )
            phases.append(phase3_result)
            
            if phase3_result.success:
                performance_improvement = phase3_result.data.get("performance_improvement", {})
            
            # 阶段4: 结果固化与系统更新
            phase4_result = self._execute_phase_4_consolidation(
                week_number=week_number,
                phases_data=[p.data for p in phases],
                weekly_config=weekly_config
            )
            phases.append(phase4_result)
            
            # 判断整周是否成功
            all_phases_success = all(phase.success for phase in phases)
            execution_time = time.time() - start_time
            
            # 更新统计
            self.stats["phases_executed"] += len(phases)
            self.stats["successful_phases"] += sum(1 for p in phases if p.success)
            self.stats["failed_phases"] += sum(1 for p in phases if not p.success)
            
            return WeekResult(
                week_number=week_number,
                success=all_phases_success,
                total_execution_time=execution_time,
                phases=phases,
                agents_optimized=agents_optimized,
                performance_improvement=performance_improvement,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"❌ 第{week_number}周执行失败: {e}")
            
            return WeekResult(
                week_number=week_number,
                success=False,
                total_execution_time=execution_time,
                phases=phases,
                agents_optimized=agents_optimized,
                performance_improvement=performance_improvement,
                timestamp=datetime.now().isoformat()
            )
    
    def _execute_phase_1_evaluation(self,
                                   week_number: int,
                                   target_agents: Optional[List[str]],
                                   max_coalitions: Optional[int],
                                   weekly_config: Optional[Dict[str, Any]]) -> WeekPhaseResult:
        """执行阶段1: 性能评估与问题识别"""
        phase_start = time.time()
        
        try:
            self.logger.info(f"  📊 阶段1: 性能评估与问题识别 (第{week_number}周)")
            
            # 使用RefactoredContributionAssessor进行评估
            evaluation_result = self.assessor.run(
                target_agents=target_agents,
                max_coalitions=max_coalitions
            )
            
            if not evaluation_result.get("success", False):
                raise RuntimeError(f"评估失败: {evaluation_result.get('error', 'Unknown error')}")
            
            execution_time = time.time() - phase_start
            
            return WeekPhaseResult(
                phase=WeekPhase.PHASE_1_EVALUATION,
                success=True,
                execution_time=execution_time,
                data={
                    "evaluation_result": evaluation_result,
                    "week_number": week_number
                },
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            execution_time = time.time() - phase_start
            self.logger.error(f"    ❌ 阶段1失败: {e}")
            
            return WeekPhaseResult(
                phase=WeekPhase.PHASE_1_EVALUATION,
                success=False,
                execution_time=execution_time,
                data={},
                error_message=str(e),
                timestamp=datetime.now().isoformat()
            )
    
    def _execute_phase_2_optimization(self,
                                     evaluation_result: Dict[str, Any],
                                     agents_per_week: int,
                                     weekly_config: Optional[Dict[str, Any]]) -> WeekPhaseResult:
        """执行阶段2: 优化策略生成与应用"""
        phase_start = time.time()
        
        try:
            self.logger.info("  🔧 阶段2: 优化策略生成与应用")
            
            # 使用优化执行器进行OPRO优化
            if hasattr(self.optimization_executor, 'execute_optimization'):
                optimization_result = self.optimization_executor.execute_optimization(
                    evaluation_result=evaluation_result,
                    max_agents=agents_per_week,
                    config=weekly_config or {}
                )
            else:
                # 备用：使用assessor的OPRO功能
                optimization_result = self.assessor.run_with_weekly_optimization(
                    weekly_config=weekly_config
                )
            
            optimized_agents = optimization_result.get("optimized_agents", [])
            
            execution_time = time.time() - phase_start
            
            return WeekPhaseResult(
                phase=WeekPhase.PHASE_2_OPTIMIZATION,
                success=True,
                execution_time=execution_time,
                data={
                    "optimization_result": optimization_result,
                    "optimized_agents": optimized_agents
                },
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            execution_time = time.time() - phase_start
            self.logger.error(f"    ❌ 阶段2失败: {e}")
            
            return WeekPhaseResult(
                phase=WeekPhase.PHASE_2_OPTIMIZATION,
                success=False,
                execution_time=execution_time,
                data={},
                error_message=str(e),
                timestamp=datetime.now().isoformat()
            )
    
    def _execute_phase_3_verification(self,
                                     optimization_result: Dict[str, Any],
                                     agents_optimized: List[str],
                                     weekly_config: Optional[Dict[str, Any]]) -> WeekPhaseResult:
        """执行阶段3: 优化效果验证"""
        phase_start = time.time()
        
        try:
            self.logger.info("  ✅ 阶段3: 优化效果验证")
            
            # 重新运行评估以验证优化效果
            verification_result = self.assessor.run(
                target_agents=agents_optimized
            )
            
            # 计算性能改进
            performance_improvement = {}
            if verification_result.get("success", False):
                shapley_values = verification_result.get("shapley_values", {})
                for agent in agents_optimized:
                    if agent in shapley_values:
                        # 简化的改进计算
                        performance_improvement[agent] = shapley_values[agent]
            
            execution_time = time.time() - phase_start
            
            return WeekPhaseResult(
                phase=WeekPhase.PHASE_3_VERIFICATION,
                success=True,
                execution_time=execution_time,
                data={
                    "verification_result": verification_result,
                    "performance_improvement": performance_improvement
                },
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            execution_time = time.time() - phase_start
            self.logger.error(f"    ❌ 阶段3失败: {e}")
            
            return WeekPhaseResult(
                phase=WeekPhase.PHASE_3_VERIFICATION,
                success=False,
                execution_time=execution_time,
                data={},
                error_message=str(e),
                timestamp=datetime.now().isoformat()
            )
    
    def _execute_phase_4_consolidation(self,
                                      week_number: int,
                                      phases_data: List[Dict[str, Any]],
                                      weekly_config: Optional[Dict[str, Any]]) -> WeekPhaseResult:
        """执行阶段4: 结果固化与系统更新"""
        phase_start = time.time()
        
        try:
            self.logger.info("  💾 阶段4: 结果固化与系统更新")
            
            # 保存本周结果
            week_data = {
                "week_number": week_number,
                "phases": phases_data,
                "timestamp": datetime.now().isoformat()
            }
            
            # 保存到文件
            results_dir = weekly_config.get("results_dir", "results/weekly_cycles") if weekly_config else "results/weekly_cycles"
            os.makedirs(results_dir, exist_ok=True)
            
            week_file = os.path.join(results_dir, f"week_{week_number}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            with open(week_file, 'w', encoding='utf-8') as f:
                json.dump(week_data, f, indent=2, ensure_ascii=False)
            
            execution_time = time.time() - phase_start
            
            return WeekPhaseResult(
                phase=WeekPhase.PHASE_4_CONSOLIDATION,
                success=True,
                execution_time=execution_time,
                data={
                    "saved_file": week_file,
                    "consolidated_data": week_data
                },
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            execution_time = time.time() - phase_start
            self.logger.error(f"    ❌ 阶段4失败: {e}")
            
            return WeekPhaseResult(
                phase=WeekPhase.PHASE_4_CONSOLIDATION,
                success=False,
                execution_time=execution_time,
                data={},
                error_message=str(e),
                timestamp=datetime.now().isoformat()
            )
    
    def _save_cycle_result(self, cycle_result: CycleResult, weekly_config: Optional[Dict[str, Any]]):
        """保存循环结果"""
        try:
            results_dir = weekly_config.get("results_dir", "results/weekly_cycles") if weekly_config else "results/weekly_cycles"
            os.makedirs(results_dir, exist_ok=True)
            
            cycle_file = os.path.join(
                results_dir, 
                f"cycle_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            )
            
            with open(cycle_file, 'w', encoding='utf-8') as f:
                json.dump(cycle_result.to_dict(), f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"📁 循环结果已保存: {cycle_file}")
            
        except Exception as e:
            self.logger.error(f"❌ 保存循环结果失败: {e}")
    
    def _log_all_agent_prompts(self, week_number: int, detailed: bool = True) -> None:
        """
        记录所有智能体的提示词状态
        
        Args:
            week_number: 当前周数
            detailed: 是否输出详细信息（包含提示词预览）
        """
        try:
            # 获取所有智能体
            agents = getattr(self.assessor, 'agents', {})
            if not agents:
                self.logger.warning(f"第{week_number}周开始前：未找到智能体实例")
                return
            
            self.logger.info(f"📋 第{week_number}周开始前 - 智能体提示词状态总览")
            self.logger.info("=" * 80)
            
            # 统计信息
            total_agents = len(agents)
            opro_enabled_count = 0
            optimized_count = 0
            
            # 遍历所有智能体
            for agent_id, agent_instance in agents.items():
                # 检查智能体是否有OPRO支持
                if hasattr(agent_instance, 'format_prompt_log'):
                    # 使用OPROBaseAgent的格式化方法
                    prompt_log = agent_instance.format_prompt_log(include_preview=detailed)
                    self.logger.info(prompt_log)
                    
                    # 统计OPRO相关信息
                    if hasattr(agent_instance, 'opro_enabled') and agent_instance.opro_enabled:
                        opro_enabled_count += 1
                        
                    if hasattr(agent_instance, '_opro_stats'):
                        opro_stats = agent_instance._opro_stats
                        if opro_stats.get("successful_optimizations", 0) > 0:
                            optimized_count += 1
                            
                elif hasattr(agent_instance, 'get_prompt_template'):
                    # 对于没有OPRO支持的智能体，显示基础信息
                    prompt = agent_instance.get_prompt_template()
                    prompt_length = len(prompt)
                    preview = prompt[:100] + "..." if len(prompt) > 100 else prompt
                    
                    self.logger.info(f"📝 {agent_id}: 静态提示词 ({prompt_length}字符)")
                    if detailed:
                        self.logger.info(f"   💬 预览: {preview}")
                else:
                    self.logger.info(f"⚠️ {agent_id}: 无法获取提示词信息")
            
            # 输出总览统计
            self.logger.info("=" * 80)
            self.logger.info(f"📊 提示词状态统计:")
            self.logger.info(f"   总智能体数: {total_agents}")
            self.logger.info(f"   OPRO启用: {opro_enabled_count}/{total_agents}")
            self.logger.info(f"   已优化智能体: {optimized_count}/{total_agents}")
            
            # 如果有优化过的智能体，显示优化传递状态
            if optimized_count > 0:
                self.logger.info(f"✅ 检测到 {optimized_count} 个智能体已完成OPRO优化，提示词将在本周继续使用")
            else:
                self.logger.info("🔄 所有智能体使用默认提示词，等待首次OPRO优化")
                
            self.logger.info("=" * 80)
            
        except Exception as e:
            self.logger.error(f"记录智能体提示词状态失败: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "total_cycles": self.stats["total_cycles"],
            "successful_cycles": self.stats["successful_cycles"],
            "failed_cycles": self.stats["failed_cycles"],
            "success_rate": (self.stats["successful_cycles"] / max(1, self.stats["total_cycles"])) * 100,
            "total_execution_time": self.stats["total_execution_time"],
            "phases_executed": self.stats["phases_executed"],
            "successful_phases": self.stats["successful_phases"],
            "failed_phases": self.stats["failed_phases"],
            "phase_success_rate": (self.stats["successful_phases"] / max(1, self.stats["phases_executed"])) * 100
        }
    
    def _execute_single_week_with_phase_coordinator(self,
                                                   week_number: int,
                                                   target_agents: Optional[List[str]],
                                                   max_coalitions: Optional[int],
                                                   weekly_config: Optional[Dict[str, Any]]) -> WeekResult:
        """
        使用PhaseCoordinator的5阶段工作流执行单周优化
        
        Args:
            week_number: 周数
            target_agents: 目标智能体列表
            max_coalitions: 最大联盟数
            weekly_config: 周期配置
            
        Returns:
            WeekResult: 单周执行结果
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"🚀 开始第 {week_number} 周优化周期 (5阶段工作流)")
            
            # 获取PhaseCoordinator
            phase_coordinator = getattr(self.assessor, 'phase_coordinator', None)
            if not phase_coordinator:
                raise RuntimeError("无法获取PhaseCoordinator")
            
            # 智能体获取逻辑：优先从状态恢复，否则创建新智能体
            agents = self._get_or_restore_agents(week_number)
            
            self.logger.info(f"可用智能体: {list(agents.keys())}")
            
            # 验证智能体状态传递的完整性
            validation_passed = self._validate_agent_state_transfer(agents, week_number)
            if not validation_passed:
                self.logger.warning("⚠️ 智能体状态传递验证未完全通过，但继续执行")
            
            # 记录智能体状态摘要
            self._log_agent_state_summary(agents, week_number)

            # 在新的一周开始时，清空所有智能体的周度IO数据
            self.logger.info(f"🔄 清理第{week_number}周的旧IO数据...")
            for agent_id, agent_instance in agents.items():
                if hasattr(agent_instance, 'clear_weekly_io_data'):
                    agent_instance.clear_weekly_io_data()
                    self.logger.debug(f"  - 已清空 {agent_id} 的IO数据")
            self.logger.info("✅ 所有智能体的周度IO数据已清空")

            # 投资组合状态管理（新旧兼容）
            config_dict = {**self.config, "current_week_number": week_number}
            
            # 确保 current_week_number 正确传递到配置中（修复日期设置问题）
            config_dict["current_week_number"] = week_number
            
            # 优先使用portfolio_tracker，回退到旧的状态管理
            if self.portfolio_tracker:
                # 通知tracker开始新的一周
                self.portfolio_tracker.start_new_week(week_number)
                # 传递tracker实例
                config_dict["portfolio_tracker"] = self.portfolio_tracker
                self.logger.info(f"📊 第{week_number}周使用日常投资组合跟踪器")
            else:
                # 使用旧的跨周状态传递机制
                previous_week_state = PortfolioStateManager.load_previous_week_state(week_number)
                
                # 如果没有找到投资组合状态，但智能体已恢复，尝试从智能体状态提取
                if not previous_week_state and week_number > 1 and agents:
                    self.logger.info(f"🔍 未找到第{week_number-1}周投资组合状态，尝试从智能体状态中提取...")
                    previous_week_state = self._extract_portfolio_state_from_agents(agents, week_number-1)
                    if previous_week_state:
                        # 保存提取的状态以供后续使用
                        PortfolioStateManager.save_week_end_state(week_number-1, previous_week_state)
                        self.logger.info(f"✅ 从智能体状态成功提取并保存第{week_number-1}周投资组合状态")
                
                if previous_week_state:
                    # 确保周收益率被正确重置：将当前周收益率保存为上周收益率，重置当前周收益率
                    previous_week_state["last_week_return"] = previous_week_state.get("weekly_return", 0.0)
                    previous_week_state["weekly_return"] = 0.0
                    self.logger.info(f"🔄 第{week_number}周继承状态: 累计收益率={previous_week_state.get('cumulative_return', 0):.4f}, 上周收益率={previous_week_state.get('last_week_return', 0):.4f}")
                else:
                    self.logger.warning(f"⚠️ 第{week_number}周无法获取上周投资组合状态，将使用初始状态")
                
                config_dict["inherited_portfolio_state"] = previous_week_state
                self.logger.info(f"🔄 第{week_number}周使用旧版跨周状态传递")
            
            # 构建评估请求
            assessment_request = AssessmentRequest(
                request_id=f"weekly_assessment_{week_number}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                agents=agents,
                target_agents=target_agents or [],
                max_coalitions=max_coalitions or 50,
                enable_opro=weekly_config.get("enable_opro", True) if weekly_config else True,
                config=config_dict,
                opro_config=weekly_config.get("opro_config", {}) if weekly_config else {},
                max_concurrent_api_calls=weekly_config.get("max_concurrent_api_calls", 3) if weekly_config else 3
            )
            
            # 通过PhaseCoordinator执行完整的5阶段工作流
            phase_result = phase_coordinator.execute_weekly_assessment_cycle(
                request=assessment_request,
                week_num=week_number
            )
            
            # 转换PhaseCoordinator的结果为WeeklyCycleManager的WeekResult格式
            execution_time = time.time() - start_time
            
            # 构建WeekPhaseResult列表（为了兼容现有接口）
            phases = []
            if phase_result.coalition_result:
                phases.append(WeekPhaseResult(
                    phase=WeekPhase.PHASE_1_EVALUATION,  # 重用现有枚举
                    success=phase_result.coalition_result.success,
                    execution_time=phase_result.coalition_result.execution_time,
                    data={"coalition_count": len(phase_result.coalition_result.valid_coalitions)},
                    timestamp=datetime.now().isoformat()
                ))
            
            # 提取优化的智能体信息
            agents_optimized = phase_result.optimized_agents or []
            
            # 提取性能改进数据
            performance_improvement = {}
            if phase_result.performance_metrics:
                for agent_id, score in phase_result.performance_metrics.items():
                    performance_improvement[agent_id] = float(score)
            
            # 保存第week_number周结束时的投资组合状态（跨周状态传递）
            if phase_result.success:
                try:
                    # 优先使用portfolio_tracker的状态（新方法）
                    if self.portfolio_tracker:
                        week_end_state = self.portfolio_tracker.get_week_end_return_state(week_number)
                        if week_end_state:
                            PortfolioStateManager.save_week_end_state(week_number, week_end_state)
                            self.logger.info(f"✅ 通过跟踪器保存第{week_number}周末状态: 累计收益率={week_end_state.get('cumulative_return', 0):.4f}")
                        else:
                            self.logger.warning(f"⚠️ 跟踪器中未找到第{week_number}周的状态数据")
                    # 回退到传统状态提取方法
                    elif hasattr(self.assessor, 'trading_simulator') and hasattr(self.assessor.trading_simulator, 'env'):
                        current_portfolio_state = PortfolioStateManager.extract_portfolio_state_from_trading_env(
                            self.assessor.trading_simulator.env
                        )
                        if current_portfolio_state:
                            PortfolioStateManager.save_week_end_state(week_number, current_portfolio_state)
                            self.logger.info(f"✅ 通过交易环境保存第{week_number}周末状态")
                    elif phase_result.simulation_result and hasattr(phase_result.simulation_result, 'final_portfolio_state'):
                        # 如果simulation_result中有final_portfolio_state，使用它
                        PortfolioStateManager.save_week_end_state(week_number, phase_result.simulation_result.final_portfolio_state)
                        self.logger.info(f"✅ 通过仿真结果保存第{week_number}周末状态")
                    else:
                        self.logger.warning(f"⚠️ 无法找到有效的状态来源保存第{week_number}周状态")
                except Exception as e:
                    self.logger.warning(f"⚠️ 保存第{week_number}周投资组合状态失败: {e}")
                
                # 保存智能体状态（新功能 - 仅完整联盟）
                if self.agent_state_manager and agents:
                    try:
                        # 检查是否运行了完整联盟
                        is_full_coalition = self._check_if_ran_full_coalition(phase_result, agents)
                        
                        self.logger.info(f"🤖 开始保存第{week_number}周智能体状态...")
                        self.logger.info(f"🔍 完整联盟运行检查: {'是' if is_full_coalition else '否'}")
                        
                        success = self.agent_state_manager.save_agents_state(
                            agents, 
                            week_number, 
                            is_full_coalition=is_full_coalition
                        )
                        
                        if success:
                            self.logger.info(f"✅ 第{week_number}周智能体状态保存成功")
                        else:
                            if is_full_coalition:
                                self.logger.warning(f"⚠️ 第{week_number}周智能体状态保存失败")
                            else:
                                self.logger.info(f"🚫 第{week_number}周非完整联盟，跳过状态保存")
                    except Exception as e:
                        self.logger.error(f"❌ 保存第{week_number}周智能体状态异常: {e}")
                else:
                    if not self.agent_state_manager:
                        self.logger.debug("🤖 智能体状态管理器未启用，跳过智能体状态保存")
                    if not agents:
                        self.logger.warning("🤖 无可用智能体，跳过智能体状态保存")
            
            # 更新统计
            self.stats["phases_executed"] += 5  # 5个阶段（移除联盟生成）
            if phase_result.success:
                self.stats["successful_phases"] += 5
            
            return WeekResult(
                week_number=week_number,
                success=phase_result.success,
                total_execution_time=execution_time,
                phases=phases,
                agents_optimized=agents_optimized,
                performance_improvement=performance_improvement,
                timestamp=datetime.now().isoformat()
            )
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"❌ 第{week_number}周5阶段工作流执行失败: {e}")
            
            # 更新失败统计
            self.stats["failed_phases"] += 5
            
            return WeekResult(
                week_number=week_number,
                success=False,
                total_execution_time=execution_time,
                phases=[],
                agents_optimized=[],
                performance_improvement={},
                timestamp=datetime.now().isoformat()
            )
    
    def _check_if_ran_full_coalition(self, phase_result, agents: Dict[str, Any]) -> bool:
        """
        检查本次评估是否运行了完整联盟
        
        Args:
            phase_result: 阶段执行结果
            agents: 智能体字典
            
        Returns:
            bool: 是否运行了完整联盟
        """
        try:
            # 获取所有可用的智能体ID
            all_agent_ids = set(agents.keys())
            
            # 检查联盟结果中是否包含完整联盟
            if hasattr(phase_result, 'coalition_result') and phase_result.coalition_result:
                coalition_values = getattr(phase_result.coalition_result, 'coalition_values', {})
                
                # 检查是否存在包含所有智能体的联盟
                for coalition in coalition_values.keys():
                    if isinstance(coalition, (frozenset, set)):
                        coalition_agents = set(coalition)
                    else:
                        # 处理其他可能的联盟格式
                        continue
                    
                    # 如果联盟包含所有智能体，则认为是完整联盟
                    if coalition_agents == all_agent_ids:
                        self.logger.info(f"🔍 检测到完整联盟运行: {sorted(list(coalition_agents))}")
                        return True
            
            # 如果没有找到完整联盟，检查模拟结果
            if hasattr(phase_result, 'simulation_result') and phase_result.simulation_result:
                active_agents = getattr(phase_result.simulation_result, 'active_agents', {})
                if isinstance(active_agents, dict) and set(active_agents.keys()) == all_agent_ids:
                    self.logger.info(f"🔍 通过模拟结果检测到完整联盟运行: {sorted(list(all_agent_ids))}")
                    return True
            
            self.logger.info(f"🔍 未检测到完整联盟运行，可用智能体: {sorted(list(all_agent_ids))}")
            return False
            
        except Exception as e:
            self.logger.warning(f"⚠️ 检查完整联盟运行时出错: {e}")
            # 出错时保守处理，假设不是完整联盟
            return False