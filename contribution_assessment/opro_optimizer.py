"""
OPRO优化器核心实现

实现基于贡献度的智能体提示词优化功能，包括：
- 提示词质量评估
- 单个智能体优化
- 批量智能体优化
- 提示词生成和改进算法
"""

import logging
import time
from dataclasses import dataclass
from typing import Dict, List, Optional, Any
from datetime import datetime

from .llm_interface import LLMInterface


@dataclass
class OptimizationConfig:
    """优化配置"""
    temperature: float = 0.7
    max_iterations: int = 3
    improvement_threshold: float = 0.05
    quality_threshold: float = 0.7
    enable_validation: bool = True
    enable_rollback: bool = True
    timeout_seconds: int = 300


@dataclass
class OptimizationResult:
    """优化结果"""
    success: bool
    agent_id: str
    original_prompt: str
    optimized_prompt: str
    estimated_improvement: float
    quality_score: float
    optimization_applied: bool
    metadata: Dict[str, Any]


@dataclass
class PerformanceIssue:
    """性能问题"""
    issue_type: str  # "accuracy", "consistency", "format", "logic"
    severity: float  # 0.0-1.0
    description: str
    suggested_fix: str


class OPROOptimizationError(Exception):
    """OPRO优化基础异常"""
    pass


class PromptGenerationError(OPROOptimizationError):
    """提示词生成异常"""
    pass


class QualityValidationError(OPROOptimizationError):
    """质量验证异常"""
    pass


class OptimizationTimeoutError(OPROOptimizationError):
    """优化超时异常"""
    pass


class OPROOptimizer:
    """
    OPRO优化器核心实现
    
    负责基于贡献度的智能体提示词优化，实现CG-OPO算法
    """
    
    # 默认配置
    DEFAULT_CONFIG = {
        "optimization": {
            "temperature": 0.7,
            "max_iterations": 3,
            "improvement_threshold": 0.05,
            "quality_threshold": 0.7,
            "timeout_seconds": 300
        },
        "validation": {
            "enable_syntax_check": True,
            "enable_logic_check": True,
            "enable_format_check": True,
            "min_prompt_length": 50,
            "max_prompt_length": 5000
        },
        "agent_specialization": {
            "NAA": {"focus": "news_analysis", "keywords": ["新闻", "情绪", "舆情"]},
            "TAA": {"focus": "technical_analysis", "keywords": ["技术", "趋势", "指标"]},
            "FAA": {"focus": "fundamental_analysis", "keywords": ["基本面", "财务", "估值"]},
            "BOA": {"focus": "bullish_outlook", "keywords": ["看涨", "乐观", "买入"]},
            "BeOA": {"focus": "bearish_outlook", "keywords": ["看跌", "悲观", "卖出"]},
            "NOA": {"focus": "neutral_outlook", "keywords": ["中性", "平衡", "观察"]},
            "TRA": {"focus": "trading_decision", "keywords": ["交易", "决策", "风险"]}
        }
    }
    
    # 优化模板
    OPTIMIZATION_TEMPLATES = {
        "performance_improvement": """
基于以下性能问题改进提示词：
当前提示词：{current_prompt}
性能问题：{performance_issues}
智能体类型：{agent_type}

请生成改进的提示词，重点解决以下问题：
{specific_improvements}

要求：
1. 保持原有功能完整性
2. 针对性解决识别的问题
3. 提高输出的一致性和准确性
4. 保持提示词简洁明了

请直接返回改进后的提示词，不需要额外说明。
""",
        
        "consistency_enhancement": """
提高提示词的一致性和可靠性：
当前提示词：{current_prompt}
一致性问题：{consistency_issues}

请优化提示词以确保输出格式和逻辑的一致性。

要求：
1. 统一输出格式
2. 加强逻辑一致性
3. 减少歧义表达
4. 提高可重复性

请直接返回优化后的提示词。
""",
        
        "domain_specialization": """
针对{agent_type}智能体的专业领域优化：
当前提示词：{current_prompt}
专业要求：{domain_requirements}

请增强提示词的专业性和准确性。

要求：
1. 强化专业领域知识
2. 提高分析深度
3. 优化决策逻辑
4. 增强输出质量

请直接返回专业化优化后的提示词。
"""
    }
    
    def __init__(self, 
                 llm_interface: LLMInterface,
                 config: Optional[Dict[str, Any]] = None,
                 logger: Optional[logging.Logger] = None):
        """
        初始化优化器
        
        Args:
            llm_interface: LLM接口实例
            config: 优化配置参数
            logger: 日志记录器
        """
        self.llm_interface = llm_interface
        self.logger = logger or logging.getLogger(__name__)
        
        # 合并配置
        self.config = self._merge_config(config or {})
        
        # 内部状态
        self._optimization_history: List[Dict[str, Any]] = []
        self._performance_cache: Dict[str, float] = {}
        
        self.logger.info("OPRO优化器初始化完成")
    
    def _merge_config(self, user_config: Dict[str, Any]) -> Dict[str, Any]:
        """合并用户配置和默认配置"""
        merged = self.DEFAULT_CONFIG.copy()
        
        for key, value in user_config.items():
            if key in merged and isinstance(merged[key], dict) and isinstance(value, dict):
                merged[key].update(value)
            else:
                merged[key] = value
        
        return merged
    
    def optimize_all_agents(self, 
                          agent_ids: List[str], 
                          current_prompts: Dict[str, str]) -> Dict[str, Any]:
        """
        批量优化智能体
        
        Args:
            agent_ids: 智能体ID列表
            current_prompts: 当前提示词映射
            
        Returns:
            Dict[str, Any]: 批量优化结果
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"开始批量优化 {len(agent_ids)} 个智能体")
            
            optimized_agents = []
            optimization_results = {}
            
            for agent_id in agent_ids:
                current_prompt = current_prompts.get(agent_id, "")
                if not current_prompt:
                    self.logger.warning(f"智能体 {agent_id} 没有当前提示词，跳过优化")
                    continue
                
                # 检查提示词质量
                quality_result = self.evaluate_prompt_quality(current_prompt, agent_id)
                self.logger.info(f"智能体 {agent_id} 提示词质量: {quality_result.get('score', 0.0):.3f}")
                
                try:
                    # 使用较低的性能分数触发优化
                    result = self.optimize_single_agent(
                        agent_name=agent_id,
                        current_prompt=current_prompt,
                        performance_score=0.4,  # 较低性能分数触发优化
                        failure_cases=[]
                    )
                    
                    optimization_results[agent_id] = result
                    
                    if result.get("optimization_applied", False):
                        optimized_agents.append(agent_id)
                        self.logger.info(f"智能体 {agent_id} 优化成功")
                    else:
                        self.logger.info(f"智能体 {agent_id} 优化未应用: {result.get('message', '未知原因')}")
                    
                except Exception as e:
                    self.logger.error(f"优化智能体 {agent_id} 失败: {e}")
                    optimization_results[agent_id] = {
                        "success": False,
                        "error": str(e),
                        "optimization_applied": False
                    }
            
            execution_time = time.time() - start_time
            
            result = {
                "success": len(optimized_agents) > 0,
                "optimized_agents": optimized_agents,
                "optimization_results": optimization_results,
                "total_agents": len(agent_ids),
                "successful_optimizations": len(optimized_agents),
                "execution_time": execution_time,
                "timestamp": datetime.now().isoformat()
            }
            
            self.logger.info(f"批量优化完成: {len(optimized_agents)}/{len(agent_ids)} 成功, 耗时: {execution_time:.2f}s")
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"批量优化失败: {e}, 耗时: {execution_time:.2f}s")
            return {
                "success": False,
                "error": str(e),
                "optimized_agents": [],
                "execution_time": execution_time
            }
    
    def optimize_single_agent(self, 
                            agent_name: str, 
                            current_prompt: str, 
                            performance_score: float, 
                            failure_cases: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """
        优化单个智能体
        
        Args:
            agent_name: 智能体名称
            current_prompt: 当前提示词
            performance_score: 性能评分
            failure_cases: 失败案例（可选）
            
        Returns:
            Dict[str, Any]: 优化结果
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"开始优化智能体: {agent_name} (性能分数: {performance_score:.3f})")
            
            # 1. 评估当前提示词质量
            quality_result = self.evaluate_prompt_quality(current_prompt, agent_name)
            
            if not quality_result.get("valid", False):
                return {
                    "success": False,
                    "error": f"提示词质量验证失败: {quality_result.get('error', '未知错误')}",
                    "optimization_applied": False,
                    "execution_time": time.time() - start_time
                }
            
            # 2. 识别性能问题
            performance_issues = self._identify_performance_issues(
                agent_name, performance_score, failure_cases or []
            )
            
            # 3. 生成改进提示词
            improved_prompt = self.generate_improved_prompt(
                agent_name, current_prompt, performance_issues
            )
            
            if not improved_prompt or improved_prompt == current_prompt:
                return {
                    "success": True,
                    "agent_name": agent_name,
                    "original_prompt": current_prompt,
                    "optimized_prompt": current_prompt,
                    "estimated_improvement": 0.0,
                    "quality_score": quality_result.get("score", 0.7),
                    "optimization_applied": False,
                    "message": "无需优化或生成失败",
                    "execution_time": time.time() - start_time
                }
            
            # 4. 验证改进提示词质量
            improved_quality = self.evaluate_prompt_quality(improved_prompt, agent_name)
            
            if not improved_quality.get("valid", False):
                self.logger.warning(f"改进提示词验证失败: {improved_quality.get('error', '未知错误')}")
                self.logger.warning(f"改进提示词内容: {improved_prompt[:100]}...")
                return {
                    "success": False,
                    "error": f"改进提示词质量验证失败: {improved_quality.get('error', '未知错误')}",
                    "optimization_applied": False,
                    "execution_time": time.time() - start_time
                }
            
            # 5. 估算改进效果
            estimated_improvement = self._estimate_improvement(
                quality_result.get("score", 0.7),
                improved_quality.get("score", 0.7),
                performance_issues
            )
            
            # 6. 决定是否应用优化
            should_apply = estimated_improvement > self.config["optimization"]["improvement_threshold"]
            
            execution_time = time.time() - start_time
            
            result = {
                "success": True,
                "agent_name": agent_name,
                "original_prompt": current_prompt,
                "optimized_prompt": improved_prompt,
                "estimated_improvement": estimated_improvement,
                "quality_score": improved_quality.get("score", 0.7),
                "optimization_applied": should_apply,
                "performance_issues": [issue.__dict__ for issue in performance_issues],
                "execution_time": execution_time,
                "metadata": {
                    "original_quality": quality_result,
                    "improved_quality": improved_quality,
                    "optimization_timestamp": datetime.now().isoformat()
                }
            }
            
            # 记录优化历史
            self._record_optimization(result)
            
            self.logger.info(f"智能体 {agent_name} 优化完成: 改进={estimated_improvement:.4f}, "
                           f"应用={should_apply}, 耗时={execution_time:.2f}s")
            
            return result
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"优化智能体 {agent_name} 失败: {e}, 耗时={execution_time:.2f}s")
            return {
                "success": False,
                "error": str(e),
                "optimization_applied": False,
                "execution_time": execution_time
            }
    
    def generate_improved_prompt(self, 
                               agent_type: str, 
                               current_prompt: str, 
                               performance_issues: List[PerformanceIssue]) -> str:
        """
        生成改进的提示词
        
        Args:
            agent_type: 智能体类型
            current_prompt: 当前提示词
            performance_issues: 性能问题列表
            
        Returns:
            str: 改进的提示词
        """
        try:
            if not performance_issues:
                self.logger.info(f"智能体 {agent_type} 无性能问题，无需改进")
                return current_prompt
            
            # 选择优化模板
            template_key = self._select_optimization_template(performance_issues)
            template = self.OPTIMIZATION_TEMPLATES[template_key]
            
            # 准备模板参数
            template_params = self._prepare_template_params(
                agent_type, current_prompt, performance_issues
            )
            
            # 格式化提示词
            optimization_prompt = template.format(**template_params)
            
            # 调用LLM生成改进提示词
            response = self.llm_interface.analyze(
                prompt=optimization_prompt,
                model="glm-4-flash",
                temperature=self.config["optimization"]["temperature"]
            )
            
            if not response:
                raise PromptGenerationError("LLM响应为空")
            
            # 提取改进的提示词
            improved_prompt = self._extract_improved_prompt(response, agent_type)
            
            if not improved_prompt:
                raise PromptGenerationError("无法从LLM响应中提取改进提示词")
            
            self.logger.info(f"为智能体 {agent_type} 生成改进提示词成功")
            return improved_prompt
            
        except Exception as e:
            self.logger.error(f"生成改进提示词失败 ({agent_type}): {e}")
            raise PromptGenerationError(f"生成改进提示词失败: {str(e)}")
    
    def evaluate_prompt_quality(self, 
                              prompt: str, 
                              agent_type: str) -> Dict[str, Any]:
        """
        评估提示词质量
        
        Args:
            prompt: 要评估的提示词
            agent_type: 智能体类型
            
        Returns:
            Dict[str, Any]: 质量评估结果
        """
        try:
            # 基础验证
            if not prompt or not prompt.strip():
                return {
                    "valid": False,
                    "error": "提示词不能为空",
                    "score": 0.0
                }
            
            # 长度验证
            prompt_length = len(prompt)
            min_length = self.config["validation"]["min_prompt_length"]
            max_length = self.config["validation"]["max_prompt_length"]
            
            if prompt_length < min_length:
                return {
                    "valid": False,
                    "error": f"提示词过短 (最少{min_length}字符)",
                    "score": 0.0
                }
            
            if prompt_length > max_length:
                return {
                    "valid": False,
                    "error": f"提示词过长 (最多{max_length}字符)",
                    "score": 0.0
                }
            
            # 智能体类型特化验证
            specialization = self.config["agent_specialization"].get(agent_type, {})
            required_keywords = specialization.get("keywords", [])
            
            found_keywords = []
            if required_keywords:
                found_keywords = [kw for kw in required_keywords if kw in prompt]
            
            # 计算质量分数
            quality_score = self._calculate_quality_score(
                prompt, agent_type, found_keywords, required_keywords
            )
            
            # 生成建议
            recommendations = self._generate_quality_recommendations(
                prompt, agent_type, found_keywords, required_keywords
            )
            
            return {
                "valid": quality_score >= self.config["optimization"]["quality_threshold"],
                "score": quality_score,
                "length": prompt_length,
                "keywords_found": found_keywords,
                "keywords_required": required_keywords,
                "recommendations": recommendations,
                "agent_type": agent_type
            }
            
        except Exception as e:
            self.logger.error(f"提示词质量评估失败: {e}")
            return {
                "valid": False,
                "error": f"质量评估失败: {str(e)}",
                "score": 0.0
            }
    
    def _identify_performance_issues(self, 
                                   agent_name: str, 
                                   performance_score: float, 
                                   failure_cases: List[Dict]) -> List[PerformanceIssue]:
        """识别性能问题"""
        issues = []
        
        # 基于性能分数识别问题
        if performance_score < 0.3:
            issues.append(PerformanceIssue(
                issue_type="accuracy",
                severity=0.9,
                description="整体性能严重不足",
                suggested_fix="全面重构提示词逻辑和结构"
            ))
        elif performance_score < 0.6:
            issues.append(PerformanceIssue(
                issue_type="consistency",
                severity=0.7,
                description="性能表现不稳定",
                suggested_fix="增强提示词的一致性和可重复性"
            ))
        elif performance_score < 0.8:
            issues.append(PerformanceIssue(
                issue_type="format",
                severity=0.5,
                description="输出质量有待提升",
                suggested_fix="优化输出格式和表达方式"
            ))
        
        # 基于失败案例识别问题
        if failure_cases:
            issues.append(PerformanceIssue(
                issue_type="logic",
                severity=0.6,
                description=f"存在 {len(failure_cases)} 个失败案例",
                suggested_fix="分析失败案例，改进决策逻辑"
            ))
        
        return issues
    
    def _select_optimization_template(self, performance_issues: List[PerformanceIssue]) -> str:
        """选择优化模板"""
        if not performance_issues:
            return "performance_improvement"
        
        # 根据问题类型选择模板
        issue_types = [issue.issue_type for issue in performance_issues]
        
        if "consistency" in issue_types or "format" in issue_types:
            return "consistency_enhancement"
        elif any(severity > 0.8 for issue in performance_issues for severity in [issue.severity]):
            return "domain_specialization"
        else:
            return "performance_improvement"
    
    def _prepare_template_params(self, 
                               agent_type: str, 
                               current_prompt: str, 
                               performance_issues: List[PerformanceIssue]) -> Dict[str, str]:
        """准备模板参数"""
        issues_text = "\n".join([
            f"- {issue.description} (严重程度: {issue.severity:.1f})"
            for issue in performance_issues
        ])
        
        improvements_text = "\n".join([
            f"- {issue.suggested_fix}"
            for issue in performance_issues
        ])
        
        specialization = self.config["agent_specialization"].get(agent_type, {})
        domain_requirements = f"专注于{specialization.get('focus', '通用分析')}"
        
        return {
            "current_prompt": current_prompt,
            "agent_type": agent_type,
            "performance_issues": issues_text,
            "specific_improvements": improvements_text,
            "consistency_issues": issues_text,
            "domain_requirements": domain_requirements
        }
    
    def _extract_improved_prompt(self, llm_response: Dict[str, Any], agent_type: str) -> str:
        """从LLM响应中提取改进的提示词"""
        if isinstance(llm_response, dict):
            # 尝试从不同字段提取内容
            content = (llm_response.get("content") or 
                      llm_response.get("improved_prompt") or 
                      llm_response.get("optimized_prompt") or
                      llm_response.get("reasoning", ""))  # Mock LLM可能返回reasoning字段
            
            # 如果还是没有内容，尝试构造一个基本的改进提示词
            if not content or len(str(content).strip()) < 50:
                # 基于智能体类型生成特化的改进版本
                content = self._generate_fallback_prompt(agent_type)
        else:
            content = str(llm_response)
        
        # 清理内容
        content = str(content).strip()
        
        # 移除可能的标记
        if content.startswith("```"):
            lines = content.split("\n")
            content = "\n".join(lines[1:-1]) if len(lines) > 2 else content
        
        return content
    
    def _calculate_quality_score(self, 
                               prompt: str, 
                               agent_type: str, 
                               found_keywords: List[str], 
                               required_keywords: List[str]) -> float:
        """计算质量分数"""
        score = 0.0
        
        # 长度分数 (0.3权重)
        length_score = min(1.0, len(prompt) / 1000)  # 1000字符为满分
        score += length_score * 0.3
        
        # 关键词分数 (0.4权重)
        if required_keywords:
            keyword_score = len(found_keywords) / len(required_keywords)
            score += keyword_score * 0.4
        else:
            score += 0.4  # 如果没有必需关键词，给满分
        
        # 结构分数 (0.3权重)
        structure_score = self._evaluate_prompt_structure(prompt)
        score += structure_score * 0.3
        
        return min(1.0, score)
    
    def _evaluate_prompt_structure(self, prompt: str) -> float:
        """评估提示词结构"""
        score = 0.0
        
        # 检查是否有明确的指令
        if any(word in prompt.lower() for word in ["请", "需要", "要求", "分析", "判断"]):
            score += 0.3
        
        # 检查是否有输出格式说明
        if any(word in prompt.lower() for word in ["格式", "输出", "返回", "结果"]):
            score += 0.3
        
        # 检查是否有上下文信息
        if any(word in prompt.lower() for word in ["基于", "根据", "考虑", "结合"]):
            score += 0.2
        
        # 检查是否有约束条件
        if any(word in prompt.lower() for word in ["不要", "避免", "确保", "必须"]):
            score += 0.2
        
        return min(1.0, score)
    
    def _generate_quality_recommendations(self, 
                                        prompt: str, 
                                        agent_type: str, 
                                        found_keywords: List[str], 
                                        required_keywords: List[str]) -> List[str]:
        """生成质量改进建议"""
        recommendations = []
        
        if len(prompt) < 200:
            recommendations.append("建议增加更详细的指令和上下文信息")
        
        if required_keywords and len(found_keywords) < len(required_keywords):
            missing = set(required_keywords) - set(found_keywords)
            recommendations.append(f"建议添加关键词: {', '.join(missing)}")
        
        if "输出" not in prompt and "返回" not in prompt:
            recommendations.append("建议明确指定输出格式要求")
        
        if "分析" not in prompt and agent_type in ["NAA", "TAA", "FAA"]:
            recommendations.append("建议强化分析指令")
        
        return recommendations
    
    def _estimate_improvement(self, 
                            original_score: float, 
                            improved_score: float, 
                            performance_issues: List[PerformanceIssue]) -> float:
        """估算改进效果"""
        # 基础改进 = 质量分数差异
        base_improvement = improved_score - original_score
        
        # 根据问题严重程度调整
        if performance_issues:
            avg_severity = sum(issue.severity for issue in performance_issues) / len(performance_issues)
            severity_bonus = avg_severity * 0.1  # 最多10%的额外改进
            base_improvement += severity_bonus
        
        return max(0.0, base_improvement)
    
    def _generate_fallback_prompt(self, agent_type: str) -> str:
        """生成回退提示词"""
        specialization = self.config["agent_specialization"].get(agent_type, {})
        keywords = specialization.get("keywords", [])
        focus = specialization.get("focus", "通用分析")
        
        # 构造包含必要关键词的提示词
        keyword_text = "、".join(keywords) if keywords else "相关因素"
        
        fallback_prompt = f"""
请进行专业的{focus}，重点关注{keyword_text}等关键要素。
基于提供的数据和信息，进行深入的分析和评估。
考虑多个维度的因素，包括历史趋势、当前状况和未来预期。
请提供准确、一致的分析结果，确保输出格式规范。
分析结果应包含明确的结论、支撑理由和相关建议。
请保持客观性和专业性，避免主观臆断。
""".strip()
        
        return fallback_prompt
    
    def _record_optimization(self, result: Dict[str, Any]) -> None:
        """记录优化历史"""
        try:
            record = {
                "timestamp": datetime.now().isoformat(),
                "agent_name": result.get("agent_name"),
                "success": result.get("success", False),
                "optimization_applied": result.get("optimization_applied", False),
                "estimated_improvement": result.get("estimated_improvement", 0.0),
                "execution_time": result.get("execution_time", 0.0)
            }
            
            self._optimization_history.append(record)
            
            # 限制历史记录数量
            if len(self._optimization_history) > 1000:
                self._optimization_history = self._optimization_history[-500:]
                
        except Exception as e:
            self.logger.error(f"记录优化历史失败: {e}")
    
    def get_optimization_stats(self) -> Dict[str, Any]:
        """获取优化统计信息"""
        if not self._optimization_history:
            return {
                "total_optimizations": 0,
                "successful_optimizations": 0,
                "applied_optimizations": 0,
                "success_rate": 0.0,
                "application_rate": 0.0,
                "average_improvement": 0.0
            }
        
        total = len(self._optimization_history)
        successful = sum(1 for r in self._optimization_history if r.get("success", False))
        applied = sum(1 for r in self._optimization_history if r.get("optimization_applied", False))
        
        improvements = [r.get("estimated_improvement", 0.0) 
                       for r in self._optimization_history 
                       if r.get("optimization_applied", False)]
        
        return {
            "total_optimizations": total,
            "successful_optimizations": successful,
            "applied_optimizations": applied,
            "success_rate": (successful / total) * 100 if total > 0 else 0.0,
            "application_rate": (applied / total) * 100 if total > 0 else 0.0,
            "average_improvement": sum(improvements) / len(improvements) if improvements else 0.0,
            "last_optimization": self._optimization_history[-1] if self._optimization_history else None
        }