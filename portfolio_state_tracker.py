"""
日常投资组合状态跟踪器 (Daily Portfolio State Tracker)

核心功能:
- 记录每日投资组合完整状态 (现金、持仓、净值、收益率)
- 支持跨周状态连续性传递
- 提供数据分析和可视化支持
- 轻量级内存存储 + 可选文件持久化

设计原则:
- 高内聚: 专注投资组合状态管理单一职责
- 低耦合: 通过配置机制与交易环境解耦
- 最小侵入: 现有代码修改最小化
"""

from typing import Dict, Any, Optional, List
from datetime import datetime, date
import json
import copy
from pathlib import Path


class PortfolioStateTracker:
    """轻量级日常投资组合状态跟踪器"""
    
    def __init__(self):
        """
        初始化跟踪器 - 纯内存存储模式
        """
        # 核心数据结构: {date_str: {cash, positions, position_values, net_worth, daily_return}}
        self._daily_records: Dict[str, Dict[str, Any]] = {}
        self._current_week = 1
        self._simulation_start_date: Optional[str] = None
    
    def add_daily_record(self, 
                        date_str: str,
                        net_worth: float,
                        daily_return: float = 0.0,
                        cumulative_return: float = 0.0,
                        weekly_return: float = 0.0,
                        last_week_return: float = 0.0,
                        coalition_info: Optional[Dict[str, Any]] = None,
                        positions: Optional[Dict[str, int]] = None,
                        position_values: Optional[Dict[str, float]] = None) -> None:
        """
        添加日常投资组合记录（支持收益率跟踪）
        
        Args:
            date_str: 交易日期 (YYYY-MM-DD格式)
            net_worth: 投资组合总净值
            daily_return: 当日收益率
            cumulative_return: 累计收益率
            weekly_return: 当前周收益率
            last_week_return: 上周收益率
            coalition_info: 联盟信息，包含联盟ID和大小等
            positions: 持仓股份数量字典
            position_values: 持仓价值字典
        """
        # 设置模拟开始日期
        if self._simulation_start_date is None:
            self._simulation_start_date = date_str
        
        # 检查是否为完整联盟记录 - 只保存完整联盟的数据用于跨周状态传递
        if coalition_info is not None:
            coalition_size = coalition_info.get("coalition_size", 0)
            
            # 完整联盟的判断：包含所有7个智能体
            is_complete_coalition = coalition_size == 7
            
            if not is_complete_coalition:
                # 子集联盟记录不保存到状态跟踪器，避免污染跨周状态传递
                print(f"🔹 跳过子集联盟记录: 大小={coalition_size}, 日期={date_str}")
                return
            else:
                print(f"💾 保存完整联盟记录: 日期={date_str}, 累计收益={cumulative_return:.6f}")
        else:
            print(f"💾 保存记录(无联盟信息): 日期={date_str}, 累计收益={cumulative_return:.6f}")
        
        # 深拷贝避免引用问题
        record = {
            "date": date_str,
            "net_worth": float(net_worth),
            "daily_return": float(daily_return),
            # 新增收益率跟踪字段
            "cumulative_return": float(cumulative_return),
            "weekly_return": float(weekly_return),
            "last_week_return": float(last_week_return),
            "week_number": self._current_week,
            "timestamp": datetime.now().isoformat(),
            # 新增持仓信息字段
            "positions": copy.deepcopy(positions) if positions else {},
            "position_values": copy.deepcopy(position_values) if position_values else {}
        }
        
        self._daily_records[date_str] = record
        
        # 验证保存的累计收益率
        if cumulative_return != 0.0:
            print(f"✅ 累计收益率验证成功: {cumulative_return:.6f} 已保存到 {date_str}")
        
        
        # 记录添加完成，无需打印调试信息
    
    def get_latest_state(self) -> Optional[Dict[str, Any]]:
        """
        获取最新的投资组合状态
        
        Returns:
            最新的投资组合状态字典，如果无记录则返回None
        """
        if not self._daily_records:
            return None
        
        # 按日期排序获取最新记录
        latest_date = max(self._daily_records.keys())
        latest_record = self._daily_records[latest_date]
        
        # 返回StockTradingEnv.reset()所需的状态格式（包含收益率和持仓信息）
        state = {
            "net_worth": latest_record["net_worth"],
            # 收益率状态字段
            "cumulative_return": latest_record.get("cumulative_return", 0.0),
            "weekly_return": latest_record.get("weekly_return", 0.0),
            "last_week_return": latest_record.get("last_week_return", 0.0),
            # 持仓状态字段
            "positions": latest_record.get("positions", {}),
            "position_values": latest_record.get("position_values", {}),
            "last_date": latest_date,
            "week_number": latest_record["week_number"]
        }
        
        # 如果当前记录的周数小于跟踪器的当前周数，说明需要进行周收益率重置
        if latest_record["week_number"] < self._current_week:
            # 将上一周的周收益率作为last_week_return，重置当前周收益率
            state["last_week_return"] = state["weekly_return"]
            state["weekly_return"] = 0.0
            state["week_number"] = self._current_week
        
        return state
    
    def get_clean_state_for_new_phase(self, current_week_number: int) -> Optional[Dict[str, Any]]:
        """
        获取用于新阶段的干净状态，确保周收益率在新周或新阶段开始时重置。
        此方法解决了阶段间收益率污染问题。

        Args:
            current_week_number: 当前运行的周数

        Returns:
            干净的状态字典，适用于新阶段或新周的开始
        """
        print(f"🔍 获取新阶段状态: 当前周={current_week_number}, 记录数量={len(self._daily_records)}")
        
        if not self._daily_records:
            print("⚠️ 没有历史记录，从零开始")
            return None  # 没有历史记录，从零开始

        latest_state = self.get_latest_state()
        if not latest_state:
            print("⚠️ 无法获取最新状态")
            return None

        clean_state = latest_state.copy()
        latest_week_in_tracker = latest_state.get("week_number", 1)
        latest_cum_return = latest_state.get("cumulative_return", 0.0)
        
        print(f"🔍 最新状态: 周={latest_week_in_tracker}, 累计收益={latest_cum_return:.6f}")

        # 案例一: 开始一个新周 (例如, 跟踪器记录的最新是第1周, 当前要运行第2周)
        if latest_week_in_tracker < current_week_number:
            # 上一周的 `weekly_return` 成为新周的 `last_week_return`
            clean_state["last_week_return"] = latest_state.get("weekly_return", 0.0)
            # 新周的 `weekly_return` 必须重置为 0
            clean_state["weekly_return"] = 0.0
            print(f"✅ 新周状态: 累计收益={clean_state.get('cumulative_return', 0.0):.6f}, 周收益=0.0 (重置)")

        # 案例二: 在同一周内开始一个新阶段 (例如, 完整联盟跑完后，子集联盟开始)
        elif latest_week_in_tracker == current_week_number:
            # 重要修正：同一周内的子集联盟不应该继承完整联盟的状态！
            # 所有联盟（完整或子集）都应该从上一周的结束状态开始
            if current_week_number > 1:
                # 查找上一周的记录，作为基础状态
                previous_week_records = [
                    record for record in self._daily_records.values()
                    if record.get("week_number", 1) == current_week_number - 1
                ]
                
                if previous_week_records:
                    # 使用上一周的最后一条记录作为基础
                    last_previous_week_record = max(previous_week_records, key=lambda x: x["date"])
                    clean_state["cumulative_return"] = last_previous_week_record.get("cumulative_return", 0.0)
                    clean_state["weekly_return"] = 0.0  # 新周从0开始
                    clean_state["last_week_return"] = last_previous_week_record.get("weekly_return", 0.0)
                    print(f"✅ 同周修正状态(找到上周记录): 累计收益={clean_state['cumulative_return']:.6f}, 周收益=0.0")
                else:
                    # 如果找不到上一周状态，使用latest_state但重置周收益率
                    # 这种情况下，latest_state就是当前周的前序状态
                    if latest_week_in_tracker == current_week_number:
                        # 如果当前是第2周但没有第1周记录，可能是因为第1周结束状态没有正确保存
                        # 这时候应该继承累计收益率，但重置周收益率
                        clean_state["weekly_return"] = 0.0
                        clean_state["last_week_return"] = 0.0
                        print(f"✅ 同周修正状态(无上周记录): 累计收益={clean_state.get('cumulative_return', 0.0):.6f}, 周收益=0.0")
                    else:
                        clean_state["cumulative_return"] = 0.0
                        clean_state["weekly_return"] = 0.0
                        clean_state["last_week_return"] = 0.0
                        print(f"✅ 同周修正状态(从0开始): 累计收益=0.0, 周收益=0.0")
            else:
                # 第一周：所有联盟都从0开始
                clean_state["cumulative_return"] = 0.0
                clean_state["weekly_return"] = 0.0
                clean_state["last_week_return"] = 0.0
                print(f"✅ 第一周状态: 累计收益=0.0, 周收益=0.0 (所有联盟都从0开始)")

        # 案例三: 异常情况 (例如, 跟踪器记录的周数大于当前运行的周数), 按原样返回
        else:
            print(f"⚠️ 异常情况: 跟踪器周数({latest_week_in_tracker}) > 当前周数({current_week_number})")

        return clean_state

    def get_week_end_return_state(self, week_number: int) -> Optional[Dict[str, Any]]:
        """
        获取指定周末的收益率状态，用于跨周传递
        
        Args:
            week_number: 周数
            
        Returns:
            周末收益率状态字典，如果未找到则返回None
        """
        # 查找指定周的最后一个交易日记录
        week_records = [record for record in self._daily_records.values() 
                       if record["week_number"] == week_number]
        
        if not week_records:
            return None
        
        # 获取该周最后一个交易日的记录
        last_record = max(week_records, key=lambda x: x["date"])
        
        return {
            "cumulative_return": last_record.get("cumulative_return", 0.0),
            "weekly_return": last_record.get("weekly_return", 0.0),
            "last_week_return": last_record.get("weekly_return", 0.0),  # 本周的周收益率作为下周的上周收益
            "net_worth": last_record["net_worth"]
        }
    
    def start_new_week(self, week_number: int) -> None:
        """
        开始新的一周（处理周收益率重置）
        
        Args:
            week_number: 新的周数
        """
        print(f"📅 开始新的一周: 第{week_number}周 (之前是第{self._current_week}周)")
        
        # 验证累计收益率在周转换时的连续性
        if week_number > 1:
            latest_state = self.get_latest_state()
            if latest_state:
                cum_return = latest_state.get("cumulative_return", 0.0)
                print(f"🔍 周转换时累计收益率: {cum_return:.6f}")
            else:
                print("⚠️ 周转换时无法获取最新状态")
        
        self._current_week = week_number
    
    def get_week_summary(self, week_number: int) -> Dict[str, Any]:
        """
        获取指定周的投资组合摘要
        
        Args:
            week_number: 周数
            
        Returns:
            周摘要数据
        """
        week_records = {
            date_str: record for date_str, record in self._daily_records.items()
            if record.get("week_number") == week_number
        }
        
        if not week_records:
            return {"week_number": week_number, "records_count": 0}
        
        dates = sorted(week_records.keys())
        start_record = week_records[dates[0]]
        end_record = week_records[dates[-1]]
        
        week_return = (end_record["net_worth"] - start_record["net_worth"]) / start_record["net_worth"]
        
        return {
            "week_number": week_number,
            "start_date": dates[0],
            "end_date": dates[-1],
            "records_count": len(week_records),
            "start_net_worth": start_record["net_worth"],
            "end_net_worth": end_record["net_worth"],
            "week_return": week_return
        }
    
    def get_all_records(self) -> List[Dict[str, Any]]:
        """
        获取所有日常记录，按日期排序
        
        Returns:
            按日期排序的所有记录列表
        """
        return [self._daily_records[date_str] for date_str in sorted(self._daily_records.keys())]
    
    def get_plotting_data(self) -> Dict[str, List]:
        """
        获取用于绘图的数据格式
        
        Returns:
            包含dates, net_worth, daily_returns等列表的字典
        """
        if not self._daily_records:
            return {"dates": [], "net_worth": [], "daily_returns": []}
        
        sorted_records = self.get_all_records()
        
        return {
            "dates": [record["date"] for record in sorted_records],
            "net_worth": [record["net_worth"] for record in sorted_records],
            "daily_returns": [record["daily_return"] for record in sorted_records],
            "week_numbers": [record.get("week_number", 1) for record in sorted_records]
        }
    
    def get_total_return(self) -> float:
        """
        计算总收益率
        
        Returns:
            从开始到最新的总收益率
        """
        if len(self._daily_records) < 2:
            return 0.0
        
        records = self.get_all_records()
        start_net_worth = records[0]["net_worth"]
        end_net_worth = records[-1]["net_worth"]
        
        return (end_net_worth - start_net_worth) / start_net_worth
    
    
    
    def clear_data(self) -> None:
        """清除所有数据（主要用于测试）"""
        self._daily_records.clear()
        self._current_week = 1
        self._simulation_start_date = None
    
    def __len__(self) -> int:
        """返回记录数量"""
        return len(self._daily_records)
    

    def __str__(self) -> str:
        """字符串表示"""
        total_return = self.get_total_return()
        return (f"PortfolioStateTracker("
                f"records={len(self._daily_records)}, "
                f"weeks={self._current_week}, "
                f"total_return={total_return:.4f})")