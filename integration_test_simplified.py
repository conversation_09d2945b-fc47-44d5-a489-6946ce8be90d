#!/usr/bin/env python3
"""
简化后的集成测试

验证状态继承修复在真实环境中的工作情况
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_trading_env import StockTradingEnv
from portfolio_state_tracker import PortfolioStateTracker

def test_realistic_scenario():
    """测试真实场景下的状态继承"""
    
    print("🧪 真实场景集成测试")
    print("=" * 50)
    
    # 创建跟踪器
    tracker = PortfolioStateTracker()
    
    # 第1周：完整集合交易
    print("\n📅 第1周完整集合交易")
    
    week1_config = {
        "start_date": "2025-01-01",
        "end_date": "2025-01-05",
        "starting_cash": 1000000,
        "stocks": ["AAPL"],
        "portfolio_tracker": tracker,
        "coalition_size": 7,
        "current_week_number": 1
    }
    
    env1 = StockTradingEnv(week1_config)
    
    # 模拟第1周的交易记录
    tracker.add_daily_record(
        "2025-01-03", 1050000, 0.05, 0.05, 0.05, 0.0,
        {"coalition_size": 7},
        {"AAPL": 100}, {"AAPL": 50000}
    )
    
    print(f"第1周结束: 累计收益率5%, 净值${env1.starting_cash * 1.05:,.0f}")
    
    # 第2周：完整集合继承状态
    print("\n📅 第2周完整集合继承")
    tracker.start_new_week(2)
    
    week2_config = {
        "start_date": "2025-01-06",
        "end_date": "2025-01-10", 
        "starting_cash": 1000000,
        "stocks": ["AAPL"],
        "portfolio_tracker": tracker,
        "coalition_size": 7,
        "current_week_number": 2
    }
    
    env2 = StockTradingEnv(week2_config)
    
    # 验证继承正确性
    inherited_correctly = (
        abs(env2.cumulative_return - 0.05) < 0.001 and
        abs(env2.weekly_return - 0.0) < 0.001 and
        env2.positions.get("AAPL", 0) == 100
    )
    
    print(f"继承验证: {'✅ 成功' if inherited_correctly else '❌ 失败'}")
    print(f"  累计收益率: {env2.cumulative_return:.3f}")  
    print(f"  周收益率: {env2.weekly_return:.3f}")
    print(f"  AAPL持仓: {env2.positions.get('AAPL', 0)}股")
    
    # 第2周：子集联盟继承相同状态
    print("\n📅 第2周子集联盟继承")
    
    subset_config = {
        "start_date": "2025-01-06",
        "end_date": "2025-01-10",
        "starting_cash": 1000000,
        "stocks": ["AAPL"],
        "portfolio_tracker": tracker,
        "coalition_size": 3,
        "current_week_number": 2
    }
    
    env_subset = StockTradingEnv(subset_config)
    
    # 验证子集继承
    subset_inherited_correctly = (
        abs(env_subset.cumulative_return - 0.05) < 0.001 and
        abs(env_subset.weekly_return - 0.0) < 0.001 and
        env_subset.positions.get("AAPL", 0) == 100
    )
    
    print(f"子集继承验证: {'✅ 成功' if subset_inherited_correctly else '❌ 失败'}")
    
    # 总结
    print("\n🎯 集成测试结果:")
    all_tests_passed = inherited_correctly and subset_inherited_correctly
    
    if all_tests_passed:
        print("🎉 所有测试通过！跨周状态继承正常工作")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    test_realistic_scenario()