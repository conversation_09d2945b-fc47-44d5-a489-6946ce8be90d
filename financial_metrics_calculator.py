#!/usr/bin/env python3
"""
财务指标计算器 - 基于每日收益计算年化收益率、年化波动率、年化夏普比率和最大回撤
"""

import json
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple
from dataclasses import dataclass
import argparse


@dataclass
class FinancialMetrics:
    """财务指标数据类"""
    symbol: str
    annualized_return: float
    annualized_volatility: float
    annualized_sharpe_ratio: float
    max_drawdown: float
    total_return: float
    total_days: int
    win_rate: float
    avg_daily_return: float
    worst_day: float
    best_day: float


class FinancialMetricsCalculator:
    """财务指标计算器"""
    
    def __init__(self, results_dir: str = "daily_analysis_results", risk_free_rate: float = 0.02):
        self.results_dir = Path(results_dir)
        self.risk_free_rate = risk_free_rate  # 无风险利率，默认2%
        self.trading_days_per_year = 252  # 年交易日数
        
    def load_daily_returns(self, symbol: str) -> List[float]:
        """加载某个股票的每日收益数据"""
        breakdown_file = self.results_dir / f"{symbol}_daily_breakdown.json"
        
        if not breakdown_file.exists():
            raise FileNotFoundError(f"找不到 {symbol} 的分析文件: {breakdown_file}")
        
        with open(breakdown_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        daily_returns = []
        weekly_breakdown = data['weekly_breakdown']
        
        # 按周数和日期顺序提取每日收益
        for week_num in sorted([int(k) for k in weekly_breakdown.keys()]):
            week_data = weekly_breakdown[str(week_num)]
            for day_data in week_data['daily_returns']:
                daily_returns.append(day_data['daily_return'])
        
        return daily_returns
    
    def calculate_annualized_return(self, daily_returns: List[float]) -> float:
        """计算年化收益率"""
        if not daily_returns:
            return 0.0
        
        # 计算累计收益率
        cumulative_return = 1.0
        for daily_return in daily_returns:
            cumulative_return *= (1 + daily_return)
        
        total_return = cumulative_return - 1
        days = len(daily_returns)
        
        if days == 0:
            return 0.0
            
        # 年化收益率 = (1 + 总收益率)^(252/天数) - 1
        annualized_return = (1 + total_return) ** (self.trading_days_per_year / days) - 1
        
        return annualized_return
    
    def calculate_annualized_volatility(self, daily_returns: List[float]) -> float:
        """计算年化波动率"""
        if len(daily_returns) < 2:
            return 0.0
        
        # 计算每日收益率的标准差
        daily_std = np.std(daily_returns, ddof=1)
        
        # 年化波动率 = 日波动率 * sqrt(252)
        annualized_volatility = daily_std * np.sqrt(self.trading_days_per_year)
        
        return annualized_volatility
    
    def calculate_annualized_sharpe_ratio(self, annualized_return: float, 
                                        annualized_volatility: float) -> float:
        """计算年化夏普比率"""
        if annualized_volatility == 0:
            return 0.0
        
        # 夏普比率 = (年化收益率 - 无风险利率) / 年化波动率
        sharpe_ratio = (annualized_return - self.risk_free_rate) / annualized_volatility
        
        return sharpe_ratio
    
    def calculate_max_drawdown(self, daily_returns: List[float]) -> float:
        """计算最大回撤"""
        if not daily_returns:
            return 0.0
        
        # 计算累计净值序列
        cumulative_values = [1.0]  # 初始净值为1
        for daily_return in daily_returns:
            cumulative_values.append(cumulative_values[-1] * (1 + daily_return))
        
        # 计算每个时点的最大回撤
        max_drawdowns = []
        peak = cumulative_values[0]
        
        for value in cumulative_values:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            max_drawdowns.append(drawdown)
        
        return max(max_drawdowns) if max_drawdowns else 0.0
    
    def calculate_additional_metrics(self, daily_returns: List[float]) -> Dict:
        """计算额外的风险指标"""
        if not daily_returns:
            return {
                'win_rate': 0.0,
                'avg_daily_return': 0.0,
                'worst_day': 0.0,
                'best_day': 0.0,
                'total_return': 0.0
            }
        
        returns_array = np.array(daily_returns)
        
        # 胜率（正收益天数占比）
        win_rate = np.sum(returns_array > 0) / len(returns_array)
        
        # 平均日收益
        avg_daily_return = np.mean(returns_array)
        
        # 最差和最好单日收益
        worst_day = np.min(returns_array)
        best_day = np.max(returns_array)
        
        # 总收益率
        total_return = np.prod(1 + returns_array) - 1
        
        return {
            'win_rate': win_rate,
            'avg_daily_return': avg_daily_return,
            'worst_day': worst_day,
            'best_day': best_day,
            'total_return': total_return
        }
    
    def calculate_metrics_for_symbol(self, symbol: str) -> FinancialMetrics:
        """计算单个股票的所有财务指标"""
        
        print(f"正在计算 {symbol} 的财务指标...")
        
        # 加载每日收益数据
        daily_returns = self.load_daily_returns(symbol)
        
        if not daily_returns:
            print(f"⚠️  {symbol} 没有有效的每日收益数据")
            return FinancialMetrics(
                symbol=symbol,
                annualized_return=0.0,
                annualized_volatility=0.0,
                annualized_sharpe_ratio=0.0,
                max_drawdown=0.0,
                total_return=0.0,
                total_days=0,
                win_rate=0.0,
                avg_daily_return=0.0,
                worst_day=0.0,
                best_day=0.0
            )
        
        # 计算核心指标
        annualized_return = self.calculate_annualized_return(daily_returns)
        annualized_volatility = self.calculate_annualized_volatility(daily_returns)
        annualized_sharpe_ratio = self.calculate_annualized_sharpe_ratio(
            annualized_return, annualized_volatility
        )
        max_drawdown = self.calculate_max_drawdown(daily_returns)
        
        # 计算额外指标
        additional_metrics = self.calculate_additional_metrics(daily_returns)
        
        return FinancialMetrics(
            symbol=symbol,
            annualized_return=annualized_return,
            annualized_volatility=annualized_volatility,
            annualized_sharpe_ratio=annualized_sharpe_ratio,
            max_drawdown=max_drawdown,
            total_return=additional_metrics['total_return'],
            total_days=len(daily_returns),
            win_rate=additional_metrics['win_rate'],
            avg_daily_return=additional_metrics['avg_daily_return'],
            worst_day=additional_metrics['worst_day'],
            best_day=additional_metrics['best_day']
        )
    
    def calculate_all_metrics(self) -> Dict[str, FinancialMetrics]:
        """计算所有股票的财务指标"""
        
        # 从汇总文件中获取股票列表
        summary_file = self.results_dir / "batch_summary.json"
        if not summary_file.exists():
            raise FileNotFoundError(f"找不到汇总文件: {summary_file}")
        
        with open(summary_file, 'r', encoding='utf-8') as f:
            summary_data = json.load(f)
        
        symbols = summary_data['symbols']
        
        print("=" * 60)
        print("📊 财务指标计算开始")
        print("=" * 60)
        
        results = {}
        
        for symbol in symbols:
            try:
                metrics = self.calculate_metrics_for_symbol(symbol)
                results[symbol] = metrics
                print(f"✅ {symbol} 计算完成")
            except Exception as e:
                print(f"❌ 计算 {symbol} 时发生错误: {e}")
        
        return results
    
    def generate_comparison_report(self, metrics_dict: Dict[str, FinancialMetrics]) -> None:
        """生成对比分析报告"""
        
        print("\n" + "=" * 80)
        print("📈 财务指标对比分析报告")
        print("=" * 80)
        
        # 创建表格数据
        table_data = []
        for symbol, metrics in metrics_dict.items():
            table_data.append([
                symbol,
                f"{metrics.annualized_return:.2%}",
                f"{metrics.annualized_volatility:.2%}",
                f"{metrics.annualized_sharpe_ratio:.3f}",
                f"{metrics.max_drawdown:.2%}",
                f"{metrics.total_return:.2%}",
                f"{metrics.win_rate:.1%}",
                metrics.total_days
            ])
        
        # 打印表格头
        headers = ["股票", "年化收益", "年化波动", "夏普比率", "最大回撤", "总收益", "胜率", "天数"]
        print(f"{'':^6} {'':^10} {'':^10} {'':^10} {'':^10} {'':^10} {'':^8} {'':^6}")
        print(f"{headers[0]:^6} {headers[1]:^10} {headers[2]:^10} {headers[3]:^10} "
              f"{headers[4]:^10} {headers[5]:^10} {headers[6]:^8} {headers[7]:^6}")
        print("-" * 80)
        
        # 打印数据行
        for row in table_data:
            print(f"{row[0]:^6} {row[1]:^10} {row[2]:^10} {row[3]:^10} "
                  f"{row[4]:^10} {row[5]:^10} {row[6]:^8} {row[7]:^6}")
        
        print("-" * 80)
        
        # 详细分析
        print("\n📋 详细指标分析:")
        print("-" * 60)
        
        for symbol, metrics in metrics_dict.items():
            print(f"\n🔍 {symbol} 详细指标:")
            print(f"  年化收益率: {metrics.annualized_return:.4f} ({metrics.annualized_return:.2%})")
            print(f"  年化波动率: {metrics.annualized_volatility:.4f} ({metrics.annualized_volatility:.2%})")
            print(f"  年化夏普比率: {metrics.annualized_sharpe_ratio:.4f}")
            print(f"  最大回撤: {metrics.max_drawdown:.4f} ({metrics.max_drawdown:.2%})")
            print(f"  总收益率: {metrics.total_return:.4f} ({metrics.total_return:.2%})")
            print(f"  平均日收益: {metrics.avg_daily_return:.6f} ({metrics.avg_daily_return:.4%})")
            print(f"  胜率: {metrics.win_rate:.3f} ({metrics.win_rate:.1%})")
            print(f"  最好单日: {metrics.best_day:.4f} ({metrics.best_day:.2%})")
            print(f"  最差单日: {metrics.worst_day:.4f} ({metrics.worst_day:.2%})")
            print(f"  交易天数: {metrics.total_days}")
        
        # 排名分析
        print(f"\n🏆 各指标排名:")
        print("-" * 40)
        
        # 按年化收益率排序
        sorted_by_return = sorted(metrics_dict.items(), 
                                key=lambda x: x[1].annualized_return, reverse=True)
        print("年化收益率排名:")
        for i, (symbol, metrics) in enumerate(sorted_by_return, 1):
            print(f"  {i}. {symbol}: {metrics.annualized_return:.2%}")
        
        # 按夏普比率排序
        sorted_by_sharpe = sorted(metrics_dict.items(), 
                                key=lambda x: x[1].annualized_sharpe_ratio, reverse=True)
        print("\n夏普比率排名:")
        for i, (symbol, metrics) in enumerate(sorted_by_sharpe, 1):
            print(f"  {i}. {symbol}: {metrics.annualized_sharpe_ratio:.3f}")
        
        # 按最大回撤排序（越小越好）
        sorted_by_drawdown = sorted(metrics_dict.items(), 
                                  key=lambda x: x[1].max_drawdown)
        print("\n风险控制排名（最大回撤最小）:")
        for i, (symbol, metrics) in enumerate(sorted_by_drawdown, 1):
            print(f"  {i}. {symbol}: {metrics.max_drawdown:.2%}")
    
    def save_results(self, metrics_dict: Dict[str, FinancialMetrics]) -> None:
        """保存计算结果到JSON文件"""
        
        # 转换为可序列化的字典
        results_data = {}
        for symbol, metrics in metrics_dict.items():
            results_data[symbol] = {
                'symbol': metrics.symbol,
                'annualized_return': metrics.annualized_return,
                'annualized_volatility': metrics.annualized_volatility,
                'annualized_sharpe_ratio': metrics.annualized_sharpe_ratio,
                'max_drawdown': metrics.max_drawdown,
                'total_return': metrics.total_return,
                'total_days': metrics.total_days,
                'win_rate': metrics.win_rate,
                'avg_daily_return': metrics.avg_daily_return,
                'worst_day': metrics.worst_day,
                'best_day': metrics.best_day
            }
        
        # 添加元数据
        output_data = {
            'calculation_params': {
                'risk_free_rate': self.risk_free_rate,
                'trading_days_per_year': self.trading_days_per_year
            },
            'metrics': results_data
        }
        
        # 保存到文件
        output_file = self.results_dir / "financial_metrics.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(output_data, f, ensure_ascii=False, indent=2)
        
        print(f"\n💾 财务指标结果已保存到: {output_file}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='计算股票的财务指标')
    parser.add_argument('-d', '--dir', default='daily_analysis_results',
                       help='每日分析结果目录')
    parser.add_argument('-r', '--risk-free-rate', type=float, default=0.02,
                       help='无风险利率 (默认: 0.02)')
    parser.add_argument('--save', action='store_true',
                       help='保存结果到JSON文件')
    
    args = parser.parse_args()
    
    try:
        calculator = FinancialMetricsCalculator(
            results_dir=args.dir,
            risk_free_rate=args.risk_free_rate
        )
        
        # 计算所有股票的财务指标
        metrics_dict = calculator.calculate_all_metrics()
        
        # 生成对比报告
        calculator.generate_comparison_report(metrics_dict)
        
        # 保存结果
        if args.save or True:  # 默认总是保存
            calculator.save_results(metrics_dict)
        
        print("\n" + "=" * 60)
        print("✅ 财务指标计算完成")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 计算过程中发生错误: {e}")


if __name__ == "__main__":
    main()