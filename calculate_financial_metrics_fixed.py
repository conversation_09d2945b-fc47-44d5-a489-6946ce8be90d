#!/usr/bin/env python3
"""
基于提取的JSON文件计算金融指标（修复版）
- 年化收益率
- 年化夏普率
- 年化波动率
- 最大回撤
"""

import json
import os
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FinancialMetricsCalculator:
    def __init__(self, test_results_dir: str, risk_free_rate: float = 0.02):
        self.test_results_dir = Path(test_results_dir)
        self.risk_free_rate = risk_free_rate  # 年化无风险利率
        
    def load_stock_data(self, stock_symbol: str) -> List[Dict]:
        """加载股票的TRA输入数据"""
        json_file = self.test_results_dir / f"{stock_symbol}_tra_inputs_extracted.json"
        
        if not json_file.exists():
            logger.warning(f"未找到 {stock_symbol} 的数据文件: {json_file}")
            return []
        
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            # 转换数值类型并过滤有效数据
            valid_data = []
            for item in data:
                try:
                    cum_ret = float(item['cumulative_return'])
                    week_ret = float(item['weekly_return'])
                    
                    item['cumulative_return'] = cum_ret
                    item['weekly_return'] = week_ret
                    valid_data.append(item)
                except (ValueError, TypeError, KeyError):
                    logger.warning(f"跳过无效数据行: {item}")
                    continue
                
            logger.info(f"加载 {stock_symbol} 数据: {len(valid_data)} 条有效记录")
            return valid_data
            
        except Exception as e:
            logger.error(f"加载 {stock_symbol} 数据时出错: {e}")
            return []
    
    def find_longest_continuous_segment(self, data: List[Dict]) -> List[Dict]:
        """找到最长的连续交易段（处理重置）"""
        if not data:
            return []
        
        # 按日期排序
        sorted_data = sorted(data, key=lambda x: x['date'])
        
        segments = []
        current_segment = []
        
        for i, item in enumerate(sorted_data):
            curr_cum = item['cumulative_return']
            
            if i == 0:
                current_segment = [item]
            else:
                prev_cum = sorted_data[i-1]['cumulative_return']
                
                # 检测重置：当前累计收益为0且前一个不为0，或大幅下跌
                is_reset = (curr_cum == 0.0 and prev_cum != 0.0) or \
                          (curr_cum < prev_cum and abs(curr_cum - prev_cum) > 0.2)
                
                if is_reset:
                    # 结束当前段，开始新段（降低最小段长度要求）
                    if len(current_segment) >= 3:  # 至少3个交易日
                        segments.append(current_segment)
                    current_segment = [item]
                else:
                    current_segment.append(item)
        
        # 添加最后一段
        if len(current_segment) >= 3:
            segments.append(current_segment)
        
        if not segments:
            logger.warning("没有找到有效的连续段")
            return []
        
        # 返回最长的段
        longest_segment = max(segments, key=len)
        logger.info(f"找到 {len(segments)} 个连续段，选择最长的 {len(longest_segment)} 天")
        
        return longest_segment
    
    def calculate_daily_returns(self, data: List[Dict]) -> np.ndarray:
        """从累计收益率计算每日收益率"""
        if not data:
            return np.array([])
        
        # 获取最长连续段
        segment = self.find_longest_continuous_segment(data)
        if not segment:
            # 如果没有找到合适的段，尝试使用所有数据
            logger.warning("未找到连续段，尝试使用全部数据")
            segment = data
        
        if len(segment) < 2:
            logger.warning("数据点不足，无法计算日收益率")
            return np.array([])
        
        cumulative_returns = np.array([item['cumulative_return'] for item in segment])
        daily_returns = []
        
        for i in range(len(cumulative_returns)):
            if i == 0:
                # 第一天的收益率等于累计收益率
                daily_return = cumulative_returns[i]
            else:
                # 计算日收益率: (1 + R_t) / (1 + R_{t-1}) - 1
                prev_cum = cumulative_returns[i-1]
                curr_cum = cumulative_returns[i]
                
                if prev_cum == -1.0:  # 避免除零
                    daily_return = 0.0
                else:
                    daily_return = (1 + curr_cum) / (1 + prev_cum) - 1
            
            daily_returns.append(daily_return)
        
        return np.array(daily_returns)
    
    def calculate_annualized_return(self, daily_returns: np.ndarray) -> float:
        """计算年化收益率"""
        if len(daily_returns) == 0:
            return 0.0
        
        # 计算总收益率
        total_return = np.prod(1 + daily_returns) - 1
        
        # 年化（假设252个交易日）
        num_days = len(daily_returns)
        if num_days == 0:
            return 0.0
        
        annualized_return = (1 + total_return) ** (252 / num_days) - 1
        return annualized_return
    
    def calculate_annualized_volatility(self, daily_returns: np.ndarray) -> float:
        """计算年化波动率"""
        if len(daily_returns) <= 1:
            return 0.0
        
        daily_volatility = np.std(daily_returns, ddof=1)
        annualized_volatility = daily_volatility * np.sqrt(252)
        return annualized_volatility
    
    def calculate_annualized_sharpe_ratio(self, daily_returns: np.ndarray) -> float:
        """计算年化夏普率"""
        if len(daily_returns) <= 1:
            return 0.0
        
        # 计算年化收益率和波动率
        annualized_return = self.calculate_annualized_return(daily_returns)
        annualized_volatility = self.calculate_annualized_volatility(daily_returns)
        
        if annualized_volatility == 0:
            return 0.0
        
        # 夏普率 = (年化收益率 - 无风险利率) / 年化波动率
        sharpe_ratio = (annualized_return - self.risk_free_rate) / annualized_volatility
        return sharpe_ratio
    
    def calculate_maximum_drawdown(self, daily_returns: np.ndarray) -> Tuple[float, int, int]:
        """计算最大回撤"""
        if len(daily_returns) == 0:
            return 0.0, 0, 0
        
        # 计算累计净值曲线
        cumulative_returns = np.cumprod(1 + daily_returns)
        
        # 计算历史最高点
        running_max = np.maximum.accumulate(cumulative_returns)
        
        # 计算每日回撤
        drawdown = (cumulative_returns - running_max) / running_max
        
        # 找到最大回撤
        max_drawdown_idx = np.argmin(drawdown)
        max_drawdown = abs(drawdown[max_drawdown_idx])
        
        # 找到最大回撤前的峰值点
        peak_idx = np.argmax(running_max[:max_drawdown_idx + 1])
        
        return max_drawdown, peak_idx, max_drawdown_idx
    
    def analyze_stock(self, stock_symbol: str) -> Dict:
        """分析单个股票的金融指标"""
        logger.info(f"分析 {stock_symbol}")
        
        # 加载数据
        data = self.load_stock_data(stock_symbol)
        if not data:
            return {}
        
        # 计算日收益率
        daily_returns = self.calculate_daily_returns(data)
        if len(daily_returns) == 0:
            logger.warning(f"{stock_symbol} 没有有效的日收益率数据")
            return {}
        
        # 计算各项指标
        annualized_return = self.calculate_annualized_return(daily_returns)
        annualized_volatility = self.calculate_annualized_volatility(daily_returns)
        annualized_sharpe = self.calculate_annualized_sharpe_ratio(daily_returns)
        max_drawdown, dd_start, dd_end = self.calculate_maximum_drawdown(daily_returns)
        
        # 其他统计指标
        total_return = np.prod(1 + daily_returns) - 1
        mean_daily_return = np.mean(daily_returns)
        win_rate = np.sum(daily_returns > 0) / len(daily_returns)
        
        results = {
            'stock_symbol': stock_symbol,
            'trading_days': len(daily_returns),
            'total_return': float(total_return),
            'annualized_return': float(annualized_return),
            'annualized_volatility': float(annualized_volatility),
            'annualized_sharpe_ratio': float(annualized_sharpe),
            'maximum_drawdown': float(max_drawdown),
            'drawdown_start_day': int(dd_start),
            'drawdown_end_day': int(dd_end),
            'mean_daily_return': float(mean_daily_return),
            'win_rate': float(win_rate),
            'risk_free_rate_used': self.risk_free_rate,
            'daily_returns': daily_returns.tolist()
        }
        
        logger.info(f"{stock_symbol} 分析完成: {len(daily_returns)} 个交易日")
        return results
    
    def analyze_all_stocks(self) -> Dict[str, Dict]:
        """分析所有股票"""
        results = {}
        
        # 查找所有提取的JSON文件
        json_files = list(self.test_results_dir.glob("*_tra_inputs_extracted.json"))
        
        if not json_files:
            logger.error("未找到任何提取的JSON文件")
            return results
        
        for json_file in json_files:
            stock_symbol = json_file.stem.replace('_tra_inputs_extracted', '')
            stock_results = self.analyze_stock(stock_symbol)
            
            if stock_results:
                results[stock_symbol] = stock_results
        
        return results
    
    def create_summary_report(self, all_results: Dict[str, Dict]) -> pd.DataFrame:
        """创建汇总报告"""
        summary_data = []
        
        for stock_symbol, results in all_results.items():
            summary_data.append({
                'Stock': stock_symbol,
                'Trading Days': results['trading_days'],
                'Total Return (%)': f"{results['total_return'] * 100:.2f}",
                'Annualized Return (%)': f"{results['annualized_return'] * 100:.2f}",
                'Annualized Volatility (%)': f"{results['annualized_volatility'] * 100:.2f}",
                'Annualized Sharpe Ratio': f"{results['annualized_sharpe_ratio']:.3f}",
                'Maximum Drawdown (%)': f"{results['maximum_drawdown'] * 100:.2f}",
                'Win Rate (%)': f"{results['win_rate'] * 100:.1f}",
                'Mean Daily Return (%)': f"{results['mean_daily_return'] * 100:.3f}"
            })
        
        return pd.DataFrame(summary_data)


def main():
    """主函数"""
    test_results_dir = "/Users/<USER>/Code/Multi_Agent_Optimization/test_results"
    output_dir = "/Users/<USER>/Code/Multi_Agent_Optimization/test_results/json_financial_analysis_fixed"
    
    logger.info("开始基于JSON文件计算金融指标（修复版）")
    logger.info("=" * 80)
    
    # 初始化计算器
    calculator = FinancialMetricsCalculator(test_results_dir, risk_free_rate=0.02)
    
    # 分析所有股票
    all_results = calculator.analyze_all_stocks()
    
    if not all_results:
        logger.error("没有成功分析任何股票")
        return
    
    logger.info(f"成功分析 {len(all_results)} 只股票")
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 生成汇总报告
    summary_df = calculator.create_summary_report(all_results)
    
    # 保存详细结果
    detailed_results_path = os.path.join(output_dir, "detailed_financial_metrics_fixed.json")
    with open(detailed_results_path, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    # 保存汇总CSV
    summary_csv_path = os.path.join(output_dir, "financial_metrics_summary_fixed.csv")
    summary_df.to_csv(summary_csv_path, index=False)
    
    # 打印汇总报告
    print("\n" + "=" * 100)
    print("基于JSON数据的金融指标分析汇总（修复版）")
    print("=" * 100)
    print(summary_df.to_string(index=False))
    print("\n" + "=" * 100)
    print(f"详细结果保存到: {detailed_results_path}")
    print(f"汇总CSV保存到: {summary_csv_path}")
    print("=" * 100)


if __name__ == "__main__":
    main()