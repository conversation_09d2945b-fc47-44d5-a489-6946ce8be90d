#!/usr/bin/env python3
"""
Daily Returns CSV Exporter
将股票每日收益率数据导出为CSV文件
"""
import json
import pandas as pd
import os
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
import re


class DailyReturnsCSVExporter:
    """每日收益率CSV导出器"""
    
    def __init__(self, data_file: str):
        """初始化，加载财务表现数据"""
        with open(data_file, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        
        # 提取段落文件夹路径
        self.sections_dir = "extracted_log_sections"
        
    def parse_daily_data_from_sections(self) -> Dict[str, List[Dict]]:
        """从段落文件中解析详细的每日数据"""
        import glob
        
        stock_daily_data = {}
        
        # 获取所有段落文件
        section_files = glob.glob(os.path.join(self.sections_dir, "*section_*.log"))
        section_files.sort()
        
        # 正则表达式模式
        return_pattern = re.compile(r'收益率=([+-]?\d+\.\d+), 净值=\$([0-9,]+\.\d+)')
        action_pattern = re.compile(r'交易动作: \{\'?([A-Z_]+)\'?: ([0-9.]+)\}')
        day_pattern = re.compile(r'第(\d+)天')
        date_pattern = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})')
        
        for file_path in section_files:
            filename = os.path.basename(file_path)
            # 提取基础文件名（去掉section部分）
            base_name = filename.split('_section_')[0]
            section_num = int(filename.split('_section_')[1].split('.')[0])
            
            if base_name not in stock_daily_data:
                stock_daily_data[base_name] = []
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    lines = content.strip().split('\n')
                
                current_stock = None
                week_num = section_num  # 段落编号即为周数
                
                for line in lines:
                    # 提取日期时间
                    date_match = date_pattern.search(line)
                    timestamp = date_match.group(1) if date_match else ""
                    
                    # 提取交易动作和股票
                    action_match = action_pattern.search(line)
                    if action_match:
                        current_stock = action_match.group(1)
                        if current_stock == '__HOLD__':
                            current_stock = 'CASH'
                    
                    # 提取收益率和净值
                    return_match = return_pattern.search(line)
                    day_match = day_pattern.search(line)
                    
                    if return_match and day_match:
                        day_in_week = int(day_match.group(1))
                        return_rate = float(return_match.group(1))
                        net_value_str = return_match.group(2).replace(',', '')
                        net_value = float(net_value_str)
                        
                        # 计算全局交易日（假设每周5个交易日）
                        global_day = (week_num - 1) * 5 + day_in_week
                        
                        daily_record = {
                            'week': week_num,
                            'day_in_week': day_in_week,
                            'global_day': global_day,
                            'timestamp': timestamp,
                            'return_rate': return_rate,
                            'net_value': net_value,
                            'stock_traded': current_stock or 'UNKNOWN',
                            'log_file': base_name
                        }
                        
                        stock_daily_data[base_name].append(daily_record)
            
            except Exception as e:
                print(f"解析文件 {file_path} 时出错: {e}")
        
        return stock_daily_data
    
    def create_individual_stock_csvs(self, daily_data: Dict[str, List[Dict]]):
        """为每个股票创建单独的CSV文件"""
        output_dir = "daily_returns_csv"
        os.makedirs(output_dir, exist_ok=True)
        
        for log_file, records in daily_data.items():
            if not records:
                continue
            
            # 按global_day排序确保数据按时间顺序
            records.sort(key=lambda x: x['global_day'])
            
            # 获取主要交易的股票
            stock_actions = [r['stock_traded'] for r in records if r['stock_traded'] != 'CASH']
            main_stock = max(set(stock_actions), key=stock_actions.count) if stock_actions else 'MIXED'
            
            # 创建DataFrame
            df = pd.DataFrame(records)
            
            # 添加一些计算列
            df['cumulative_return'] = ((df['net_value'] / df['net_value'].iloc[0]) - 1) * 100
            df['return_percentage'] = df['return_rate'] * 100
            
            # 重新排列列的顺序
            columns_order = [
                'global_day', 'week', 'day_in_week', 'timestamp',
                'return_rate', 'return_percentage', 'cumulative_return',
                'net_value', 'stock_traded', 'log_file'
            ]
            df = df[columns_order]
            
            # 保存CSV文件
            csv_filename = f"{main_stock}_{log_file}_daily_returns.csv"
            csv_path = os.path.join(output_dir, csv_filename)
            df.to_csv(csv_path, index=False, encoding='utf-8')
            
            print(f"✅ {main_stock} 每日收益率已保存: {csv_filename}")
            print(f"   - 交易日数: {len(df)}")
            print(f"   - 最终净值: ${df['net_value'].iloc[-1]:,.2f}")
            print(f"   - 累计收益率: {df['cumulative_return'].iloc[-1]:.2f}%")
            print()
    
    def create_combined_csv(self, daily_data: Dict[str, List[Dict]]):
        """创建包含所有股票的综合CSV文件"""
        output_dir = "daily_returns_csv"
        os.makedirs(output_dir, exist_ok=True)
        
        all_records = []
        
        for log_file, records in daily_data.items():
            if not records:
                continue
            
            # 获取主要交易的股票
            stock_actions = [r['stock_traded'] for r in records if r['stock_traded'] != 'CASH']
            main_stock = max(set(stock_actions), key=stock_actions.count) if stock_actions else 'MIXED'
            
            for record in records:
                enhanced_record = record.copy()
                enhanced_record['main_stock'] = main_stock
                all_records.append(enhanced_record)
        
        if not all_records:
            print("❌ 没有找到有效的数据记录")
            return
        
        # 创建综合DataFrame
        df = pd.DataFrame(all_records)
        
        # 按log_file和global_day排序
        df = df.sort_values(['log_file', 'global_day'])
        
        # 计算每个股票的累计收益率
        df['cumulative_return'] = 0.0
        for log_file in df['log_file'].unique():
            mask = df['log_file'] == log_file
            initial_value = df.loc[mask, 'net_value'].iloc[0]
            df.loc[mask, 'cumulative_return'] = ((df.loc[mask, 'net_value'] / initial_value) - 1) * 100
        
        df['return_percentage'] = df['return_rate'] * 100
        
        # 重新排列列的顺序
        columns_order = [
            'main_stock', 'log_file', 'global_day', 'week', 'day_in_week', 
            'timestamp', 'return_rate', 'return_percentage', 'cumulative_return',
            'net_value', 'stock_traded'
        ]
        df = df[columns_order]
        
        # 保存综合CSV文件
        combined_csv_path = os.path.join(output_dir, "all_stocks_daily_returns.csv")
        df.to_csv(combined_csv_path, index=False, encoding='utf-8')
        
        print(f"✅ 综合数据已保存: all_stocks_daily_returns.csv")
        print(f"   - 总记录数: {len(df)}")
        print(f"   - 股票数量: {df['main_stock'].nunique()}")
        print(f"   - 交易日范围: {df['global_day'].min()} - {df['global_day'].max()}")
        print()
    
    def create_pivot_table_csv(self, daily_data: Dict[str, List[Dict]]):
        """创建以日期为行、股票为列的透视表CSV"""
        output_dir = "daily_returns_csv"
        os.makedirs(output_dir, exist_ok=True)
        
        # 准备数据
        pivot_data = []
        stock_mapping = {}
        
        for log_file, records in daily_data.items():
            if not records:
                continue
            
            # 获取主要交易的股票
            stock_actions = [r['stock_traded'] for r in records if r['stock_traded'] != 'CASH']
            main_stock = max(set(stock_actions), key=stock_actions.count) if stock_actions else 'MIXED'
            stock_mapping[log_file] = main_stock
            
            for record in records:
                pivot_data.append({
                    'global_day': record['global_day'],
                    'week': record['week'],
                    'day_in_week': record['day_in_week'],
                    'stock': main_stock,
                    'return_rate': record['return_rate'],
                    'return_percentage': record['return_rate'] * 100,
                    'net_value': record['net_value'],
                    'timestamp': record['timestamp']
                })
        
        if not pivot_data:
            print("❌ 没有找到有效的数据记录")
            return
        
        df = pd.DataFrame(pivot_data)
        
        # 创建收益率透视表
        returns_pivot = df.pivot_table(
            index=['global_day', 'week', 'day_in_week'], 
            columns='stock', 
            values='return_percentage',
            fill_value=0
        )
        
        # 重置索引
        returns_pivot = returns_pivot.reset_index()
        
        # 保存收益率透视表
        returns_pivot_path = os.path.join(output_dir, "daily_returns_pivot_table.csv")
        returns_pivot.to_csv(returns_pivot_path, index=False, encoding='utf-8')
        
        print(f"✅ 收益率透视表已保存: daily_returns_pivot_table.csv")
        print(f"   - 维度: {len(returns_pivot)} 天 × {len(returns_pivot.columns)-3} 股票")
        print()
        
        # 创建净值透视表
        netvalue_pivot = df.pivot_table(
            index=['global_day', 'week', 'day_in_week'], 
            columns='stock', 
            values='net_value',
            fill_value=0
        )
        
        netvalue_pivot = netvalue_pivot.reset_index()
        
        # 保存净值透视表
        netvalue_pivot_path = os.path.join(output_dir, "daily_netvalues_pivot_table.csv")
        netvalue_pivot.to_csv(netvalue_pivot_path, index=False, encoding='utf-8')
        
        print(f"✅ 净值透视表已保存: daily_netvalues_pivot_table.csv")
        print(f"   - 维度: {len(netvalue_pivot)} 天 × {len(netvalue_pivot.columns)-3} 股票")
        print()
    
    def generate_summary_stats_csv(self, daily_data: Dict[str, List[Dict]]):
        """生成统计摘要CSV"""
        output_dir = "daily_returns_csv"
        os.makedirs(output_dir, exist_ok=True)
        
        summary_data = []
        
        for log_file, records in daily_data.items():
            if not records:
                continue
            
            # 获取主要交易的股票
            stock_actions = [r['stock_traded'] for r in records if r['stock_traded'] != 'CASH']
            main_stock = max(set(stock_actions), key=stock_actions.count) if stock_actions else 'MIXED'
            
            # 计算统计指标
            returns = [r['return_rate'] for r in records]
            net_values = [r['net_value'] for r in records]
            
            import numpy as np
            
            summary_data.append({
                'stock': main_stock,
                'log_file': log_file,
                'trading_days': len(records),
                'total_weeks': max([r['week'] for r in records]),
                'initial_value': net_values[0],
                'final_value': net_values[-1],
                'total_return_pct': ((net_values[-1] / net_values[0]) - 1) * 100,
                'mean_daily_return_pct': np.mean(returns) * 100,
                'std_daily_return_pct': np.std(returns, ddof=1) * 100,
                'min_daily_return_pct': min(returns) * 100,
                'max_daily_return_pct': max(returns) * 100,
                'positive_days': sum(1 for r in returns if r > 0),
                'negative_days': sum(1 for r in returns if r < 0),
                'win_rate_pct': (sum(1 for r in returns if r > 0) / len(returns)) * 100,
                'volatility_annualized_pct': np.std(returns, ddof=1) * np.sqrt(252) * 100,
                'sharpe_ratio': (np.mean(returns) * 252 - 0.02) / (np.std(returns, ddof=1) * np.sqrt(252)) if np.std(returns, ddof=1) > 0 else 0
            })
        
        # 创建摘要DataFrame
        summary_df = pd.DataFrame(summary_data)
        
        # 按总收益率排序
        summary_df = summary_df.sort_values('total_return_pct', ascending=False)
        
        # 保存摘要统计CSV
        summary_path = os.path.join(output_dir, "stocks_summary_statistics.csv")
        summary_df.to_csv(summary_path, index=False, encoding='utf-8')
        
        print(f"✅ 统计摘要已保存: stocks_summary_statistics.csv")
        print(f"   - 包含股票: {', '.join(summary_df['stock'].tolist())}")
        print()
    
    def export_all_csv_formats(self):
        """导出所有CSV格式"""
        print("🚀 开始导出每日收益率CSV文件...")
        print("=" * 60)
        
        # 解析每日数据
        print("📊 解析每日收益率数据...")
        daily_data = self.parse_daily_data_from_sections()
        
        if not daily_data:
            print("❌ 未找到有效的每日数据")
            return
        
        print(f"✅ 成功解析 {len(daily_data)} 个股票的数据")
        print()
        
        # 1. 创建单独的股票CSV文件
        print("📝 1. 创建单独股票CSV文件...")
        self.create_individual_stock_csvs(daily_data)
        
        # 2. 创建综合CSV文件
        print("📝 2. 创建综合CSV文件...")
        self.create_combined_csv(daily_data)
        
        # 3. 创建透视表CSV文件
        print("📝 3. 创建透视表CSV文件...")
        self.create_pivot_table_csv(daily_data)
        
        # 4. 创建统计摘要CSV文件
        print("📝 4. 创建统计摘要CSV文件...")
        self.generate_summary_stats_csv(daily_data)
        
        print("=" * 60)
        print("🎉 所有CSV文件导出完成！")
        print(f"📁 文件保存位置: daily_returns_csv/")


def main():
    """主函数"""
    data_file = "financial_performance_data.json"
    
    if not os.path.exists(data_file):
        print(f"❌ 错误: 找不到数据文件 {data_file}")
        print("请先运行 financial_performance_analyzer.py")
        return
    
    exporter = DailyReturnsCSVExporter(data_file)
    exporter.export_all_csv_formats()


if __name__ == "__main__":
    main()