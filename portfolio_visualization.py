"""
投资组合可视化工具 (Portfolio Visualization Tool)

基于PortfolioStateTracker的数据生成投资组合表现图表：
- 净值曲线图
- 日收益率分布图
- 现金和持仓变化图
- 跨周表现对比图

使用方法:
    from portfolio_visualization import PortfolioVisualizer
    visualizer = PortfolioVisualizer(tracker)
    visualizer.plot_all()
"""

import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import pandas as pd
from typing import Optional, Dict, List, Any
import numpy as np


class PortfolioVisualizer:
    """投资组合可视化器"""
    
    def __init__(self, portfolio_tracker):
        """
        初始化可视化器
        
        Args:
            portfolio_tracker: PortfolioStateTracker实例
        """
        self.tracker = portfolio_tracker
        self.plotting_data = self.tracker.get_plotting_data()
        
    def plot_net_worth_curve(self, save_path: Optional[str] = None) -> None:
        """绘制净值曲线图"""
        if not self.plotting_data["dates"]:
            print("⚠️ 无数据可绘制")
            return
        
        # 转换日期
        dates = [datetime.strptime(d, "%Y-%m-%d") for d in self.plotting_data["dates"]]
        net_worth = self.plotting_data["net_worth"]
        week_numbers = self.plotting_data["week_numbers"]
        
        plt.figure(figsize=(12, 6))
        
        # 绘制净值曲线
        plt.plot(dates, net_worth, linewidth=2, color='blue', label='投资组合净值')
        
        # 标记周分界线
        unique_weeks = list(set(week_numbers))
        if len(unique_weeks) > 1:
            for i, (date, week) in enumerate(zip(dates, week_numbers)):
                if i > 0 and week_numbers[i-1] != week:
                    plt.axvline(x=date, color='red', linestyle='--', alpha=0.7, 
                               label=f'第{week}周开始' if week == unique_weeks[1] else '')
        
        # 格式化
        plt.title('投资组合净值变化', fontsize=14, fontweight='bold')
        plt.xlabel('日期')
        plt.ylabel('净值 ($)')
        plt.grid(True, alpha=0.3)
        plt.legend()
        
        # 格式化x轴日期
        plt.gca().xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        plt.gca().xaxis.set_major_locator(mdates.DayLocator(interval=2))
        plt.xticks(rotation=45)
        
        # 添加数据标签
        total_return = (net_worth[-1] - net_worth[0]) / net_worth[0]
        plt.text(0.02, 0.98, f'总收益率: {total_return:.2%}', 
                transform=plt.gca().transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 净值曲线图已保存: {save_path}")
        
        plt.show()
    
    def plot_daily_returns(self, save_path: Optional[str] = None) -> None:
        """绘制日收益率分布图"""
        if not self.plotting_data["daily_returns"]:
            print("⚠️ 无收益率数据可绘制")
            return
        
        daily_returns = [r for r in self.plotting_data["daily_returns"] if r != 0]  # 排除第一天
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 子图1: 日收益率时间序列
        dates = [datetime.strptime(d, "%Y-%m-%d") for d in self.plotting_data["dates"]]
        ax1.plot(dates, self.plotting_data["daily_returns"], marker='o', markersize=4, 
                linewidth=1, color='green', alpha=0.7)
        ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax1.set_title('日收益率时间序列')
        ax1.set_xlabel('日期')
        ax1.set_ylabel('日收益率')
        ax1.grid(True, alpha=0.3)
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
        
        # 子图2: 收益率分布直方图
        ax2.hist(daily_returns, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax2.axvline(x=np.mean(daily_returns), color='red', linestyle='--', 
                   label=f'均值: {np.mean(daily_returns):.4f}')
        ax2.set_title('日收益率分布')
        ax2.set_xlabel('日收益率')
        ax2.set_ylabel('频数')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 添加统计信息
        stats_text = (f'日收益率统计:\n'
                     f'均值: {np.mean(daily_returns):.4f}\n'
                     f'标准差: {np.std(daily_returns):.4f}\n'
                     f'最大值: {np.max(daily_returns):.4f}\n'
                     f'最小值: {np.min(daily_returns):.4f}')
        ax2.text(0.02, 0.98, stats_text, transform=ax2.transAxes, 
                verticalalignment='top', fontsize=9,
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 日收益率图已保存: {save_path}")
        
        plt.show()
    
    def plot_cash_positions(self, save_path: Optional[str] = None) -> None:
        """绘制净值构成变化图"""
        if not self.plotting_data["dates"]:
            print("⚠️ 无数据可绘制")
            return
        
        dates = [datetime.strptime(d, "%Y-%m-%d") for d in self.plotting_data["dates"]]
        net_worth = self.plotting_data["net_worth"]
        
        # 计算每日收益率
        daily_returns = self.plotting_data["daily_returns"]
        
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        # 子图1: 净值变化图
        ax1.plot(dates, net_worth, linewidth=2, color='blue', label='投资组合净值')
        ax1.set_title('投资组合净值变化')
        ax1.set_ylabel('净值 ($)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        
        # 子图2: 日收益率
        ax2.plot(dates, daily_returns, marker='o', markersize=4, 
                linewidth=1, color='green', alpha=0.7, label='日收益率')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax2.set_title('日收益率时间序列')
        ax2.set_xlabel('日期')
        ax2.set_ylabel('日收益率')
        ax2.grid(True, alpha=0.3)
        ax2.legend()
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%m-%d'))
        plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 净值构成图已保存: {save_path}")
        
        plt.show()
    
    def plot_weekly_performance(self, save_path: Optional[str] = None) -> None:
        """绘制跨周表现对比图"""
        if not self.plotting_data["week_numbers"]:
            print("⚠️ 无周数据可绘制")
            return
        
        # 获取所有周的摘要数据
        unique_weeks = sorted(list(set(self.plotting_data["week_numbers"])))
        if len(unique_weeks) < 2:
            print("⚠️ 数据不足2周，无法绘制跨周对比")
            return
        
        week_summaries = []
        for week in unique_weeks:
            summary = self.tracker.get_week_summary(week)
            if summary["records_count"] > 0:
                week_summaries.append(summary)
        
        if len(week_summaries) < 2:
            print("⚠️ 有效周数据不足，无法绘制跨周对比")
            return
        
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
        
        weeks = [s["week_number"] for s in week_summaries]
        
        # 子图1: 周收益率
        week_returns = [s["week_return"] for s in week_summaries]
        bars1 = ax1.bar(weeks, week_returns, alpha=0.7, 
                       color=['green' if r >= 0 else 'red' for r in week_returns])
        ax1.set_title('各周收益率')
        ax1.set_xlabel('周数')
        ax1.set_ylabel('周收益率')
        ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, ret in zip(bars1, week_returns):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height,
                    f'{ret:.2%}', ha='center', va='bottom' if ret >= 0 else 'top')
        
        # 子图2: 周末净值
        end_net_worths = [s["end_net_worth"] for s in week_summaries]
        ax2.plot(weeks, end_net_worths, marker='o', markersize=8, 
                linewidth=2, color='blue')
        ax2.set_title('各周末净值')
        ax2.set_xlabel('周数')
        ax2.set_ylabel('净值 ($)')
        ax2.grid(True, alpha=0.3)
        
        # 子图3: 周末现金
        end_cash = [s["end_cash"] for s in week_summaries]
        ax3.bar(weeks, end_cash, alpha=0.7, color='green')
        ax3.set_title('各周末现金')
        ax3.set_xlabel('周数')
        ax3.set_ylabel('现金 ($)')
        ax3.grid(True, alpha=0.3)
        
        # 子图4: 累计收益率
        start_net_worth = week_summaries[0]["start_net_worth"]
        cumulative_returns = [(s["end_net_worth"] - start_net_worth) / start_net_worth 
                             for s in week_summaries]
        ax4.plot(weeks, cumulative_returns, marker='o', markersize=8, 
                linewidth=2, color='purple')
        ax4.set_title('累计收益率')
        ax4.set_xlabel('周数')
        ax4.set_ylabel('累计收益率')
        ax4.grid(True, alpha=0.3)
        
        # 添加最终统计信息
        final_return = cumulative_returns[-1]
        fig.suptitle(f'跨周投资表现分析 (总收益率: {final_return:.2%})', 
                    fontsize=16, fontweight='bold')
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 跨周表现图已保存: {save_path}")
        
        plt.show()
    
    def plot_all(self, save_dir: Optional[str] = None) -> None:
        """绘制所有图表"""
        print("📊 开始生成投资组合可视化图表...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        if save_dir:
            import os
            os.makedirs(save_dir, exist_ok=True)
            net_worth_path = f"{save_dir}/net_worth_{timestamp}.png"
            returns_path = f"{save_dir}/daily_returns_{timestamp}.png"
            cash_pos_path = f"{save_dir}/cash_positions_{timestamp}.png"
            weekly_path = f"{save_dir}/weekly_performance_{timestamp}.png"
        else:
            net_worth_path = returns_path = cash_pos_path = weekly_path = None
        
        self.plot_net_worth_curve(net_worth_path)
        self.plot_daily_returns(returns_path)
        self.plot_cash_positions(cash_pos_path)
        self.plot_weekly_performance(weekly_path)
        
        print("✅ 所有图表生成完成！")
    
    def export_data_summary(self, save_path: str) -> None:
        """导出数据摘要到CSV"""
        if not self.plotting_data["dates"]:
            print("⚠️ 无数据可导出")
            return
        
        # 构建DataFrame
        df_data = {
            "日期": self.plotting_data["dates"],
            "净值": self.plotting_data["net_worth"],
            "现金": self.plotting_data["cash"],
            "日收益率": self.plotting_data["daily_returns"],
            "周数": self.plotting_data["week_numbers"]
        }
        
        # 添加持仓信息
        for i, positions in enumerate(self.plotting_data["positions"]):
            for stock, amount in positions.items():
                col_name = f"持仓_{stock}"
                if col_name not in df_data:
                    df_data[col_name] = [0] * len(self.plotting_data["dates"])
                df_data[col_name][i] = amount
        
        df = pd.DataFrame(df_data)
        df.to_csv(save_path, index=False, encoding='utf-8-sig')
        print(f"📄 数据摘要已导出: {save_path}")


# 使用示例
if __name__ == "__main__":
    # 这里是使用示例，实际使用时需要传入真实的tracker实例
    print("投资组合可视化工具")
    print("使用方法:")
    print("  from portfolio_visualization import PortfolioVisualizer")
    print("  visualizer = PortfolioVisualizer(your_tracker)")
    print("  visualizer.plot_all(save_dir='charts')")