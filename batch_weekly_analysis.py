#!/usr/bin/env python3
"""
批量提取多个日志文件中的周性能分析数据并生成可视化图表
"""

import re
import json
import glob
import os
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def extract_weekly_performance(log_file_path):
    """从日志文件中提取17周的性能分析数据"""
    
    weekly_data = []
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"读取文件 {log_file_path} 失败: {e}")
        return weekly_data
    
    # 使用正则表达式匹配周性能分析块
    pattern = r'============================================================\n.*?第 (\d+) 周性能分析 - 联盟: (.*?)\n.*?周总收益率: ([-\d\.]+)\n.*?周夏普比率: ([-\d\.]+)\n.*?交易天数: (\d+)\n.*?============================================================'
    
    matches = re.findall(pattern, content, re.DOTALL)
    
    # 目标联盟：完整的7个智能体
    target_agents = {'FAA', 'TAA', 'BOA', 'BeOA', 'TRA', 'NAA', 'NOA'}
    
    for match in matches:
        week_num = int(match[0])
        coalition = match[1].strip()
        return_rate = float(match[2])
        sharpe_ratio = float(match[3])
        trading_days = int(match[4])
        
        # 解析frozenset字符串，提取智能体集合
        if coalition.startswith("frozenset(") and coalition.endswith(")"):
            agents_str = coalition[10:-1]  # 去掉 "frozenset(" 和 ")"
            if agents_str.startswith("{") and agents_str.endswith("}"):
                agents_str = agents_str[1:-1]  # 去掉 "{" 和 "}"
                # 解析智能体名称
                agents = set()
                for agent in agents_str.split(", "):
                    agent = agent.strip().strip("'\"")
                    if agent:
                        agents.add(agent)
                
                # 只保留包含完整7个智能体的联盟记录
                if agents == target_agents:
                    weekly_data.append({
                        "周数": week_num,
                        "联盟": coalition,
                        "周总收益率": return_rate,
                        "周夏普比率": sharpe_ratio,
                        "交易天数": trading_days
                    })
    
    # 按周数排序
    weekly_data.sort(key=lambda x: x["周数"])
    
    return weekly_data

def calculate_cumulative_returns(weekly_data):
    """计算累计收益率"""
    cumulative_returns = []
    cumulative = 1.0
    
    for data in weekly_data:
        cumulative *= (1 + data['周总收益率'])
        cumulative_returns.append(cumulative - 1)
    
    return cumulative_returns

def get_stock_name_from_filename(filename):
    """从文件名中提取股票代码"""
    basename = os.path.basename(filename)
    if basename.startswith('AAPL'):
        return 'AAPL'
    elif basename.startswith('GOOG'):
        return 'GOOG'
    elif basename.startswith('META'):
        return 'META'
    elif basename.startswith('NVDA'):
        return 'NVDA'
    else:
        return basename.replace('.log', '').replace('_0101_0430', '').replace('_0101-0430', '')

def plot_cumulative_returns(all_results, output_dir):
    """绘制所有股票的累计收益率曲线"""
    
    plt.figure(figsize=(15, 10))
    
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']
    
    for i, (stock_name, data) in enumerate(all_results.items()):
        if not data['weekly_data']:
            continue
            
        weeks = [d['周数'] for d in data['weekly_data']]
        cumulative_returns = data['cumulative_returns']
        
        plt.plot(weeks, [r * 100 for r in cumulative_returns], 
                marker='o', linewidth=2, markersize=4,
                label=f"{stock_name} (最终: {cumulative_returns[-1]*100:.2f}%)",
                color=colors[i % len(colors)])
    
    plt.title('多股票17周累计收益率对比', fontsize=16, fontweight='bold', pad=20)
    plt.xlabel('周数', fontsize=12)
    plt.ylabel('累计收益率 (%)', fontsize=12)
    plt.grid(True, alpha=0.3, linestyle='--')
    plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    
    # 添加零轴线
    plt.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    
    # 设置x轴刻度
    if all_results:
        max_weeks = max([len(data['weekly_data']) for data in all_results.values() if data['weekly_data']])
        plt.xticks(range(1, max_weeks + 1))
    
    plt.tight_layout()
    
    # 保存图表
    chart_path = os.path.join(output_dir, 'cumulative_returns_comparison.png')
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    print(f"累计收益率对比图已保存到: {chart_path}")
    
    plt.show()

def plot_weekly_returns_heatmap(all_results, output_dir):
    """绘制周收益率热力图"""
    
    if not all_results:
        return
    
    # 准备数据
    stocks = list(all_results.keys())
    max_weeks = max([len(data['weekly_data']) for data in all_results.values() if data['weekly_data']])
    
    # 创建数据矩阵
    returns_matrix = np.full((len(stocks), max_weeks), np.nan)
    
    for i, (stock_name, data) in enumerate(all_results.items()):
        for j, week_data in enumerate(data['weekly_data']):
            if j < max_weeks:
                returns_matrix[i, j] = week_data['周总收益率'] * 100
    
    # 创建热力图
    plt.figure(figsize=(16, 8))
    
    im = plt.imshow(returns_matrix, cmap='RdYlGn', aspect='auto', vmin=-15, vmax=15)
    
    # 设置标签
    plt.yticks(range(len(stocks)), stocks)
    plt.xticks(range(max_weeks), [f'第{i+1}周' for i in range(max_weeks)], rotation=45)
    
    # 添加数值标注
    for i in range(len(stocks)):
        for j in range(max_weeks):
            if not np.isnan(returns_matrix[i, j]):
                text = plt.text(j, i, f'{returns_matrix[i, j]:.1f}%',
                               ha="center", va="center", color="black", fontsize=8)
    
    plt.colorbar(im, label='周收益率 (%)')
    plt.title('各股票17周收益率热力图', fontsize=16, fontweight='bold', pad=20)
    plt.tight_layout()
    
    # 保存图表
    heatmap_path = os.path.join(output_dir, 'weekly_returns_heatmap.png')
    plt.savefig(heatmap_path, dpi=300, bbox_inches='tight')
    print(f"周收益率热力图已保存到: {heatmap_path}")
    
    plt.show()

def generate_comparison_report(all_results, output_dir):
    """生成对比分析报告"""
    
    report_lines = []
    report_lines.append("# 多股票17周投资表现对比分析报告\n")
    report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    # 总体统计表
    report_lines.append("## 总体表现统计\n")
    report_lines.append("| 股票代码 | 总收益率 | 年化收益率 | 最大周收益 | 最小周收益 | 盈利周数 | 胜率 |")
    report_lines.append("|----------|----------|------------|------------|------------|----------|------|")
    
    performance_summary = []
    
    for stock_name, data in all_results.items():
        if not data['weekly_data']:
            continue
            
        total_returns = [d['周总收益率'] for d in data['weekly_data']]
        
        # 计算总收益率
        cumulative = 1.0
        for r in total_returns:
            cumulative *= (1 + r)
        total_return = cumulative - 1
        
        # 计算年化收益率
        periods = len(total_returns)
        annualized_return = (cumulative ** (52/periods)) - 1
        
        # 计算胜率
        profit_weeks = len([r for r in total_returns if r > 0])
        win_rate = profit_weeks / len(total_returns)
        
        performance_summary.append({
            'stock': stock_name,
            'total_return': total_return,
            'annualized_return': annualized_return,
            'max_weekly': max(total_returns),
            'min_weekly': min(total_returns),
            'profit_weeks': profit_weeks,
            'win_rate': win_rate
        })
        
        report_lines.append(f"| {stock_name} | {total_return*100:.2f}% | {annualized_return*100:.2f}% | {max(total_returns)*100:.2f}% | {min(total_returns)*100:.2f}% | {profit_weeks} | {win_rate*100:.1f}% |")
    
    # 排名分析
    report_lines.append("\n## 表现排名\n")
    
    # 按总收益率排名
    performance_summary.sort(key=lambda x: x['total_return'], reverse=True)
    report_lines.append("### 按总收益率排名\n")
    for i, perf in enumerate(performance_summary, 1):
        report_lines.append(f"{i}. **{perf['stock']}**: {perf['total_return']*100:.2f}%")
    
    report_lines.append("")
    
    # 按胜率排名
    performance_summary.sort(key=lambda x: x['win_rate'], reverse=True)
    report_lines.append("### 按胜率排名\n")
    for i, perf in enumerate(performance_summary, 1):
        report_lines.append(f"{i}. **{perf['stock']}**: {perf['win_rate']*100:.1f}%")
    
    # 详细周度数据
    report_lines.append("\n## 详细周度表现\n")
    
    for stock_name, data in all_results.items():
        if not data['weekly_data']:
            continue
            
        report_lines.append(f"### {stock_name}\n")
        report_lines.append("| 周数 | 周收益率 | 累计收益率 | 夏普比率 |")
        report_lines.append("|------|----------|------------|----------|")
        
        for i, week_data in enumerate(data['weekly_data']):
            cumulative_return = data['cumulative_returns'][i]
            report_lines.append(f"| 第{week_data['周数']}周 | {week_data['周总收益率']*100:.2f}% | {cumulative_return*100:.2f}% | {week_data['周夏普比率']:.2f} |")
        
        report_lines.append("")
    
    # 保存报告
    report_path = os.path.join(output_dir, 'multi_stock_comparison_report.md')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))
    
    print(f"对比分析报告已保存到: {report_path}")

def process_multiple_logs(log_files, output_dir):
    """批量处理多个日志文件"""
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    all_results = {}
    
    print("开始批量处理日志文件...")
    print("="*80)
    
    for log_file in log_files:
        print(f"\n处理文件: {log_file}")
        
        # 提取股票名称
        stock_name = get_stock_name_from_filename(log_file)
        
        # 提取周性能数据
        weekly_data = extract_weekly_performance(log_file)
        
        if not weekly_data:
            print(f"❌ 未找到完整7个智能体联盟的数据")
            continue
        
        # 计算累计收益率
        cumulative_returns = calculate_cumulative_returns(weekly_data)
        
        # 保存结果
        all_results[stock_name] = {
            'weekly_data': weekly_data,
            'cumulative_returns': cumulative_returns,
            'log_file': log_file
        }
        
        # 显示基本统计
        total_returns = [d['周总收益率'] for d in weekly_data]
        final_return = cumulative_returns[-1] if cumulative_returns else 0
        
        print(f"✅ 成功提取 {len(weekly_data)} 周数据")
        print(f"   总收益率: {final_return*100:.2f}%")
        print(f"   最高周收益: {max(total_returns)*100:.2f}%")
        print(f"   最低周收益: {min(total_returns)*100:.2f}%")
        print(f"   胜率: {len([r for r in total_returns if r > 0])/len(total_returns)*100:.1f}%")
        
        # 保存单个股票的JSON数据
        stock_output_file = os.path.join(output_dir, f'{stock_name}_weekly_performance.json')
        with open(stock_output_file, 'w', encoding='utf-8') as f:
            json.dump(weekly_data, f, ensure_ascii=False, indent=2)
        print(f"   数据已保存到: {stock_output_file}")
    
    print("\n" + "="*80)
    print(f"批量处理完成! 共处理 {len(all_results)} 个股票")
    
    if all_results:
        # 生成对比图表
        print("\n生成可视化图表...")
        plot_cumulative_returns(all_results, output_dir)
        plot_weekly_returns_heatmap(all_results, output_dir)
        
        # 生成对比报告
        print("\n生成对比分析报告...")
        generate_comparison_report(all_results, output_dir)
        
        # 保存汇总数据
        summary_file = os.path.join(output_dir, 'all_stocks_summary.json')
        summary_data = {}
        for stock, data in all_results.items():
            summary_data[stock] = {
                'weekly_data': data['weekly_data'],
                'cumulative_returns': data['cumulative_returns'],
                'total_return': data['cumulative_returns'][-1] if data['cumulative_returns'] else 0
            }
        
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, ensure_ascii=False, indent=2)
        print(f"汇总数据已保存到: {summary_file}")
    
    return all_results

def main():
    # 定义日志文件路径
    log_files = [
        "/Users/<USER>/Code/Multi_Agent_Optimization/test_results/AAPL_0101-0430.log",
        "/Users/<USER>/Code/Multi_Agent_Optimization/test_results/GOOG_0101_0430.log", 
        "/Users/<USER>/Code/Multi_Agent_Optimization/test_results/META_0101_0430.log",
        "/Users/<USER>/Code/Multi_Agent_Optimization/test_results/NVDA_0101_0430.log"
    ]
    
    # 检查文件是否存在
    existing_files = []
    for log_file in log_files:
        if os.path.exists(log_file):
            existing_files.append(log_file)
        else:
            print(f"⚠️  文件不存在: {log_file}")
    
    if not existing_files:
        print("❌ 没有找到任何有效的日志文件!")
        return
    
    # 输出目录
    output_dir = "/Users/<USER>/Code/Multi_Agent_Optimization/batch_analysis_results"
    
    # 批量处理
    results = process_multiple_logs(existing_files, output_dir)
    
    print(f"\n🎉 分析完成! 所有结果已保存到: {output_dir}")

if __name__ == "__main__":
    main()
