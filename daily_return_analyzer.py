#!/usr/bin/env python3
"""
日收益分析器 - 从OPRO系统日志中提取并计算每日收益
"""

import re
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import argparse
import json


@dataclass
class DailyReturn:
    """日收益数据类"""
    date: str
    cumulative_return: float
    week_return: float
    daily_return: float
    week_number: int


@dataclass
class WeeklyPerformance:
    """周度性能数据类"""
    week_number: int
    weekly_return: float
    sharpe_ratio: float
    trading_days: int


class DailyReturnAnalyzer:
    """日收益分析器"""
    
    def __init__(self, log_file_path: str):
        self.log_file_path = log_file_path
        self.daily_data: List[DailyReturn] = []
        self.weekly_data: List[WeeklyPerformance] = []
        
    def parse_log_file(self) -> None:
        """解析日志文件，提取交易数据"""
        
        # 正则表达式模式
        tra_input_pattern = r"🤖 TRA 输入: 日期=(\d{4}-\d{2}-\d{2}), 累计收益=([-+]?\d*\.?\d+), 周收益=([-+]?\d*\.?\d+)"
        weekly_performance_pattern = r"第 (\d+) 周性能分析.*?周总收益率: ([-+]?\d*\.?\d+)"
        
        current_week = 1
        
        with open(self.log_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 提取每日TRA输入数据
        tra_matches = re.findall(tra_input_pattern, content)
        
        # 提取每周性能数据
        weekly_matches = re.findall(weekly_performance_pattern, content)
        
        # 解析每周性能数据
        for week_num, weekly_return in weekly_matches:
            # 查找对应的夏普比率和交易天数
            week_pattern = f"第 {week_num} 周性能分析.*?周总收益率: {re.escape(weekly_return)}.*?周夏普比率: ([-+]?\\d*\\.?\\d+).*?交易天数: (\\d+)"
            week_detail = re.search(week_pattern, content, re.DOTALL)
            
            if week_detail:
                sharpe_ratio = float(week_detail.group(1))
                trading_days = int(week_detail.group(2))
            else:
                sharpe_ratio = 0.0
                trading_days = 5
                
            self.weekly_data.append(WeeklyPerformance(
                week_number=int(week_num),
                weekly_return=float(weekly_return),
                sharpe_ratio=sharpe_ratio,
                trading_days=trading_days
            ))
        
        # 解析每日数据并计算日收益
        previous_cumulative = 0.0
        current_week_number = 1
        
        # 根据日期确定周数
        date_to_week = {}
        current_week_start = None
        
        for i, (date_str, cumulative_str, week_str) in enumerate(tra_matches):
            cumulative_return = float(cumulative_str)
            week_return = float(week_str)
            
            # 计算日收益 = 当日累计收益 - 前一日累计收益
            daily_return = cumulative_return - previous_cumulative
            
            # 解析日期
            current_date = datetime.strptime(date_str, '%Y-%m-%d')
            
            # 确定周数 - 基于日期的周一为一周开始
            if current_week_start is None:
                current_week_start = current_date - timedelta(days=current_date.weekday())
                current_week_number = 1
            else:
                week_start = current_date - timedelta(days=current_date.weekday())
                if week_start > current_week_start:
                    current_week_number += 1
                    current_week_start = week_start
            
            self.daily_data.append(DailyReturn(
                date=date_str,
                cumulative_return=cumulative_return,
                week_return=week_return,
                daily_return=daily_return,
                week_number=current_week_number
            ))
            
            previous_cumulative = cumulative_return
    
    def validate_weekly_consistency(self) -> Dict[int, Dict[str, float]]:
        """验证每周累计收益与周总收益率的一致性"""
        validation_results = {}
        
        # 按周分组日数据
        weeks_data = {}
        for daily in self.daily_data:
            week = daily.week_number
            if week not in weeks_data:
                weeks_data[week] = []
            weeks_data[week].append(daily)
        
        # 验证每周数据
        for week_num, daily_list in weeks_data.items():
            if not daily_list:
                continue
                
            # 计算该周最后一天的周收益（应该是周累计收益）
            last_day_week_return = daily_list[-1].week_return
            
            # 计算该周所有日收益之和
            calculated_week_return = sum(d.daily_return for d in daily_list)
            
            # 查找对应的周度性能数据
            weekly_performance = next(
                (w for w in self.weekly_data if w.week_number == week_num), 
                None
            )
            
            official_week_return = weekly_performance.weekly_return if weekly_performance else 0.0
            
            validation_results[week_num] = {
                'last_day_week_return': last_day_week_return,
                'calculated_sum': calculated_week_return,
                'official_weekly_return': official_week_return,
                'consistent_with_last_day': abs(last_day_week_return - official_week_return) < 1e-6,
                'consistent_with_sum': abs(calculated_week_return - official_week_return) < 1e-6
            }
        
        return validation_results
    
    def generate_daily_breakdown(self) -> Dict:
        """生成每日收益分解报告"""
        
        # 按周分组
        weeks_breakdown = {}
        
        for daily in self.daily_data:
            week = daily.week_number
            if week not in weeks_breakdown:
                weeks_breakdown[week] = {
                    'week_number': week,
                    'daily_returns': [],
                    'total_week_return': 0.0,
                    'official_week_return': 0.0
                }
            
            weeks_breakdown[week]['daily_returns'].append({
                'date': daily.date,
                'daily_return': round(daily.daily_return, 6),
                'cumulative_return': round(daily.cumulative_return, 6),
                'week_return': round(daily.week_return, 6)
            })
        
        # 添加官方周收益数据
        for week_num, week_data in weeks_breakdown.items():
            weekly_perf = next(
                (w for w in self.weekly_data if w.week_number == week_num), 
                None
            )
            if weekly_perf:
                week_data['official_week_return'] = weekly_perf.weekly_return
                week_data['sharpe_ratio'] = weekly_perf.sharpe_ratio
                week_data['trading_days'] = weekly_perf.trading_days
            
            # 计算日收益总和
            week_data['calculated_week_return'] = sum(
                d['daily_return'] for d in week_data['daily_returns']
            )
        
        return {
            'summary': {
                'total_weeks': len(weeks_breakdown),
                'total_trading_days': len(self.daily_data),
                'total_cumulative_return': self.daily_data[-1].cumulative_return if self.daily_data else 0.0
            },
            'weekly_breakdown': weeks_breakdown,
            'validation': self.validate_weekly_consistency()
        }
    
    def export_to_json(self, output_path: str) -> None:
        """导出结果到JSON文件"""
        results = self.generate_daily_breakdown()
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"结果已导出到: {output_path}")
    
    def print_summary(self) -> None:
        """打印分析摘要"""
        results = self.generate_daily_breakdown()
        
        print("=" * 60)
        print("📊 日收益分解分析报告")
        print("=" * 60)
        
        summary = results['summary']
        print(f"总周数: {summary['total_weeks']}")
        print(f"总交易日数: {summary['total_trading_days']}")
        print(f"总累计收益: {summary['total_cumulative_return']:.4f}")
        print()
        
        print("📈 各周收益分解:")
        print("-" * 60)
        
        for week_num in sorted(results['weekly_breakdown'].keys()):
            week_data = results['weekly_breakdown'][week_num]
            print(f"第 {week_num:2d} 周:")
            print(f"  官方周收益: {week_data['official_week_return']:8.4f}")
            print(f"  计算周收益: {week_data['calculated_week_return']:8.4f}")
            print(f"  日收益明细:")
            
            for day in week_data['daily_returns']:
                print(f"    {day['date']}: {day['daily_return']:8.6f}")
            print()
        
        # 验证结果
        validation = results['validation']
        print("🔍 数据一致性验证:")
        print("-" * 60)
        
        inconsistent_weeks = []
        for week_num, validation_data in validation.items():
            if not validation_data['consistent_with_sum']:
                inconsistent_weeks.append(week_num)
                print(f"⚠️  第 {week_num} 周存在不一致:")
                print(f"    官方周收益: {validation_data['official_weekly_return']:8.4f}")
                print(f"    日收益总和: {validation_data['calculated_sum']:8.4f}")
                print(f"    最后一日周收益: {validation_data['last_day_week_return']:8.4f}")
        
        if not inconsistent_weeks:
            print("✅ 所有周的数据都一致")
        else:
            print(f"❌ 共有 {len(inconsistent_weeks)} 周存在数据不一致")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='分析OPRO系统日志中的每日收益')
    parser.add_argument('log_file', help='日志文件路径')
    parser.add_argument('-o', '--output', help='输出JSON文件路径')
    parser.add_argument('-s', '--summary', action='store_true', help='显示分析摘要')
    
    args = parser.parse_args()
    
    try:
        analyzer = DailyReturnAnalyzer(args.log_file)
        analyzer.parse_log_file()
        
        if args.summary:
            analyzer.print_summary()
        
        if args.output:
            analyzer.export_to_json(args.output)
        
        if not args.summary and not args.output:
            # 默认显示摘要
            analyzer.print_summary()
            
    except FileNotFoundError:
        print(f"❌ 错误: 找不到日志文件 {args.log_file}")
    except Exception as e:
        print(f"❌ 处理过程中发生错误: {e}")


if __name__ == "__main__":
    main()