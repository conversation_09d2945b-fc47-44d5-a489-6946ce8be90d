STDOUT:
============================= test session starts ==============================
platform darwin -- Python 3.12.3, pytest-8.3.5, pluggy-1.5.0 -- /Applications/anaconda3/bin/python
cachedir: .pytest_cache
rootdir: /Users/<USER>/Code/Multi_Agent_Optimization
plugins: anyio-4.9.0, asyncio-1.1.0, langsmith-0.3.45, mock-3.14.0
asyncio: mode=Mode.STRICT, asyncio_default_fixture_loop_scope=None, asyncio_default_test_loop_scope=function
collecting ... collected 6 items

contribution_assessment/tests/test_simple_integration.py::SimpleIntegrationTestSuite::test_api_compatibility PASSED [ 16%]
contribution_assessment/tests/test_simple_integration.py::SimpleIntegrationTestSuite::test_configuration_driven_assessor PASSED [ 33%]
contribution_assessment/tests/test_simple_integration.py::SimpleIntegrationTestSuite::test_error_handling PASSED [ 50%]
contribution_assessment/tests/test_simple_integration.py::SimpleIntegrationTestSuite::test_mock_run_execution PASSED [ 66%]
contribution_assessment/tests/test_simple_integration.py::SimpleIntegrationTestSuite::test_refactored_assessor_initialization PASSED [ 83%]
contribution_assessment/tests/test_simple_integration.py::SimpleIntegrationTestSuite::test_service_factory_functionality PASSED [100%]

============================== 6 passed in 0.91s ===============================


STDERR:
/Applications/anaconda3/lib/python3.12/site-packages/requests/__init__.py:86: RequestsDependencyWarning: Unable to find acceptable character detection dependency (chardet or charset_normalizer).
  warnings.warn(
/Applications/anaconda3/lib/python3.12/site-packages/pytest_asyncio/plugin.py:211: PytestDeprecationWarning: The configuration option "asyncio_default_fixture_loop_scope" is unset.
The event loop scope for asynchronous fixtures will default to the fixture caching scope. Future versions of pytest-asyncio will default the loop scope for asynchronous fixtures to function scope. Set the default fixture loop scope explicitly in order to avoid unexpected behavior in the future. Valid fixture loop scopes are: "function", "class", "module", "package", "session"

  warnings.warn(PytestDeprecationWarning(_DEFAULT_FIXTURE_LOOP_SCOPE_UNSET))
