#!/usr/bin/env python3
"""
前16周每日表现快速总结
"""

import json
import os

def show_daily_summary():
    """显示每日表现快速总结"""
    
    summary_file = "/Users/<USER>/Code/Multi_Agent_Optimization/daily_analysis_16weeks/daily_comparison_summary_16weeks.json"
    
    try:
        with open(summary_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print("❌ 找不到汇总数据文件")
        return
    
    print("📈 前16周每日交易表现快速总结")
    print("="*70)
    
    # 按最终收益率排序
    sorted_stocks = sorted(data.items(), key=lambda x: x[1]['final_return'], reverse=True)
    
    print(f"{'股票':>6} {'交易天数':>8} {'最终收益':>8} {'日胜率':>8} {'日均收益':>10} {'最大日涨':>8} {'最大日跌':>8} {'波动率':>8}")
    print("-" * 70)
    
    for stock, stats in sorted_stocks:
        emoji = "🚀" if stats['final_return'] > 0 else "📉"
        
        print(f"{emoji} {stock:>4} "
              f"{stats['total_days']:>8}天 "
              f"{stats['final_return']*100:>7.2f}% "
              f"{stats['daily_win_rate']*100:>7.1f}% "
              f"{stats['avg_daily_return']*100:>9.3f}% "
              f"{stats['max_daily_return']*100:>7.2f}% "
              f"{stats['min_daily_return']*100:>7.2f}% "
              f"{stats['volatility']*100:>7.3f}")
    
    print("\n📊 关键发现:")
    print("-" * 70)
    
    # 找出各种表现最好的
    best_return = max(sorted_stocks, key=lambda x: x[1]['final_return'])
    best_winrate = max(sorted_stocks, key=lambda x: x[1]['daily_win_rate'])
    most_stable = min(sorted_stocks, key=lambda x: x[1]['volatility'])
    best_avg_daily = max(sorted_stocks, key=lambda x: x[1]['avg_daily_return'])
    
    print(f"🏆 最佳总收益: {best_return[0]} ({best_return[1]['final_return']*100:+.2f}%)")
    print(f"🎯 最高日胜率: {best_winrate[0]} ({best_winrate[1]['daily_win_rate']*100:.1f}%)")
    print(f"⚡ 最佳日均收益: {best_avg_daily[0]} ({best_avg_daily[1]['avg_daily_return']*100:+.3f}%/天)")
    print(f"🛡️  最稳定股票: {most_stable[0]} (波动率: {most_stable[1]['volatility']*100:.3f}%)")
    
    # 计算平均表现
    avg_return = sum([s[1]['final_return'] for s in sorted_stocks]) / len(sorted_stocks)
    avg_winrate = sum([s[1]['daily_win_rate'] for s in sorted_stocks]) / len(sorted_stocks)
    
    print(f"\n📈 平均表现:")
    print(f"   平均最终收益: {avg_return*100:.2f}%")
    print(f"   平均日胜率: {avg_winrate*100:.1f}%")
    
    # 风险分析
    print(f"\n⚠️  风险分析:")
    high_risk_stocks = [s for s in sorted_stocks if s[1]['volatility'] > 0.02]  # 日波动率>2%
    if high_risk_stocks:
        print(f"   高波动股票: {', '.join([s[0] for s in high_risk_stocks])}")
    
    losing_stocks = [s for s in sorted_stocks if s[1]['final_return'] < 0]
    if losing_stocks:
        print(f"   亏损股票: {', '.join([s[0] for s in losing_stocks])}")
    
    print(f"\n💡 投资建议:")
    print("-" * 70)
    
    if best_return[1]['final_return'] > 0:
        print(f"✅ {best_return[0]} 收益最佳，适合追求收益的投资者")
    else:
        print("⚠️  所有股票均亏损，建议重新评估策略")
    
    if most_stable[0] != best_return[0] and most_stable[1]['final_return'] >= 0:
        print(f"✅ {most_stable[0]} 波动最小，适合稳健型投资者")
    
    if best_winrate[1]['daily_win_rate'] > 0.5:
        print(f"🎯 {best_winrate[0]} 日胜率最高({best_winrate[1]['daily_win_rate']*100:.1f}%)，短线操作表现较好")
    
    print(f"\n📁 详细数据和图表请查看:")
    print("   📊 /Users/<USER>/Code/Multi_Agent_Optimization/daily_analysis_16weeks/")
    print("   📈 每日收益图表: *_daily_returns_16weeks.png")
    print("   📋 详细CSV数据: *_daily_analysis_16weeks.csv") 
    print("   📄 完整报告: daily_performance_report_16weeks.md")

if __name__ == "__main__":
    show_daily_summary()
