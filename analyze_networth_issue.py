# 让我们仔细分析1月2日买入后的净值变化
import sqlite3
import pandas as pd

conn = sqlite3.connect('data/tickers/AAPL/AAPL_data.db')
query = '''
SELECT trade_date, Open, Close 
FROM ohlcv 
WHERE ticker = 'AAPL' 
AND trade_date >= '2025-01-02' 
AND trade_date <= '2025-01-10' 
ORDER BY trade_date
'''
df = pd.read_sql(query, conn)
conn.close()

print('=== 详细分析：1月2日买入的净值变化 ===')
print(df[['trade_date', 'Close']])
print()

# 模拟1月2日用收盘价买入
initial_cash = 1000000.0
trade_price = 243.85  # 1月2日收盘价
position_size = 0.7   # 70%仓位

shares_bought = int(initial_cash * 0.99 * position_size / trade_price)
cost = shares_bought * trade_price * 1.001  # 包含0.1%手续费
remaining_cash = initial_cash - cost

print(f'1月2日交易:')
print(f'  买入价格: ${trade_price:.2f}')
print(f'  买入股数: {shares_bought}')
print(f'  交易成本: ${cost:.2f}')
print(f'  剩余现金: ${remaining_cash:.2f}')
print()

print('逐日净值计算:')
for i, row in df.iterrows():
    date = row['trade_date']
    close_price = row['Close']
    position_value = shares_bought * close_price
    net_worth = remaining_cash + position_value
    
    if i == 0:
        initial_net_worth = net_worth
        daily_return = 0.0
    else:
        daily_return = (net_worth / prev_net_worth) - 1
    
    print(f'{date}: 收盘${close_price:.2f}, 持仓${position_value:.2f}, 净值${net_worth:.2f}, 日收益{daily_return:.4%}')
    prev_net_worth = net_worth

print()
print('=== 关键发现 ===')
jan2_close = 243.85
jan3_close = 243.36
stock_change = (jan3_close - jan2_close) / jan2_close
print(f'1月2日->1月3日股价变化: {stock_change:.4%}')
print('如果净值在股价下跌时增加，说明计算逻辑有问题！')