import sqlite3
import pandas as pd

# 查询苹果股票第一周的价格数据
conn = sqlite3.connect('data/tickers/AAPL/AAPL_data.db')
query = '''
SELECT trade_date, Open, Close 
FROM ohlcv 
WHERE ticker = 'AAPL' 
AND trade_date >= '2025-01-01' 
AND trade_date <= '2025-01-10' 
ORDER BY trade_date
'''
df = pd.read_sql(query, conn)
conn.close()

print('苹果股票第一周价格数据:')
print(df)

# 计算第一周的实际涨跌
if len(df) > 1:
    first_price = df.iloc[0]['Open']
    last_price = df.iloc[-1]['Close']
    price_change = (last_price - first_price) / first_price
    print(f'\n第一周价格变化:')
    print(f'开始价格: ${first_price:.2f}')
    print(f'结束价格: ${last_price:.2f}')
    print(f'价格变化率: {price_change:.4%}')