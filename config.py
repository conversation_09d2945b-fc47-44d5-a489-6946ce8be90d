"""
项目配置文件
从 .env 文件读取 API keys 和其他配置
"""
import os
from dotenv import load_dotenv

# 加载 .env 文件
load_dotenv()

# API Keys
ALPHAVANTAGE_API_KEY = os.getenv('ALPHAVANTAGE_API_KEY')
ZHIPU_AI_API_KEY = os.getenv('ZHIPU_AI_API_KEY')
ZHIPUAI_API_KEY = os.getenv('ZHIPUAI_API_KEY')
GEMINI_API_KEY = os.getenv('GEMINI_API_KEY')
GROQ_API_KEY = os.getenv('GROQ_API_KEY')
POLYGON_API_KEY = os.getenv('POLYGON_API_KEY')

# Server URLs
EMBEDDING_SERVER_URL = os.getenv('EMBEDDING_SERVER_URL')

# 数据目录配置
PROJECT_ROOT = os.path.dirname(os.path.abspath(__file__))
DATA_DIR = os.path.join(PROJECT_ROOT, 'data')

# 验证必要的 API keys
if not ALPHAVANTAGE_API_KEY or ALPHAVANTAGE_API_KEY == "YOUR_ALPHA_VANTAGE_API_KEY":
    print("Warning: ALPHAVANTAGE_API_KEY not properly configured in .env file")

if not ZHIPU_AI_API_KEY or ZHIPU_AI_API_KEY == "YOUR_ZHIPU_AI_API_KEY":
    print("Warning: ZHIPU_AI_API_KEY not properly configured in .env file")
