#!/usr/bin/env python3
"""
Log Section Extractor
从OPRO系统日志中提取阶段分析段落

提取规则:
- 起始标记: 包含"阶段X:"的行
- 结束标记: 包含"检测到周末"的行
- 按顺序提取所有匹配的段落
"""
import re
import os
import glob
from typing import List, Dict, Optional
from pathlib import Path


class LogSectionExtractor:
    """日志段落提取器"""
    
    def __init__(self):
        self.start_pattern = re.compile(r'阶段\d+:')
        self.end_pattern = re.compile(r'检测到周末')
    
    def extract_sections_from_file(self, file_path: str) -> List[Dict[str, any]]:
        """
        从单个日志文件中提取所有段落
        
        Args:
            file_path: 日志文件路径
            
        Returns:
            段落列表，每个段落包含起始行号、结束行号和内容
        """
        sections = []
        current_section = None
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    
                    # 检查起始标记
                    if self.start_pattern.search(line):
                        # 如果有未完成的段落，先保存它（不完整段落）
                        if current_section:
                            current_section['status'] = 'incomplete'
                            current_section['end_line'] = line_num - 1
                            sections.append(current_section)
                        
                        # 开始新段落
                        current_section = {
                            'start_line': line_num,
                            'start_content': line,
                            'content': [line],
                            'file_path': file_path,
                            'status': 'incomplete'
                        }
                    
                    # 如果在段落中，添加内容
                    elif current_section:
                        current_section['content'].append(line)
                        
                        # 检查结束标记
                        if self.end_pattern.search(line):
                            current_section['end_line'] = line_num
                            current_section['end_content'] = line
                            current_section['status'] = 'complete'
                            sections.append(current_section)
                            current_section = None
            
            # 处理文件结束时的未完成段落
            if current_section:
                current_section['status'] = 'incomplete'
                current_section['end_line'] = line_num
                sections.append(current_section)
                
        except Exception as e:
            print(f"读取文件 {file_path} 时出错: {e}")
            return []
        
        return sections
    
    def extract_from_directory(self, directory: str, pattern: str = "opro_system_new_*.log") -> Dict[str, List[Dict]]:
        """
        从目录中的所有匹配文件提取段落
        
        Args:
            directory: 目录路径
            pattern: 文件名模式
            
        Returns:
            字典，键为文件名，值为段落列表
        """
        results = {}
        log_files = glob.glob(os.path.join(directory, pattern))
        log_files.sort()  # 按文件名排序
        
        for file_path in log_files:
            filename = os.path.basename(file_path)
            sections = self.extract_sections_from_file(file_path)
            if sections:
                results[filename] = sections
                print(f"从 {filename} 中提取了 {len(sections)} 个段落")
        
        return results
    
    def save_sections_to_files(self, sections: List[Dict], output_dir: str, base_name: str):
        """
        将提取的段落保存到单独的文件中
        
        Args:
            sections: 段落列表
            output_dir: 输出目录
            base_name: 基础文件名
        """
        os.makedirs(output_dir, exist_ok=True)
        
        for i, section in enumerate(sections, 1):
            if section['status'] == 'complete':
                filename = f"{base_name}_section_{i}.log"
                file_path = os.path.join(output_dir, filename)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write('\n'.join(section['content']))
                
                print(f"段落 {i} 已保存到: {filename}")
                print(f"  起始行: {section['start_line']}")
                print(f"  结束行: {section['end_line']}")
                print(f"  行数: {len(section['content'])}")
            else:
                print(f"段落 {i} 不完整，跳过保存")
    
    def print_section_summary(self, results: Dict[str, List[Dict]]):
        """打印段落提取摘要"""
        total_sections = 0
        complete_sections = 0
        
        for filename, sections in results.items():
            print(f"\n文件: {filename}")
            print("=" * 50)
            
            for i, section in enumerate(sections, 1):
                status_icon = "✅" if section['status'] == 'complete' else "⚠️"
                print(f"{status_icon} 段落 {i}: 行 {section['start_line']} -> {section.get('end_line', '?')}")
                print(f"   起始: {section['start_content'][:50]}...")
                if section['status'] == 'complete':
                    print(f"   结束: {section['end_content'][:50]}...")
                    complete_sections += 1
                print(f"   状态: {section['status']}")
                total_sections += 1
        
        print(f"\n总结:")
        print(f"总段落数: {total_sections}")
        print(f"完整段落数: {complete_sections}")
        print(f"不完整段落数: {total_sections - complete_sections}")


def main():
    """主函数"""
    extractor = LogSectionExtractor()
    
    # 当前目录
    current_dir = os.getcwd()
    
    # 提取所有匹配的log文件
    print("正在搜索OPRO系统日志文件...")
    results = extractor.extract_from_directory(current_dir)
    
    if not results:
        print("未找到匹配的日志文件")
        return
    
    # 打印摘要
    extractor.print_section_summary(results)
    
    # 自动保存所有完整段落到文件
    print("\n自动保存所有完整段落到文件...")
    
    if True:  # 总是保存
        output_dir = "extracted_log_sections"
        
        for filename, sections in results.items():
            base_name = filename.replace('.log', '')
            complete_sections = [s for s in sections if s['status'] == 'complete']
            
            if complete_sections:
                print(f"\n保存来自 {filename} 的段落...")
                extractor.save_sections_to_files(complete_sections, output_dir, base_name)
        
        print(f"\n所有段落已保存到目录: {output_dir}")


if __name__ == "__main__":
    main()