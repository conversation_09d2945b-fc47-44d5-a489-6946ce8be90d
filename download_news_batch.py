#!/usr/bin/env python3
"""
批量下载多个股票的新闻数据
用法: python download_news_batch.py
"""

import subprocess
import sys
import os
from datetime import datetime

def run_time_migration(stock, script_dir):
    """运行时间格式迁移"""
    try:
        migration_script = os.path.join(script_dir, "data", "migrate_time_format.py")
        
        if not os.path.exists(migration_script):
            print(f"⚠️ 迁移脚本不存在: {migration_script}")
            return False
        
        result = subprocess.run([
            sys.executable,
            migration_script,
            stock,
            "--quiet"
        ], capture_output=True, text=True, timeout=300)  # 5分钟超时
        
        return result.returncode == 0
        
    except Exception as e:
        print(f"❌ {stock} 时间格式迁移异常: {e}")
        return False

def download_news_for_stocks(auto_migrate=True):
    """批量下载指定股票的新闻数据"""
    
    # 股票列表和日期范围
    stocks = ["GOOG", "NVDA", "META", "MSFT"]
    start_date = "2024-08-01"
    end_date = "2024-12-30"
    
    print(f"开始批量下载新闻数据...")
    print(f"股票: {', '.join(stocks)}")
    print(f"日期范围: {start_date} 到 {end_date}")
    print("=" * 50)
    
    # 获取项目根目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    news_script_path = os.path.join(script_dir, "data", "get_news_data.py")
    
    if not os.path.exists(news_script_path):
        print(f"错误: 找不到新闻下载脚本 {news_script_path}")
        return False
    
    success_count = 0
    total_count = len(stocks)
    
    for i, stock in enumerate(stocks, 1):
        print(f"\n[{i}/{total_count}] 正在下载 {stock} 的新闻数据...")
        print(f"命令: python {news_script_path} {stock} {start_date} {end_date}")
        
        try:
            # 运行新闻下载脚本
            result = subprocess.run([
                sys.executable, 
                news_script_path,
                stock,
                start_date,
                end_date
            ], capture_output=True, text=True, timeout=1800)  # 30分钟超时
            
            if result.returncode == 0:
                print(f"✅ {stock} 新闻数据下载完成")
                success_count += 1
                
                # 自动运行时间格式迁移（如果启用）
                if auto_migrate:
                    print(f"🔄 开始 {stock} 时间格式迁移...")
                    migration_result = run_time_migration(stock, script_dir)
                    if migration_result:
                        print(f"✅ {stock} 时间格式迁移完成")
                    else:
                        print(f"⚠️ {stock} 时间格式迁移可能存在问题")
                else:
                    print(f"ℹ️ 跳过 {stock} 自动时间格式迁移")
            else:
                print(f"❌ {stock} 新闻数据下载失败")
                print(f"错误信息: {result.stderr}")
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {stock} 新闻数据下载超时")
        except Exception as e:
            print(f"❌ {stock} 新闻数据下载异常: {e}")
    
    # 汇总结果
    print("\n" + "=" * 50)
    print(f"批量下载完成！")
    print(f"成功: {success_count}/{total_count} 只股票")
    print(f"失败: {total_count - success_count}/{total_count} 只股票")
    
    return success_count == total_count

if __name__ == "__main__":
    # 检查命令行参数
    auto_migrate = True
    if len(sys.argv) > 1 and "--no-migrate" in sys.argv:
        auto_migrate = False
        print("ℹ️ 禁用自动时间格式迁移")
    
    try:
        success = download_news_for_stocks(auto_migrate=auto_migrate)
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n用户中断下载")
        sys.exit(1)
    except Exception as e:
        print(f"批量下载异常: {e}")
        sys.exit(1)