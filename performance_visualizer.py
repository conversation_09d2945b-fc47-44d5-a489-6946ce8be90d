#!/usr/bin/env python3
"""
Performance Visualizer
为股票交易表现分析创建可视化图表
"""
import json
import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from pathlib import Path
import pandas as pd

# 设置中文字体支持
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PerformanceVisualizer:
    """表现可视化器"""
    
    def __init__(self, data_file: str):
        """初始化，加载数据"""
        with open(data_file, 'r', encoding='utf-8') as f:
            self.data = json.load(f)
        
        # 提取股票映射
        self.stock_mapping = {}
        for log_file, perf_data in self.data.items():
            stock = perf_data['stock_symbol']
            self.stock_mapping[log_file] = stock
    
    def create_summary_comparison(self):
        """创建关键指标对比图"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('股票交易表现关键指标对比', fontsize=16, fontweight='bold')
        
        stocks = []
        annual_returns = []
        sharpe_ratios = []
        max_drawdowns = []
        win_rates = []
        
        for log_file, perf_data in self.data.items():
            stocks.append(perf_data['stock_symbol'])
            annual_returns.append(perf_data['annualized_return'] * 100)
            sharpe_ratios.append(perf_data['annualized_sharpe'])
            max_drawdowns.append(perf_data['max_drawdown'] * 100)
            win_rates.append(perf_data['win_rate'] * 100)
        
        # 年化收益率
        colors = ['#2E8B57', '#4169E1', '#DC143C', '#FF8C00']
        bars1 = ax1.bar(stocks, annual_returns, color=colors, alpha=0.7)
        ax1.set_title('年化收益率 (%)', fontweight='bold')
        ax1.set_ylabel('收益率 (%)')
        ax1.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, value in zip(bars1, annual_returns):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + (1 if height >= 0 else -3),
                    f'{value:.1f}%', ha='center', va='bottom' if height >= 0 else 'top')
        
        # 夏普率
        bars2 = ax2.bar(stocks, sharpe_ratios, color=colors, alpha=0.7)
        ax2.set_title('年化夏普率', fontweight='bold')
        ax2.set_ylabel('夏普率')
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)
        ax2.grid(True, alpha=0.3)
        
        for bar, value in zip(bars2, sharpe_ratios):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + (0.05 if height >= 0 else -0.05),
                    f'{value:.3f}', ha='center', va='bottom' if height >= 0 else 'top')
        
        # 最大回撤
        bars3 = ax3.bar(stocks, max_drawdowns, color=colors, alpha=0.7)
        ax3.set_title('最大回撤 (%)', fontweight='bold')
        ax3.set_ylabel('回撤 (%)')
        ax3.grid(True, alpha=0.3)
        
        for bar, value in zip(bars3, max_drawdowns):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{value:.1f}%', ha='center', va='bottom')
        
        # 胜率
        bars4 = ax4.bar(stocks, win_rates, color=colors, alpha=0.7)
        ax4.set_title('胜率 (%)', fontweight='bold')
        ax4.set_ylabel('胜率 (%)')
        ax4.grid(True, alpha=0.3)
        
        for bar, value in zip(bars4, win_rates):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{value:.1f}%', ha='center', va='bottom')
        
        plt.tight_layout()
        plt.savefig('stock_performance_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("关键指标对比图已保存: stock_performance_comparison.png")
    
    def create_cumulative_returns_chart(self):
        """创建累计收益率曲线图"""
        plt.figure(figsize=(14, 8))
        
        colors = ['#2E8B57', '#4169E1', '#DC143C', '#FF8C00']
        
        for i, (log_file, perf_data) in enumerate(self.data.items()):
            stock = perf_data['stock_symbol']
            cum_returns = np.array(perf_data['cumulative_returns']) * 100
            days = range(1, len(cum_returns) + 1)
            
            plt.plot(days, cum_returns, 
                    color=colors[i], 
                    linewidth=2.5, 
                    label=f'{stock}', 
                    alpha=0.8)
        
        plt.title('累计收益率走势对比 (12周)', fontsize=16, fontweight='bold')
        plt.xlabel('交易日', fontsize=12)
        plt.ylabel('累计收益率 (%)', fontsize=12)
        plt.legend(fontsize=11)
        plt.grid(True, alpha=0.3)
        plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # 添加周标记
        for week in range(0, 13):
            plt.axvline(x=week*5, color='gray', linestyle='--', alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('cumulative_returns_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("累计收益率曲线图已保存: cumulative_returns_comparison.png")
    
    def create_risk_return_scatter(self):
        """创建风险收益散点图"""
        plt.figure(figsize=(12, 8))
        
        risks = []
        returns = []
        stocks = []
        colors = ['#2E8B57', '#4169E1', '#DC143C', '#FF8C00']
        
        for log_file, perf_data in self.data.items():
            risks.append(perf_data['volatility'] * 100)
            returns.append(perf_data['annualized_return'] * 100)
            stocks.append(perf_data['stock_symbol'])
        
        for i, (risk, ret, stock) in enumerate(zip(risks, returns, stocks)):
            plt.scatter(risk, ret, 
                       s=200, 
                       color=colors[i], 
                       alpha=0.7, 
                       edgecolors='black',
                       linewidth=1)
            plt.annotate(stock, 
                        (risk, ret), 
                        xytext=(5, 5), 
                        textcoords='offset points',
                        fontsize=11,
                        fontweight='bold')
        
        plt.title('风险-收益散点图', fontsize=16, fontweight='bold')
        plt.xlabel('年化波动率 (%)', fontsize=12)
        plt.ylabel('年化收益率 (%)', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        plt.axvline(x=0, color='black', linestyle='-', alpha=0.5)
        
        plt.tight_layout()
        plt.savefig('risk_return_scatter.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("风险收益散点图已保存: risk_return_scatter.png")
    
    def create_performance_heatmap(self):
        """创建表现热力图"""
        # 准备数据
        metrics = ['年化收益率', '夏普率', '最大回撤', '胜率', '波动率']
        stocks = []
        data_matrix = []
        
        for log_file, perf_data in self.data.items():
            stock = perf_data['stock_symbol']
            stocks.append(stock)
            
            # 标准化数据 (为了更好的可视化)
            row = [
                perf_data['annualized_return'] * 100,
                perf_data['annualized_sharpe'],
                -perf_data['max_drawdown'] * 100,  # 负值，因为回撤越小越好
                perf_data['win_rate'] * 100,
                -perf_data['volatility'] * 100  # 负值，因为波动率越小越好
            ]
            data_matrix.append(row)
        
        # 创建DataFrame
        df = pd.DataFrame(data_matrix, index=stocks, columns=metrics)
        
        # 创建热力图
        plt.figure(figsize=(10, 6))
        sns.heatmap(df, 
                   annot=True, 
                   fmt='.1f', 
                   cmap='RdYlGn', 
                   center=0,
                   cbar_kws={'label': '表现分数'},
                   linewidths=0.5)
        
        plt.title('股票交易表现热力图', fontsize=16, fontweight='bold')
        plt.ylabel('股票', fontsize=12)
        plt.xlabel('表现指标', fontsize=12)
        
        plt.tight_layout()
        plt.savefig('performance_heatmap.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("表现热力图已保存: performance_heatmap.png")
    
    def generate_all_charts(self):
        """生成所有图表"""
        print("生成股票交易表现可视化图表...")
        print("1. 关键指标对比图")
        self.create_summary_comparison()
        
        print("\n2. 累计收益率曲线图")
        self.create_cumulative_returns_chart()
        
        print("\n3. 风险收益散点图")
        self.create_risk_return_scatter()
        
        print("\n4. 表现热力图")
        self.create_performance_heatmap()
        
        print("\n所有图表生成完成！")


def main():
    """主函数"""
    data_file = "financial_performance_data.json"
    
    if not Path(data_file).exists():
        print(f"错误: 找不到数据文件 {data_file}")
        print("请先运行 financial_performance_analyzer.py")
        return
    
    visualizer = PerformanceVisualizer(data_file)
    visualizer.generate_all_charts()


if __name__ == "__main__":
    main()