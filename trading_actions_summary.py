#!/usr/bin/env python3
"""
<PERSON>ript to extract trading actions from phase1 test results for all four stocks.
Extracts 80 trading days of decisions for AAPL, GOOG, NVDA, and META.
"""

import re
import json
from pathlib import Path

def extract_trading_actions_from_file(file_path):
    """Extract trading actions with dates from a single phase1 test result file."""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Extract stock symbol from filename
    stock_symbol = file_path.stem.split('_')[0]
    
    # Find all trading dates first
    date_pattern = r'🤖 TRA 输入: 日期=([0-9-]+),'
    dates = re.findall(date_pattern, content)
    
    # Pattern to find TRA decisions with trading actions
    action_pattern = r'✅ 决策层智能体 TRA 完成: (\w+).*?🔍 调试: 找到trading_actions字段=({[^}]+})'
    action_matches = re.findall(action_pattern, content, re.DOTALL)
    
    trading_actions = []
    for i, ((action, trading_dict_str), date) in enumerate(zip(action_matches, dates), 1):
        try:
            # Parse the trading dictionary
            trading_dict = eval(trading_dict_str)
            
            # Determine position size and direction
            if '__HOLD__' in trading_dict:
                position_size = 0
                direction = 'hold'
            else:
                position_size = list(trading_dict.values())[0]
                direction = 'buy' if position_size > 0 else 'sell'
            
            trading_actions.append({
                'day': i,
                'date': date,
                'action': action,
                'direction': direction, 
                'position_size': abs(position_size),
                'trading_dict': trading_dict
            })
        except Exception as e:
            print(f"Error parsing trading action for day {i} in {stock_symbol}: {e}")
            continue
    
    return stock_symbol, trading_actions

def main():
    """Extract and summarize trading actions for all stocks."""
    test_results_dir = Path('/Users/<USER>/Code/Multi_Agent_Optimization/test_results')
    phase1_files = list(test_results_dir.glob('*_phase1.md'))
    
    all_trading_data = {}
    
    for file_path in phase1_files:
        stock_symbol, trading_actions = extract_trading_actions_from_file(file_path)
        all_trading_data[stock_symbol] = trading_actions
        print(f"Extracted {len(trading_actions)} trading days for {stock_symbol}")
    
    # Create summary
    print("\n" + "="*80)
    print("交易动作汇总 - 80个交易日")
    print("="*80)
    
    for stock in ['AAPL', 'GOOG', 'NVDA', 'META']:
        if stock in all_trading_data:
            actions = all_trading_data[stock]
            print(f"\n{stock} 股票交易动作:")
            print("-" * 50)
            
            # Count actions
            buy_count = sum(1 for a in actions if a['direction'] == 'buy')
            sell_count = sum(1 for a in actions if a['direction'] == 'sell') 
            hold_count = sum(1 for a in actions if a['direction'] == 'hold')
            
            print(f"总交易日: {len(actions)}")
            print(f"买入次数: {buy_count}")
            print(f"卖出次数: {sell_count}")
            print(f"持有次数: {hold_count}")
            
            # Show first 10 days as example
            print(f"\n前10个交易日明细:")
            for action in actions[:10]:
                print(f"第{action['day']:2d}天 ({action['date']}): {action['direction']:4s} - {action['trading_dict']}")
            
            if len(actions) > 10:
                print("...")
                # Show last few days
                print(f"最后3个交易日:")
                for action in actions[-3:]:
                    print(f"第{action['day']:2d}天 ({action['date']}): {action['direction']:4s} - {action['trading_dict']}")
    
    # Save detailed data to JSON
    output_file = '/Users/<USER>/Code/Multi_Agent_Optimization/trading_actions_80days.json'
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(all_trading_data, f, ensure_ascii=False, indent=2)
    
    print(f"\n详细数据已保存到: {output_file}")

if __name__ == "__main__":
    main()