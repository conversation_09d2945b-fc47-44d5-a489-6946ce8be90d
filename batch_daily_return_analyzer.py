#!/usr/bin/env python3
"""
批量日收益分析器 - 批量处理多个股票的测试结果文件
支持.md日志文件和.csv净值变化文件
"""

import os
import re
import csv
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import argparse
import json
from pathlib import Path


@dataclass
class DailyReturn:
    """日收益数据类"""
    date: str
    cumulative_return: float
    week_return: float
    daily_return: float
    week_number: int


@dataclass
class WeeklyPerformance:
    """周度性能数据类"""
    week_number: int
    weekly_return: float
    sharpe_ratio: float
    trading_days: int


class BatchDailyReturnAnalyzer:
    """批量日收益分析器"""
    
    def __init__(self, output_dir: str = "daily_analysis_results"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.results = {}
        
    def detect_file_type(self, file_path: str) -> str:
        """检测文件类型"""
        if file_path.endswith('.csv'):
            return 'csv'
        elif file_path.endswith('.md'):
            return 'md'
        else:
            raise ValueError(f"不支持的文件类型: {file_path}")
    
    def extract_symbol_from_filename(self, file_path: str) -> str:
        """从文件名提取股票代码"""
        filename = Path(file_path).stem
        
        # 尝试匹配常见的股票代码模式
        for symbol in ['AAPL', 'META', 'NVDA', 'GOOG', 'GOOGL', 'TSLA', 'MSFT', 'AMZN']:
            if symbol in filename.upper():
                return symbol
        
        # 如果没有找到，使用文件名的第一部分
        return filename.split('_')[0].upper()
    
    def parse_md_log_file(self, file_path: str) -> Tuple[List[DailyReturn], List[WeeklyPerformance]]:
        """解析MD日志文件"""
        daily_data = []
        weekly_data = []
        
        # 正则表达式模式
        tra_input_pattern = r"🤖 TRA 输入: 日期=(\d{4}-\d{2}-\d{2}), 累计收益=([-+]?\d*\.?\d+), 周收益=([-+]?\d*\.?\d+)"
        weekly_performance_pattern = r"第 (\d+) 周性能分析.*?周总收益率: ([-+]?\d*\.?\d+)"
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            
        # 提取每日TRA输入数据
        tra_matches = re.findall(tra_input_pattern, content)
        
        # 提取每周性能数据
        weekly_matches = re.findall(weekly_performance_pattern, content)
        
        # 解析每周性能数据
        for week_num, weekly_return in weekly_matches:
            # 查找对应的夏普比率和交易天数
            week_pattern = f"第 {week_num} 周性能分析.*?周总收益率: {re.escape(weekly_return)}.*?周夏普比率: ([-+]?\\d*\\.?\\d+).*?交易天数: (\\d+)"
            week_detail = re.search(week_pattern, content, re.DOTALL)
            
            if week_detail:
                sharpe_ratio = float(week_detail.group(1))
                trading_days = int(week_detail.group(2))
            else:
                sharpe_ratio = 0.0
                trading_days = 5
                
            weekly_data.append(WeeklyPerformance(
                week_number=int(week_num),
                weekly_return=float(weekly_return),
                sharpe_ratio=sharpe_ratio,
                trading_days=trading_days
            ))
        
        # 解析每日数据并计算日收益
        previous_cumulative = 0.0
        current_week_number = 1
        current_week_start = None
        
        for i, (date_str, cumulative_str, week_str) in enumerate(tra_matches):
            cumulative_return = float(cumulative_str)
            week_return = float(week_str)
            
            # 计算日收益 = 当日累计收益 - 前一日累计收益
            daily_return = cumulative_return - previous_cumulative
            
            # 解析日期
            current_date = datetime.strptime(date_str, '%Y-%m-%d')
            
            # 确定周数 - 基于日期的周一为一周开始
            if current_week_start is None:
                current_week_start = current_date - timedelta(days=current_date.weekday())
                current_week_number = 1
            else:
                week_start = current_date - timedelta(days=current_date.weekday())
                if week_start > current_week_start:
                    current_week_number += 1
                    current_week_start = week_start
            
            daily_data.append(DailyReturn(
                date=date_str,
                cumulative_return=cumulative_return,
                week_return=week_return,
                daily_return=daily_return,
                week_number=current_week_number
            ))
            
            previous_cumulative = cumulative_return
        
        return daily_data, weekly_data
    
    def parse_csv_file(self, file_path: str) -> List[DailyReturn]:
        """解析CSV净值变化文件"""
        daily_data = []
        
        with open(file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            
            # 按天数分组
            days_data = {}
            for row in reader:
                day_num = int(row['day_number'])
                return_rate = float(row['return_rate'])
                
                if day_num not in days_data:
                    days_data[day_num] = []
                days_data[day_num].append(return_rate)
            
            # 计算每天的平均收益率（如果有多个记录）
            previous_cumulative = 0.0
            current_week_number = 1
            
            for day_num in sorted(days_data.keys()):
                returns = days_data[day_num]
                daily_return = sum(returns) / len(returns)  # 取平均值
                cumulative_return = previous_cumulative + daily_return
                
                # 简单的周数计算（每5个交易日为一周）
                current_week_number = (day_num - 1) // 5 + 1
                
                # 生成虚拟日期
                base_date = datetime(2025, 1, 2)  # 假设从2025年1月2日开始
                current_date = base_date + timedelta(days=day_num-1)
                
                daily_data.append(DailyReturn(
                    date=current_date.strftime('%Y-%m-%d'),
                    cumulative_return=cumulative_return,
                    week_return=0.0,  # CSV中没有周收益信息
                    daily_return=daily_return,
                    week_number=current_week_number
                ))
                
                previous_cumulative = cumulative_return
        
        return daily_data
    
    def validate_weekly_consistency(self, daily_data: List[DailyReturn], 
                                  weekly_data: List[WeeklyPerformance]) -> Dict[int, Dict[str, float]]:
        """验证每周累计收益与周总收益率的一致性"""
        validation_results = {}
        
        # 按周分组日数据
        weeks_data = {}
        for daily in daily_data:
            week = daily.week_number
            if week not in weeks_data:
                weeks_data[week] = []
            weeks_data[week].append(daily)
        
        # 验证每周数据
        for week_num, daily_list in weeks_data.items():
            if not daily_list:
                continue
                
            # 计算该周最后一天的周收益（应该是周累计收益）
            last_day_week_return = daily_list[-1].week_return
            
            # 计算该周所有日收益之和
            calculated_week_return = sum(d.daily_return for d in daily_list)
            
            # 查找对应的周度性能数据
            weekly_performance = next(
                (w for w in weekly_data if w.week_number == week_num), 
                None
            )
            
            official_week_return = weekly_performance.weekly_return if weekly_performance else 0.0
            
            validation_results[week_num] = {
                'last_day_week_return': last_day_week_return,
                'calculated_sum': calculated_week_return,
                'official_weekly_return': official_week_return,
                'consistent_with_last_day': abs(last_day_week_return - official_week_return) < 1e-6,
                'consistent_with_sum': abs(calculated_week_return - official_week_return) < 1e-6
            }
        
        return validation_results
    
    def generate_daily_breakdown(self, daily_data: List[DailyReturn], 
                               weekly_data: List[WeeklyPerformance]) -> Dict:
        """生成每日收益分解报告"""
        
        # 按周分组
        weeks_breakdown = {}
        
        for daily in daily_data:
            week = daily.week_number
            if week not in weeks_breakdown:
                weeks_breakdown[week] = {
                    'week_number': week,
                    'daily_returns': [],
                    'total_week_return': 0.0,
                    'official_week_return': 0.0
                }
            
            weeks_breakdown[week]['daily_returns'].append({
                'date': daily.date,
                'daily_return': round(daily.daily_return, 6),
                'cumulative_return': round(daily.cumulative_return, 6),
                'week_return': round(daily.week_return, 6)
            })
        
        # 添加官方周收益数据
        for week_num, week_data in weeks_breakdown.items():
            weekly_perf = next(
                (w for w in weekly_data if w.week_number == week_num), 
                None
            )
            if weekly_perf:
                week_data['official_week_return'] = weekly_perf.weekly_return
                week_data['sharpe_ratio'] = weekly_perf.sharpe_ratio
                week_data['trading_days'] = weekly_perf.trading_days
            
            # 计算日收益总和
            week_data['calculated_week_return'] = sum(
                d['daily_return'] for d in week_data['daily_returns']
            )
        
        return {
            'summary': {
                'total_weeks': len(weeks_breakdown),
                'total_trading_days': len(daily_data),
                'total_cumulative_return': daily_data[-1].cumulative_return if daily_data else 0.0
            },
            'weekly_breakdown': weeks_breakdown,
            'validation': self.validate_weekly_consistency(daily_data, weekly_data)
        }
    
    def process_single_file(self, file_path: str) -> Dict:
        """处理单个文件"""
        file_type = self.detect_file_type(file_path)
        symbol = self.extract_symbol_from_filename(file_path)
        
        print(f"处理 {symbol} - {file_path} ({file_type})")
        
        if file_type == 'md':
            daily_data, weekly_data = self.parse_md_log_file(file_path)
        elif file_type == 'csv':
            daily_data = self.parse_csv_file(file_path)
            weekly_data = []  # CSV文件没有周度性能数据
        else:
            raise ValueError(f"不支持的文件类型: {file_type}")
        
        # 生成分析报告
        results = self.generate_daily_breakdown(daily_data, weekly_data)
        results['symbol'] = symbol
        results['file_path'] = file_path
        results['file_type'] = file_type
        
        return results
    
    def batch_process(self, file_paths: List[str]) -> None:
        """批量处理多个文件"""
        
        print("=" * 60)
        print("📊 批量日收益分析开始")
        print("=" * 60)
        
        for file_path in file_paths:
            try:
                symbol = self.extract_symbol_from_filename(file_path)
                results = self.process_single_file(file_path)
                self.results[symbol] = results
                
                # 保存单个股票的详细结果
                output_file = self.output_dir / f"{symbol}_daily_breakdown.json"
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(results, f, ensure_ascii=False, indent=2)
                print(f"✅ {symbol} 分析完成，结果保存到: {output_file}")
                
            except Exception as e:
                print(f"❌ 处理 {file_path} 时发生错误: {e}")
        
        # 生成汇总报告
        self.generate_summary_report()
        
        print("=" * 60)
        print("📋 批量处理完成")
        print("=" * 60)
    
    def generate_summary_report(self) -> None:
        """生成汇总报告"""
        summary_data = {
            'processed_files': len(self.results),
            'symbols': list(self.results.keys()),
            'comparison': {}
        }
        
        # 比较各股票的关键指标
        for symbol, data in self.results.items():
            summary = data['summary']
            summary_data['comparison'][symbol] = {
                'total_weeks': summary['total_weeks'],
                'total_trading_days': summary['total_trading_days'],
                'total_cumulative_return': summary['total_cumulative_return'],
                'file_type': data['file_type'],
                'inconsistent_weeks': len([
                    week for week, validation in data['validation'].items()
                    if not validation.get('consistent_with_sum', True)
                ])
            }
        
        # 保存汇总报告
        summary_file = self.output_dir / "batch_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, ensure_ascii=False, indent=2)
        
        print(f"📊 汇总报告保存到: {summary_file}")
        
        # 打印简要汇总
        print("\n📈 处理结果汇总:")
        print("-" * 60)
        for symbol, comp_data in summary_data['comparison'].items():
            print(f"{symbol:6s}: {comp_data['total_weeks']:2d}周, "
                  f"{comp_data['total_trading_days']:3d}天, "
                  f"累计收益: {comp_data['total_cumulative_return']:8.4f}, "
                  f"类型: {comp_data['file_type']}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='批量分析多个股票的日收益数据')
    parser.add_argument('files', nargs='+', help='要处理的文件路径列表')
    parser.add_argument('-o', '--output', default='daily_analysis_results', 
                       help='输出目录路径 (默认: daily_analysis_results)')
    
    args = parser.parse_args()
    
    try:
        analyzer = BatchDailyReturnAnalyzer(args.output)
        analyzer.batch_process(args.files)
        
    except Exception as e:
        print(f"❌ 批量处理过程中发生错误: {e}")


if __name__ == "__main__":
    main()