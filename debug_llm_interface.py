#!/usr/bin/env python3
"""
调试LLM接口创建过程
"""

import sys
import os
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from contribution_assessment.service_factory import ServiceFactory
from contribution_assessment.refactored_assessor import RefactoredContributionAssessor

def debug_llm_interface_creation():
    """调试LLM接口创建过程"""
    print("=== 调试LLM接口创建过程 ===")
    
    # 创建日志器
    logger = logging.getLogger("debug_llm")
    logger.setLevel(logging.DEBUG)
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    
    # 测试1: 直接创建ServiceFactory和AgentCreationService
    print("\n--- 测试1: 直接创建ServiceFactory ---")
    try:
        factory = ServiceFactory()
        factory.configure_services({})
        
        # 测试有llm_provider的情况
        print("测试有llm_provider的情况:")
        agent_service = factory.create_agent_creation_service(
            llm_provider="zhipuai",
            logger=logger,
            enable_opro=False
        )
        print(f"AgentCreationService创建成功，llm_interface: {agent_service.llm_interface}")
        print(f"llm_interface类型: {type(agent_service.llm_interface)}")
        
        # 测试没有llm_provider的情况
        print("\n测试没有llm_provider的情况:")
        agent_service_none = factory.create_agent_creation_service(
            llm_provider=None,
            logger=logger,
            enable_opro=False
        )
        print(f"AgentCreationService创建成功，llm_interface: {agent_service_none.llm_interface}")
        print(f"llm_interface类型: {type(agent_service_none.llm_interface)}")
        
    except Exception as e:
        print(f"ServiceFactory测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试2: 通过RefactoredContributionAssessor创建智能体
    print("\n--- 测试2: 通过RefactoredContributionAssessor创建智能体 ---")
    try:
        config = {
            "stocks": ["NVDA"],
            "start_date": "2025-01-01",
            "end_date": "2025-01-10"
        }
        
        # 测试有llm_provider的情况
        print("测试有llm_provider的情况:")
        assessor = RefactoredContributionAssessor(
            config=config,
            logger=logger,
            llm_provider="zhipuai",
            enable_opro=False
        )
        
        agents = assessor._create_agents()
        print(f"智能体创建成功: {list(agents.keys())}")
        
        # 检查智能体的LLM接口
        for agent_id, agent in agents.items():
            has_llm = hasattr(agent, 'llm_interface') and agent.llm_interface is not None
            print(f"智能体 {agent_id}: has_llm_interface={has_llm}")
            if has_llm:
                print(f"  LLM接口类型: {type(agent.llm_interface)}")
                print(f"  provider: {getattr(agent.llm_interface, 'provider', 'unknown')}")
        
        # 测试没有llm_provider的情况
        print("\n测试没有llm_provider的情况:")
        assessor_none = RefactoredContributionAssessor(
            config=config,
            logger=logger,
            llm_provider=None,
            enable_opro=False
        )
        
        agents_none = assessor_none._create_agents()
        print(f"智能体创建成功: {list(agents_none.keys())}")
        
        # 检查智能体的LLM接口
        for agent_id, agent in agents_none.items():
            has_llm = hasattr(agent, 'llm_interface') and agent.llm_interface is not None
            print(f"智能体 {agent_id}: has_llm_interface={has_llm}")
            if has_llm:
                print(f"  LLM接口类型: {type(agent.llm_interface)}")
                print(f"  provider: {getattr(agent.llm_interface, 'provider', 'unknown')}")
        
    except Exception as e:
        print(f"RefactoredContributionAssessor测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_llm_interface_creation()