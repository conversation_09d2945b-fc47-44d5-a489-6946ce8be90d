#!/usr/bin/env python3
"""
生成批量分析的可读报告
"""

import json
from pathlib import Path
import argparse


def generate_readable_report(results_dir: str = "daily_analysis_results"):
    """生成可读的分析报告"""
    results_path = Path(results_dir)
    
    # 读取汇总数据
    summary_file = results_path / "batch_summary.json"
    if not summary_file.exists():
        print(f"❌ 找不到汇总文件: {summary_file}")
        return
    
    with open(summary_file, 'r', encoding='utf-8') as f:
        summary = json.load(f)
    
    print("=" * 80)
    print("📊 批量日收益分析报告")
    print("=" * 80)
    
    print(f"处理文件数量: {summary['processed_files']}")
    print(f"分析股票代码: {', '.join(summary['symbols'])}")
    print()
    
    # 详细对比表格
    print("📈 各股票性能对比:")
    print("-" * 80)
    print(f"{'股票':^6} {'周数':^4} {'交易日':^6} {'累计收益':^12} {'文件类型':^8} {'不一致周数':^10}")
    print("-" * 80)
    
    for symbol, data in summary['comparison'].items():
        print(f"{symbol:^6} {data['total_weeks']:^4} {data['total_trading_days']:^6} "
              f"{data['total_cumulative_return']:^12.4f} {data['file_type']:^8} "
              f"{data['inconsistent_weeks']:^10}")
    
    print("-" * 80)
    
    # 为每个股票生成详细报告
    print("\n📋 各股票详细分析:")
    print("=" * 80)
    
    for symbol in summary['symbols']:
        detail_file = results_path / f"{symbol}_daily_breakdown.json"
        if not detail_file.exists():
            continue
            
        with open(detail_file, 'r', encoding='utf-8') as f:
            detail_data = json.load(f)
        
        print(f"\n🔍 {symbol} 详细分析:")
        print("-" * 40)
        
        summary_data = detail_data['summary']
        print(f"总周数: {summary_data['total_weeks']}")
        print(f"总交易日数: {summary_data['total_trading_days']}")
        print(f"总累计收益: {summary_data['total_cumulative_return']:.6f}")
        
        # 显示前5周的每日收益
        print(f"\n📅 前5周每日收益明细:")
        weekly_breakdown = detail_data['weekly_breakdown']
        
        week_count = 0
        for week_num in sorted([int(k) for k in weekly_breakdown.keys()]):
            if week_count >= 5:
                break
                
            week_data = weekly_breakdown[str(week_num)]
            print(f"  第{week_num}周 (官方周收益: {week_data.get('official_week_return', 0):.4f}):")
            
            for day in week_data['daily_returns']:
                print(f"    {day['date']}: {day['daily_return']:8.6f}")
            
            week_count += 1
        
        # 验证结果摘要
        validation = detail_data.get('validation', {})
        inconsistent_count = sum(1 for v in validation.values() 
                               if not v.get('consistent_with_sum', True))
        
        if inconsistent_count > 0:
            print(f"\n⚠️  发现 {inconsistent_count} 周数据不一致")
        else:
            print(f"\n✅ 所有周数据一致")


def main():
    parser = argparse.ArgumentParser(description='生成批量分析的可读报告')
    parser.add_argument('-d', '--dir', default='daily_analysis_results',
                       help='结果目录路径')
    
    args = parser.parse_args()
    generate_readable_report(args.dir)


if __name__ == "__main__":
    main()