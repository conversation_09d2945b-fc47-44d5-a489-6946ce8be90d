#!/usr/bin/env python3
"""
提取前16周的每日收益详情分析
"""

import re
import json
import glob
import os
from datetime import datetime
import matplotlib.pyplot as plt
import matplotlib.font_manager as fm
import numpy as np
import pandas as pd
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def extract_daily_performance(log_file_path, max_weeks=16):
    """从日志文件中提取前16周的每日收益数据"""
    
    daily_data = []
    weekly_summaries = []
    
    try:
        with open(log_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"读取文件 {log_file_path} 失败: {e}")
        return daily_data, weekly_summaries
    
    # 提取每日交易和收益信息
    daily_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) - __main__ - INFO - 📈 第(\d+)天交易动作: ({.*?})\n.*?💰 第\d+天净值变化: 收益率=([-\d\.]+), 净值=\$([,\d\.]+)'
    
    daily_matches = re.findall(daily_pattern, content, re.DOTALL)
    
    # 提取周性能分析
    weekly_pattern = r'============================================================\n.*?第 (\d+) 周性能分析 - 联盟: (.*?)\n.*?周总收益率: ([-\d\.]+)\n.*?周夏普比率: ([-\d\.]+)\n.*?交易天数: (\d+)\n.*?============================================================'
    
    weekly_matches = re.findall(weekly_pattern, content, re.DOTALL)
    
    # 目标联盟：完整的7个智能体
    target_agents = {'FAA', 'TAA', 'BOA', 'BeOA', 'TRA', 'NAA', 'NOA'}
    
    # 处理每日数据
    current_week = 1
    day_in_week = 1
    
    for match in daily_matches:
        timestamp, day_num, trading_action, return_rate, net_value = match
        
        day_num = int(day_num)
        return_rate = float(return_rate)
        net_value = float(net_value.replace(',', ''))
        
        # 计算是第几周第几天
        week_num = ((day_num - 1) // 5) + 1
        day_in_week = ((day_num - 1) % 5) + 1
        
        # 只保留前16周的数据
        if week_num <= max_weeks:
            daily_data.append({
                "日期": timestamp.split(',')[0].split(' ')[0],
                "时间戳": timestamp,
                "交易日": day_num,
                "周数": week_num,
                "周内天数": day_in_week,
                "交易动作": trading_action,
                "日收益率": return_rate,
                "净值": net_value,
                "累计收益率": (net_value - 1000000) / 1000000  # 假设初始净值为100万
            })
    
    # 处理周汇总数据
    for match in weekly_matches:
        week_num = int(match[0])
        coalition = match[1].strip()
        weekly_return = float(match[2])
        sharpe_ratio = float(match[3])
        trading_days = int(match[4])
        
        # 只保留前16周且是完整联盟的数据
        if week_num <= max_weeks:
            # 解析联盟信息
            if coalition.startswith("frozenset(") and coalition.endswith(")"):
                agents_str = coalition[10:-1]  # 去掉 "frozenset(" 和 ")"
                if agents_str.startswith("{") and agents_str.endswith("}"):
                    agents_str = agents_str[1:-1]  # 去掉 "{" 和 "}"
                    agents = set()
                    for agent in agents_str.split(", "):
                        agent = agent.strip().strip("'\"")
                        if agent:
                            agents.add(agent)
                    
                    # 只保留完整7个智能体的联盟记录
                    if agents == target_agents:
                        weekly_summaries.append({
                            "周数": week_num,
                            "联盟": coalition,
                            "周总收益率": weekly_return,
                            "周夏普比率": sharpe_ratio,
                            "交易天数": trading_days
                        })
    
    # 按日期排序
    daily_data.sort(key=lambda x: (x["周数"], x["周内天数"]))
    weekly_summaries.sort(key=lambda x: x["周数"])
    
    return daily_data, weekly_summaries

def plot_daily_returns(daily_data, stock_name, output_dir):
    """绘制每日收益率图表"""
    
    if not daily_data:
        return
    
    # 准备数据
    days = [d["交易日"] for d in daily_data]
    daily_returns = [d["日收益率"] * 100 for d in daily_data]
    cumulative_returns = [d["累计收益率"] * 100 for d in daily_data]
    
    # 创建子图
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))
    
    # 每日收益率柱状图
    colors = ['green' if r >= 0 else 'red' for r in daily_returns]
    bars = ax1.bar(days, daily_returns, color=colors, alpha=0.7, width=0.8)
    
    ax1.set_title(f'{stock_name} - 前16周每日收益率', fontsize=14, fontweight='bold')
    ax1.set_xlabel('交易日')
    ax1.set_ylabel('日收益率 (%)')
    ax1.grid(True, alpha=0.3)
    ax1.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    
    # 添加周分隔线
    for week in range(1, 17):
        week_start = (week - 1) * 5 + 1
        if week_start <= len(days):
            ax1.axvline(x=week_start - 0.5, color='blue', linestyle='--', alpha=0.3)
    
    # 累计收益率曲线
    ax2.plot(days, cumulative_returns, marker='o', linewidth=2, markersize=3, color='blue')
    ax2.fill_between(days, cumulative_returns, alpha=0.3, color='blue')
    
    ax2.set_title(f'{stock_name} - 前16周累计收益率', fontsize=14, fontweight='bold')
    ax2.set_xlabel('交易日')
    ax2.set_ylabel('累计收益率 (%)')
    ax2.grid(True, alpha=0.3)
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
    
    # 添加周分隔线
    for week in range(1, 17):
        week_start = (week - 1) * 5 + 1
        if week_start <= len(days):
            ax2.axvline(x=week_start - 0.5, color='blue', linestyle='--', alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    chart_path = os.path.join(output_dir, f'{stock_name}_daily_returns_16weeks.png')
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    print(f"{stock_name} 每日收益图表已保存到: {chart_path}")
    
    plt.show()

def create_daily_analysis_table(daily_data, weekly_summaries, stock_name, output_dir):
    """创建每日分析表格"""
    
    if not daily_data:
        return
    
    # 创建DataFrame
    df = pd.DataFrame(daily_data)
    
    # 添加周汇总信息
    week_summary_dict = {w['周数']: w for w in weekly_summaries}
    
    df['周总收益率'] = df['周数'].map(lambda x: week_summary_dict.get(x, {}).get('周总收益率', 0))
    df['周夏普比率'] = df['周数'].map(lambda x: week_summary_dict.get(x, {}).get('周夏普比率', 0))
    
    # 保存为CSV
    csv_path = os.path.join(output_dir, f'{stock_name}_daily_analysis_16weeks.csv')
    df.to_csv(csv_path, index=False, encoding='utf-8-sig')
    print(f"{stock_name} 每日分析数据已保存到: {csv_path}")
    
    return df

def generate_daily_report(all_daily_data, output_dir):
    """生成每日分析报告"""
    
    report_lines = []
    report_lines.append("# 前16周每日投资表现详细分析报告\n")
    report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
    
    report_lines.append("## 概览统计\n")
    
    for stock_name, (daily_data, weekly_data) in all_daily_data.items():
        if not daily_data:
            continue
            
        report_lines.append(f"### {stock_name}\n")
        
        # 基本统计
        total_days = len(daily_data)
        total_weeks = len(weekly_data)
        
        daily_returns = [d['日收益率'] for d in daily_data]
        positive_days = len([r for r in daily_returns if r > 0])
        negative_days = len([r for r in daily_returns if r < 0])
        flat_days = len([r for r in daily_returns if r == 0])
        
        final_cumulative = daily_data[-1]['累计收益率'] if daily_data else 0
        
        max_daily_return = max(daily_returns) if daily_returns else 0
        min_daily_return = min(daily_returns) if daily_returns else 0
        avg_daily_return = sum(daily_returns) / len(daily_returns) if daily_returns else 0
        
        report_lines.append(f"- **总交易天数**: {total_days}天 ({total_weeks}周)")
        report_lines.append(f"- **最终累计收益**: {final_cumulative*100:.2f}%")
        report_lines.append(f"- **日均收益率**: {avg_daily_return*100:.3f}%")
        report_lines.append(f"- **最大单日收益**: {max_daily_return*100:.2f}%")
        report_lines.append(f"- **最大单日亏损**: {min_daily_return*100:.2f}%")
        report_lines.append(f"- **盈利天数**: {positive_days}天 ({positive_days/total_days*100:.1f}%)")
        report_lines.append(f"- **亏损天数**: {negative_days}天 ({negative_days/total_days*100:.1f}%)")
        report_lines.append(f"- **平盘天数**: {flat_days}天 ({flat_days/total_days*100:.1f}%)")
        report_lines.append("")
        
        # 周度表现汇总
        if weekly_data:
            weekly_returns = [w['周总收益率'] for w in weekly_data]
            positive_weeks = len([r for r in weekly_returns if r > 0])
            
            report_lines.append(f"**周度表现**:")
            report_lines.append(f"- 盈利周数: {positive_weeks}/{len(weekly_data)}周")
            report_lines.append(f"- 最佳周收益: {max(weekly_returns)*100:.2f}%")
            report_lines.append(f"- 最差周收益: {min(weekly_returns)*100:.2f}%")
            report_lines.append("")
    
    # 详细每日数据表格
    report_lines.append("## 详细每日表现\n")
    
    for stock_name, (daily_data, weekly_data) in all_daily_data.items():
        if not daily_data:
            continue
            
        report_lines.append(f"### {stock_name} 每日明细\n")
        report_lines.append("| 交易日 | 日期 | 周 | 日收益率 | 累计收益率 | 净值 | 交易动作 |")
        report_lines.append("|--------|------|----|---------|-----------|----|----------|")
        
        for d in daily_data:
            trading_action = d['交易动作'].replace('{', '').replace('}', '').replace("'", "")
            report_lines.append(f"| {d['交易日']}天 | {d['日期']} | 第{d['周数']}周 | {d['日收益率']*100:+.2f}% | {d['累计收益率']*100:+.2f}% | ${d['净值']:,.0f} | {trading_action} |")
        
        report_lines.append("")
    
    # 保存报告
    report_path = os.path.join(output_dir, 'daily_performance_report_16weeks.md')
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))
    
    print(f"每日详细分析报告已保存到: {report_path}")

def get_stock_name_from_filename(filename):
    """从文件名中提取股票代码"""
    basename = os.path.basename(filename)
    if basename.startswith('AAPL'):
        return 'AAPL'
    elif basename.startswith('GOOG'):
        return 'GOOG'
    elif basename.startswith('META'):
        return 'META'
    elif basename.startswith('NVDA'):
        return 'NVDA'
    else:
        return basename.replace('.log', '').replace('_0101_0430', '').replace('_0101-0430', '')

def process_daily_analysis(log_files, output_dir, max_weeks=16):
    """批量处理每日分析"""
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    all_daily_data = {}
    
    print(f"开始批量提取前{max_weeks}周每日收益数据...")
    print("="*80)
    
    for log_file in log_files:
        print(f"\n处理文件: {log_file}")
        
        # 提取股票名称
        stock_name = get_stock_name_from_filename(log_file)
        
        # 提取每日和周性能数据
        daily_data, weekly_summaries = extract_daily_performance(log_file, max_weeks)
        
        if not daily_data:
            print(f"❌ 未找到每日交易数据")
            continue
        
        # 保存结果
        all_daily_data[stock_name] = (daily_data, weekly_summaries)
        
        # 显示基本统计
        total_days = len(daily_data)
        total_weeks = len(weekly_summaries)
        final_return = daily_data[-1]['累计收益率'] if daily_data else 0
        
        daily_returns = [d['日收益率'] for d in daily_data]
        positive_days = len([r for r in daily_returns if r > 0])
        
        print(f"✅ 成功提取 {total_days} 天数据 ({total_weeks} 周)")
        print(f"   最终累计收益: {final_return*100:.2f}%")
        print(f"   盈利天数: {positive_days}/{total_days} ({positive_days/total_days*100:.1f}%)")
        print(f"   最大单日收益: {max(daily_returns)*100:.2f}%")
        print(f"   最大单日亏损: {min(daily_returns)*100:.2f}%")
        
        # 生成图表
        plot_daily_returns(daily_data, stock_name, output_dir)
        
        # 创建分析表格
        df = create_daily_analysis_table(daily_data, weekly_summaries, stock_name, output_dir)
        
        # 保存JSON数据
        json_data = {
            'daily_data': daily_data,
            'weekly_summaries': weekly_summaries,
            'summary_stats': {
                'total_days': total_days,
                'total_weeks': total_weeks,
                'final_return': final_return,
                'daily_win_rate': positive_days/total_days if total_days > 0 else 0,
                'max_daily_return': max(daily_returns) if daily_returns else 0,
                'min_daily_return': min(daily_returns) if daily_returns else 0
            }
        }
        
        json_path = os.path.join(output_dir, f'{stock_name}_daily_analysis_16weeks.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
        print(f"   JSON数据已保存到: {json_path}")
    
    print("\n" + "="*80)
    print(f"每日分析完成! 共处理 {len(all_daily_data)} 个股票")
    
    if all_daily_data:
        # 生成综合报告
        print("\n生成每日详细分析报告...")
        generate_daily_report(all_daily_data, output_dir)
        
        # 创建汇总对比
        print("\n生成汇总对比数据...")
        summary_comparison = {}
        for stock_name, (daily_data, weekly_data) in all_daily_data.items():
            if daily_data:
                daily_returns = [d['日收益率'] for d in daily_data]
                summary_comparison[stock_name] = {
                    'total_days': len(daily_data),
                    'final_return': daily_data[-1]['累计收益率'],
                    'daily_win_rate': len([r for r in daily_returns if r > 0]) / len(daily_returns),
                    'avg_daily_return': sum(daily_returns) / len(daily_returns),
                    'max_daily_return': max(daily_returns),
                    'min_daily_return': min(daily_returns),
                    'volatility': np.std(daily_returns) if daily_returns else 0
                }
        
        summary_path = os.path.join(output_dir, 'daily_comparison_summary_16weeks.json')
        with open(summary_path, 'w', encoding='utf-8') as f:
            json.dump(summary_comparison, f, ensure_ascii=False, indent=2)
        print(f"汇总对比数据已保存到: {summary_path}")
    
    return all_daily_data

def main():
    # 定义日志文件路径
    log_files = [
        "/Users/<USER>/Code/Multi_Agent_Optimization/test_results/AAPL_0101-0430.log",
        "/Users/<USER>/Code/Multi_Agent_Optimization/test_results/GOOG_0101_0430.log", 
        "/Users/<USER>/Code/Multi_Agent_Optimization/test_results/META_0101_0430.log",
        "/Users/<USER>/Code/Multi_Agent_Optimization/test_results/NVDA_0101_0430.log"
    ]
    
    # 检查文件是否存在
    existing_files = []
    for log_file in log_files:
        if os.path.exists(log_file):
            existing_files.append(log_file)
        else:
            print(f"⚠️  文件不存在: {log_file}")
    
    if not existing_files:
        print("❌ 没有找到任何有效的日志文件!")
        return
    
    # 输出目录
    output_dir = "/Users/<USER>/Code/Multi_Agent_Optimization/daily_analysis_16weeks"
    
    # 批量处理 - 只看前16周
    results = process_daily_analysis(existing_files, output_dir, max_weeks=16)
    
    print(f"\n🎉 前16周每日分析完成! 所有结果已保存到: {output_dir}")

if __name__ == "__main__":
    main()
