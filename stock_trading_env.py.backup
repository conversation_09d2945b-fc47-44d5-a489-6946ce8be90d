"""
美股交易环境模拟器

提供美股交易环境的模拟，包括价格数据、交易执行、收益计算等功能
"""

import os
import pandas as pd
import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple, Optional
import glob

# 导入决策快照模块
try:
    from contribution_assessment.decision_snapshot import (
        DecisionSnapshotManager,
        DecisionType,
        MarketOutcome,
    )

    DECISION_SNAPSHOT_AVAILABLE = True
except ImportError:
    # 如果导入失败，创建一个简单的枚举类作为fallback
    from enum import Enum

    class DecisionType(Enum):
        BUY = "BUY"
        SELL = "SELL"
        HOLD = "HOLD"
        UNKNOWN = "UNKNOWN"

    class MarketOutcome(Enum):
        POSITIVE = "POSITIVE"
        NEGATIVE = "NEGATIVE"
        NEUTRAL = "NEUTRAL"
        UNKNOWN = "UNKNOWN"

    DECISION_SNAPSHOT_AVAILABLE = False


class StockTradingEnv:
    """美股交易环境类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化交易环境

        参数:
            config: 环境配置，包含起始日期、结束日期、初始资金等
        """
        self.config = config
        self.starting_cash = config.get("starting_cash", 1_000_000)  # 默认100万美元
        self.trading_fee_rate = config.get("trading_fee_rate", 0.001)  # 交易费率0.1%
        self.price_window = config.get("price_window", 30)  # 价格历史窗口(默认30天)
        self.news_window = config.get("news_window", 1)  # 新闻历史窗口(默认3天)
        self.current_day_index = 0

        # 初始化决策快照管理器
        self.enable_decision_snapshots = config.get("enable_decision_snapshots", True)
        if self.enable_decision_snapshots and DECISION_SNAPSHOT_AVAILABLE:
            self.snapshot_manager = DecisionSnapshotManager()
        else:
            self.snapshot_manager = None

        # 决策上下文缓存（用于记录智能体决策过程）
        self._decision_contexts = {}
        self._current_coalition_id = "unknown"
        self._current_evaluation_run_id = "unknown"

        # 加载股票数据
        self.load_data(
            config.get("start_date", "2025-01-01"),
            config.get("end_date", "2025-02-15"),
            config.get("stocks", ["AAPL", "MSFT", "AMZN", "GOOGL", "META"]),
        )

        # 初始化投资组合
        self.reset()

    def load_data(self, start_date: str, end_date: str, stocks: List[str]) -> None:
        """
        加载股票数据

        参数:
            start_date: 起始日期
            end_date: 结束日期
            stocks: 股票代码列表
        """
        self.stocks = stocks
        self.start_date = start_date
        self.end_date = end_date

        # 加载真实数据或生成模拟数据
        self.price_data = self._load_price_data_and_get_trading_days(
            start_date, end_date, stocks
        )
        self.news_data = self._load_news_data(stocks)
        self.fundamental_data = self._load_fundamental_data(stocks)

    def _load_price_data_and_get_trading_days(
        self, start_date: str, end_date: str, stocks: List[str]
    ) -> Dict[str, pd.DataFrame]:
        """
        加载价格数据并从中提取实际的交易日

        优先从SQLite数据库加载，如果不存在则尝试从JSON文件加载
        """
        price_data = {}
        all_trading_days = set()  # 用于收集所有股票的交易日

        for stock in stocks:
            stock_data = []

            # 首先尝试从SQLite数据库加载
            # 获取项目根目录路径
            project_root = os.path.dirname(os.path.abspath(__file__))
            db_path = os.path.join(
                project_root, "data", "tickers", stock, f"{stock}_data.db"
            )
            if os.path.exists(db_path):
                try:
                    import sqlite3

                    conn = sqlite3.connect(db_path)

                    # 查询OHLCV数据 - 扩展查询范围以支持价格窗口
                    # 计算需要加载的历史数据起始日期
                    from datetime import datetime, timedelta

                    # 处理可能包含时间戳的日期字符串
                    if "T" in start_date:
                        start_date_clean = start_date.split("T")[0]
                    else:
                        start_date_clean = start_date

                    if "T" in end_date:
                        end_date_clean = end_date.split("T")[0]
                    else:
                        end_date_clean = end_date

                    start_date_obj = datetime.strptime(start_date_clean, "%Y-%m-%d")
                    # 为价格窗口预留足够的历史数据（价格窗口大小 + 额外缓冲）
                    history_buffer_days = max(self.price_window * 2, 60)  # 至少60天缓冲
                    extended_start_date = start_date_obj - timedelta(
                        days=history_buffer_days
                    )
                    extended_start_date_str = extended_start_date.strftime("%Y-%m-%d")

                    query = """
                    SELECT ticker, trade_date, Open, High, Low, Close, Adj_Close, Volume
                    FROM ohlcv
                    WHERE ticker = ? AND trade_date BETWEEN ? AND ?
                    ORDER BY trade_date ASC
                    """

                    cursor = conn.cursor()
                    cursor.execute(
                        query, (stock, extended_start_date_str, end_date_clean)
                    )
                    rows = cursor.fetchall()

                    if rows:
                        # 转换为DataFrame
                        df = pd.DataFrame(
                            rows,
                            columns=[
                                "ticker",
                                "date",
                                "open",
                                "high",
                                "low",
                                "close",
                                "adj_close",
                                "volume",
                            ],
                        )
                        df["date"] = pd.to_datetime(df["date"])
                        df = df.drop("ticker", axis=1)  # 移除ticker列
                        price_data[stock] = df

                        # 只从用户指定的日期范围内收集交易日（不包括历史缓冲数据）
                        user_range_df = df[
                            (df["date"] >= start_date_clean)
                            & (df["date"] <= end_date_clean)
                        ]
                        stock_trading_days = set(
                            user_range_df["date"].dt.strftime("%Y-%m-%d").tolist()
                        )
                        all_trading_days.update(stock_trading_days)

                        # 数据加载信息已记录在日志中
                        conn.close()
                        continue
                    else:
                        # 数据库查询结果已记录在日志中
                        # 尝试查询数据库中所有可用的日期范围
                        cursor.execute(
                            "SELECT MIN(trade_date), MAX(trade_date) FROM ohlcv WHERE ticker = ?",
                            (stock,),
                        )
                        date_range_result = cursor.fetchone()
                        if date_range_result and date_range_result[0]:
                            # 数据库日期范围信息已记录在日志中
                            pass
                        conn.close()
                        # 为这个股票创建空的DataFrame
                        price_data[stock] = pd.DataFrame()

                except Exception as e:
                    # 记录详细的数据库加载错误信息
                    import traceback

                    error_msg = f"❌ 加载 {stock} 数据库时发生异常: {e}"
                    print(error_msg)
                    print(f"📁 数据库路径: {db_path}")
                    print(f"📅 查询日期范围: {extended_start_date_str} 到 {end_date}")
                    print(f"🔍 完整异常追踪:\n{traceback.format_exc()}")

                    if hasattr(self, "logger") and self.logger:
                        self.logger.error(error_msg)
                        self.logger.error(f"数据库路径: {db_path}")
                        self.logger.error(
                            f"查询日期范围: {extended_start_date_str} 到 {end_date}"
                        )
                        self.logger.error(f"完整异常追踪:\n{traceback.format_exc()}")

                    if "conn" in locals():
                        conn.close()
                    # 为这个股票创建空的DataFrame
                    price_data[stock] = pd.DataFrame()

            else:
                # 如果数据库不存在，尝试从JSON文件加载
                # 由于我们不知道实际的交易日，这里使用pandas工作日作为fallback
                # 处理可能包含时间戳的日期字符串
                start_date_fallback = (
                    start_date.split("T")[0] if "T" in start_date else start_date
                )
                end_date_fallback = (
                    end_date.split("T")[0] if "T" in end_date else end_date
                )
                date_range_fallback = pd.date_range(
                    start=start_date_fallback, end=end_date_fallback, freq="B"
                )
                for date in date_range_fallback:
                    date_str = date.strftime("%Y-%m-%d")
                    date_key = date_str.replace("-", "_")  # 转换为 YYYY_MM_DD 格式
                    # 获取项目根目录路径
                    project_root = os.path.dirname(os.path.abspath(__file__))
                    file_path = os.path.join(
                        project_root, "data", "ohlcv", f"{stock}_price_{date_key}.json"
                    )

                    if os.path.exists(file_path):
                        try:
                            with open(file_path, "r") as f:
                                daily_data = json.load(f)
                                stock_data.append(daily_data)
                                all_trading_days.add(date_str)
                        except Exception as e:
                            # 文件加载错误已记录在日志中
                            pass

                # 如果找到了JSON数据，将其转换为 DataFrame
                if stock_data:
                    df = pd.DataFrame(stock_data)
                    # 确保日期列是 datetime 类型
                    df["date"] = pd.to_datetime(df["date"])
                    price_data[stock] = df
                    # JSON数据加载信息已记录在日志中
                else:
                    # 如果没有找到任何数据，使用空的DataFrame
                    pass
                    price_data[stock] = pd.DataFrame()

        # 关键修复：从实际数据中确定交易日，并检查连续性
        if all_trading_days:
            # 将交易日转换为排序的日期列表
            sorted_trading_days = sorted(list(all_trading_days))

            # 新增：检查日期间隙并报告
            gaps = self._check_and_report_date_gaps(sorted_trading_days)

            # 可选：根据配置决定如何处理间隙
            if self.config.get("fail_on_large_gaps", True) and gaps:
                large_gaps = [
                    gap
                    for gap in gaps
                    if gap[2] > self.config.get("max_date_gap_days", 7)
                ]
                if large_gaps:
                    raise ValueError(
                        f"发现 {len(large_gaps)} 个大的日期间隙，这可能导致交易环境出现日期跳跃问题！请检查数据完整性。"
                    )

            # 可选：填补小间隙（如果配置启用）
            if self.config.get("fill_date_gaps", False):
                sorted_trading_days = self._fill_small_date_gaps(sorted_trading_days)
                # 日期间隙填补信息已记录在日志中

            self.trading_days = [pd.Timestamp(date) for date in sorted_trading_days]
            self.trading_days_str = sorted_trading_days  # 保存字符串格式的交易日列表
            self.total_days = len(self.trading_days)
            # 交易日确定信息已记录在日志中
        else:
            # 如果没有任何数据，使用pandas工作日作为fallback
            pass
            date_range = pd.date_range(start=start_date, end=end_date, freq="B")
            self.trading_days = date_range.tolist()
            self.trading_days_str = [
                date.strftime("%Y-%m-%d") for date in date_range
            ]  # 保存字符串格式
            self.total_days = len(self.trading_days)

        return price_data

    def get_current_date_str(self) -> str:
        """获取当前日期的字符串格式"""
        if hasattr(self, "current_date") and self.current_date is not None:
            if isinstance(self.current_date, pd.Timestamp):
                return self.current_date.strftime("%Y-%m-%d")
            else:
                return str(self.current_date)
        elif hasattr(self, "current_day_index") and hasattr(self, "trading_days_str"):
            if 0 <= self.current_day_index < len(self.trading_days_str):
                return self.trading_days_str[self.current_day_index]
        return ""

    def get_trading_days_str(self) -> List[str]:
        """获取字符串格式的交易日列表"""
        if hasattr(self, "trading_days_str"):
            return self.trading_days_str
        elif hasattr(self, "trading_days"):
            return [
                (
                    date.strftime("%Y-%m-%d")
                    if isinstance(date, pd.Timestamp)
                    else str(date)
                )
                for date in self.trading_days
            ]
        return []

    def _check_and_report_date_gaps(
        self, trading_days: List[str]
    ) -> List[Tuple[str, str, int, int, int]]:
        """检查并报告交易日间隙"""
        gaps = []
        for i in range(1, len(trading_days)):
            prev_date = pd.to_datetime(trading_days[i - 1])
            curr_date = pd.to_datetime(trading_days[i])
            days_diff = (curr_date - prev_date).days

            if days_diff > 7:  # 超过一周的间隙
                gaps.append((trading_days[i - 1], trading_days[i], days_diff, i - 1, i))

        if gaps:
            # 日期间隙检查结果已记录在日志中
            pass
        else:
            # 交易日连续性检查结果已记录在日志中
            pass

        return gaps

    def _fill_small_date_gaps(
        self, trading_days: List[str], max_gap_days: int = None
    ) -> List[str]:
        """填补小的日期间隙"""
        if max_gap_days is None:
            max_gap_days = self.config.get("max_date_gap_days", 7)

        filled_days = []

        for i, date_str in enumerate(trading_days):
            filled_days.append(date_str)

            # 检查与下一个日期的间隙
            if i < len(trading_days) - 1:
                curr_date = pd.to_datetime(date_str)
                next_date = pd.to_datetime(trading_days[i + 1])
                days_diff = (next_date - curr_date).days

                # 如果间隙小于阈值，填补工作日
                if 1 < days_diff <= max_gap_days:
                    date_range = pd.date_range(
                        start=curr_date + pd.Timedelta(days=1),
                        end=next_date - pd.Timedelta(days=1),
                        freq="B",  # 只包含工作日
                    )
                    for fill_date in date_range:
                        filled_days.append(fill_date.strftime("%Y-%m-%d"))

        return sorted(filled_days)

    def _load_news_data(
        self, stocks: List[str]
    ) -> Dict[str, Dict[str, List[Dict[str, Any]]]]:
        """
        加载新闻数据

        首先尝试从SQLite数据库加载，然后尝试从按天存储的JSON文件加载
        扩展日期范围以包含交易日期开始前的新闻数据
        """
        news_data = {}

        # 扩展日期范围：包含交易日期开始前的 news_window 天
        # 这样确保第一个交易日也能获取到足够的历史新闻
        first_trading_day = self.trading_days[0]
        extended_start_date = first_trading_day - pd.Timedelta(days=self.news_window)

        # 生成扩展的日期范围（包含交易日前的日期）
        extended_date_range = pd.date_range(
            start=extended_start_date,
            end=self.trading_days[-1],
            freq="D",  # 每日频率，包含所有日期（不仅仅是交易日）
        )

        # 初始化扩展日期范围内每个日期的新闻数据字典
        for date in extended_date_range:
            date_str = date.strftime("%Y-%m-%d")
            news_data[date_str] = {stock: [] for stock in stocks}

        # 首先尝试从SQLite数据库加载新闻数据
        for stock in stocks:
            # 获取项目根目录路径
            project_root = os.path.dirname(os.path.abspath(__file__))
            db_path = os.path.join(
                project_root, "data", "tickers", stock, f"{stock}_data.db"
            )
            if os.path.exists(db_path):
                try:
                    import sqlite3

                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()

                    # 检查是否存在新闻表
                    cursor.execute(
                        "SELECT name FROM sqlite_master WHERE type='table' AND name='news'"
                    )
                    if cursor.fetchone():
                        # 查询扩展日期范围内的新闻（包含交易日前的news_window天）
                        first_trading_day = self.trading_days[0]
                        extended_start_date = first_trading_day - pd.Timedelta(
                            days=self.news_window
                        )

                        start_date_str = extended_start_date.strftime("%Y-%m-%d")
                        end_date_str = self.trading_days[-1].strftime("%Y-%m-%d")

                        # 修复SQL查询以处理多种时间戳格式
                        cursor.execute(
                            """
                            SELECT ticker, time_published, title, summary, overall_sentiment_label, url
                            FROM news 
                            WHERE ticker = ? 
                            AND (
                                DATE(time_published) BETWEEN ? AND ?
                                OR SUBSTR(time_published, 1, 4) || '-' || SUBSTR(time_published, 5, 2) || '-' || SUBSTR(time_published, 7, 2) BETWEEN ? AND ?
                            )
                            ORDER BY time_published ASC
                        """,
                            (
                                stock,
                                start_date_str,
                                end_date_str,
                                start_date_str,
                                end_date_str,
                            ),
                        )

                        news_rows = cursor.fetchall()

                        # 按日期组织新闻数据 - 增强时间戳处理
                        for row in news_rows:
                            _, time_str, title, summary, sentiment, url = row

                            # 处理多种时间戳格式，保留完整时间信息
                            try:
                                if (
                                    len(time_str) >= 8
                                    and "T" in time_str
                                    and "-" not in time_str[:10]
                                ):
                                    # 自定义格式: 20250131T213650
                                    date_part = time_str[:8]  # 20250131
                                    time_part = (
                                        time_str[9:] if len(time_str) > 9 else "000000"
                                    )  # 213650
                                    news_date = f"{date_part[:4]}-{date_part[4:6]}-{date_part[6:8]}"  # 2025-01-31
                                    # 构建完整时间戳
                                    if len(time_part) >= 6:
                                        full_timestamp = f"{news_date} {time_part[:2]}:{time_part[2:4]}:{time_part[4:6]}"
                                    else:
                                        full_timestamp = f"{news_date} 00:00:00"
                                else:
                                    # 标准格式: 2025-01-01 18:00:19 或其他格式
                                    news_date = time_str[:10]
                                    full_timestamp = time_str  # 保留完整时间戳
                            except Exception as e:
                                print(f"⚠️ 解析时间戳 {time_str} 时出错: {e}")
                                continue

                            # 修复：处理非交易日的新闻数据
                            target_date = news_date
                            if news_date not in news_data:
                                # 如果新闻日期不是交易日，找到最近的下一个交易日
                                target_date = self._find_next_trading_day(news_date)
                                if target_date is None:
                                    # 如果找不到下一个交易日，尝试找前一个交易日
                                    target_date = self._find_previous_trading_day(
                                        news_date
                                    )

                            if target_date and target_date in news_data:
                                news_item = {
                                    "time": time_str,
                                    "full_timestamp": full_timestamp,  # 保存完整时间戳
                                    "title": title,
                                    "content": summary,
                                    "sentiment": sentiment,
                                    "url": url,
                                    "original_date": news_date,  # 保存原始日期信息
                                    "parsed_date": news_date,  # 保存解析后的日期
                                    "parsed_time": full_timestamp,  # 保存解析后的完整时间
                                }
                                news_data[target_date][stock].append(news_item)

                        if news_rows:
                            pass  # 数据验证通过，无需记录成功日志

                    conn.close()
                except Exception as e:
                    print(f"从SQLite数据库加载 {stock} 新闻数据时出错: {e}")
                    if "conn" in locals():
                        conn.close()

        # 然后尝试从JSON文件加载新闻数据（作为补充）
        for stock in stocks:
            # 只加载指定日期范围内的新闻文件
            for date in self.trading_days:
                date_str = date.strftime("%Y-%m-%d")
                # 检查该日期还没有新闻数据
                if not news_data[date_str][stock]:
                    # 尝试加载对应日期的新闻文件
                    # 获取项目根目录路径
                    project_root = os.path.dirname(os.path.abspath(__file__))
                    news_file_path = os.path.join(
                        project_root,
                        "data",
                        "news",
                        f"{stock}_news_{date_str.replace('-', '_')}.json",
                    )
                    if os.path.exists(news_file_path):
                        try:
                            with open(news_file_path, "r") as f:
                                stock_news = json.load(f)
                                news_data[date_str][stock] = stock_news
                                print(
                                    f"从JSON文件补充加载了 {stock} 在 {date_str} 的新闻数据"
                                )
                        except Exception as e:
                            print(f"加载 {news_file_path} 出错: {e}")

        # 检查是否有新闻数据
        has_real_data = False
        total_news_count = 0
        for date_str, stocks_news in news_data.items():
            for stock, news_list in stocks_news.items():
                if news_list:
                    has_real_data = True
                    total_news_count += len(news_list)

        if has_real_data:
            pass  # 数据验证通过，无需记录成功日志
        else:
            print("⚠️ 未找到任何新闻数据")
            # 即使没有真实数据，也确保返回一个有效的结构，以防止智能体完全失败
            # 为每个日期和股票提供空的新闻列表，而不是返回空字典
            if not news_data:
                for date in self.trading_days:
                    date_str = date.strftime("%Y-%m-%d")
                    news_data[date_str] = {stock: [] for stock in stocks}

        return news_data

    def _find_next_trading_day(self, date_str: str) -> Optional[str]:
        """
        找到指定日期之后的下一个交易日

        Args:
            date_str: 日期字符串 (YYYY-MM-DD)

        Returns:
            下一个交易日的字符串，如果找不到则返回None
        """
        try:
            target_date = pd.Timestamp(date_str)
            for trading_day in self.trading_days:
                if trading_day > target_date:
                    return trading_day.strftime("%Y-%m-%d")
            return None
        except Exception:
            return None

    def _find_previous_trading_day(self, date_str: str) -> Optional[str]:
        """
        找到指定日期之前的上一个交易日

        Args:
            date_str: 日期字符串 (YYYY-MM-DD)

        Returns:
            上一个交易日的字符串，如果找不到则返回None
        """
        try:
            target_date = pd.Timestamp(date_str)
            for trading_day in reversed(self.trading_days):
                if trading_day < target_date:
                    return trading_day.strftime("%Y-%m-%d")
            return None
        except Exception:
            return None

    def _load_fundamental_data(self, stocks: List[str]) -> Dict[str, Dict[str, Any]]:
        """
        加载基本面数据

        尝试从SQLite数据库加载，然后尝试从文件加载，如果都不存在则生成模拟数据
        """
        fundamental_data = {}

        # 首先尝试从SQLite数据库加载基本面数据
        for stock in stocks:
            fundamental_data[stock] = {}
            # 获取项目根目录路径
            project_root = os.path.dirname(os.path.abspath(__file__))
            db_path = os.path.join(
                project_root, "data", "tickers", stock, f"{stock}_data.db"
            )

            if os.path.exists(db_path):
                try:
                    import sqlite3
                    import json as json_lib  # 避免与模块名冲突

                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()

                    # 检查是否存在季度财务报表和年度财务报表表
                    cursor.execute(
                        "SELECT name FROM sqlite_master WHERE type='table' AND (name='quarterly_financials' OR name='annual_financials')"
                    )
                    if cursor.fetchone():
                        # 首先加载季度财务数据
                        cursor.execute(
                            """
                            SELECT ticker, fiscal_date, report_type, data_json 
                            FROM quarterly_financials 
                            WHERE ticker = ? 
                            ORDER BY fiscal_date DESC
                        """,
                            (stock,),
                        )
                        quarterly_rows = cursor.fetchall()

                        # 然后加载年度财务数据
                        cursor.execute(
                            """
                            SELECT ticker, fiscal_date, report_type, data_json 
                            FROM annual_financials 
                            WHERE ticker = ? 
                            ORDER BY fiscal_date DESC
                        """,
                            (stock,),
                        )
                        annual_rows = cursor.fetchall()

                        # 处理季度数据
                        for row in quarterly_rows:
                            _, fiscal_date, report_type, data_json = row
                            # 解析 JSON 数据
                            try:
                                data = json_lib.loads(data_json)
                                # 从日期生成季度标识
                                try:
                                    date_obj = datetime.strptime(
                                        fiscal_date, "%Y-%m-%d"
                                    )
                                    quarter = (date_obj.month - 1) // 3 + 1
                                    quarter_str = f"{date_obj.year}-Q{quarter}"

                                    # 如果这个季度标识还不存在，初始化它
                                    if quarter_str not in fundamental_data[stock]:
                                        fundamental_data[stock][quarter_str] = {
                                            "fiscal_date": fiscal_date,
                                            "report_date": fiscal_date,  # 用作报告日期
                                        }

                                    # 根据报表类型添加数据
                                    if report_type == "income":
                                        fundamental_data[stock][quarter_str][
                                            "revenue"
                                        ] = self._safe_float(
                                            data.get("totalRevenue", 0)
                                        )
                                        fundamental_data[stock][quarter_str][
                                            "net_income"
                                        ] = self._safe_float(data.get("netIncome", 0))
                                        fundamental_data[stock][quarter_str]["eps"] = (
                                            self._safe_float(data.get("eps", 0))
                                        )
                                    elif report_type == "balance":
                                        fundamental_data[stock][quarter_str][
                                            "total_assets"
                                        ] = self._safe_float(data.get("totalAssets", 0))
                                        fundamental_data[stock][quarter_str][
                                            "total_liabilities"
                                        ] = self._safe_float(
                                            data.get("totalLiabilities", 0)
                                        )
                                        # 计算债务股本比
                                        total_equity = self._safe_float(
                                            data.get("totalEquity", 0)
                                        )
                                        if total_equity != 0:
                                            fundamental_data[stock][quarter_str][
                                                "debt_to_equity"
                                            ] = (
                                                self._safe_float(
                                                    data.get("totalLiabilities", 0)
                                                )
                                                / total_equity
                                            )
                                        else:
                                            fundamental_data[stock][quarter_str][
                                                "debt_to_equity"
                                            ] = 0
                                    elif report_type == "cashflow":
                                        fundamental_data[stock][quarter_str][
                                            "operating_cash_flow"
                                        ] = self._safe_float(
                                            data.get("operatingCashFlow", 0)
                                        )
                                        fundamental_data[stock][quarter_str][
                                            "free_cash_flow"
                                        ] = self._safe_float(
                                            data.get("freeCashFlow", 0)
                                        )

                                    # 计算利润率（如果有收入和净利润数据）
                                    if (
                                        "revenue"
                                        in fundamental_data[stock][quarter_str]
                                        and "net_income"
                                        in fundamental_data[stock][quarter_str]
                                    ):
                                        revenue = fundamental_data[stock][quarter_str][
                                            "revenue"
                                        ]
                                        if revenue != 0:
                                            fundamental_data[stock][quarter_str][
                                                "profit_margin"
                                            ] = (
                                                fundamental_data[stock][quarter_str][
                                                    "net_income"
                                                ]
                                                / revenue
                                            )
                                        else:
                                            fundamental_data[stock][quarter_str][
                                                "profit_margin"
                                            ] = 0
                                except Exception as e:
                                    print(f"处理季度数据时出错: {e}")
                            except json_lib.JSONDecodeError:
                                print(f"解析JSON数据失败: {data_json[:100]}...")

                        # 处理年度数据，方式类似处理季度数据
                        # 只有当某个季度没有特定数据时，才使用年度数据填补
                        for row in annual_rows:
                            _, fiscal_date, report_type, data_json = row
                            try:
                                data = json_lib.loads(data_json)
                                # 从年度日期提取年份
                                try:
                                    date_obj = datetime.strptime(
                                        fiscal_date, "%Y-%m-%d"
                                    )
                                    year = date_obj.year

                                    # 对每个季度检查是否已有数据，没有则从年度数据填充
                                    for q in range(1, 5):
                                        quarter_str = f"{year}-Q{q}"

                                        # 如果这个季度还没有数据，初始化它
                                        if quarter_str not in fundamental_data[stock]:
                                            fundamental_data[stock][quarter_str] = {
                                                "fiscal_date": fiscal_date,
                                                "report_date": fiscal_date,
                                                "is_annual_data": True,  # 标记为年度数据
                                            }

                                        # 根据报表类型添加数据（如果该季度数据中没有对应字段）
                                        if report_type == "income":
                                            if (
                                                "revenue"
                                                not in fundamental_data[stock][
                                                    quarter_str
                                                ]
                                            ):
                                                fundamental_data[stock][quarter_str][
                                                    "revenue"
                                                ] = (
                                                    self._safe_float(
                                                        data.get("totalRevenue", 0)
                                                    )
                                                    / 4
                                                )  # 粗略平均到季度
                                            if (
                                                "net_income"
                                                not in fundamental_data[stock][
                                                    quarter_str
                                                ]
                                            ):
                                                fundamental_data[stock][quarter_str][
                                                    "net_income"
                                                ] = (
                                                    self._safe_float(
                                                        data.get("netIncome", 0)
                                                    )
                                                    / 4
                                                )
                                            if (
                                                "eps"
                                                not in fundamental_data[stock][
                                                    quarter_str
                                                ]
                                            ):
                                                fundamental_data[stock][quarter_str][
                                                    "eps"
                                                ] = (
                                                    self._safe_float(data.get("eps", 0))
                                                    / 4
                                                )
                                        elif report_type == "balance":
                                            if (
                                                "total_assets"
                                                not in fundamental_data[stock][
                                                    quarter_str
                                                ]
                                            ):
                                                fundamental_data[stock][quarter_str][
                                                    "total_assets"
                                                ] = self._safe_float(
                                                    data.get("totalAssets", 0)
                                                )
                                            if (
                                                "total_liabilities"
                                                not in fundamental_data[stock][
                                                    quarter_str
                                                ]
                                            ):
                                                fundamental_data[stock][quarter_str][
                                                    "total_liabilities"
                                                ] = self._safe_float(
                                                    data.get("totalLiabilities", 0)
                                                )
                                            if (
                                                "debt_to_equity"
                                                not in fundamental_data[stock][
                                                    quarter_str
                                                ]
                                            ):
                                                total_equity = self._safe_float(
                                                    data.get("totalEquity", 0)
                                                )
                                                if total_equity != 0:
                                                    fundamental_data[stock][
                                                        quarter_str
                                                    ]["debt_to_equity"] = (
                                                        self._safe_float(
                                                            data.get(
                                                                "totalLiabilities", 0
                                                            )
                                                        )
                                                        / total_equity
                                                    )
                                                else:
                                                    fundamental_data[stock][
                                                        quarter_str
                                                    ]["debt_to_equity"] = 0
                                        elif report_type == "cashflow":
                                            if (
                                                "operating_cash_flow"
                                                not in fundamental_data[stock][
                                                    quarter_str
                                                ]
                                            ):
                                                fundamental_data[stock][quarter_str][
                                                    "operating_cash_flow"
                                                ] = (
                                                    self._safe_float(
                                                        data.get("operatingCashFlow", 0)
                                                    )
                                                    / 4
                                                )
                                            if (
                                                "free_cash_flow"
                                                not in fundamental_data[stock][
                                                    quarter_str
                                                ]
                                            ):
                                                fundamental_data[stock][quarter_str][
                                                    "free_cash_flow"
                                                ] = (
                                                    self._safe_float(
                                                        data.get("freeCashFlow", 0)
                                                    )
                                                    / 4
                                                )

                                        # 计算利润率（如果有收入和净利润数据且尚未计算）
                                        if (
                                            "profit_margin"
                                            not in fundamental_data[stock][quarter_str]
                                            and "revenue"
                                            in fundamental_data[stock][quarter_str]
                                            and "net_income"
                                            in fundamental_data[stock][quarter_str]
                                        ):
                                            revenue = fundamental_data[stock][
                                                quarter_str
                                            ]["revenue"]
                                            if revenue != 0:
                                                fundamental_data[stock][quarter_str][
                                                    "profit_margin"
                                                ] = (
                                                    fundamental_data[stock][
                                                        quarter_str
                                                    ]["net_income"]
                                                    / revenue
                                                )
                                            else:
                                                fundamental_data[stock][quarter_str][
                                                    "profit_margin"
                                                ] = 0
                                except Exception as e:
                                    print(f"处理年度数据时出错: {e}")
                            except json_lib.JSONDecodeError:
                                print(f"解析JSON数据失败: {data_json[:100]}...")

                        # 添加PE比率数据（如果价格数据可用）
                        for quarter_str, quarter_data in list(
                            fundamental_data[stock].items()
                        ):
                            if (
                                "eps" in quarter_data
                                and self._safe_float(quarter_data["eps"]) != 0
                            ):
                                try:
                                    # 尝试在价格数据中找到与财报日期最接近的价格
                                    report_date = quarter_data.get(
                                        "fiscal_date", quarter_data.get("report_date")
                                    )
                                    if (
                                        report_date
                                        and hasattr(self, "price_data")
                                        and stock in self.price_data
                                        and not self.price_data[stock].empty
                                    ):
                                        # 找到最接近报告日期的价格数据
                                        closest_price_idx = self.price_data[stock][
                                            "date"
                                        ].searchsorted(report_date)
                                        if closest_price_idx < len(
                                            self.price_data[stock]
                                        ):
                                            closest_price = self._safe_float(
                                                self.price_data[stock].iloc[
                                                    closest_price_idx
                                                ]["close"]
                                            )
                                            quarter_data["pe_ratio"] = (
                                                closest_price
                                                / self._safe_float(quarter_data["eps"])
                                            )
                                        else:
                                            # 如果没有找到，使用最后一个价格
                                            last_price = self._safe_float(
                                                self.price_data[stock].iloc[-1]["close"]
                                            )
                                            quarter_data["pe_ratio"] = (
                                                last_price
                                                / self._safe_float(quarter_data["eps"])
                                            )
                                except Exception as e:
                                    print(f"计算PE比率时出错: {e}")
                                    # 如果出错，使用一个合理的默认值
                                    quarter_data["pe_ratio"] = (
                                        20.0  # 设置一个合理的默认PE值
                                    )

                    conn.close()

                    # 检查是否成功加载了数据
                    if any(fundamental_data[stock].values()):
                        pass  # 数据验证通过，无需记录成功日志
                    else:
                        print(
                            f"从SQLite数据库 {db_path} 中没有找到 {stock} 的基本面数据"
                        )
                except Exception as e:
                    print(
                        f"从SQLite数据库 {db_path} 加载 {stock} 的基本面数据失败: {e}"
                    )
                    import traceback

                    traceback.print_exc()

        # 检查是否有任何数据，如果没有则生成模拟数据
        has_real_data = any(bool(data) for data in fundamental_data.values())

        if not has_real_data:
            # print("未找到基本面数据，生成模拟数据") # 移除此行
            pass  # 不生成模拟数据，保持为空

        return fundamental_data

    def reset(self) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """
        重置交易环境

        返回:
            (状态, 信息)
        """
        # 初始化当前日期索引
        self.current_day_index = 0
        self.current_date = self.trading_days[self.current_day_index]

        # 初始化简化的收益率状态 - 支持跨周继承
        inherited_state = self.config.get("inherited_portfolio_state")
        portfolio_tracker = self.config.get("portfolio_tracker")
        
        # 初始化收益率跟踪变量
        self.cumulative_return = 0.0  # 累计收益率
        self.weekly_return = 0.0      # 当前周收益率
        self.last_week_return = 0.0   # 上周收益率
        
        # 优先使用portfolio_tracker的最新状态
        if portfolio_tracker:
            latest_state = portfolio_tracker.get_latest_state()
            if latest_state:
                # 从跟踪器继承收益率状态
                self.cumulative_return = latest_state.get("cumulative_return", 0.0)
                self.weekly_return = latest_state.get("weekly_return", 0.0)
                self.last_week_return = latest_state.get("last_week_return", 0.0)
                # 保持现有的positions结构以兼容现有代码
                self.positions = latest_state.get("positions", {stock: 0 for stock in self.stocks}).copy()
                self.position_values = latest_state.get("position_values", {stock: 0.0 for stock in self.stocks}).copy()
                print(f"🔄 从跟踪器继承收益率状态: 累计收益率={self.cumulative_return:.4f}, 当前周收益率={self.weekly_return:.4f}")
            else:
                # tracker存在但无历史数据，使用默认初始化
                self.positions = {stock: 0 for stock in self.stocks}
                self.position_values = {stock: 0.0 for stock in self.stocks}
                print(f"💰 初始收益率状态(新跟踪器): 累计收益率={self.cumulative_return:.4f}")
        elif inherited_state:
            # 使用继承的状态(支持收益率继承)
            self.cumulative_return = inherited_state.get("cumulative_return", 0.0)
            self.weekly_return = inherited_state.get("weekly_return", 0.0)
            self.last_week_return = inherited_state.get("last_week_return", 0.0)
            # 保持现有的positions结构以兼容现有代码
            self.positions = inherited_state.get("positions", {stock: 0 for stock in self.stocks})
            self.position_values = inherited_state.get("position_values", {stock: 0.0 for stock in self.stocks})
            # 确保所有股票在positions中都有记录
            for stock in self.stocks:
                if stock not in self.positions:
                    self.positions[stock] = 0
                if stock not in self.position_values:
                    self.position_values[stock] = 0.0
            
            # 记录状态继承信息
            print(f"🔄 继承收益率状态: 累计收益率={self.cumulative_return:.4f}, 上周收益率={self.last_week_return:.4f}")
        else:
            # 默认初始化
            self.positions = {stock: 0 for stock in self.stocks}
            self.position_values = {stock: 0.0 for stock in self.stocks}
            
            # 记录默认初始化信息
            print(f"💰 初始收益率状态: 累计收益率={self.cumulative_return:.4f}")
        
        # 存储tracker引用用于后续记录
        self._portfolio_tracker = portfolio_tracker

        # 计算初始净值 - 基于实际投资组合状态
        if portfolio_tracker:
            latest_state = portfolio_tracker.get_latest_state()
            if latest_state:
                # 使用tracker中的净值数据，基于收益率计算
                current_net_worth = self.starting_cash * (1 + self.cumulative_return)
                self.initial_net_worth = latest_state.get("net_worth", current_net_worth)
                self.previous_net_worth = self.initial_net_worth
                self.net_worth = current_net_worth
            else:
                # 新tracker，使用默认初始化
                self.initial_net_worth = self.starting_cash
                self.previous_net_worth = self.initial_net_worth
                self.net_worth = self.starting_cash
        elif inherited_state:
            # 继承状态时，基于收益率计算净值
            current_net_worth = self.starting_cash * (1 + self.cumulative_return)
            self.initial_net_worth = inherited_state.get("net_worth", current_net_worth)
            self.previous_net_worth = self.initial_net_worth
            self.net_worth = current_net_worth
        else:
            # 默认初始化
            self.initial_net_worth = self.starting_cash
            self.previous_net_worth = self.initial_net_worth
            self.net_worth = self.starting_cash

        # 初始化上一交易日收益率
        self.last_day_return = 0.0

        # 获取当前状态
        state = self._get_state()

        # 初始化为未结束
        self.done = False

        # 创建初始信息字典
        info = {
            "net_worth": self.net_worth,
            "positions": self.positions.copy(),
            "current_date": (
                self.current_date.strftime("%Y-%m-%d")
                if hasattr(self.current_date, "strftime")
                else str(self.current_date)
            ),
            "trading_day_index": self.current_day_index,
            "total_trading_days": self.total_days,
        }

        # 返回初始状态和信息
        return state, info

    def step(
        self, actions: Dict[str, float]
    ) -> Tuple[Dict[str, Any], float, bool, Dict[str, Any]]:
        """
        执行交易动作

        参数:
            actions: 交易动作字典，键为股票代码，值为操作比例（-1到1，负值表示卖出，正值表示买入）

        返回:
            (状态, 奖励, 是否结束, 信息)
        """
        # 检查是否已经结束交易
        if self.current_day_index >= self.total_days - 1:
            # 如果已经到了最后一天或超出范围，返回结束状态
            state = self.get_state()
            reward = 0.0
            done = True
            info = {
                "daily_return": 0.0,
                "net_worth": self.net_worth,
                "positions": self.positions.copy(),
                "trading_completed": True,
                "reason": "到达交易结束日期",
            }
            return state, reward, done, info

        # 获取当前价格
        current_prices = {}
        for stock in self.stocks:
            # 确保 self.current_day_index 不会超过 price_data 的范围
            if self.current_day_index < len(self.price_data[stock]):
                current_prices[stock] = self.price_data[stock].iloc[
                    self.current_day_index
                ]["close"]
            elif len(self.price_data[stock]) > 0:  # 如果有数据，但索引越界
                # 降低警告级别，使用日志而不是print
                # print(f"警告 (step): {stock} 的 current_day_index ({self.current_day_index}) 超出价格数据范围 ({len(self.price_data[stock])}条)。使用最后已知价格。")
                current_prices[stock] = self.price_data[stock].iloc[-1]["close"]
            else:  # 如果根本没有价格数据
                print(f"错误 (step): {stock} 没有任何价格数据。返回0。")
                current_prices[stock] = 0  # 或者抛出更严重的错误

        # 执行交易
        # 首先处理特殊的全仓卖出标记
        if "__SELL_ALL__" in actions:
            print(f"  💰 执行全仓卖出操作")
            # 卖出所有持仓
            for stock in self.stocks:
                if self.positions.get(stock, 0) > 0:
                    shares_to_sell = self.positions[stock]
                    price = current_prices[stock]
                    sell_value = shares_to_sell * price
                    fee = sell_value * self.trading_fee_rate
                    # 交易费用已集成到收益率计算中
                    print(
                        f"    📤 卖出 {stock}: {shares_to_sell} 股，价格 ${price:.2f}，价值 ${sell_value - fee:.2f}"
                    )
                    self.positions[stock] = 0
            # 移除特殊标记，避免后续处理
            actions = {k: v for k, v in actions.items() if k != "__SELL_ALL__"}

        # 处理观望操作（保持仓位不变）
        if "__HOLD__" in actions:
            print("  👁️ 观望操作 - 保持现有仓位不变")
            # 观望意味着本日不进行任何交易，直接移除该标记即可
            actions = {k: v for k, v in actions.items() if k != "__HOLD__"}

        # 如果actions为空且没有特殊标记，记录这种情况
        if not actions:
            print("  ⚠️ 没有有效的交易动作，保持现有仓位")

        # 处理正常的买卖操作
        for stock, action in actions.items():
            if stock not in self.stocks:
                continue

            price = current_prices[stock]

            # 全仓买入操作 (action > 0)
            if action > 0:
                # 真正的全仓买入：先卖出所有现有持仓，然后用全部资产买入目标股票
                
                # 第一步：卖出所有现有持仓（转为现金）
                total_cash_from_sales = 0.0
                for sell_stock in self.stocks:
                    if self.positions.get(sell_stock, 0) > 0:
                        shares_to_sell = self.positions[sell_stock]
                        sell_price = current_prices[sell_stock]
                        sell_value = shares_to_sell * sell_price
                        fee = sell_value * self.trading_fee_rate
                        cash_from_sale = sell_value - fee
                        # 交易记录已集成到收益率计算中
                        total_cash_from_sales += cash_from_sale
                        self.positions[sell_stock] = 0
                        # print(f"    📤 清仓 {sell_stock}: {shares_to_sell} 股，价格 ${sell_price:.2f}，获得现金 ${cash_from_sale:.2f}")
                
                # 第二步：全仓买入目标股票（按照所有持仓金额计算）
                if price > 0:
                    # 基于当前组合价值计算买入量（估算）
                    current_portfolio_value = self.starting_cash * (1 + self.cumulative_return)
                    affordable_amount = current_portfolio_value * 0.99  # 保留1%作为缓冲
                    shares_to_buy = int(affordable_amount / price)
                    if shares_to_buy > 0:
                        self.positions[stock] = shares_to_buy  # 设置新持仓，不累加
                        # print(f"    📥 全仓买入 {stock}: {shares_to_buy} 股，价格 ${price:.2f}")
                    else:
                        print(f"    ⚠️ 组合价值不足，无法买入 {stock}")
                else:
                    print(f"    ⚠️ 无法买入 {stock}，价格为 ${price:.2f}")

            # 全仓卖出操作 (action = -1.0)
            elif action < 0:
                shares_to_sell = self.positions.get(stock, 0)
                if shares_to_sell > 0:
                    sell_value = shares_to_sell * price
                    fee = sell_value * self.trading_fee_rate
                    # 交易费用已集成到收益率计算中
                    # print(f"    📤 全仓卖出 {stock}: {shares_to_sell} 股，价格 ${price:.2f}，价值 ${sell_value - fee:.2f}")
                    self.positions[stock] = 0

        # 移动到下一天
        self.current_day_index += 1
        if self.current_day_index >= self.total_days:
            self.done = True
            # 保持在最后一个有效日期
            self.current_day_index = self.total_days - 1
        else:
            self.current_date = self.trading_days[self.current_day_index]

        # 计算新的净值和回报，基于收益率
        current_net_worth = self.starting_cash * (1 + self.cumulative_return)

        # 添加除零保护和调试信息
        if self.previous_net_worth <= 0:
            daily_return = 0.0
            # 记录警告信息
            print(
                f"⚠️  警告: previous_net_worth为 {self.previous_net_worth}，设置daily_return为0"
            )
        else:
            daily_return = (current_net_worth / self.previous_net_worth) - 1

        # 调试信息：记录所有净值变化（包括微小变化）
        # print(f"📊 净值变化: {self.previous_net_worth:.2f} -> {current_net_worth:.2f}, 收益率: {daily_return:.6f}")

        total_return = (
            (current_net_worth / self.initial_net_worth) - 1
            if self.initial_net_worth > 0
            else 0.0
        )

        # 保存当前日收益率作为下一日的"上一交易日收益率"
        self.last_day_return = daily_return

        # 更新简化的收益率状态
        if hasattr(self, 'cumulative_return'):
            # 更新累计收益率：(1 + 累计收益率) * (1 + 日收益率) - 1
            self.cumulative_return = (1 + self.cumulative_return) * (1 + daily_return) - 1
            # 更新当前周收益率
            self.weekly_return = (1 + self.weekly_return) * (1 + daily_return) - 1

        # 更新前一天净值和当前净资产
        self.previous_net_worth = current_net_worth
        self.net_worth = current_net_worth

        # 获取新状态
        state = self._get_state()

        # 计算奖励（使用累计收益率作为奖励信号）
        reward = getattr(self, 'cumulative_return', daily_return)

        # 记录日常投资组合状态到跟踪器
        if hasattr(self, '_portfolio_tracker') and self._portfolio_tracker is not None:
            current_date_str = self.current_date.strftime("%Y-%m-%d")
            self._portfolio_tracker.add_daily_record(
                date_str=current_date_str,
                net_worth=current_net_worth,
                daily_return=daily_return,
                cumulative_return=getattr(self, 'cumulative_return', 0.0),
                weekly_return=getattr(self, 'weekly_return', 0.0),
                last_week_return=getattr(self, 'last_week_return', 0.0)
            )

        # 返回信息
        info = {
            "net_worth": current_net_worth,
            "daily_return": daily_return,
            "total_return": total_return,
            "positions": self.positions.copy(),
        }

        return state, reward, self.done, info

    def _get_state(self) -> Dict[str, Any]:
        """
        获取当前环境状态

        返回:
            包含当前市场状态的字典
        """
        current_date_str = self.current_date.strftime("%Y-%m-%d")

        # 获取价格历史
        price_history = {}
        for stock in self.stocks:
            # 确保使用的索引在范围内
            # idx = min(self.current_day_index, len(self.price_data[stock]) - 1) if len(self.price_data[stock]) > 0 else 0
            # 考虑到 current_day_index 可能在 step() 结束时增加到 total_days，此时 _get_state() 获取的是下一天的开盘状态
            # 如果 current_day_index 已经是 total_days，表示模拟已结束，我们不应该尝试读取这一天的数据
            # price_history 将是到 current_day_index - 1 (即上一天收盘) 的数据

            # 获取价格历史 - 修复窗口逻辑确保始终返回正确的历史数据量
            # 价格历史应该包含到当前日期的数据（包括当前日）
            # 确保窗口大小准确：如果price_window=30，应该返回30天的数据

            if not self.price_data[stock].empty:
                # 初始化变量以避免UnboundLocalError
                stock_price_data = pd.DataFrame()
                current_price_idx = -1

                # 找到当前交易日在完整价格数据中的索引
                current_date_str = self.current_date.strftime("%Y-%m-%d")
                price_dates = self.price_data[stock]["date"].dt.strftime("%Y-%m-%d")

                # 找到当前日期在价格数据中的位置
                current_date_indices = price_dates[
                    price_dates == current_date_str
                ].index

                if len(current_date_indices) > 0:
                    # 使用价格数据中的实际索引
                    current_price_idx = int(current_date_indices[0])  # 确保是整数类型
                    # 计算窗口结束索引（包含当前日）
                    available_end_idx = current_price_idx + 1
                    # 计算窗口开始索引，确保获取完整的price_window天数
                    window_start_idx = max(0, available_end_idx - self.price_window)

                    # 获取历史价格数据
                    stock_price_data = self.price_data[stock].iloc[
                        window_start_idx:available_end_idx
                    ]
                    price_history[stock] = stock_price_data.to_dict("records")
                else:
                    # 如果找不到当前日期的价格数据，返回空列表
                    price_history[stock] = []

                # 调试输出：验证窗口大小（仅在有数据时）
                if (
                    current_price_idx >= 0 and not stock_price_data.empty
                ):  # 只在找到当前日期且有价格数据时进行验证
                    actual_window_size = len(stock_price_data)
                    # 期望窗口大小应该是price_window或可用历史数据量的较小值
                    available_history_count = current_price_idx + 1  # 包括当前日期
                    expected_window_size = min(
                        self.price_window, available_history_count
                    )
                    if actual_window_size != expected_window_size:
                        print(
                            f"⚠️ {stock} 价格窗口大小异常: 期望{expected_window_size}天, 实际{actual_window_size}天"
                        )
            else:
                price_history[stock] = []

        # 获取新闻历史
        news_history = {}

        # 修复：获取完整的新闻历史窗口（包含当前日期）
        # 新闻窗口应该包含当前日期在内的完整news_window天数
        news_days_collected = 0

        # 修复：从当前交易日开始，向前收集news_window天的新闻
        # 不再限制于trading_days范围，而是从实际日期向前收集
        current_trading_date = self.trading_days[self.current_day_index]

        for i in range(self.news_window):
            # 计算目标日期（向前推i天）
            target_date = current_trading_date - pd.Timedelta(days=i)
            target_date_str = target_date.strftime("%Y-%m-%d")

            # 检查该日期是否有新闻数据
            if target_date_str in self.news_data:
                news_history[target_date_str] = self.news_data[target_date_str]
                news_days_collected += 1

        # 调试输出：验证新闻窗口大小
        # 修复：现在应该能够收集到完整的news_window天数的新闻
        expected_news_days = self.news_window
        if news_days_collected != expected_news_days:
            print(
                f"⚠️ 新闻窗口大小异常: 期望{expected_news_days}天, 实际收集{news_days_collected}天"
            )

        # 获取扩展的基本面数据（支持同比和环比分析）
        fundamental_history = {}
        for stock in self.stocks:
            fundamental_history[stock] = self._get_comprehensive_fundamental_data(
                stock, self.current_date
            )

        # 构建状态字典
        state = {
            "date": current_date_str,
            "current_date": current_date_str,  # 确保智能体能正确获取日期
            "symbol": self.stocks[0] if self.stocks else "AAPL",  # 添加主要股票代码
            "symbols": self.stocks,  # 添加所有股票代码列表
            "positions": self.positions,
            "position_values": self.position_values,
            "previous_day_return": self.last_day_return,  # 上一交易日收益率
            # 收益率信息，用于基于收益率的跟踪
            "cumulative_return": getattr(self, 'cumulative_return', 0.0),
            "weekly_return": getattr(self, 'weekly_return', 0.0),
            "last_week_return": getattr(self, 'last_week_return', 0.0),
            "price_history": price_history,
            "news_history": news_history,
            "fundamental_data": fundamental_history,  # 基本面数据应当获取同比和环比所需的数据
            # 预置智能体输出字段 - 用于每日状态管理
            "analyst_outputs": {},
            "outlook_outputs": {},
            "trading_outputs": {},
        }

        return state

    def _get_comprehensive_fundamental_data(
        self, stock: str, current_date: datetime
    ) -> Dict[str, Any]:
        """
        获取综合的基本面数据，支持同比和环比分析

        返回包含以下内容的字典：
        - current: 最新的季度数据
        - quarterly_history: 最近8个季度的数据（用于环比分析）
        - annual_history: 最近3年的年度数据（用于同比分析）
        - growth_metrics: 计算好的同比和环比增长率
        """
        stock_fundamentals = self.fundamental_data.get(stock, {})

        if not stock_fundamentals:
            return {
                "current": {},
                "quarterly_history": [],
                "annual_history": [],
                "growth_metrics": {},
            }

        # 获取所有可用的季度数据，按日期排序
        available_quarters = []
        for quarter, data in stock_fundamentals.items():
            try:
                quarter_date = datetime.strptime(data["report_date"], "%Y-%m-%d")
                if quarter_date <= current_date:
                    available_quarters.append((quarter_date, quarter, data))
            except (ValueError, KeyError):
                continue

        # 按日期降序排序（最新的在前）
        available_quarters.sort(key=lambda x: x[0], reverse=True)

        # 获取最新的季度数据作为当前数据
        current_data = available_quarters[0][2] if available_quarters else {}

        # 获取最近8个季度的数据（用于环比分析）
        quarterly_history = []
        for quarter_date, quarter, data in available_quarters[:8]:
            quarterly_entry = {
                "quarter": quarter,
                "date": quarter_date.strftime("%Y-%m-%d"),
                "data": data,
            }
            quarterly_history.append(quarterly_entry)

        # 获取年度数据（用于同比分析）
        annual_history = self._extract_annual_data(available_quarters, current_date)

        # 计算增长率指标
        growth_metrics = self._calculate_growth_metrics(
            quarterly_history, annual_history
        )

        return {
            "current": current_data,
            "quarterly_history": quarterly_history,
            "annual_history": annual_history,
            "growth_metrics": growth_metrics,
        }

    def _extract_annual_data(
        self, available_quarters: List[Tuple], current_date: datetime
    ) -> List[Dict[str, Any]]:
        """
        从季度数据中提取年度数据，用于同比分析

        返回最近3年的年度汇总数据
        """
        annual_data = {}

        # 按年份组织数据
        for quarter_date, quarter, data in available_quarters:
            year = quarter_date.year
            if year not in annual_data:
                annual_data[year] = {
                    "year": year,
                    "quarters": [],
                    "revenue_sum": 0,
                    "net_income_sum": 0,
                    "operating_cash_flow_sum": 0,
                    "free_cash_flow_sum": 0,
                    "quarter_count": 0,
                }

            annual_data[year]["quarters"].append(
                {
                    "quarter": quarter,
                    "date": quarter_date.strftime("%Y-%m-%d"),
                    "data": data,
                }
            )

            # 累加年度数据
            annual_data[year]["revenue_sum"] += self._safe_float(data.get("revenue", 0))
            annual_data[year]["net_income_sum"] += self._safe_float(
                data.get("net_income", 0)
            )
            annual_data[year]["operating_cash_flow_sum"] += self._safe_float(
                data.get("operating_cash_flow", 0)
            )
            annual_data[year]["free_cash_flow_sum"] += self._safe_float(
                data.get("free_cash_flow", 0)
            )
            annual_data[year]["quarter_count"] += 1

        # 计算年度平均值和总值
        for year_data in annual_data.values():
            if year_data["quarter_count"] > 0:
                # 对于资产负债表项目，使用最新季度的值
                latest_quarter_data = (
                    year_data["quarters"][0]["data"] if year_data["quarters"] else {}
                )
                year_data["total_assets"] = self._safe_float(
                    latest_quarter_data.get("total_assets", 0)
                )
                year_data["total_liabilities"] = self._safe_float(
                    latest_quarter_data.get("total_liabilities", 0)
                )
                year_data["debt_to_equity"] = self._safe_float(
                    latest_quarter_data.get("debt_to_equity", 0)
                )

                # 对于损益表项目，使用累计值
                year_data["annual_revenue"] = year_data["revenue_sum"]
                year_data["annual_net_income"] = year_data["net_income_sum"]
                year_data["annual_operating_cash_flow"] = year_data[
                    "operating_cash_flow_sum"
                ]
                year_data["annual_free_cash_flow"] = year_data["free_cash_flow_sum"]

                # 计算年度利润率
                if year_data["annual_revenue"] != 0:
                    year_data["annual_profit_margin"] = (
                        year_data["annual_net_income"] / year_data["annual_revenue"]
                    )
                else:
                    year_data["annual_profit_margin"] = 0

        # 返回最近3年的数据，按年份降序排序
        sorted_years = sorted(annual_data.keys(), reverse=True)
        return [annual_data[year] for year in sorted_years[:3]]

    def _calculate_growth_metrics(
        self, quarterly_history: List[Dict], annual_history: List[Dict]
    ) -> Dict[str, Any]:
        """
        计算同比和环比增长率指标
        """
        growth_metrics = {
            "quarterly_growth": {},  # 环比增长率
            "annual_growth": {},  # 同比增长率
            "trends": {},  # 趋势分析
        }

        # 计算季度环比增长率
        if len(quarterly_history) >= 2:
            current_q = quarterly_history[0]["data"]
            previous_q = quarterly_history[1]["data"]

            growth_metrics["quarterly_growth"] = self._calculate_period_growth(
                current_q, previous_q, "QoQ"
            )

        # 计算年度同比增长率
        if len(annual_history) >= 2:
            current_year = annual_history[0]
            previous_year = annual_history[1]

            growth_metrics["annual_growth"] = self._calculate_annual_growth(
                current_year, previous_year
            )

        # 计算同季度同比增长率（如果有足够的季度数据）
        if len(quarterly_history) >= 4:
            current_q = quarterly_history[0]["data"]
            same_q_last_year = (
                quarterly_history[4]["data"]
                if len(quarterly_history) > 4
                else quarterly_history[-1]["data"]
            )

            growth_metrics["yoy_quarterly_growth"] = self._calculate_period_growth(
                current_q, same_q_last_year, "YoY"
            )

        # 计算趋势指标
        growth_metrics["trends"] = self._calculate_trends(
            quarterly_history, annual_history
        )

        return growth_metrics

    def _calculate_period_growth(
        self, current_data: Dict, previous_data: Dict, period_type: str
    ) -> Dict[str, float]:
        """
        计算两个时期之间的增长率
        """
        growth = {}

        # 定义需要计算增长率的关键指标
        key_metrics = [
            "revenue",
            "net_income",
            "eps",
            "operating_cash_flow",
            "free_cash_flow",
        ]

        for metric in key_metrics:
            current_value = self._safe_float(current_data.get(metric, 0))
            previous_value = self._safe_float(previous_data.get(metric, 0))

            if previous_value != 0:
                growth_rate = (current_value - previous_value) / previous_value
                growth[f"{metric}_{period_type.lower()}_growth"] = growth_rate
            else:
                growth[f"{metric}_{period_type.lower()}_growth"] = 0.0

        return growth

    def _calculate_annual_growth(
        self, current_year: Dict, previous_year: Dict
    ) -> Dict[str, float]:
        """
        计算年度增长率
        """
        growth = {}

        # 年度指标映射
        annual_metrics = {
            "revenue": "annual_revenue",
            "net_income": "annual_net_income",
            "operating_cash_flow": "annual_operating_cash_flow",
            "free_cash_flow": "annual_free_cash_flow",
        }

        for metric, annual_key in annual_metrics.items():
            current_value = self._safe_float(current_year.get(annual_key, 0))
            previous_value = self._safe_float(previous_year.get(annual_key, 0))

            if previous_value != 0:
                growth_rate = (current_value - previous_value) / previous_value
                growth[f"{metric}_yoy_growth"] = growth_rate
            else:
                growth[f"{metric}_yoy_growth"] = 0.0

        return growth

    def _calculate_trends(
        self, quarterly_history: List[Dict], annual_history: List[Dict]
    ) -> Dict[str, Any]:
        """
        计算趋势分析指标
        """
        trends = {}

        # 计算季度收入趋势（最近4个季度）
        if len(quarterly_history) >= 4:
            revenues = [
                self._safe_float(q["data"].get("revenue", 0))
                for q in quarterly_history[:4]
            ]
            trends["revenue_trend"] = self._analyze_trend(revenues)

        # 计算季度净利润趋势
        if len(quarterly_history) >= 4:
            net_incomes = [
                self._safe_float(q["data"].get("net_income", 0))
                for q in quarterly_history[:4]
            ]
            trends["net_income_trend"] = self._analyze_trend(net_incomes)

        # 计算利润率趋势
        if len(quarterly_history) >= 4:
            profit_margins = [
                self._safe_float(q["data"].get("profit_margin", 0))
                for q in quarterly_history[:4]
            ]
            trends["profit_margin_trend"] = self._analyze_trend(profit_margins)

        # 计算年度增长一致性
        if len(annual_history) >= 3:
            annual_revenues = [
                self._safe_float(y.get("annual_revenue", 0)) for y in annual_history[:3]
            ]
            trends["annual_growth_consistency"] = self._analyze_growth_consistency(
                annual_revenues
            )

        return trends

    def _analyze_trend(self, values: List[float]) -> Dict[str, Any]:
        """
        分析数值序列的趋势
        """
        if len(values) < 2:
            return {"direction": "insufficient_data", "strength": 0}

        # 计算趋势方向
        increases = 0
        decreases = 0

        for i in range(1, len(values)):
            if values[i - 1] > values[i]:  # 注意：values是按时间倒序的
                increases += 1
            elif values[i - 1] < values[i]:
                decreases += 1

        # 确定趋势方向
        if increases > decreases:
            direction = "improving"
        elif decreases > increases:
            direction = "declining"
        else:
            direction = "stable"

        # 计算趋势强度（0-1之间）
        total_changes = increases + decreases
        if total_changes > 0:
            strength = max(increases, decreases) / total_changes
        else:
            strength = 0

        return {
            "direction": direction,
            "strength": strength,
            "data_points": len(values),
        }

    def _analyze_growth_consistency(self, values: List[float]) -> Dict[str, Any]:
        """
        分析增长的一致性
        """
        if len(values) < 2:
            return {"consistency": "insufficient_data", "average_growth": 0}

        growth_rates = []
        for i in range(1, len(values)):
            if values[i] != 0:
                growth_rate = (values[i - 1] - values[i]) / values[
                    i
                ]  # 注意：values是按时间倒序的
                growth_rates.append(growth_rate)

        if not growth_rates:
            return {"consistency": "no_growth_data", "average_growth": 0}

        # 计算平均增长率
        avg_growth = sum(growth_rates) / len(growth_rates)

        # 计算增长率的标准差（一致性指标）
        std_dev = 0
        if len(growth_rates) > 1:
            variance = sum((rate - avg_growth) ** 2 for rate in growth_rates) / len(
                growth_rates
            )
            std_dev = variance**0.5

            # 一致性评分：标准差越小，一致性越高
            if avg_growth != 0:
                consistency_score = 1 / (1 + abs(std_dev / avg_growth))
            else:
                consistency_score = 1 / (1 + std_dev)
        else:
            consistency_score = 1.0

        return {
            "consistency": (
                "high"
                if consistency_score > 0.7
                else "medium" if consistency_score > 0.4 else "low"
            ),
            "consistency_score": consistency_score,
            "average_growth": avg_growth,
            "growth_volatility": std_dev,
        }

    def _safe_float(self, value):
        try:
            return float(value)
        except (ValueError, TypeError):
            return 0.0  # 返回一个合理的默认值

    def get_state(self) -> Dict[str, Any]:
        """
        获取当前环境状态（公共方法）

        返回:
            包含当前市场状态的字典
        """
        return self._get_state()
    
    def get_simplified_return_state(self) -> Dict[str, Any]:
        """
        获取简化的收益率状态，用于跨周传递
        
        返回:
            包含收益率信息的简化状态字典
        """
        return {
            "cumulative_return": self.cumulative_return,
            "weekly_return": self.weekly_return,
            "last_week_return": self.last_week_return,
            "net_worth": self.starting_cash * (1 + self.cumulative_return),
            "starting_cash": self.starting_cash
        }
    
    def start_new_week(self, week_number: int) -> None:
        """
        开始新周时重置周收益率，保留累计收益率
        
        Args:
            week_number: 新的周数
        """
        if hasattr(self, 'weekly_return') and hasattr(self, 'cumulative_return'):
            # 保存当前周收益率为上周收益率
            self.last_week_return = self.weekly_return
            # 重置当前周收益率
            self.weekly_return = 0.0
            print(f"🔄 开始第{week_number}周: 累计收益率={self.cumulative_return:.4f}, 上周收益率={self.last_week_return:.4f}")

    def is_done(self) -> bool:
        """
        检查环境是否已结束

        返回:
            如果环境已结束，则为 True，否则为 False
        """
        return self.done

    @property
    def current_net_worth(self) -> float:
        """
        获取当前净资产

        返回:
            当前净资产值
        """
        return self.starting_cash * (1 + self.cumulative_return)

    def is_trading_day(self, date=None) -> bool:
        """
        检查指定日期是否为交易日

        参数:
            date: 日期字符串(YYYY-MM-DD)或datetime对象，默认为当前日期

        返回:
            如果是交易日返回True，否则返回False
        """
        if date is None:
            check_date = self.current_date
        elif isinstance(date, str):
            check_date = datetime.strptime(date, "%Y-%m-%d")
        else:
            check_date = date

        # 检查该日期是否在交易日列表中
        date_str = check_date.strftime("%Y-%m-%d")

        # 检查所有股票是否都有该日期的价格数据
        for stock in self.stocks:
            if stock in self.price_data and len(self.price_data[stock]) > 0:
                stock_dates = self.price_data[stock]["date"].dt.strftime("%Y-%m-%d")
                if date_str in stock_dates.values:
                    return True

        return False

    def get_next_trading_day(self, date=None) -> Optional[datetime]:
        """
        获取下一个交易日

        参数:
            date: 起始日期，默认为当前日期

        返回:
            下一个交易日的datetime对象，如果没有则返回None
        """
        if date is None:
            start_date = self.current_date
        elif isinstance(date, str):
            start_date = datetime.strptime(date, "%Y-%m-%d")
        else:
            start_date = date

        # 在交易日列表中查找下一个交易日
        for trading_day in self.trading_days:
            if trading_day > start_date:
                return trading_day

        return None

    def has_price_data_for_date(self, date=None) -> bool:
        """
        检查指定日期是否有价格数据

        参数:
            date: 日期字符串(YYYY-MM-DD)或datetime对象，默认为当前日期

        返回:
            如果有价格数据返回True，否则返回False
        """
        if date is None:
            check_date = self.current_date
        elif isinstance(date, str):
            check_date = datetime.strptime(date, "%Y-%m-%d")
        else:
            check_date = date

        date_str = check_date.strftime("%Y-%m-%d")

        # 检查是否有任何股票在该日期有价格数据
        for stock in self.stocks:
            if stock in self.price_data and len(self.price_data[stock]) > 0:
                stock_dates = self.price_data[stock]["date"].dt.strftime("%Y-%m-%d")
                if date_str in stock_dates.values:
                    return True

        return False

    def get_trading_day_info(self) -> Dict[str, Any]:
        """
        获取当前交易日信息

        返回:
            包含交易日信息的字典
        """
        current_date_str = self.current_date.strftime("%Y-%m-%d")

        return {
            "current_date": current_date_str,
            "is_trading_day": self.is_trading_day(),
            "has_price_data": self.has_price_data_for_date(),
            "trading_day_index": self.current_day_index,
            "total_trading_days": len(self.trading_days),
            "next_trading_day": (
                self.get_next_trading_day().strftime("%Y-%m-%d")
                if self.get_next_trading_day() is not None
                else None
            ),
        }

    # =================
    # 决策快照捕获方法
    # =================

    def set_coalition_context(self, coalition_id: str, evaluation_run_id: str):
        """
        设置当前联盟上下文

        参数:
            coalition_id: 联盟ID
            evaluation_run_id: 评估运行ID
        """
        self._current_coalition_id = coalition_id
        self._current_evaluation_run_id = evaluation_run_id

        if self.snapshot_manager:
            self.snapshot_manager.start_evaluation_run(evaluation_run_id)

    def capture_agent_decision_context(
        self,
        agent_id: str,
        prompt_used: str,
        agent_output: str,
        parsed_decision: str,
        confidence_score: float = 0.0,
        reasoning: str = "",
        execution_time: float = 0.0,
        model_used: str = "",
        temperature: float = 0.0,
    ) -> Optional[str]:
        """
        捕获智能体决策上下文

        参数:
            agent_id: 智能体ID
            prompt_used: 使用的提示词
            agent_output: 智能体原始输出
            parsed_decision: 解析后的决策
            confidence_score: 置信度
            reasoning: 决策推理
            execution_time: 执行时间
            model_used: 使用的模型
            temperature: 温度参数

        返回:
            快照ID，如果创建失败则返回None
        """
        if not self.snapshot_manager:
            return None

        try:
            current_date_str = self.get_current_date_str()
            current_state = self.get_state()

            # 获取当前市场数据
            current_prices = {}
            for stock in self.stocks:
                if (
                    self.current_day_index < len(self.price_data[stock])
                    and not self.price_data[stock].empty
                ):
                    current_prices[stock] = self.price_data[stock].iloc[
                        self.current_day_index
                    ]

            # 解析决策类型
            decision_type = DecisionType.UNKNOWN
            if parsed_decision.upper() in ["BUY", "HOLD", "SELL"]:
                decision_type = DecisionType(parsed_decision.upper())

            # 创建决策快照（暂时不填充性能数据，将在step执行后更新）
            snapshot = self.snapshot_manager.create_snapshot(
                agent_id=agent_id,
                trade_date=current_date_str,
                symbol=(
                    self.stocks[0] if self.stocks else "UNKNOWN"
                ),  # 使用第一个股票作为主要标的
                coalition_id=self._current_coalition_id,
                # 智能体上下文
                price_history=current_state.get("price_history", {}),
                news_context=current_state.get("news_history", {}),
                fundamental_data=current_state.get("fundamental_data", {}),
                other_agent_outputs={},  # 可以在后续改进中填充
                prompt_used=prompt_used,
                agent_output=agent_output,
                parsed_decision=decision_type.value,
                confidence_score=confidence_score,
                reasoning=reasoning,
                execution_time=execution_time,
                model_used=model_used,
                temperature=temperature,
                # 市场上下文（使用当前价格数据）
                opening_price=(
                    float(current_prices[self.stocks[0]]["open"])
                    if current_prices and self.stocks
                    else 0.0
                ),
                closing_price=(
                    float(current_prices[self.stocks[0]]["close"])
                    if current_prices and self.stocks
                    else 0.0
                ),
                high_price=(
                    float(current_prices[self.stocks[0]]["high"])
                    if current_prices and self.stocks
                    else 0.0
                ),
                low_price=(
                    float(current_prices[self.stocks[0]]["low"])
                    if current_prices and self.stocks
                    else 0.0
                ),
                volume=(
                    int(current_prices[self.stocks[0]]["volume"])
                    if current_prices and self.stocks
                    else 0
                ),
                price_change=0.0,  # 将在后续计算
                price_change_percent=0.0,  # 将在后续计算
                # 性能影响（将在step执行后更新）
                decision_outcome="UNKNOWN",
                optimal_decision="UNKNOWN",
                actual_return=0.0,
                optimal_return=0.0,
                opportunity_cost=0.0,
                risk_exposure=0.0,
                sharpe_impact=0.0,
                coalition_performance_impact=0.0,
                shapley_contribution=0.0,
            )

            # 缓存决策上下文，用于后续性能更新
            self._decision_contexts[agent_id] = {
                "snapshot_id": snapshot.snapshot_id,
                "pre_decision_net_worth": self.current_net_worth,
                "decision_time": time.time(),
                "decision_type": decision_type,
            }

            return snapshot.snapshot_id

        except Exception as e:
            print(f"捕获决策上下文失败: {e}")
            return None

    def update_decision_performance(
        self,
        agent_id: str,
        post_decision_net_worth: float,
        market_movement: Dict[str, float] = None,
    ) -> bool:
        """
        更新决策性能数据

        参数:
            agent_id: 智能体ID
            post_decision_net_worth: 决策后的净值
            market_movement: 市场变动数据

        返回:
            是否更新成功
        """
        if not self.snapshot_manager or agent_id not in self._decision_contexts:
            return False

        try:
            context = self._decision_contexts[agent_id]
            snapshot_id = context["snapshot_id"]
            pre_net_worth = context["pre_decision_net_worth"]
            decision_type = context["decision_type"]

            # 计算实际收益
            actual_return = (
                (post_decision_net_worth - pre_net_worth) / pre_net_worth
                if pre_net_worth > 0
                else 0.0
            )

            # 估算最优决策（简单启发式方法）
            optimal_decision = self._estimate_optimal_decision(market_movement)

            # 估算最优收益（假设完美决策）
            optimal_return = self._estimate_optimal_return(
                market_movement, optimal_decision
            )

            # 计算机会成本
            opportunity_cost = optimal_return - actual_return

            # 判断市场结果
            market_outcome = MarketOutcome.NEUTRAL
            if market_movement and self.stocks:
                main_stock = self.stocks[0]
                if main_stock in market_movement:
                    movement = market_movement[main_stock]
                    if movement > 0.01:  # 1%以上上涨
                        market_outcome = MarketOutcome.POSITIVE
                    elif movement < -0.01:  # 1%以上下跌
                        market_outcome = MarketOutcome.NEGATIVE

            # 更新快照性能数据
            performance_data = {
                "decision_outcome": market_outcome.value,
                "optimal_decision": optimal_decision.value,
                "actual_return": actual_return,
                "optimal_return": optimal_return,
                "opportunity_cost": opportunity_cost,
                "risk_exposure": abs(actual_return),  # 简单的风险暴露度量
                "sharpe_impact": 0.0,  # 需要更复杂的计算
                "coalition_performance_impact": actual_return,  # 简化处理
                "shapley_contribution": 0.0,  # 将在Shapley计算时更新
            }

            success = self.snapshot_manager.update_snapshot_performance(
                snapshot_id, performance_data
            )

            if success:
                # 清除已处理的上下文
                del self._decision_contexts[agent_id]

            return success

        except Exception as e:
            print(f"更新决策性能失败: {e}")
            return False

    def _estimate_optimal_decision(
        self, market_movement: Dict[str, float] = None
    ) -> DecisionType:
        """
        估算最优决策（基于后验市场数据的启发式方法）

        参数:
            market_movement: 市场变动数据

        返回:
            估算的最优决策类型
        """
        if not market_movement or not self.stocks:
            return DecisionType.HOLD

        main_stock = self.stocks[0]
        if main_stock not in market_movement:
            return DecisionType.HOLD

        movement = market_movement[main_stock]

        # 简单的后验最优策略
        if movement > 0.02:  # 2%以上上涨，最优策略是买入
            return DecisionType.BUY
        elif movement < -0.02:  # 2%以上下跌，最优策略是卖出
            return DecisionType.SELL
        else:  # 小幅波动，最优策略是持有
            return DecisionType.HOLD

    def _estimate_optimal_return(
        self,
        market_movement: Dict[str, float] = None,
        optimal_decision: DecisionType = DecisionType.HOLD,
    ) -> float:
        """
        估算最优收益

        参数:
            market_movement: 市场变动数据
            optimal_decision: 最优决策类型

        返回:
            估算的最优收益率
        """
        if not market_movement or not self.stocks:
            return 0.0

        main_stock = self.stocks[0]
        if main_stock not in market_movement:
            return 0.0

        movement = market_movement[main_stock]

        # 根据最优决策计算理论最大收益
        if optimal_decision == DecisionType.BUY and movement > 0:
            return movement  # 买入获得全部上涨收益
        elif optimal_decision == DecisionType.SELL and movement < 0:
            return -movement  # 卖出避免全部下跌损失
        else:
            return 0.0  # 持有或其他情况

    def get_failure_cases(
        self, agent_id: str = None, min_severity: float = 0.0, limit: int = None
    ) -> List:
        """
        获取失败案例

        参数:
            agent_id: 智能体ID
            min_severity: 最小失败严重程度
            limit: 返回数量限制

        返回:
            失败案例列表
        """
        if not self.snapshot_manager:
            return []

        return self.snapshot_manager.get_failure_cases(agent_id, min_severity, limit)

    def export_decision_snapshots(self, file_path: str) -> bool:
        """
        导出决策快照到文件

        参数:
            file_path: 文件路径

        返回:
            是否导出成功
        """
        if not self.snapshot_manager:
            return False

        return self.snapshot_manager.export_snapshots(file_path)

    def get_snapshot_statistics(self) -> Dict[str, Any]:
        """
        获取快照统计信息

        返回:
            统计信息字典
        """
        if not self.snapshot_manager:
            return {"enabled": False}

        stats = self.snapshot_manager.get_statistics()
        stats["enabled"] = True
        return stats

    # 周期性优化支持方法
    def initialize_weekly_optimization_support(
        self,
        optimization_frequency: int = 7,
        optimization_start_date: Optional[str] = None,
    ):
        """
        初始化周期性优化支持

        参数:
            optimization_frequency: 优化频率（天数）
            optimization_start_date: 优化开始日期，如果为None则使用模拟开始日期
        """
        self.optimization_frequency = optimization_frequency
        self.optimization_start_date = optimization_start_date or self.start_date
        self.optimization_enabled = True
        self.last_optimization_day = -1
        self.optimization_history = []
        self.weekly_snapshots = {}  # 按周存储快照

        # 计算优化开始的交易日索引
        self.optimization_start_index = 0
        if optimization_start_date and optimization_start_date != self.start_date:
            for i, day in enumerate(self.trading_days):
                if day >= optimization_start_date:
                    self.optimization_start_index = i
                    break

        print(
            f"📅 周期性优化已启用: 每 {optimization_frequency} 天优化一次，从第 {self.optimization_start_index} 个交易日开始"
        )

    def should_trigger_optimization(self) -> bool:
        """
        检查是否应该触发优化

        返回:
            是否应该触发优化
        """
        if not hasattr(self, "optimization_enabled") or not self.optimization_enabled:
            return False

        # 检查是否达到最小优化间隔
        days_since_start = self.current_day_index - self.optimization_start_index

        if days_since_start < 0:
            return False

        # 检查是否到达优化周期边界
        if days_since_start > 0 and days_since_start % self.optimization_frequency == 0:
            # 确保不重复优化同一天
            if self.current_day_index != self.last_optimization_day:
                return True

        return False

    def get_current_optimization_week(self) -> int:
        """
        获取当前优化周数

        返回:
            当前周数（从1开始）
        """
        if not hasattr(self, "optimization_enabled") or not self.optimization_enabled:
            return 0

        days_since_start = max(
            0, self.current_day_index - self.optimization_start_index
        )
        return (days_since_start // self.optimization_frequency) + 1

    def get_week_date_range(self, week_number: Optional[int] = None) -> Tuple[str, str]:
        """
        获取指定周的日期范围

        参数:
            week_number: 周数，如果为None则使用当前周

        返回:
            (开始日期, 结束日期) 元组
        """
        if week_number is None:
            week_number = self.get_current_optimization_week()

        if week_number <= 0:
            return self.start_date, self.start_date

        # 计算该周的开始和结束索引
        week_start_index = (
            self.optimization_start_index
            + (week_number - 1) * self.optimization_frequency
        )
        week_end_index = min(
            week_start_index + self.optimization_frequency - 1,
            len(self.trading_days) - 1,
            self.current_day_index,
        )

        week_start_index = max(0, week_start_index)
        week_end_index = max(week_start_index, week_end_index)

        start_date = self.trading_days[week_start_index]
        end_date = self.trading_days[week_end_index]

        return start_date, end_date

    def capture_weekly_snapshot(
        self, week_number: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        捕获周期性快照

        参数:
            week_number: 周数，如果为None则使用当前周

        返回:
            周期性快照数据
        """
        if week_number is None:
            week_number = self.get_current_optimization_week()

        start_date, end_date = self.get_week_date_range(week_number)

        snapshot = {
            "week_number": week_number,
            "start_date": start_date,
            "end_date": end_date,
            "capture_date": self.get_current_date_str(),
            "trading_day_index": self.current_day_index,
            # 财务状态
            "net_worth": self.current_net_worth,
            "positions": self.positions.copy(),
            "position_values": self.position_values.copy(),
            # 性能指标
            "total_return": (self.current_net_worth - self.initial_net_worth)
            / self.initial_net_worth,
            "daily_returns": self._calculate_week_daily_returns(week_number),
            # 市场数据
            "market_prices": self._get_current_market_prices(),
            "market_movements": self._calculate_week_market_movements(week_number),
            # 决策快照
            "decision_snapshots": (
                self._get_week_decision_snapshots(week_number)
                if self.snapshot_manager
                else []
            ),
        }

        # 存储快照
        self.weekly_snapshots[week_number] = snapshot

        return snapshot

    def mark_optimization_executed(self):
        """标记优化已执行"""
        self.last_optimization_day = self.current_day_index
        current_week = self.get_current_optimization_week()

        self.optimization_history.append(
            {
                "week": current_week,
                "trading_day_index": self.current_day_index,
                "date": self.get_current_date_str(),
                "net_worth": self.current_net_worth,
                "timestamp": time.time(),
            }
        )

        print(
            f"✅ 已标记第 {current_week} 周优化完成 (交易日 {self.current_day_index})"
        )

    def update_agent_configuration(self, agent_id: str, config_updates: Dict[str, Any]):
        """
        更新智能体配置（用于动态配置更新）

        参数:
            agent_id: 智能体ID
            config_updates: 配置更新字典
        """
        if not hasattr(self, "agent_configurations"):
            self.agent_configurations = {}

        if agent_id not in self.agent_configurations:
            self.agent_configurations[agent_id] = {}

        self.agent_configurations[agent_id].update(config_updates)

        print(f"🔄 智能体 {agent_id} 配置已更新: {list(config_updates.keys())}")

    def get_agent_configuration(self, agent_id: str) -> Dict[str, Any]:
        """
        获取智能体配置

        参数:
            agent_id: 智能体ID

        返回:
            智能体配置字典
        """
        if not hasattr(self, "agent_configurations"):
            return {}

        return self.agent_configurations.get(agent_id, {})

    def _calculate_week_daily_returns(self, week_number: int) -> List[float]:
        """
        计算指定周的每日收益率

        参数:
            week_number: 周数

        返回:
            每日收益率列表
        """
        start_date, end_date = self.get_week_date_range(week_number)
        daily_returns = []

        # 这里需要根据实际的历史净值数据计算
        # 简化实现：使用当前净值作为基准
        base_net_worth = self.initial_net_worth
        current_return = (self.current_net_worth - base_net_worth) / base_net_worth

        # 模拟每日收益（实际实现中应该使用历史数据）
        for i in range(self.optimization_frequency):
            daily_returns.append(current_return / self.optimization_frequency)

        return daily_returns

    def _calculate_week_market_movements(self, week_number: int) -> Dict[str, float]:
        """
        计算指定周的市场变动

        参数:
            week_number: 周数

        返回:
            市场变动字典
        """
        start_date, end_date = self.get_week_date_range(week_number)
        market_movements = {}

        for stock in self.stocks:
            try:
                # 获取周开始和结束的价格
                week_start_index = (
                    self.optimization_start_index
                    + (week_number - 1) * self.optimization_frequency
                )
                week_end_index = min(
                    week_start_index + self.optimization_frequency - 1,
                    len(self.price_data[stock]) - 1,
                )

                if week_start_index < len(
                    self.price_data[stock]
                ) and week_end_index < len(self.price_data[stock]):
                    start_price = self.price_data[stock].iloc[week_start_index]["close"]
                    end_price = self.price_data[stock].iloc[week_end_index]["close"]
                    movement = (
                        (end_price - start_price) / start_price
                        if start_price > 0
                        else 0.0
                    )
                    market_movements[stock] = movement
                else:
                    market_movements[stock] = 0.0

            except Exception as e:
                print(f"计算 {stock} 市场变动失败: {e}")
                market_movements[stock] = 0.0

        return market_movements

    def _get_current_market_prices(self) -> Dict[str, float]:
        """
        获取当前市场价格

        返回:
            当前价格字典
        """
        prices = {}
        for stock in self.stocks:
            if self.current_day_index < len(self.price_data[stock]):
                prices[stock] = self.price_data[stock].iloc[self.current_day_index][
                    "close"
                ]
            else:
                prices[stock] = 0.0
        return prices

    def _get_week_decision_snapshots(self, week_number: int) -> List[Dict[str, Any]]:
        """
        获取指定周的决策快照

        参数:
            week_number: 周数

        返回:
            决策快照列表
        """
        if not self.snapshot_manager:
            return []

        # 获取所有快照
        all_snapshots = self.snapshot_manager.snapshots

        # 过滤出指定周的快照
        start_date, end_date = self.get_week_date_range(week_number)
        week_snapshots = []

        for snapshot in all_snapshots:
            snapshot_date = snapshot.market_context.trade_date
            if start_date <= snapshot_date <= end_date:
                week_snapshots.append(snapshot.to_dict())

        return week_snapshots

    def get_optimization_status(self) -> Dict[str, Any]:
        """
        获取优化状态信息

        返回:
            优化状态字典
        """
        if not hasattr(self, "optimization_enabled"):
            return {"enabled": False}

        return {
            "enabled": self.optimization_enabled,
            "optimization_frequency": getattr(self, "optimization_frequency", 7),
            "current_week": self.get_current_optimization_week(),
            "current_trading_day": self.current_day_index,
            "total_trading_days": len(self.trading_days),
            "last_optimization_day": getattr(self, "last_optimization_day", -1),
            "should_optimize": self.should_trigger_optimization(),
            "optimization_history_count": len(
                getattr(self, "optimization_history", [])
            ),
            "weekly_snapshots_count": len(getattr(self, "weekly_snapshots", {})),
        }

    def export_weekly_data(self, output_path: str) -> bool:
        """
        导出周期性数据

        参数:
            output_path: 输出路径

        返回:
            是否导出成功
        """
        try:
            export_data = {
                "optimization_status": self.get_optimization_status(),
                "weekly_snapshots": getattr(self, "weekly_snapshots", {}),
                "optimization_history": getattr(self, "optimization_history", []),
                "agent_configurations": getattr(self, "agent_configurations", {}),
                "export_timestamp": time.time(),
                "export_date": self.get_current_date_str(),
            }

            import json

            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)

            print(f"📁 周期性数据已导出: {output_path}")
            return True

        except Exception as e:
            print(f"导出周期性数据失败: {e}")
            return False
