#!/usr/bin/env python3
"""
测试TradingSimulator的_is_week_end方法
"""

import unittest
from unittest.mock import MagicMock, patch
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from contribution_assessment.trading_simulator import TradingSimulator


class TestTradingSimulatorWeekEnd(unittest.TestCase):
    """测试TradingSimulator的_is_week_end方法"""

    def setUp(self):
        """设置测试环境"""
        # 创建模拟日志记录器
        self.mock_logger = MagicMock(spec=logging.Logger)
        
        # 创建基础配置
        self.base_config = {
            "trading_days_per_week": 5,  # 每周5个交易日
            "risk_free_rate": 0.02,
            "weekly_evaluation_enabled": True
        }
        
        # 创建TradingSimulator实例
        self.simulator = TradingSimulator(
            base_config=self.base_config,
            logger=self.mock_logger
        )
        
        # 启用详细日志记录
        self.simulator.detailed_logging = True

    def test_is_week_end_first_week(self):
        """测试第一周的周末检测"""
        # 第一周的交易日（从0开始）
        days = [0, 1, 2, 3, 4]
        
        # 预期结果：只有第4天（索引为4）是周末
        expected_results = [False, False, False, False, True]
        
        for day, expected in zip(days, expected_results):
            with self.subTest(day=day):
                result = self.simulator._is_week_end(day)
                self.assertEqual(result, expected, f"第{day}天应该返回{expected}")

    def test_is_week_end_second_week(self):
        """测试第二周的周末检测"""
        # 第二周的交易日（从5开始）
        days = [5, 6, 7, 8, 9]
        
        # 预期结果：只有第9天（索引为9）是周末
        expected_results = [False, False, False, False, True]
        
        for day, expected in zip(days, expected_results):
            with self.subTest(day=day):
                result = self.simulator._is_week_end(day)
                self.assertEqual(result, expected, f"第{day}天应该返回{expected}")

    def test_is_week_end_third_week(self):
        """测试第三周的周末检测"""
        # 第三周的交易日（从10开始）
        days = [10, 11, 12, 13, 14]
        
        # 预期结果：只有第14天（索引为14）是周末
        expected_results = [False, False, False, False, True]
        
        for day, expected in zip(days, expected_results):
            with self.subTest(day=day):
                result = self.simulator._is_week_end(day)
                self.assertEqual(result, expected, f"第{day}天应该返回{expected}")

    def test_is_week_end_different_trading_days(self):
        """测试不同交易日数量的周末检测"""
        # 修改每周交易日数量为3
        self.simulator.trading_days_per_week = 3
        
        # 测试前两周的交易日
        days = [0, 1, 2, 3, 4, 5]
        
        # 预期结果：第2天和第5天是周末
        expected_results = [False, False, True, False, False, True]
        
        for day, expected in zip(days, expected_results):
            with self.subTest(day=day):
                result = self.simulator._is_week_end(day)
                self.assertEqual(result, expected, f"第{day}天应该返回{expected}")


if __name__ == "__main__":
    unittest.main()