#!/usr/bin/env python3
"""
测试周期边界处理逻辑
"""

import unittest
from unittest.mock import MagicMock, patch
import logging
import sys
import os
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from contribution_assessment.trading_simulator import TradingSimulator
from contribution_assessment.services.simulation_service import SimulationService


class TestWeeklyCycleBoundary(unittest.TestCase):
    """测试周期边界处理逻辑"""

    def setUp(self):
        """设置测试环境"""
        # 创建模拟日志记录器
        self.mock_logger = MagicMock(spec=logging.Logger)
        
        # 创建基础配置
        self.base_config = {
            "trading_days_per_week": 5,  # 每周5个交易日
            "risk_free_rate": 0.02,
            "weekly_evaluation_enabled": True,
            "start_date": "2025-01-01",
            "end_date": "2025-01-15",
            "stocks": ["AAPL"],
            "starting_cash": 1000000
        }
        
        # 创建TradingSimulator实例
        self.simulator = TradingSimulator(
            base_config=self.base_config,
            logger=self.mock_logger
        )
        
        # 启用详细日志记录
        self.simulator.detailed_logging = True

    @patch('contribution_assessment.trading_simulator.StockTradingEnv')
    def test_run_simulation_with_week_number(self, mock_env_class):
        """测试使用周数运行模拟"""
        # 创建模拟环境
        mock_env = MagicMock()
        mock_env.total_days = 10
        mock_env.get_state.return_value = {"current_date": "2025-01-01", "cash": 1000000, "positions": {}}
        mock_env.step.return_value = (None, None, False, {"daily_return": 0.01, "net_worth": 1010000})
        mock_env_class.return_value = mock_env
        
        # 创建模拟智能体
        mock_agent = MagicMock()
        mock_agent.process.return_value = {"action": "buy", "confidence": 0.8}
        
        # 创建联盟
        coalition = frozenset(["NAA", "TRA"])
        agents = {"NAA": mock_agent, "TRA": mock_agent}
        
        # 运行模拟，指定周数为2
        result = self.simulator.run_simulation_for_coalition(
            coalition=coalition,
            agents=agents,
            simulation_days=10,
            current_week_number=2,  # 指定为第2周
            stop_after_one_week=True
        )
        
        # 验证结果
        self.assertTrue(result["success"] if "success" in result else True)
        self.assertEqual(self.simulator.current_simulation_week, 2)
        
        # 验证模拟环境的调用
        mock_env_class.assert_called_once()
        self.assertTrue(mock_env.step.called)
        
        # 验证日志记录
        log_calls = [call[0][0] for call in self.mock_logger.info.call_args_list]
        week_number_logs = [log for log in log_calls if "当前模拟周数" in log]
        self.assertTrue(any("当前模拟周数: 2" in log for log in week_number_logs))

    @patch('contribution_assessment.trading_simulator.TradingSimulator.run_simulation_for_coalition')
    def test_simulation_service_passes_week_number(self, mock_run_simulation):
        """测试SimulationService正确传递周数"""
        # 创建模拟TradingSimulator
        mock_trading_simulator = MagicMock()
        mock_run_simulation.return_value = {
            "sharpe_ratio": 1.5,
            "daily_returns": [0.01] * 5,
            "weekly_data": [{"week": 2, "returns": [0.01] * 5}],
            "total_days": 5,
            "simulation_time": 0.5
        }
        
        # 创建SimulationService实例
        simulation_service = SimulationService(
            trading_simulator=self.simulator,
            logger=self.mock_logger
        )
        
        # 创建测试配置
        config = {
            "current_week_number": 2,  # 指定为第2周
            "simulation_days": 10,
            "start_date": "2025-01-01",
            "end_date": "2025-01-15"
        }
        
        # 创建联盟
        coalition = frozenset(["NAA", "TRA"])
        agents = {"NAA": MagicMock(), "TRA": MagicMock()}
        
        # 调用_run_phased_simulation方法
        with patch.object(simulation_service, 'trading_simulator', mock_trading_simulator):
            simulation_service._run_phased_simulation(
                coalitions={coalition},
                agents=agents,
                config=config,
                max_concurrent=1
            )
            
            # 验证run_simulation_for_coalition的调用
            mock_trading_simulator.run_simulation_for_coalition.assert_called_once()
            call_args = mock_trading_simulator.run_simulation_for_coalition.call_args[1]
            self.assertEqual(call_args.get("current_week_number"), 2)


if __name__ == "__main__":
    unittest.main()