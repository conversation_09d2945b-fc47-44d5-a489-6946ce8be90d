"""
智能体状态管理功能的单元测试

测试内容：
1. OPROBaseAgent的序列化和反序列化功能
2. AgentStateManager的状态保存和恢复功能
3. 跨周状态传递的完整性验证
4. 错误处理和边界情况
"""

import unittest
import tempfile
import shutil
import os
import json
from datetime import datetime
from typing import Dict, Any
from unittest.mock import Mock, patch

# 添加项目根目录到路径
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.opro_base_agent import OPROBaseAgent
from state_management.agent_state_manager import AgentStateManager


class MockOPROAgent(OPROBaseAgent):
    """用于测试的Mock OPRO智能体类"""
    
    def get_default_prompt_template(self) -> str:
        return "这是一个测试提示词模板，用于验证智能体状态管理功能。"
    
    def process(self, state: Dict[str, Any], is_full_coalition: bool = False) -> Dict[str, Any]:
        # 模拟处理过程，更新统计信息
        self.analysis_count += 1
        result = {
            "agent_id": self.agent_id,
            "analysis": f"模拟分析结果 #{self.analysis_count}",
            "confidence": 0.85,
            "timestamp": datetime.now().isoformat()
        }
        self.last_analysis = result
        return result


class TestOPROBaseAgentSerialization(unittest.TestCase):
    """测试OPROBaseAgent序列化功能"""
    
    def setUp(self):
        """测试前准备"""
        self.agent_id = "TEST_AGENT"
        self.mock_logger = Mock()
        self.agent = MockOPROAgent(
            agent_id=self.agent_id,
            llm_interface=None,
            logger=self.mock_logger,
            opro_enabled=True,
            max_prompt_history=10
        )
    
    def test_serialization_basic_attributes(self):
        """测试基础属性序列化"""
        # 设置一些状态
        self.agent.analysis_count = 5
        self.agent.total_processing_time = 12.34
        self.agent.last_analysis = {"test": "result"}
        
        # 创建快照
        snapshot = self.agent.create_serializable_snapshot()
        
        # 验证基础属性
        self.assertEqual(snapshot["agent_id"], self.agent_id)
        self.assertEqual(snapshot["analysis_count"], 5)
        self.assertEqual(snapshot["total_processing_time"], 12.34)
        self.assertEqual(snapshot["last_analysis"], {"test": "result"})
        self.assertTrue(snapshot["_is_serialized_snapshot"])
    
    def test_serialization_opro_attributes(self):
        """测试OPRO相关属性序列化"""
        # 设置OPRO状态
        self.agent.current_prompt = "优化后的提示词"
        self.agent.prompt_version = "2.1.0"
        self.agent.prompt_history = [{"version": "1.0.0", "prompt": "原始提示词"}]
        self.agent._opro_stats["total_optimizations"] = 3
        
        # 获取自定义序列化属性
        custom_attrs = self.agent._get_custom_serializable_attributes()
        
        # 验证OPRO属性
        self.assertTrue(custom_attrs["opro_enabled"])
        self.assertEqual(custom_attrs["current_prompt"], "优化后的提示词")
        self.assertEqual(custom_attrs["prompt_version"], "2.1.0")
        self.assertEqual(len(custom_attrs["prompt_history"]), 1)
        self.assertEqual(custom_attrs["opro_stats"]["total_optimizations"], 3)
    
    def test_serialization_weekly_io_data(self):
        """测试weekly_io_data序列化"""
        # 设置IO数据
        self.agent.weekly_io_data = [
            {
                "input_state": {"cash": 1000, "date": "2025-01-01"},
                "output": {"action": "BUY", "amount": 100},
                "timestamp": datetime.now().isoformat()
            },
            {
                "input_state": {"cash": 900, "date": "2025-01-02"},
                "output": {"action": "HOLD", "amount": 0},
                "timestamp": datetime.now().isoformat()
            }
        ]
        
        # 获取自定义序列化属性
        custom_attrs = self.agent._get_custom_serializable_attributes()
        
        # 验证IO数据序列化
        self.assertIn("weekly_io_data", custom_attrs)
        self.assertEqual(len(custom_attrs["weekly_io_data"]), 2)
        self.assertEqual(custom_attrs["weekly_io_data"][0]["output"]["action"], "BUY")
    
    def test_restoration_from_snapshot(self):
        """测试从快照恢复智能体"""
        # 设置原始智能体状态
        self.agent.analysis_count = 8
        self.agent.total_processing_time = 25.67
        self.agent.current_prompt = "测试优化提示词"
        self.agent.prompt_version = "3.0.0"
        self.agent._opro_stats["total_optimizations"] = 5
        
        # 创建快照
        snapshot = self.agent.create_serializable_snapshot()
        
        # 从快照恢复新实例
        restored_agent = MockOPROAgent.restore_from_snapshot(
            snapshot=snapshot,
            llm_interface=None,
            logger=self.mock_logger
        )
        
        # 验证恢复的状态
        self.assertEqual(restored_agent.agent_id, self.agent_id)
        self.assertEqual(restored_agent.analysis_count, 8)
        self.assertEqual(restored_agent.total_processing_time, 25.67)
        self.assertEqual(restored_agent.current_prompt, "测试优化提示词")
        self.assertEqual(restored_agent.prompt_version, "3.0.0")
        self.assertEqual(restored_agent._opro_stats["total_optimizations"], 5)
    
    def test_restoration_with_invalid_snapshot(self):
        """测试无效快照的错误处理"""
        invalid_snapshot = {"agent_id": "TEST", "invalid": True}
        
        with self.assertRaises(ValueError):
            MockOPROAgent.restore_from_snapshot(
                snapshot=invalid_snapshot,
                llm_interface=None,
                logger=self.mock_logger
            )


class TestAgentStateManager(unittest.TestCase):
    """测试AgentStateManager功能"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.mock_logger = Mock()
        self.state_manager = AgentStateManager(
            state_dir=self.temp_dir,
            logger=self.mock_logger
        )
        
        # 创建测试智能体
        self.agents = {}
        for i in range(3):
            agent_id = f"AGENT_{i}"
            agent = MockOPROAgent(
                agent_id=agent_id,
                llm_interface=None,
                logger=self.mock_logger,
                opro_enabled=True
            )
            # 设置一些状态
            agent.analysis_count = i * 2 + 1
            agent.total_processing_time = (i + 1) * 10.5
            agent.current_prompt = f"智能体{i}的优化提示词"
            agent._opro_stats["total_optimizations"] = i + 1
            
            self.agents[agent_id] = agent
    
    def tearDown(self):
        """测试后清理"""
        shutil.rmtree(self.temp_dir)
    
    def test_save_agents_state(self):
        """测试保存智能体状态"""
        week_number = 1
        
        # 保存状态
        success = self.state_manager.save_agents_state(self.agents, week_number, is_full_coalition=True)
        
        # 验证保存成功
        self.assertTrue(success)
        
        # 验证文件存在
        state_file = os.path.join(self.temp_dir, f"agent_states_week_{week_number}.json")
        self.assertTrue(os.path.exists(state_file))
        
        # 验证文件内容
        with open(state_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        self.assertIn("metadata", data)
        self.assertIn("agent_snapshots", data)
        self.assertEqual(data["metadata"]["week_number"], week_number)
        self.assertEqual(data["metadata"]["total_agents"], 3)
        self.assertEqual(len(data["agent_snapshots"]), 3)
    
    def test_restore_agents_state(self):
        """测试恢复智能体状态"""
        week_number = 2
        
        # 先保存状态
        self.state_manager.save_agents_state(self.agents, week_number, is_full_coalition=True)
        
        # 创建智能体创建回调
        def agent_creation_callback(agent_id: str) -> Dict[str, Any]:
            return {
                "llm_interface": None,
                "logger": self.mock_logger
            }
        
        # 恢复状态
        restored_agents = self.state_manager.restore_agents_state(
            week_number=week_number,
            agent_creation_callback=agent_creation_callback
        )
        
        # 验证恢复成功
        self.assertIsNotNone(restored_agents)
        self.assertEqual(len(restored_agents), 3)
        
        # 验证状态正确恢复
        for agent_id, original_agent in self.agents.items():
            restored_agent = restored_agents[agent_id]
            self.assertEqual(restored_agent.agent_id, agent_id)
            self.assertEqual(restored_agent.analysis_count, original_agent.analysis_count)
            self.assertEqual(restored_agent.total_processing_time, original_agent.total_processing_time)
            self.assertEqual(restored_agent.current_prompt, original_agent.current_prompt)
    
    def test_has_state_for_week(self):
        """测试检查周状态存在性"""
        week_number = 3
        
        # 初始状态：不存在
        self.assertFalse(self.state_manager.has_state_for_week(week_number))
        
        # 保存状态后：存在
        self.state_manager.save_agents_state(self.agents, week_number, is_full_coalition=True)
        self.assertTrue(self.state_manager.has_state_for_week(week_number))
    
    def test_get_available_weeks(self):
        """测试获取可用周数"""
        # 保存多个周的状态
        weeks = [1, 2, 5, 3]
        for week in weeks:
            self.state_manager.save_agents_state(self.agents, week, is_full_coalition=True)
        
        # 获取可用周数
        available_weeks = self.state_manager.get_available_weeks()
        
        # 验证结果（应该按降序排列）
        expected_weeks = sorted(weeks, reverse=True)
        self.assertEqual(available_weeks, expected_weeks)
    
    def test_get_state_info(self):
        """测试获取状态信息"""
        week_number = 4
        
        # 保存状态
        self.state_manager.save_agents_state(self.agents, week_number, is_full_coalition=True)
        
        # 获取状态信息
        state_info = self.state_manager.get_state_info(week_number)
        
        # 验证信息
        self.assertIsNotNone(state_info)
        self.assertIn("metadata", state_info)
        self.assertIn("agent_info", state_info)
        self.assertEqual(len(state_info["agent_info"]), 3)
        
        # 验证智能体信息
        for agent_id, agent_info in state_info["agent_info"].items():
            self.assertIn("analysis_count", agent_info)
            self.assertIn("opro_enabled", agent_info)
            self.assertIn("optimization_count", agent_info)
    
    def test_cleanup_old_versions(self):
        """测试清理旧版本"""
        # 设置较小的保留数量
        self.state_manager.max_versions_per_agent = 2
        
        # 保存多个版本
        weeks = [1, 2, 3, 4, 5]
        for week in weeks:
            self.state_manager.save_agents_state(self.agents, week, is_full_coalition=True)
        
        # 触发清理（通过保存新版本）
        self.state_manager.save_agents_state(self.agents, 6, is_full_coalition=True)
        
        # 验证只保留最近的版本
        available_weeks = self.state_manager.get_available_weeks()
        self.assertLessEqual(len(available_weeks), 3)  # 最多保留3个版本（包括刚保存的）
    
    def test_error_handling_corrupted_file(self):
        """测试损坏文件的错误处理"""
        week_number = 7
        
        # 创建损坏的状态文件
        state_file = os.path.join(self.temp_dir, f"agent_states_week_{week_number}.json")
        with open(state_file, 'w') as f:
            f.write("invalid json content")
        
        # 尝试恢复状态
        restored_agents = self.state_manager.restore_agents_state(
            week_number=week_number,
            agent_creation_callback=lambda x: {"llm_interface": None, "logger": self.mock_logger}
        )
        
        # 应该返回None并记录错误
        self.assertIsNone(restored_agents)


class TestWeeklyStateTransfer(unittest.TestCase):
    """测试跨周状态传递的完整流程"""
    
    def setUp(self):
        """测试前准备"""
        self.temp_dir = tempfile.mkdtemp()
        self.mock_logger = Mock()
    
    def tearDown(self):
        """测试后清理"""
        shutil.rmtree(self.temp_dir)
    
    def test_complete_state_transfer_cycle(self):
        """测试完整的状态传递周期"""
        state_manager = AgentStateManager(
            state_dir=self.temp_dir,
            logger=self.mock_logger
        )
        
        # 第1周：创建初始智能体
        week1_agents = {}
        for i in range(2):
            agent_id = f"AGENT_{i}"
            agent = MockOPROAgent(
                agent_id=agent_id,
                llm_interface=None,
                logger=self.mock_logger,
                opro_enabled=True
            )
            # 模拟一些工作
            agent.analysis_count = i + 1
            agent.current_prompt = f"第1周智能体{i}提示词"
            week1_agents[agent_id] = agent
        
        # 保存第1周状态
        success = state_manager.save_agents_state(week1_agents, 1, is_full_coalition=True)
        self.assertTrue(success)
        
        # 第2周：从状态恢复智能体
        def agent_creation_callback(agent_id: str):
            return {"llm_interface": None, "logger": self.mock_logger}
        
        week2_agents = state_manager.restore_agents_state(
            week_number=1,  # 从第1周恢复
            agent_creation_callback=agent_creation_callback
        )
        
        # 验证恢复成功并保持状态
        self.assertIsNotNone(week2_agents)
        self.assertEqual(len(week2_agents), 2)
        
        for agent_id, original_agent in week1_agents.items():
            restored_agent = week2_agents[agent_id]
            self.assertEqual(restored_agent.analysis_count, original_agent.analysis_count)
            self.assertEqual(restored_agent.current_prompt, original_agent.current_prompt)
        
        # 模拟第2周的更多工作
        for agent in week2_agents.values():
            agent.analysis_count += 3  # 增加分析次数
            agent.current_prompt = agent.current_prompt.replace("第1周", "第2周")  # 更新提示词
            agent._opro_stats["total_optimizations"] += 1  # 增加优化次数
        
        # 保存第2周状态
        success = state_manager.save_agents_state(week2_agents, 2, is_full_coalition=True)
        self.assertTrue(success)
        
        # 验证状态累积效果
        week3_agents = state_manager.restore_agents_state(
            week_number=2,  # 从第2周恢复
            agent_creation_callback=agent_creation_callback
        )
        
        self.assertIsNotNone(week3_agents)
        for agent_id in week1_agents.keys():
            week3_agent = week3_agents[agent_id]
            original_count = week1_agents[agent_id].analysis_count
            # 应该包含第1周和第2周的累积分析次数
            self.assertEqual(week3_agent.analysis_count, original_count + 3)
            self.assertIn("第2周", week3_agent.current_prompt)
            self.assertEqual(week3_agent._opro_stats["total_optimizations"], 1)


if __name__ == '__main__':
    # 运行测试
    unittest.main(verbosity=2)