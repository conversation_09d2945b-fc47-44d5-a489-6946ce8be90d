#!/usr/bin/env python3
"""
测试交易日期调整逻辑
"""

import unittest
from unittest.mock import MagicMock, patch
import logging
import sys
import os
import pandas as pd
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from contribution_assessment.trading_simulator import TradingSimulator


class TestTradingDateAdjustment(unittest.TestCase):
    """测试交易日期调整逻辑"""

    def setUp(self):
        """设置测试环境"""
        # 创建模拟日志记录器
        self.mock_logger = MagicMock(spec=logging.Logger)
        
        # 创建基础配置
        self.base_config = {
            "trading_days_per_week": 5,  # 每周5个交易日
            "risk_free_rate": 0.02,
            "weekly_evaluation_enabled": True,
            "start_date": "2025-01-01",
            "end_date": "2025-01-31",
            "stocks": ["AAPL"],
            "starting_cash": 1000000
        }
        
        # 创建TradingSimulator实例
        self.simulator = TradingSimulator(
            base_config=self.base_config,
            logger=self.mock_logger
        )
        
        # 启用详细日志记录
        self.simulator.detailed_logging = True

    @patch('contribution_assessment.trading_simulator.StockTradingEnv')
    def test_create_trading_environment_week1(self, mock_env_class):
        """测试第一周的交易环境创建"""
        # 创建模拟环境
        mock_env = MagicMock()
        mock_env_class.return_value = mock_env
        
        # 调用_create_trading_environment方法，指定为第1周
        env = self.simulator._create_trading_environment(
            simulation_days=5,
            coalition_id="test_coalition",
            current_week_number=1
        )
        
        # 验证StockTradingEnv的调用
        mock_env_class.assert_called_once()
        
        # 获取传递给StockTradingEnv的配置
        call_args = mock_env_class.call_args[0][0]
        
        # 验证起始日期未被调整（第1周）
        self.assertEqual(call_args.get("start_date"), "2025-01-01")

    @patch('contribution_assessment.trading_simulator.StockTradingEnv')
    def test_create_trading_environment_week2(self, mock_env_class):
        """测试第二周的交易环境创建"""
        # 创建模拟环境
        mock_env = MagicMock()
        mock_env_class.return_value = mock_env
        
        # 调用_create_trading_environment方法，指定为第2周
        env = self.simulator._create_trading_environment(
            simulation_days=5,
            coalition_id="test_coalition",
            current_week_number=2
        )
        
        # 验证StockTradingEnv的调用
        mock_env_class.assert_called_once()
        
        # 获取传递给StockTradingEnv的配置
        call_args = mock_env_class.call_args[0][0]
        
        # 验证起始日期已被调整（第2周）
        # 假设每周5个交易日对应7个日历日
        expected_start_date = pd.to_datetime("2025-01-01") + pd.Timedelta(days=7)
        self.assertEqual(call_args.get("start_date"), expected_start_date.strftime("%Y-%m-%d"))

    @patch('contribution_assessment.trading_simulator.StockTradingEnv')
    def test_create_trading_environment_week3(self, mock_env_class):
        """测试第三周的交易环境创建"""
        # 创建模拟环境
        mock_env = MagicMock()
        mock_env_class.return_value = mock_env
        
        # 调用_create_trading_environment方法，指定为第3周
        env = self.simulator._create_trading_environment(
            simulation_days=5,
            coalition_id="test_coalition",
            current_week_number=3
        )
        
        # 验证StockTradingEnv的调用
        mock_env_class.assert_called_once()
        
        # 获取传递给StockTradingEnv的配置
        call_args = mock_env_class.call_args[0][0]
        
        # 验证起始日期已被调整（第3周）
        # 假设每周5个交易日对应7个日历日
        expected_start_date = pd.to_datetime("2025-01-01") + pd.Timedelta(days=14)
        self.assertEqual(call_args.get("start_date"), expected_start_date.strftime("%Y-%m-%d"))

    @patch('contribution_assessment.trading_simulator.StockTradingEnv')
    def test_run_simulation_passes_week_number(self, mock_env_class):
        """测试run_simulation_for_coalition方法传递周数"""
        # 创建模拟环境
        mock_env = MagicMock()
        mock_env.total_days = 10
        mock_env.get_state.return_value = {"current_date": "2025-01-08", "cash": 1000000, "positions": {}}
        mock_env.step.return_value = (None, None, False, {"daily_return": 0.01, "net_worth": 1010000})
        mock_env_class.return_value = mock_env
        
        # 创建模拟智能体
        mock_agent = MagicMock()
        mock_agent.process.return_value = {"action": "buy", "confidence": 0.8}
        
        # 创建联盟
        coalition = frozenset(["NAA", "TRA"])
        agents = {"NAA": mock_agent, "TRA": mock_agent}
        
        # 运行模拟，指定周数为2
        with patch.object(self.simulator, '_create_trading_environment') as mock_create_env:
            mock_create_env.return_value = mock_env
            
            result = self.simulator.run_simulation_for_coalition(
                coalition=coalition,
                agents=agents,
                simulation_days=5,
                current_week_number=2,
                stop_after_one_week=True
            )
            
            # 验证_create_trading_environment的调用
            mock_create_env.assert_called_once()
            call_args = mock_create_env.call_args
            self.assertEqual(call_args[0][2], 2)  # 验证传递的current_week_number参数


if __name__ == "__main__":
    unittest.main()