#!/usr/bin/env python3
"""
交易日历管理器

负责管理交易日的获取、验证和周期划分功能
"""

import os
import sys
import sqlite3
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Set
import pandas as pd

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from data.prepare_data import get_database_path


class TradingCalendar:
    """
    交易日历管理器
    
    负责从数据库获取交易日数据，并提供周期划分功能
    """
    
    def __init__(self, logger=None):
        """
        初始化交易日历
        
        Args:
            logger: 日志记录器
        """
        self.logger = logger
        self._trading_days_cache = {}  # 缓存交易日数据
        
        if self.logger:
            self.logger.info("交易日历管理器初始化完成")
    
    def get_trading_days(self, 
                        start_date: str, 
                        end_date: str, 
                        use_database: bool = True, 
                        stocks: Optional[List[str]] = None) -> List[str]:
        """
        获取指定日期范围内的交易日
        
        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            use_database: 是否使用数据库数据
            stocks: 股票代码列表，用于从数据库获取交易日
            
        Returns:
            交易日列表 (YYYY-MM-DD格式的字符串)
        """
        cache_key = f"{start_date}_{end_date}_{use_database}_{stocks}"
        if cache_key in self._trading_days_cache:
            return self._trading_days_cache[cache_key]
        
        trading_days = []
        
        if use_database and stocks:
            # 从数据库获取实际交易日
            trading_days = self._get_trading_days_from_database(start_date, end_date, stocks)
        
        if not trading_days:
            # 降级到pandas工作日
            trading_days = self._get_trading_days_from_pandas(start_date, end_date)
        
        # 缓存结果
        self._trading_days_cache[cache_key] = trading_days
        
        if self.logger:
            self.logger.info(f"获取交易日: {start_date} 到 {end_date}, 共 {len(trading_days)} 天")
        
        return trading_days
    
    def _get_trading_days_from_database(self, 
                                      start_date: str, 
                                      end_date: str, 
                                      stocks: List[str]) -> List[str]:
        """
        从数据库获取交易日
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            stocks: 股票代码列表
            
        Returns:
            交易日列表
        """
        all_trading_days = set()
        
        for stock in stocks:
            try:
                db_path = get_database_path(stock)
                if not os.path.exists(db_path):
                    continue
                
                conn = sqlite3.connect(db_path)
                cursor = conn.cursor()
                
                cursor.execute("""
                    SELECT DISTINCT trade_date 
                    FROM ohlcv 
                    WHERE ticker = ? 
                    AND trade_date >= ? 
                    AND trade_date <= ?
                    ORDER BY trade_date
                """, (stock.upper(), start_date, end_date))
                
                stock_dates = [row[0] for row in cursor.fetchall()]
                all_trading_days.update(stock_dates)
                conn.close()
                
            except Exception as e:
                if self.logger:
                    self.logger.warning(f"获取 {stock} 交易日失败: {e}")
                continue
        
        return sorted(list(all_trading_days))
    
    def _get_trading_days_from_pandas(self, start_date: str, end_date: str) -> List[str]:
        """
        使用pandas生成工作日作为交易日
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            交易日列表
        """
        try:
            date_range = pd.date_range(start=start_date, end=end_date, freq='B')
            return [date.strftime('%Y-%m-%d') for date in date_range]
        except Exception as e:
            if self.logger:
                self.logger.error(f"生成pandas工作日失败: {e}")
            return []
    
    def get_trading_weeks(self, 
                         trading_days: List[str], 
                         strategy: str = "adaptive",
                         days_per_week: int = 5) -> List[Dict[str, Any]]:
        """
        将交易日按周划分
        
        Args:
            trading_days: 交易日列表
            strategy: 划分策略 ("adaptive" 或 "fixed")
            days_per_week: 每周交易日数
            
        Returns:
            周信息列表，每个元素包含：
            {
                'week_number': int,
                'start_date': str,
                'end_date': str,
                'trading_days': List[str]
            }
        """
        if not trading_days:
            return []
        
        weeks = []
        
        if strategy == "adaptive":
            # 自适应策略：严格按照指定天数划分
            for i in range(0, len(trading_days), days_per_week):
                week_days = trading_days[i:i + days_per_week]
                if len(week_days) >= 1:  # 至少有1天数据
                    week_info = {
                        'week_number': len(weeks) + 1,
                        'start_date': week_days[0],
                        'end_date': week_days[-1],
                        'trading_days': week_days
                    }
                    weeks.append(week_info)
        else:
            # 固定策略：按日历周划分（暂不实现）
            pass
        
        if self.logger:
            self.logger.info(f"交易日周划分完成: {len(trading_days)} 天 -> {len(weeks)} 周")
            for week in weeks:
                self.logger.info(f"  第{week['week_number']}周: {len(week['trading_days'])}天 "
                               f"({week['start_date']} 到 {week['end_date']})")
        
        return weeks
    
    def validate_trading_days(self, trading_days: List[str]) -> Dict[str, Any]:
        """
        验证交易日数据的完整性
        
        Args:
            trading_days: 交易日列表
            
        Returns:
            验证结果字典
        """
        if not trading_days:
            return {
                'valid': False,
                'total_days': 0,
                'gaps': [],
                'weekends': [],
                'message': '没有交易日数据'
            }
        
        gaps = []
        weekends = []
        
        # 检查日期间隔和周末
        for i in range(len(trading_days) - 1):
            current_date = pd.to_datetime(trading_days[i])
            next_date = pd.to_datetime(trading_days[i + 1])
            
            # 检查是否有超过3天的间隔（可能的数据缺失）
            if (next_date - current_date).days > 3:
                gaps.append({
                    'from': trading_days[i],
                    'to': trading_days[i + 1],
                    'days': (next_date - current_date).days
                })
            
            # 检查是否包含周末
            if current_date.weekday() >= 5:  # 周六或周日
                weekends.append(trading_days[i])
        
        return {
            'valid': len(gaps) == 0 and len(weekends) == 0,
            'total_days': len(trading_days),
            'gaps': gaps,
            'weekends': weekends,
            'message': f'共{len(trading_days)}个交易日，{len(gaps)}个间隔，{len(weekends)}个周末'
        }
