#!/usr/bin/env python3
"""
清理日志文件中的DEBUG信息

此脚本用于从现有的日志文件中移除所有DEBUG级别的日志条目，
只保留INFO、WARNING、ERROR级别的重要信息。
"""

import os
import re
import argparse
import shutil
from typing import List, <PERSON><PERSON>
from pathlib import Path


def is_debug_line(line: str) -> bool:
    """
    判断是否为DEBUG级别的日志行
    
    参数:
        line: 日志行内容
    
    返回:
        True如果是DEBUG行，False否则
    """
    # 匹配DEBUG级别的日志行
    debug_patterns = [
        r'- DEBUG -',  # 标准DEBUG格式
        r'- httpcore\.',  # httpcore相关的所有日志
        r'- httpx -',  # httpx相关日志
        r'- zhipuai\..*- DEBUG -',  # zhipuai的DEBUG日志
        r'- urllib3\.',  # urllib3相关日志
        r'- requests\.',  # requests相关日志
    ]
    
    for pattern in debug_patterns:
        if re.search(pattern, line):
            return True
    
    return False


def clean_log_file(input_file: str, output_file: str = None, backup: bool = True) -> <PERSON><PERSON>[int, int]:
    """
    清理单个日志文件
    
    参数:
        input_file: 输入日志文件路径
        output_file: 输出文件路径（如果为None，则覆盖原文件）
        backup: 是否创建备份文件
    
    返回:
        (原始行数, 清理后行数)
    """
    if not os.path.exists(input_file):
        print(f"❌ 文件不存在: {input_file}")
        return 0, 0
    
    # 创建备份
    if backup:
        backup_file = f"{input_file}.backup"
        shutil.copy2(input_file, backup_file)
        print(f"📁 已创建备份文件: {backup_file}")
    
    # 读取原文件
    with open(input_file, 'r', encoding='utf-8', errors='ignore') as f:
        lines = f.readlines()
    
    original_count = len(lines)
    
    # 过滤DEBUG行
    clean_lines = []
    for line in lines:
        if not is_debug_line(line.strip()):
            clean_lines.append(line)
    
    cleaned_count = len(clean_lines)
    
    # 写入清理后的内容
    output_path = output_file or input_file
    with open(output_path, 'w', encoding='utf-8') as f:
        f.writelines(clean_lines)
    
    removed_count = original_count - cleaned_count
    reduction_percent = (removed_count / original_count * 100) if original_count > 0 else 0
    
    print(f"✅ 清理完成: {input_file}")
    print(f"   原始行数: {original_count:,}")
    print(f"   清理后行数: {cleaned_count:,}")
    print(f"   删除行数: {removed_count:,} ({reduction_percent:.1f}%)")
    
    return original_count, cleaned_count


def find_log_files(directory: str = ".") -> List[str]:
    """
    查找目录中的所有日志文件
    
    参数:
        directory: 搜索目录
    
    返回:
        日志文件路径列表
    """
    log_files = []
    log_patterns = ["*.log", "*.LOG"]
    
    for pattern in log_patterns:
        log_files.extend(Path(directory).glob(pattern))
        log_files.extend(Path(directory).glob(f"**/{pattern}"))
    
    return [str(f) for f in log_files if f.is_file()]


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="清理日志文件中的DEBUG信息")
    parser.add_argument("files", nargs="*", help="要清理的日志文件路径")
    parser.add_argument("--directory", "-d", help="搜索日志文件的目录")
    parser.add_argument("--no-backup", action="store_true", help="不创建备份文件")
    parser.add_argument("--output-dir", help="输出目录（保留原文件）")
    parser.add_argument("--dry-run", action="store_true", help="只显示将要处理的文件，不实际执行")
    
    args = parser.parse_args()
    
    # 确定要处理的文件
    files_to_process = []
    
    if args.files:
        files_to_process.extend(args.files)
    
    if args.directory:
        found_files = find_log_files(args.directory)
        files_to_process.extend(found_files)
        print(f"🔍 在目录 {args.directory} 中找到 {len(found_files)} 个日志文件")
    
    if not files_to_process:
        # 默认搜索当前目录
        found_files = find_log_files(".")
        files_to_process.extend(found_files)
        print(f"🔍 在当前目录中找到 {len(found_files)} 个日志文件")
    
    if not files_to_process:
        print("❌ 没有找到要处理的日志文件")
        return
    
    # 去重
    files_to_process = list(set(files_to_process))
    
    print(f"\n📋 将要处理的文件:")
    for i, file_path in enumerate(files_to_process, 1):
        file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
        print(f"   {i}. {file_path} ({file_size:.1f} MB)")
    
    if args.dry_run:
        print("\n🔍 这是试运行模式，不会实际修改文件")
        return
    
    # 确认处理
    if len(files_to_process) > 1:
        response = input(f"\n❓ 确定要处理这 {len(files_to_process)} 个文件吗？(y/N): ")
        if response.lower() not in ['y', 'yes']:
            print("❌ 操作已取消")
            return
    
    print(f"\n🚀 开始处理...")
    print("=" * 60)
    
    total_original = 0
    total_cleaned = 0
    
    # 处理每个文件
    for file_path in files_to_process:
        try:
            output_file = None
            if args.output_dir:
                os.makedirs(args.output_dir, exist_ok=True)
                filename = os.path.basename(file_path)
                output_file = os.path.join(args.output_dir, f"cleaned_{filename}")
            
            original, cleaned = clean_log_file(
                file_path, 
                output_file, 
                backup=not args.no_backup
            )
            
            total_original += original
            total_cleaned += cleaned
            
        except Exception as e:
            print(f"❌ 处理文件 {file_path} 时出错: {e}")
        
        print("-" * 40)
    
    # 总结
    total_removed = total_original - total_cleaned
    total_reduction = (total_removed / total_original * 100) if total_original > 0 else 0
    
    print(f"\n📊 处理总结:")
    print(f"   处理文件数: {len(files_to_process)}")
    print(f"   总原始行数: {total_original:,}")
    print(f"   总清理后行数: {total_cleaned:,}")
    print(f"   总删除行数: {total_removed:,} ({total_reduction:.1f}%)")
    print(f"\n✅ 所有文件处理完成！")


if __name__ == "__main__":
    main()
