#!/usr/bin/env python3
"""
综合日志记录器

提供简单的日志记录功能
"""

import logging
import sys
from datetime import datetime
from typing import Optional


class SimpleLogger:
    """
    简单日志记录器
    
    提供基本的日志记录功能，兼容现有代码
    """
    
    def __init__(self, name: str = "SimpleLogger", level: int = logging.INFO):
        """
        初始化日志记录器
        
        Args:
            name: 日志记录器名称
            level: 日志级别
        """
        self.name = name
        self.logger = logging.getLogger(name)
        self.logger.setLevel(level)
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            handler = logging.StreamHandler(sys.stdout)
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
    
    def info(self, message: str):
        """记录信息级别日志"""
        self.logger.info(message)
    
    def warning(self, message: str):
        """记录警告级别日志"""
        self.logger.warning(message)
    
    def error(self, message: str):
        """记录错误级别日志"""
        self.logger.error(message)
    
    def debug(self, message: str):
        """记录调试级别日志"""
        self.logger.debug(message)
    
    def critical(self, message: str):
        """记录严重错误级别日志"""
        self.logger.critical(message)
