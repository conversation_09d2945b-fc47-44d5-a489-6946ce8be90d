#!/usr/bin/env python3
"""
提取所有股票文件中含有🤖 TRA 输入的行，并为每个股票创建JSON文件
"""

import json
import os
import re
from pathlib import Path
from typing import Dict, List
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def extract_tra_inputs_from_file(file_path: str) -> List[Dict]:
    """
    从文件中提取所有含有🤖 TRA 输入的行
    
    Args:
        file_path: 文件路径
        
    Returns:
        包含TRA输入数据的字典列表
    """
    tra_inputs = []
    
    # 匹配模式：🤖 TRA 输入: 日期=YYYY-MM-DD, 累计收益=X.XXXX, 周收益=X.XXXX
    tra_pattern = r'🤖 TRA 输入: 日期=([^,]+), 累计收益=([-+]?\d*\.?\d+), 周收益=([-+]?\d*\.?\d+)'
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if '🤖 TRA 输入' in line:
                    # 尝试匹配完整的TRA输入格式
                    match = re.search(tra_pattern, line)
                    if match:
                        date_str = match.group(1)
                        cumulative_return = match.group(2)
                        weekly_return = match.group(3)
                        
                        # 提取时间戳（如果存在）
                        timestamp_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3})', line)
                        timestamp = timestamp_match.group(1) if timestamp_match else ""
                        
                        tra_inputs.append({
                            'line_number': str(line_num),
                            'timestamp': timestamp,
                            'date': date_str,
                            'cumulative_return': cumulative_return,
                            'weekly_return': weekly_return,
                            'full_line': line.strip()
                        })
                    else:
                        # 如果无法完全匹配，至少记录含有TRA输入的行
                        logger.warning(f"Found TRA input line but couldn't parse: {line.strip()}")
                        tra_inputs.append({
                            'line_number': str(line_num),
                            'timestamp': "",
                            'date': "",
                            'cumulative_return': "",
                            'weekly_return': "",
                            'full_line': line.strip(),
                            'parse_error': True
                        })
                        
    except Exception as e:
        logger.error(f"Error processing {file_path}: {e}")
        
    return tra_inputs

def find_all_stock_files(test_results_dir: str) -> Dict[str, str]:
    """
    查找所有包含股票数据的文件
    
    Args:
        test_results_dir: 测试结果目录
        
    Returns:
        股票代码到文件路径的映射
    """
    stock_files = {}
    test_dir = Path(test_results_dir)
    
    # 查找所有可能包含TRA数据的文件
    file_patterns = [
        "*_phase1.md",      # AAPL_phase1.md
        "*_0101_0430.log",  # GOOG_0101_0430.log
        "*_phase1_tra_inputs.json"  # 已存在的JSON文件
    ]
    
    for pattern in file_patterns:
        for file_path in test_dir.glob(pattern):
            if pattern.endswith('.json'):
                # 对于JSON文件，提取股票代码
                stock_symbol = file_path.stem.replace('_phase1_tra_inputs', '')
            elif pattern.endswith('.md'):
                # 对于MD文件，提取股票代码
                stock_symbol = file_path.stem.replace('_phase1', '')
            else:
                # 对于LOG文件，提取股票代码
                stock_symbol = file_path.stem.replace('_0101_0430', '')
            
            # 只处理非JSON文件（因为JSON文件是我们要创建的目标）
            if not pattern.endswith('.json'):
                stock_files[stock_symbol] = str(file_path)
                logger.info(f"Found file for {stock_symbol}: {file_path.name}")
    
    return stock_files

def process_all_stocks(test_results_dir: str, output_dir: str = None):
    """
    处理所有股票文件，提取TRA输入并保存为JSON
    
    Args:
        test_results_dir: 测试结果目录
        output_dir: 输出目录，如果为None则使用test_results_dir
    """
    if output_dir is None:
        output_dir = test_results_dir
    
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)
    
    # 查找所有股票文件
    stock_files = find_all_stock_files(test_results_dir)
    
    if not stock_files:
        logger.error("没有找到任何股票数据文件")
        return
    
    logger.info(f"找到 {len(stock_files)} 个股票文件")
    
    # 处理每个股票
    total_extracted = 0
    
    for stock_symbol, file_path in stock_files.items():
        logger.info(f"正在处理 {stock_symbol}: {file_path}")
        
        # 提取TRA输入数据
        tra_inputs = extract_tra_inputs_from_file(file_path)
        
        if tra_inputs:
            # 保存为JSON文件
            output_file = os.path.join(output_dir, f"{stock_symbol}_tra_inputs_extracted.json")
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(tra_inputs, f, indent=2, ensure_ascii=False)
            
            logger.info(f"✅ {stock_symbol}: 提取了 {len(tra_inputs)} 条TRA输入记录，保存到 {output_file}")
            total_extracted += len(tra_inputs)
        else:
            logger.warning(f"❌ {stock_symbol}: 没有找到TRA输入数据")
    
    # 生成汇总报告
    from datetime import datetime
    summary_report = {
        'extraction_time': str(datetime.now()),
        'total_stocks_processed': len(stock_files),
        'total_tra_inputs_extracted': total_extracted,
        'stocks_summary': {}
    }
    
    # 重新读取生成的JSON文件以创建汇总
    for stock_symbol in stock_files.keys():
        json_file = os.path.join(output_dir, f"{stock_symbol}_tra_inputs_extracted.json")
        if os.path.exists(json_file):
            with open(json_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                summary_report['stocks_summary'][stock_symbol] = {
                    'total_records': len(data),
                    'date_range': {
                        'first_date': data[0]['date'] if data else None,
                        'last_date': data[-1]['date'] if data else None
                    },
                    'parse_errors': len([r for r in data if r.get('parse_error', False)])
                }
    
    # 保存汇总报告
    summary_file = os.path.join(output_dir, "tra_extraction_summary.json")
    with open(summary_file, 'w', encoding='utf-8') as f:
        json.dump(summary_report, f, indent=2, ensure_ascii=False)
    
    logger.info("="*80)
    logger.info("提取完成汇总:")
    logger.info(f"处理股票数量: {len(stock_files)}")
    logger.info(f"总提取记录数: {total_extracted}")
    logger.info(f"汇总报告保存到: {summary_file}")
    logger.info("="*80)
    
    # 打印每个股票的统计
    for stock_symbol, stats in summary_report['stocks_summary'].items():
        logger.info(f"{stock_symbol}: {stats['total_records']} 条记录 "
                   f"({stats['date_range']['first_date']} ~ {stats['date_range']['last_date']})")
        if stats['parse_errors'] > 0:
            logger.warning(f"  ⚠️  {stats['parse_errors']} 条记录解析失败")

def main():
    """主函数"""
    
    test_results_dir = "/Users/<USER>/Code/Multi_Agent_Optimization/test_results"
    
    logger.info("开始提取所有股票的TRA输入数据")
    logger.info("="*80)
    
    process_all_stocks(test_results_dir)
    
    logger.info("提取完成！")

if __name__ == "__main__":
    main()