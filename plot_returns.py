import re
import argparse
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from glob import glob
from pathlib import Path

def parse_log_file(file_path):
    """
    Parse a single log file to extract daily returns from "Phase 1" section.
    """
    daily_returns = []
    in_phase_1 = False
    # Regex to extract date and return from log lines
    # Match "日期=YYYY-MM-DD" and "周收益=X.XXXX"
    line_pattern = re.compile(r"日期=([\d-]+).*周收益=([-\d.]+)")

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                # Check if entering "Phase 1"
                if "🔍 阶段1: 完整联盟详细分析" in line:
                    in_phase_1 = True
                # Assume phase 1 ends when another phase is encountered
                if "🔍 阶段" in line and "阶段1" not in line:
                    in_phase_1 = False
                # If inside phase 1 and line contains TRA input
                if in_phase_1 and "TRA 输入:" in line:
                    match = line_pattern.search(line)
                    if match:
                        date_str = match.group(1)
                        # Interpret "周收益" as daily return
                        daily_return = float(match.group(2))
                        daily_returns.append({'date': date_str, 'return': daily_return})
    except Exception as e:
        print(f"Failed to process file {file_path}: {e}")
    return daily_returns

def main():
    """
    Main function to parse logs and plot return curve.
    """
    parser = argparse.ArgumentParser(
        description="Analyze log files and plot daily return curve from Phase 1 section.",
        formatter_class=argparse.RawTextHelpFormatter
    )
    parser.add_argument(
        'log_files',
        nargs='+',
        help="Paths to log files to analyze.\nYou can use wildcards (glob patterns), e.g.: 'opro_system_*.log'"
    )
    args = parser.parse_args()

    all_returns = []
    # Expand glob patterns to get file list
    files_to_process = []
    for pattern in args.log_files:
        files_to_process.extend(glob(pattern))

    if not files_to_process:
        print(f"Error: No log files found for pattern(s) '{' '.join(args.log_files)}'.")
        return

    print(f"Found {len(files_to_process)} file(s) to process...")
    for log_file in files_to_process:
        returns = parse_log_file(log_file)
        if returns:
            all_returns.extend(returns)

    if not all_returns:
        print("No daily return data found in Phase 1 section of provided files.")
        return

    # Create DataFrame and process data
    df = pd.DataFrame(all_returns)
    df['date'] = pd.to_datetime(df['date'])
    # Remove duplicate dates and sort
    df = df.drop_duplicates(subset=['date']).sort_values(by='date').reset_index(drop=True)

    # Calculate cumulative return
    df['cumulative_return'] = (1 + df['return']).cumprod() - 1

    # Calculate Sharpe Ratio (assume risk-free rate = 0)
    mean_return = df['return'].mean()
    std_return = df['return'].std()
    sharpe_ratio = None
    if std_return != 0:
        sharpe_ratio = mean_return / std_return * (252 ** 0.5)  # annualized Sharpe ratio for daily returns

    print("\nExtracted data preview:")
    print(df.head())
    print(f"\nTotal {len(df)} unique daily records extracted.")
    if sharpe_ratio is not None:
        print(f"\nTotal Sharpe Ratio (annualized, risk-free rate=0): {sharpe_ratio:.4f}")
    else:
        print("\nSharpe Ratio could not be calculated (std=0).")

    # Plot
    plt.style.use('seaborn-v0_8-whitegrid')
    fig, ax = plt.subplots(figsize=(15, 8))

    ax.plot(df['date'], df['cumulative_return'], marker='o', linestyle='-', markersize=4, label='Cumulative Return')

    # Format chart
    ax.set_title('Phase 1 - Cumulative Daily Return Curve', fontsize=16)
    ax.set_xlabel('Date', fontsize=12)
    ax.set_ylabel('Cumulative Return', fontsize=12)
    ax.legend()
    ax.grid(True, which='both', linestyle='--', linewidth=0.5)
    # Format x-axis date labels
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    fig.autofmt_xdate(rotation=45)

    plt.tight_layout()
    # Save chart
    output_filename = 'cumulative_return_curve.png'
    plt.savefig(output_filename)
    print(f"\nChart saved to: {Path(output_filename).resolve()}")
    # Optional: show chart
    # plt.show()

if __name__ == "__main__":
    main()
