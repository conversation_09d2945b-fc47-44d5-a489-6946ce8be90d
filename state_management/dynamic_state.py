"""
动态状态管理系统 - 核心状态类

基于现有state结构，实现动态状态管理功能
"""

import copy
import json
import pickle
from datetime import datetime
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass, asdict
import logging


@dataclass
class AgentExecutionRecord:
    """智能体执行记录"""
    agent_id: str
    execution_order: int
    start_time: str
    end_time: str
    processing_time: float
    input_data: Dict[str, Any]
    output_data: Dict[str, Any]
    success: bool
    error_message: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AgentExecutionRecord':
        """从字典创建实例"""
        return cls(**data)


@dataclass
class DailyStateSnapshot:
    """日级状态快照"""
    date: str
    initial_state: Dict[str, Any]
    final_state: Dict[str, Any]
    agent_executions: List[AgentExecutionRecord]
    market_data: Dict[str, Any]
    trading_decisions: Dict[str, Any]
    performance_metrics: Dict[str, Any]
    metadata: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'date': self.date,
            'initial_state': self.initial_state,
            'final_state': self.final_state,
            'agent_executions': [exec_record.to_dict() for exec_record in self.agent_executions],
            'market_data': self.market_data,
            'trading_decisions': self.trading_decisions,
            'performance_metrics': self.performance_metrics,
            'metadata': self.metadata
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DailyStateSnapshot':
        """从字典创建实例"""
        agent_executions = [
            AgentExecutionRecord.from_dict(exec_data) 
            for exec_data in data.get('agent_executions', [])
        ]
        
        return cls(
            date=data['date'],
            initial_state=data['initial_state'],
            final_state=data['final_state'],
            agent_executions=agent_executions,
            market_data=data.get('market_data', {}),
            trading_decisions=data.get('trading_decisions', {}),
            performance_metrics=data.get('performance_metrics', {}),
            metadata=data.get('metadata', {})
        )


class DynamicState:
    """
    动态状态管理类
    
    基于现有state结构，提供动态状态管理功能：
    - 智能体执行过程中的状态更新
    - 输入输出数据的记录和追踪
    - 状态历史的维护
    """
    
    def __init__(self, initial_state: Dict[str, Any], date: str):
        """
        初始化动态状态
        
        Args:
            initial_state: 初始状态数据
            date: 交易日期
        """
        self.date = date
        self.initial_state = copy.deepcopy(initial_state)
        self.current_state = copy.deepcopy(initial_state)
        self.agent_executions: List[AgentExecutionRecord] = []
        self.execution_order = 0
        
        # 智能体执行顺序定义
        self.agent_execution_order = {
            "NAA": 1, "TAA": 2, "FAA": 3,  # 分析层
            "BOA": 4, "BeOA": 5, "NOA": 6,  # 展望层
            "TRA": 7  # 决策层
        }
        
        # 日志记录器
        self.logger = logging.getLogger(f"DynamicState_{date}")
        
        # 元数据
        self.metadata = {
            "created_at": datetime.now().isoformat(),
            "last_updated": datetime.now().isoformat(),
            "total_agents_executed": 0,
            "execution_status": "initialized"
        }
        
        self.logger.info(f"动态状态初始化完成: {date}")
    
    def get_current_state(self) -> Dict[str, Any]:
        """获取当前状态"""
        return copy.deepcopy(self.current_state)
    
    def get_agent_input_state(self, agent_id: str) -> Dict[str, Any]:
        """
        获取智能体的输入状态
        
        根据智能体类型和执行顺序，返回包含前序智能体输出的状态
        """
        input_state = copy.deepcopy(self.current_state)
        
        # 添加前序智能体的输出
        if agent_id in ["BOA", "BeOA", "NOA"]:
            # 展望层智能体需要分析层的输出
            analyst_outputs = {}
            for exec_record in self.agent_executions:
                if exec_record.agent_id in ["NAA", "TAA", "FAA"] and exec_record.success:
                    analyst_outputs[exec_record.agent_id] = exec_record.output_data
            
            if analyst_outputs:
                input_state["analyst_outputs"] = analyst_outputs
                
        elif agent_id == "TRA":
            # 交易层智能体需要所有前序智能体的输出
            analyst_outputs = {}
            outlook_outputs = {}
            
            for exec_record in self.agent_executions:
                if exec_record.success:
                    if exec_record.agent_id in ["NAA", "TAA", "FAA"]:
                        analyst_outputs[exec_record.agent_id] = exec_record.output_data
                    elif exec_record.agent_id in ["BOA", "BeOA", "NOA"]:
                        outlook_outputs[exec_record.agent_id] = exec_record.output_data
            
            if analyst_outputs:
                input_state["analyst_outputs"] = analyst_outputs
            if outlook_outputs:
                input_state["outlook_outputs"] = outlook_outputs
        
        return input_state
    
    def record_agent_execution(self, 
                             agent_id: str, 
                             input_data: Dict[str, Any],
                             output_data: Dict[str, Any],
                             processing_time: float,
                             success: bool = True,
                             error_message: Optional[str] = None) -> None:
        """
        记录智能体执行结果
        
        Args:
            agent_id: 智能体ID
            input_data: 输入数据
            output_data: 输出数据
            processing_time: 处理时间
            success: 是否成功
            error_message: 错误信息（如果失败）
        """
        execution_order = self.agent_execution_order.get(agent_id, 999)
        current_time = datetime.now().isoformat()
        
        # 创建执行记录
        exec_record = AgentExecutionRecord(
            agent_id=agent_id,
            execution_order=execution_order,
            start_time=current_time,  # 简化处理，实际应该在执行开始时记录
            end_time=current_time,
            processing_time=processing_time,
            input_data=copy.deepcopy(input_data),
            output_data=copy.deepcopy(output_data),
            success=success,
            error_message=error_message
        )
        
        self.agent_executions.append(exec_record)
        
        # 更新当前状态
        if success and output_data:
            self._update_state_with_agent_output(agent_id, output_data)
        
        # 更新元数据
        self.metadata["last_updated"] = current_time
        self.metadata["total_agents_executed"] += 1
        
        if not success:
            self.metadata["execution_status"] = "error"
            self.logger.error(f"智能体 {agent_id} 执行失败: {error_message}")
        else:
            self.logger.info(f"智能体 {agent_id} 执行成功，处理时间: {processing_time:.3f}秒")
    
    def _update_state_with_agent_output(self, agent_id: str, output_data: Dict[str, Any]) -> None:
        """使用智能体输出更新当前状态"""
        # 根据智能体类型更新不同的状态字段
        if agent_id in ["NAA", "TAA", "FAA"]:
            # 分析层智能体输出
            if "analyst_outputs" not in self.current_state:
                self.current_state["analyst_outputs"] = {}
            self.current_state["analyst_outputs"][agent_id] = output_data
            
        elif agent_id in ["BOA", "BeOA", "NOA"]:
            # 展望层智能体输出
            if "outlook_outputs" not in self.current_state:
                self.current_state["outlook_outputs"] = {}
            self.current_state["outlook_outputs"][agent_id] = output_data
            
        elif agent_id == "TRA":
            # 交易层智能体输出 - 更新交易相关状态
            if "trading_actions" in output_data:
                self.current_state["last_trading_actions"] = output_data["trading_actions"]
            
            # 更新其他交易相关字段
            for key in ["action", "confidence"]:
                if key in output_data:
                    self.current_state[f"last_{key}"] = output_data[key]
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """获取执行摘要"""
        successful_executions = [exec_rec for exec_rec in self.agent_executions if exec_rec.success]
        failed_executions = [exec_rec for exec_rec in self.agent_executions if not exec_rec.success]
        
        total_processing_time = sum(exec_rec.processing_time for exec_rec in self.agent_executions)
        
        return {
            "date": self.date,
            "total_executions": len(self.agent_executions),
            "successful_executions": len(successful_executions),
            "failed_executions": len(failed_executions),
            "total_processing_time": total_processing_time,
            "average_processing_time": total_processing_time / len(self.agent_executions) if self.agent_executions else 0,
            "execution_order": [exec_rec.agent_id for exec_rec in sorted(self.agent_executions, key=lambda x: x.execution_order)],
            "metadata": self.metadata
        }
    
    def create_daily_snapshot(self) -> DailyStateSnapshot:
        """创建日级状态快照"""
        return DailyStateSnapshot(
            date=self.date,
            initial_state=self.initial_state,
            final_state=self.current_state,
            agent_executions=self.agent_executions,
            market_data=self._extract_market_data(),
            trading_decisions=self._extract_trading_decisions(),
            performance_metrics=self._extract_performance_metrics(),
            metadata=self.metadata
        )
    
    def get_standardized_agent_outputs(self) -> Dict[str, Any]:
        """
        获取标准化的智能体输出，符合Framework.md规范
        
        基于第143-159行现有逻辑，添加标准化格式转换
        
        Returns:
            Dict[str, Any]: 标准化的智能体输出字典
        """
        standardized_outputs = {
            "analyst_outputs": {},
            "outlook_outputs": {},
            "trading_outputs": {}
        }
        
        # 基于现有逻辑收集并标准化分析层输出 (第143-146行逻辑)
        for exec_record in self.agent_executions:
            if exec_record.agent_id in ["NAA", "TAA", "FAA"] and exec_record.success:
                # 标准化分析层输出格式
                standardized_output = self._standardize_analyst_output(
                    exec_record.agent_id, exec_record.output_data
                )
                standardized_outputs["analyst_outputs"][exec_record.agent_id] = standardized_output
        
        # 基于现有逻辑收集并标准化展望层输出 (第153-159行逻辑)
        for exec_record in self.agent_executions:
            if exec_record.agent_id in ["BOA", "BeOA", "NOA"] and exec_record.success:
                # 标准化展望层输出格式
                standardized_output = self._standardize_outlook_output(
                    exec_record.agent_id, exec_record.output_data
                )
                standardized_outputs["outlook_outputs"][exec_record.agent_id] = standardized_output
        
        # 收集并标准化交易层输出
        for exec_record in self.agent_executions:
            if exec_record.agent_id == "TRA" and exec_record.success:
                # 标准化交易层输出格式
                standardized_output = self._standardize_trading_output(
                    exec_record.agent_id, exec_record.output_data
                )
                standardized_outputs["trading_outputs"][exec_record.agent_id] = standardized_output
        
        self.logger.debug(
            f"Standardized agent outputs collected: "
            f"A:{len(standardized_outputs['analyst_outputs'])}, "
            f"O:{len(standardized_outputs['outlook_outputs'])}, "
            f"T:{len(standardized_outputs['trading_outputs'])}"
        )
        
        return standardized_outputs
    
    def _standardize_analyst_output(self, agent_id: str, raw_output: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化分析层智能体输出格式
        
        Args:
            agent_id: 智能体ID (NAA, TAA, FAA)
            raw_output: 原始输出数据
            
        Returns:
            Dict[str, Any]: 标准化后的输出
        """
        standardized = {
            "agent_id": agent_id,
            "timestamp": datetime.now().isoformat(),
            "success": True
        }
        
        # 根据不同的分析层智能体类型进行标准化
        if agent_id == "NAA":  # News Analysis Agent
            standardized.update({
                "sentiment": raw_output.get("sentiment", "neutral"),
                "confidence": self._normalize_confidence(raw_output.get("confidence", 0.5)),
                "news_summary": raw_output.get("news_summary", ""),
                "market_impact": raw_output.get("market_impact", "neutral")
            })
        elif agent_id == "TAA":  # Technical Analysis Agent
            standardized.update({
                "trend": raw_output.get("trend", "neutral"),
                "confidence": self._normalize_confidence(raw_output.get("confidence", 0.5)),
                "support_level": raw_output.get("support_level"),
                "resistance_level": raw_output.get("resistance_level"),
                "indicators": raw_output.get("indicators", {})
            })
        elif agent_id == "FAA":  # Fundamental Analysis Agent
            standardized.update({
                "valuation_assessment": raw_output.get("valuation_assessment", "neutral"),
                "confidence": self._normalize_confidence(raw_output.get("confidence", 0.5)),
                "financial_metrics": raw_output.get("financial_metrics", {}),
                "recommendation": raw_output.get("recommendation", "hold")
            })
        
        # 保留原始数据以备分析
        standardized["raw_output"] = raw_output
        
        return standardized
    
    def _standardize_outlook_output(self, agent_id: str, raw_output: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化展望层智能体输出格式
        
        Args:
            agent_id: 智能体ID (BOA, BeOA, NOA)
            raw_output: 原始输出数据
            
        Returns:
            Dict[str, Any]: 标准化后的输出
        """
        standardized = {
            "agent_id": agent_id,
            "timestamp": datetime.now().isoformat(),
            "success": True
        }
        
        # 标准化展望层输出
        standardized.update({
            "market_outlook": raw_output.get("market_outlook", "neutral"),
            "confidence": self._normalize_confidence(raw_output.get("confidence", 0.5)),
            "time_horizon": raw_output.get("time_horizon", "short_term"),
            "key_factors": raw_output.get("key_factors", []),
            "risk_assessment": raw_output.get("risk_assessment", "medium")
        })
        
        # 特定于agent类型的字段
        if agent_id == "BOA":  # Bullish Outlook Agent
            standardized["bullish_indicators"] = raw_output.get("bullish_indicators", [])
        elif agent_id == "BeOA":  # Bearish Outlook Agent  
            standardized["bearish_indicators"] = raw_output.get("bearish_indicators", [])
        elif agent_id == "NOA":  # Neutral Outlook Agent
            standardized["neutral_factors"] = raw_output.get("neutral_factors", [])
        
        # 保留原始数据
        standardized["raw_output"] = raw_output
        
        return standardized
    
    def _standardize_trading_output(self, agent_id: str, raw_output: Dict[str, Any]) -> Dict[str, Any]:
        """
        标准化交易层智能体输出格式
        
        Args:
            agent_id: 智能体ID (TRA)
            raw_output: 原始输出数据
            
        Returns:
            Dict[str, Any]: 标准化后的输出
        """
        standardized = {
            "agent_id": agent_id,
            "timestamp": datetime.now().isoformat(),
            "success": True
        }
        
        # 标准化交易决策输出
        standardized.update({
            "action": raw_output.get("action", "hold"),
            "confidence": self._normalize_confidence(raw_output.get("confidence", 0.5)),
            "trading_actions": raw_output.get("trading_actions", {"__HOLD__": 1.0}),
            "reasoning": raw_output.get("reasoning", ""),
            "risk_level": raw_output.get("risk_level", "medium")
        })
        
        # 保留原始数据
        standardized["raw_output"] = raw_output
        
        return standardized
    
    def _normalize_confidence(self, confidence_value: Any) -> float:
        """
        标准化信心度值到0.0-1.0范围
        
        Args:
            confidence_value: 原始信心度值
            
        Returns:
            float: 标准化后的信心度 (0.0-1.0)
        """
        try:
            if isinstance(confidence_value, (int, float)):
                # 确保在0.0-1.0范围内
                return max(0.0, min(1.0, float(confidence_value)))
            elif isinstance(confidence_value, str):
                # 尝试解析字符串形式的数值
                return max(0.0, min(1.0, float(confidence_value)))
            else:
                # 默认中性信心度
                return 0.5
        except (ValueError, TypeError):
            self.logger.warning(f"Invalid confidence value: {confidence_value}, using 0.5")
            return 0.5
    
    def _extract_market_data(self) -> Dict[str, Any]:
        """提取市场数据"""
        market_data = {}
        for key in ["price_history", "news_history", "fundamental_data", "current_date", "cash", "positions"]:
            if key in self.current_state:
                market_data[key] = self.current_state[key]
        return market_data
    
    def _extract_trading_decisions(self) -> Dict[str, Any]:
        """提取交易决策数据"""
        trading_decisions = {}
        for key in ["last_trading_actions", "last_action", "last_confidence"]:
            if key in self.current_state:
                trading_decisions[key] = self.current_state[key]
        return trading_decisions
    
    def _extract_performance_metrics(self) -> Dict[str, Any]:
        """提取性能指标"""
        # 这里可以计算一些基础的性能指标
        return {
            "execution_time": sum(exec_rec.processing_time for exec_rec in self.agent_executions),
            "success_rate": len([e for e in self.agent_executions if e.success]) / len(self.agent_executions) if self.agent_executions else 0,
            "agent_count": len(self.agent_executions)
        }
