"""
性能评估器 (Performance Evaluator)

计算多智能体系统的交易表现，包括：
1. 每日交易表现：(明日收盘价/今日收盘价-1) * 交易动作
2. 智能体贡献度分析
3. 决策质量评估
4. 风险指标计算
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import numpy as np


class PerformanceEvaluator:
    """
    性能评估器

    负责计算和分析多智能体系统的交易表现
    """

    def __init__(self):
        """初始化性能评估器"""
        self.logger = logging.getLogger("PerformanceEvaluator")
        self.performance_history = []

    def calculate_daily_performance(self,
                                  trading_action: Dict[str, float],
                                  current_prices: Dict[str, float],
                                  next_prices: Dict[str, float],
                                  state: Dict[str, Any]) -> Dict[str, Any]:
        """
        计算当日交易表现

        Args:
            trading_action: 交易动作字典
            current_prices: 当日收盘价
            next_prices: 明日收盘价
            state: 当前状态字典

        Returns:
            性能评估结果
        """
        current_date = state.get("current_date", "unknown")

        try:
            # 1. 计算基础交易收益
            trading_return = self._calculate_trading_return(
                trading_action, current_prices, next_prices
            )

            # 2. 计算风险调整收益
            risk_metrics = self._calculate_risk_metrics(
                trading_action, current_prices, state
            )

            # 3. 分析智能体贡献
            agent_contributions = self._analyze_agent_contributions(state)

            # 4. 评估决策质量
            decision_quality = self._evaluate_decision_quality(
                trading_action, state, trading_return
            )

            # 5. 构建完整的性能报告
            performance_report = {
                "date": current_date,
                "trading_return": trading_return,
                "risk_metrics": risk_metrics,
                "agent_contributions": agent_contributions,
                "decision_quality": decision_quality,
                "trading_action": trading_action.copy(),
                "market_data": {
                    "current_prices": current_prices.copy(),
                    "next_prices": next_prices.copy()
                },
                "timestamp": datetime.now().isoformat()
            }

            # 6. 存储到历史记录
            self.performance_history.append(performance_report)

            self.logger.info(f"日期 {current_date} 性能评估完成，交易收益: {trading_return:.4f}")

            return performance_report

        except Exception as e:
            self.logger.error(f"性能评估失败: {e}")
            return {
                "date": current_date,
                "trading_return": 0.0,
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def _calculate_trading_return(self,
                                trading_action: Dict[str, float],
                                current_prices: Dict[str, float],
                                next_prices: Dict[str, float]) -> float:
        """
        计算交易收益：(明日收盘价/今日收盘价-1) * 交易动作

        Args:
            trading_action: 交易动作
            current_prices: 当日价格
            next_prices: 明日价格

        Returns:
            总交易收益
        """
        total_return = 0.0

        for symbol, action_value in trading_action.items():
            # 跳过持有动作
            if symbol == "__HOLD__":
                continue

            # 检查价格数据是否可用
            if symbol not in current_prices or symbol not in next_prices:
                self.logger.warning(f"股票 {symbol} 缺少价格数据，跳过收益计算")
                continue

            current_price = current_prices[symbol]
            next_price = next_prices[symbol]

            # 验证价格数据有效性
            if current_price <= 0 or next_price <= 0:
                self.logger.warning(f"股票 {symbol} 价格数据无效，跳过收益计算")
                continue

            # 计算价格收益率
            price_return = (next_price / current_price) - 1

            # 计算加权收益：价格收益率 * 交易动作
            weighted_return = price_return * action_value
            total_return += weighted_return

            self.logger.debug(f"股票 {symbol}: 价格收益={price_return:.4f}, 动作={action_value:.4f}, 加权收益={weighted_return:.4f}")

        return total_return

    def _calculate_risk_metrics(self,
                              trading_action: Dict[str, float],
                              current_prices: Dict[str, float],
                              state: Dict[str, Any]) -> Dict[str, Any]:
        """计算风险指标"""
        risk_metrics = {}

        # 1. 计算总敞口
        total_exposure = 0.0
        position_count = 0

        for symbol, action_value in trading_action.items():
            if symbol == "__HOLD__":
                continue

            if symbol in current_prices:
                exposure = abs(action_value) * current_prices[symbol]
                total_exposure += exposure
                if abs(action_value) > 0.01:  # 忽略极小仓位
                    position_count += 1

        risk_metrics["total_exposure"] = total_exposure
        risk_metrics["position_count"] = position_count

        # 2. 计算集中度风险
        if total_exposure > 0:
            concentration_risk = max(
                abs(action_value) * current_prices.get(symbol, 0) / total_exposure
                for symbol, action_value in trading_action.items()
                if symbol != "__HOLD__" and symbol in current_prices
            ) if trading_action else 0.0
        else:
            concentration_risk = 0.0

        risk_metrics["concentration_risk"] = concentration_risk

        # 3. 计算方向性风险
        long_exposure = sum(
            action_value * current_prices.get(symbol, 0)
            for symbol, action_value in trading_action.items()
            if symbol != "__HOLD__" and action_value > 0 and symbol in current_prices
        )

        short_exposure = sum(
            abs(action_value) * current_prices.get(symbol, 0)
            for symbol, action_value in trading_action.items()
            if symbol != "__HOLD__" and action_value < 0 and symbol in current_prices
        )

        risk_metrics["long_exposure"] = long_exposure
        risk_metrics["short_exposure"] = short_exposure
        risk_metrics["net_exposure"] = long_exposure - short_exposure

        return risk_metrics

    def _analyze_agent_contributions(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """分析智能体贡献度"""
        contributions = {}

        # 1. 分析层贡献分析
        analyst_outputs = state.get("analyst_outputs", {})
        contributions["analyst_layer"] = self._analyze_analyst_contributions(analyst_outputs)

        # 2. 展望层贡献分析
        outlook_outputs = state.get("outlook_outputs", {})
        contributions["outlook_layer"] = self._analyze_outlook_contributions(outlook_outputs)

        # 3. 交易层分析
        trading_outputs = state.get("trading_outputs", {})
        contributions["trading_layer"] = self._analyze_trading_contributions(trading_outputs)

        # 4. 整体协调性分析
        contributions["coordination"] = self._analyze_coordination(
            analyst_outputs, outlook_outputs, trading_outputs
        )

        return contributions

    def _analyze_analyst_contributions(self, analyst_outputs: Dict[str, Any]) -> Dict[str, Any]:
        """分析分析层智能体贡献"""
        analysis = {
            "active_agents": list(analyst_outputs.keys()),
            "agent_count": len(analyst_outputs),
            "confidence_scores": {},
            "data_coverage": {}
        }

        for agent_id, output in analyst_outputs.items():
            if isinstance(output, dict):
                # 提取信心度
                confidence = output.get("confidence", 0.0)
                if isinstance(confidence, (int, float)):
                    analysis["confidence_scores"][agent_id] = confidence

                # 分析数据覆盖度
                if agent_id == "NAA":
                    analysis["data_coverage"]["news_analysis"] = bool(output.get("analysis"))
                elif agent_id == "TAA":
                    analysis["data_coverage"]["technical_analysis"] = bool(output.get("analysis"))
                elif agent_id == "FAA":
                    analysis["data_coverage"]["fundamental_analysis"] = bool(output.get("analysis"))

        # 计算平均信心度
        if analysis["confidence_scores"]:
            analysis["average_confidence"] = sum(analysis["confidence_scores"].values()) / len(analysis["confidence_scores"])
        else:
            analysis["average_confidence"] = 0.0

        return analysis

    def _analyze_outlook_contributions(self, outlook_outputs: Dict[str, Any]) -> Dict[str, Any]:
        """分析展望层智能体贡献"""
        analysis = {
            "active_agents": list(outlook_outputs.keys()),
            "agent_count": len(outlook_outputs),
            "outlook_consensus": {},
            "confidence_scores": {}
        }

        outlook_directions = []

        for agent_id, output in outlook_outputs.items():
            if isinstance(output, dict):
                # 提取信心度
                confidence = output.get("confidence", 0.0)
                if isinstance(confidence, (int, float)):
                    analysis["confidence_scores"][agent_id] = confidence

                # 分析展望方向
                outlook = output.get("outlook", "").lower()
                if "bullish" in outlook or "看涨" in outlook:
                    outlook_directions.append(1)
                elif "bearish" in outlook or "看跌" in outlook:
                    outlook_directions.append(-1)
                else:
                    outlook_directions.append(0)

        # 计算展望共识
        if outlook_directions:
            analysis["outlook_consensus"]["direction_average"] = sum(outlook_directions) / len(outlook_directions)
            analysis["outlook_consensus"]["direction_consistency"] = len(set(outlook_directions)) == 1
        else:
            analysis["outlook_consensus"]["direction_average"] = 0.0
            analysis["outlook_consensus"]["direction_consistency"] = False

        return analysis

    def _analyze_trading_contributions(self, trading_outputs: Dict[str, Any]) -> Dict[str, Any]:
        """分析交易层贡献"""
        analysis = {
            "trading_decision_made": "TRA" in trading_outputs,
            "decision_quality": {}
        }

        if "TRA" in trading_outputs:
            tra_output = trading_outputs["TRA"]
            if isinstance(tra_output, dict):
                # 分析决策质量
                analysis["decision_quality"]["has_reasoning"] = bool(tra_output.get("reasoning"))
                analysis["decision_quality"]["has_confidence"] = "confidence" in tra_output
                analysis["decision_quality"]["action_type"] = tra_output.get("action", "unknown")

                confidence = tra_output.get("confidence", 0.0)
                if isinstance(confidence, (int, float)):
                    analysis["decision_quality"]["confidence_score"] = confidence

        return analysis

    def _analyze_coordination(self,
                            analyst_outputs: Dict[str, Any],
                            outlook_outputs: Dict[str, Any],
                            trading_outputs: Dict[str, Any]) -> Dict[str, Any]:
        """分析智能体间协调性"""
        coordination = {
            "layer_completion": {
                "analyst_layer": len(analyst_outputs) > 0,
                "outlook_layer": len(outlook_outputs) > 0,
                "trading_layer": len(trading_outputs) > 0
            },
            "information_flow": {},
            "consensus_metrics": {}
        }

        # 分析信息流
        coordination["information_flow"]["analyst_to_outlook"] = (
            len(analyst_outputs) > 0 and len(outlook_outputs) > 0
        )
        coordination["information_flow"]["outlook_to_trading"] = (
            len(outlook_outputs) > 0 and len(trading_outputs) > 0
        )

        # 计算共识指标
        all_confidences = []

        # 收集所有信心度分数
        for outputs in [analyst_outputs, outlook_outputs, trading_outputs]:
            for output in outputs.values():
                if isinstance(output, dict) and "confidence" in output:
                    confidence = output["confidence"]
                    if isinstance(confidence, (int, float)):
                        all_confidences.append(confidence)

        if all_confidences:
            coordination["consensus_metrics"]["overall_confidence"] = sum(all_confidences) / len(all_confidences)
            coordination["consensus_metrics"]["confidence_std"] = np.std(all_confidences) if len(all_confidences) > 1 else 0.0
        else:
            coordination["consensus_metrics"]["overall_confidence"] = 0.0
            coordination["consensus_metrics"]["confidence_std"] = 0.0

        return coordination

    def _evaluate_decision_quality(self,
                                 trading_action: Dict[str, float],
                                 state: Dict[str, Any],
                                 trading_return: float) -> Dict[str, Any]:
        """评估决策质量"""
        quality_metrics = {
            "action_decisiveness": self._calculate_action_decisiveness(trading_action),
            "information_utilization": self._assess_information_utilization(state),
            "risk_appropriateness": self._assess_risk_appropriateness(trading_action, state),
            "return_expectation": trading_return
        }

        # 计算综合决策质量分数
        quality_score = (
            quality_metrics["action_decisiveness"] * 0.3 +
            quality_metrics["information_utilization"] * 0.3 +
            quality_metrics["risk_appropriateness"] * 0.4
        )

        quality_metrics["overall_quality_score"] = quality_score

        return quality_metrics

    def _calculate_action_decisiveness(self, trading_action: Dict[str, float]) -> float:
        """计算动作果断性"""
        if "__HOLD__" in trading_action and len(trading_action) == 1:
            return 0.0  # 完全持有

        # 计算非持有动作的强度
        action_strengths = [
            abs(action_value) for symbol, action_value in trading_action.items()
            if symbol != "__HOLD__"
        ]

        if not action_strengths:
            return 0.0

        # 平均动作强度作为果断性指标
        return min(sum(action_strengths) / len(action_strengths), 1.0)

    def _assess_information_utilization(self, state: Dict[str, Any]) -> float:
        """评估信息利用度"""
        utilization_score = 0.0

        # 检查各层级信息是否被利用
        if state.get("analyst_outputs"):
            utilization_score += 0.4  # 分析层信息

        if state.get("outlook_outputs"):
            utilization_score += 0.3  # 展望层信息

        if state.get("trading_outputs"):
            utilization_score += 0.3  # 交易层决策

        return min(utilization_score, 1.0)

    def _assess_risk_appropriateness(self,
                                   trading_action: Dict[str, float],
                                   state: Dict[str, Any]) -> float:
        """评估风险适当性"""
        # 简化的风险适当性评估
        # 基于仓位大小和市场条件

        total_position = sum(
            abs(action_value) for symbol, action_value in trading_action.items()
            if symbol != "__HOLD__"
        )

        # 适中的仓位被认为是合适的（0.3-0.7之间）
        if 0.3 <= total_position <= 0.7:
            return 1.0
        elif total_position < 0.3:
            return 0.7  # 过于保守
        else:
            return 0.5  # 过于激进

    def get_performance_summary(self, days: int = 30) -> Dict[str, Any]:
        """获取性能摘要"""
        if not self.performance_history:
            return {"error": "无性能历史数据"}

        recent_history = self.performance_history[-days:] if len(self.performance_history) > days else self.performance_history

        returns = [record["trading_return"] for record in recent_history]

        summary = {
            "period_days": len(recent_history),
            "total_return": sum(returns),
            "average_daily_return": sum(returns) / len(returns) if returns else 0.0,
            "return_volatility": np.std(returns) if len(returns) > 1 else 0.0,
            "positive_days": sum(1 for r in returns if r > 0),
            "negative_days": sum(1 for r in returns if r < 0),
            "win_rate": sum(1 for r in returns if r > 0) / len(returns) if returns else 0.0
        }

        return summary