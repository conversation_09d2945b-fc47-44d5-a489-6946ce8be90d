"""
动态状态管理系统

为多智能体交易系统提供完整的状态管理解决方案，包括新的统一架构组件
"""

from .dynamic_state import DynamicState, DailyStateSnapshot, AgentExecutionRecord
from .state_manager import StateManager
from .integration_adapter import StateManagementAdapter
from .opro_integration import OPRODataProvider

# 新的统一架构组件
try:
    from .unified_state_interface import UnifiedStateInterface, LayeredDataAccessController
    from .state_evolution_engine import StateEvolutionEngine
    from .performance_evaluator import PerformanceEvaluator
    UNIFIED_ARCHITECTURE_AVAILABLE = True
except ImportError:
    UNIFIED_ARCHITECTURE_AVAILABLE = False

__version__ = "2.0.0"
__author__ = "Multi-Agent Optimization Team"

# 导出主要类
__all__ = [
    "DynamicState",
    "DailyStateSnapshot",
    "AgentExecutionRecord",
    "StateManager",
    "StateManagementAdapter",
    "OPRODataProvider",
    "create_state_management_system"
]

# 如果新架构可用，添加到导出列表
if UNIFIED_ARCHITECTURE_AVAILABLE:
    __all__.extend([
        "UnifiedStateInterface",
        "LayeredDataAccessController",
        "StateEvolutionEngine",
        "PerformanceEvaluator"
    ])


def create_state_management_system(storage_path: str = "data/state_storage",
                                 compression: bool = True,
                                 enable_detailed_logging: bool = True,
                                 max_daily_states: int = 100):
    """
    快速创建完整的状态管理系统
    
    Args:
        storage_path: 存储路径
        compression: 是否启用压缩
        enable_detailed_logging: 是否启用详细日志
        max_daily_states: 内存中保持的最大日级状态数
        
    Returns:
        tuple: (StateManagementAdapter, OPRODataProvider)
    """
    # 创建状态管理器
    state_manager = StateManager(
        storage_path=storage_path,
        compression=compression,
        max_daily_states=max_daily_states
    )
    
    # 创建集成适配器
    adapter = StateManagementAdapter(
        state_manager=state_manager,
        enable_detailed_logging=enable_detailed_logging
    )
    
    # 创建OPRO数据提供器
    opro_provider = OPRODataProvider(state_manager)
    
    return adapter, opro_provider


# 便捷导入
StateManagement = StateManagementAdapter
OPROData = OPRODataProvider
