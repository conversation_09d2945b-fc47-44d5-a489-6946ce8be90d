"""
统一状态接口 (Unified State Interface)

为多智能体系统提供统一的状态访问和管理接口，确保所有智能体都通过同一个接口与state字典交互。
实现严格的分层数据访问控制和完整的状态演进机制。
"""

import copy
import logging
from typing import Dict, Any, List, Optional, Set
from datetime import datetime


class LayeredDataAccessController:
    """分层数据访问控制器 - 定义各层级智能体的数据访问权限"""

    # 数据访问权限矩阵
    ACCESS_PERMISSIONS = {
        # 分析层：只能访问基础市场数据，互相隔离
        "NAA": {
            "allowed_fields": ["current_date", "date", "news_history"],
            "forbidden_fields": ["price_history", "fundamental_data", "outlook_outputs"]
        },
        "TAA": {
            "allowed_fields": ["current_date", "date", "price_history"],
            "forbidden_fields": ["news_history", "fundamental_data", "outlook_outputs"]
        },
        "FAA": {
            "allowed_fields": ["current_date", "date", "fundamental_data"],
            "forbidden_fields": ["news_history", "price_history", "analyst_outputs"]
        },

        # 展望层：可以访问分析层输出，但不能访问原始市场数据
        "BOA": {
            "allowed_fields": ["current_date", "date", "analyst_outputs"],
            "forbidden_fields": ["news_history", "price_history", "fundamental_data", "outlook_outputs"]
        },
        "BeOA": {
            "allowed_fields": ["current_date", "date", "analyst_outputs", "positions", "position_values"],
            "forbidden_fields": ["news_history", "price_history", "fundamental_data", "outlook_outputs"]
        },
        "NOA": {
            "allowed_fields": ["current_date", "date", "analyst_outputs", "positions", "position_values"],
            "forbidden_fields": ["news_history", "price_history", "fundamental_data", "outlook_outputs"]
        },

        # 交易层：可以访问展望层输出和必要的交易信息
        "TRA": {
            "allowed_fields": ["current_date", "date", "analyst_outputs", "outlook_outputs", "positions", "position_values", "cash"],
            "forbidden_fields": ["news_history", "price_history", "fundamental_data"]
        }
    }

    @classmethod
    def filter_state_for_agent(cls, agent_id: str, full_state: Dict[str, Any]) -> Dict[str, Any]:
        """为特定智能体过滤状态数据"""
        permissions = cls.ACCESS_PERMISSIONS.get(agent_id, {})
        allowed_fields = permissions.get("allowed_fields", [])
        forbidden_fields = permissions.get("forbidden_fields", [])

        filtered_state = {}

        # 只包含允许的字段
        for field in allowed_fields:
            if field in full_state:
                if field == "analyst_outputs":
                    # 展望层智能体只能看到分析层输出
                    analyst_outputs = full_state.get("analyst_outputs", {})
                    filtered_outputs = {}
                    for analyst_id in ["NAA", "TAA", "FAA"]:
                        if analyst_id in analyst_outputs:
                            filtered_outputs[analyst_id] = analyst_outputs[analyst_id]
                    filtered_state[field] = filtered_outputs
                elif field == "outlook_outputs":
                    # 交易层智能体可以看到展望层输出
                    outlook_outputs = full_state.get("outlook_outputs", {})
                    filtered_outputs = {}
                    for outlook_id in ["BOA", "BeOA", "NOA"]:
                        if outlook_id in outlook_outputs:
                            filtered_outputs[outlook_id] = outlook_outputs[outlook_id]
                    filtered_state[field] = filtered_outputs
                else:
                    filtered_state[field] = copy.deepcopy(full_state[field])

        # 确保禁止字段不被包含（双重保护）
        for field in forbidden_fields:
            if field in filtered_state:
                del filtered_state[field]

        return filtered_state


class UnifiedStateInterface:
    """
    统一状态接口

    作为所有智能体与state字典交互的唯一接口，提供：
    1. 严格的分层数据访问控制
    2. 智能体输出的统一存储机制
    3. 完整的状态演进追踪
    4. 性能评估数据管理
    """

    def __init__(self, initial_state: Dict[str, Any]):
        """初始化统一状态接口"""
        self.state = copy.deepcopy(initial_state)
        self.access_controller = LayeredDataAccessController()
        self.execution_history = []
        self.logger = logging.getLogger("UnifiedStateInterface")

        # 初始化智能体输出存储区域
        if "analyst_outputs" not in self.state:
            self.state["analyst_outputs"] = {}
        if "outlook_outputs" not in self.state:
            self.state["outlook_outputs"] = {}
        if "trading_outputs" not in self.state:
            self.state["trading_outputs"] = {}

        # 初始化性能评估区域
        if "performance_metrics" not in self.state:
            self.state["performance_metrics"] = {}

        self.logger.info(f"统一状态接口初始化完成，日期: {self.state.get('current_date', 'Unknown')}")

    def get_agent_input(self, agent_id: str) -> Dict[str, Any]:
        """为智能体获取严格过滤的输入数据"""
        # 应用分层数据访问控制
        filtered_state = self.access_controller.filter_state_for_agent(agent_id, self.state)

        # 记录数据访问日志
        original_fields = set(self.state.keys())
        filtered_fields = set(filtered_state.keys())
        excluded_fields = original_fields - filtered_fields

        self.logger.debug(f"智能体 {agent_id} 数据访问控制:")
        self.logger.debug(f"  - 允许字段: {list(filtered_fields)}")
        self.logger.debug(f"  - 排除字段: {list(excluded_fields)}")

        # 特别验证关键数据隔离
        self._validate_data_isolation(agent_id, filtered_state)

        return filtered_state

    def store_agent_output(self, agent_id: str, output: Dict[str, Any]) -> None:
        """存储智能体输出到state字典"""
        # 添加元数据
        enhanced_output = copy.deepcopy(output)
        enhanced_output.update({
            "agent_id": agent_id,
            "timestamp": datetime.now().isoformat(),
            "execution_order": len(self.execution_history) + 1
        })

        # 根据智能体类型存储到相应区域
        if agent_id in ["NAA", "TAA", "FAA"]:
            self.state["analyst_outputs"][agent_id] = enhanced_output
        elif agent_id in ["BOA", "BeOA", "NOA"]:
            self.state["outlook_outputs"][agent_id] = enhanced_output
        elif agent_id == "TRA":
            self.state["trading_outputs"][agent_id] = enhanced_output

        # 记录执行历史
        execution_record = {
            "agent_id": agent_id,
            "timestamp": enhanced_output["timestamp"],
            "execution_order": enhanced_output["execution_order"],
            "output_keys": list(output.keys()) if isinstance(output, dict) else []
        }
        self.execution_history.append(execution_record)

        self.logger.info(f"智能体 {agent_id} 输出已存储到state字典")

    def get_current_state(self) -> Dict[str, Any]:
        """获取当前完整状态的副本"""
        return copy.deepcopy(self.state)

    def update_base_state(self, updates: Dict[str, Any]) -> None:
        """更新基础状态数据（如市场数据更新）"""
        for key, value in updates.items():
            # 保护智能体输出区域不被意外覆盖
            if key not in ["analyst_outputs", "outlook_outputs", "trading_outputs"]:
                self.state[key] = copy.deepcopy(value)

        self.logger.debug(f"基础状态已更新: {list(updates.keys())}")

    def store_performance_metrics(self, metrics: Dict[str, Any]) -> None:
        """存储性能评估指标"""
        current_date = self.state.get("current_date", "unknown")
        self.state["performance_metrics"][current_date] = {
            **metrics,
            "timestamp": datetime.now().isoformat()
        }

        self.logger.info(f"性能指标已存储: {current_date}")

    def get_execution_summary(self) -> Dict[str, Any]:
        """获取执行摘要"""
        return {
            "total_executions": len(self.execution_history),
            "execution_history": self.execution_history.copy(),
            "analyst_count": len(self.state.get("analyst_outputs", {})),
            "outlook_count": len(self.state.get("outlook_outputs", {})),
            "trading_count": len(self.state.get("trading_outputs", {})),
            "current_date": self.state.get("current_date", "unknown")
        }

    def _validate_data_isolation(self, agent_id: str, filtered_state: Dict[str, Any]) -> None:
        """验证数据隔离的正确性"""
        # 验证分析层数据隔离
        if agent_id == "NAA":
            if "price_history" in filtered_state or "fundamental_data" in filtered_state:
                self.logger.error(f"❌ NAA数据隔离失败: 包含了不应访问的数据")
                return
        elif agent_id == "TAA":
            if "news_history" in filtered_state or "fundamental_data" in filtered_state:
                self.logger.error(f"❌ TAA数据隔离失败: 包含了不应访问的数据")
                return
        elif agent_id == "FAA":
            if "news_history" in filtered_state or "price_history" in filtered_state:
                self.logger.error(f"❌ FAA数据隔离失败: 包含了不应访问的数据")
                return

        # 验证展望层只能看到分析层输出
        elif agent_id in ["BOA", "BeOA", "NOA"]:
            if any(field in filtered_state for field in ["news_history", "price_history", "fundamental_data"]):
                self.logger.error(f"❌ {agent_id}数据隔离失败: 包含了原始市场数据")
                return

        # 验证交易层只能看到前序输出
        elif agent_id == "TRA":
            if any(field in filtered_state for field in ["news_history", "price_history", "fundamental_data"]):
                self.logger.error(f"❌ TRA数据隔离失败: 包含了原始市场数据")
                return

        self.logger.debug(f"✅ {agent_id} 数据隔离验证通过")