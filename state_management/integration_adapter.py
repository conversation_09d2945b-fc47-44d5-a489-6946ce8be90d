"""
状态管理集成适配器

将动态状态管理系统集成到现有的多智能体架构中，
确保不影响现有的智能体执行流程
"""

import copy
import time
from datetime import datetime
from typing import Dict, Any, Optional, Callable, Set
import logging

from .state_manager import StateManager
from .dynamic_state import DynamicState


class StateManagementAdapter:
    """
    状态管理适配器
    
    负责将状态管理功能无缝集成到现有的智能体执行流程中
    """
    
    def __init__(self, 
                 state_manager: StateManager,
                 enable_detailed_logging: bool = True):
        """
        初始化状态管理适配器
        
        Args:
            state_manager: 状态管理器实例
            enable_detailed_logging: 是否启用详细日志
        """
        self.state_manager = state_manager
        self.enable_detailed_logging = enable_detailed_logging
        self.logger = logging.getLogger("StateManagementAdapter")
        
        # 当前活动的日级状态
        self.current_daily_state: Optional[DynamicState] = None
        self.current_date: Optional[str] = None
        
        self.logger.info("状态管理适配器初始化完成")
    
    def initialize_daily_trading(self, date: str, initial_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        初始化日级交易状态
        
        Args:
            date: 交易日期
            initial_state: 初始状态
            
        Returns:
            Dict: 处理后的初始状态
        """
        self.current_date = date
        self.current_daily_state = self.state_manager.create_daily_state(date, initial_state)
        
        if self.enable_detailed_logging:
            self.logger.info(f"初始化日级交易状态: {date}")
            self.logger.info(f"初始状态包含字段: {list(initial_state.keys())}")
        
        return self.current_daily_state.get_current_state()
    
    def prepare_agent_input(self, agent_id: str, base_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        为智能体准备输入状态（集成新的统一状态接口）

        Args:
            agent_id: 智能体ID
            base_state: 基础状态

        Returns:
            Dict: 经过严格数据访问控制的输入状态
        """
        try:
            # 尝试使用新的统一状态接口
            from .unified_state_interface import UnifiedStateInterface

            # 如果有当前日级状态，优先使用其数据
            if self.current_daily_state:
                current_state = self.current_daily_state.get_current_state()

                # 合并基础状态的更新
                for key, value in base_state.items():
                    if key not in ["analyst_outputs", "outlook_outputs", "trading_outputs"]:
                        current_state[key] = value
            else:
                current_state = base_state

            # 创建统一状态接口
            unified_state = UnifiedStateInterface(current_state)

            # 使用新的分层数据访问控制获取输入
            input_state = unified_state.get_agent_input(agent_id)

            if self.enable_detailed_logging:
                self.logger.debug(f"为 {agent_id} 准备输入状态（新架构），包含字段: {list(input_state.keys())}")

                # 记录数据访问控制效果
                original_fields = set(current_state.keys())
                filtered_fields = set(input_state.keys())
                excluded_fields = original_fields - filtered_fields

                if excluded_fields:
                    self.logger.debug(f"  数据访问控制排除字段: {list(excluded_fields)}")

                # 记录前序智能体输出信息
                if "analyst_outputs" in input_state:
                    analyst_agents = list(input_state["analyst_outputs"].keys())
                    self.logger.debug(f"  包含分析层输出: {analyst_agents}")

                if "outlook_outputs" in input_state:
                    outlook_agents = list(input_state["outlook_outputs"].keys())
                    self.logger.debug(f"  包含展望层输出: {outlook_agents}")

            return input_state

        except ImportError:
            # 如果新架构不可用，回退到原有实现
            self.logger.debug(f"新架构不可用，使用原有状态准备方法: {agent_id}")
            return self._prepare_agent_input_legacy(agent_id, base_state)

        except Exception as e:
            # 如果新架构执行失败，回退到原有实现
            self.logger.warning(f"新架构状态准备失败，回退到原有实现: {e}")
            return self._prepare_agent_input_legacy(agent_id, base_state)

    def _prepare_agent_input_legacy(self, agent_id: str, base_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        原有的智能体输入准备实现（作为回退方案）
        """
        if not self.current_daily_state:
            self.logger.warning("当前日级状态未初始化，返回基础状态")
            return base_state

        # 获取包含前序智能体输出的输入状态
        input_state = self.current_daily_state.get_agent_input_state(agent_id)

        # 合并基础状态的更新（如市场数据更新）
        for key, value in base_state.items():
            if key not in ["analyst_outputs", "outlook_outputs"]:  # 保护智能体输出字段
                input_state[key] = value

        if self.enable_detailed_logging:
            self.logger.debug(f"为 {agent_id} 准备输入状态（原有架构），包含字段: {list(input_state.keys())}")

            # 记录前序智能体输出信息
            if "analyst_outputs" in input_state:
                analyst_agents = list(input_state["analyst_outputs"].keys())
                self.logger.debug(f"  包含分析层输出: {analyst_agents}")

            if "outlook_outputs" in input_state:
                outlook_agents = list(input_state["outlook_outputs"].keys())
                self.logger.debug(f"  包含展望层输出: {outlook_agents}")

        return input_state

    def execute_daily_cycle_with_new_architecture(self,
                                                coalition: Set[str],
                                                agents: Dict[str, Any],
                                                base_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用新架构执行完整的日级智能体循环

        Args:
            coalition: 智能体联盟
            agents: 智能体实例字典
            base_state: 基础状态数据

        Returns:
            执行结果，包含智能体输出和性能指标
        """
        try:
            from .unified_state_interface import UnifiedStateInterface
            from .state_evolution_engine import StateEvolutionEngine
            from .performance_evaluator import PerformanceEvaluator

            current_date = base_state.get("current_date", "unknown")
            self.logger.info(f"使用新架构执行日级循环: {current_date}, 联盟: {coalition}")

            # 1. 初始化日级状态（如果尚未初始化）
            if not self.current_daily_state or self.current_date != current_date:
                self.initialize_daily_trading(current_date, base_state)

            # 2. 创建统一状态接口
            unified_state = UnifiedStateInterface(base_state)

            # 3. 创建状态演进引擎
            evolution_engine = StateEvolutionEngine(unified_state)

            # 4. 执行日级循环
            execution_result = evolution_engine.execute_daily_cycle(coalition, agents)

            # 5. 记录所有智能体的执行结果到动态状态管理系统
            if execution_result.get("success", False):
                agent_outputs = execution_result.get("agent_outputs", {})

                # 记录分析层智能体
                for agent_id, output in agent_outputs.get("analyst_outputs", {}).items():
                    self._record_agent_execution_from_unified_state(agent_id, output, unified_state)

                # 记录展望层智能体
                for agent_id, output in agent_outputs.get("outlook_outputs", {}).items():
                    self._record_agent_execution_from_unified_state(agent_id, output, unified_state)

                # 记录交易层智能体
                for agent_id, output in agent_outputs.get("trading_outputs", {}).items():
                    self._record_agent_execution_from_unified_state(agent_id, output, unified_state)

                # 6. 存储性能指标
                performance_metrics = execution_result.get("performance_metrics", {})
                if performance_metrics:
                    self._store_performance_metrics(performance_metrics)

                self.logger.info(f"新架构日级循环执行成功: {current_date}")
            else:
                self.logger.error(f"新架构日级循环执行失败: {current_date}")

            return execution_result

        except ImportError as e:
            self.logger.warning(f"新架构组件不可用: {e}")
            return {"success": False, "error": "新架构组件不可用"}

        except Exception as e:
            self.logger.error(f"新架构执行失败: {e}")
            return {"success": False, "error": str(e)}

    def _record_agent_execution_from_unified_state(self,
                                                 agent_id: str,
                                                 output: Dict[str, Any],
                                                 unified_state: 'UnifiedStateInterface') -> None:
        """
        从统一状态接口记录智能体执行结果

        Args:
            agent_id: 智能体ID
            output: 智能体输出
            unified_state: 统一状态接口实例
        """
        try:
            # 获取智能体的输入状态
            agent_input = unified_state.get_agent_input(agent_id)

            # 提取处理时间（如果有）
            processing_time = output.get("processing_time", 0.0)

            # 记录执行结果
            self.record_agent_execution(
                agent_id=agent_id,
                input_state=agent_input,
                output_data=output,
                processing_time=processing_time,
                success=True
            )

        except Exception as e:
            self.logger.error(f"记录智能体 {agent_id} 执行结果失败: {e}")

    def _store_performance_metrics(self, performance_metrics: Dict[str, Any]) -> None:
        """
        存储性能指标到状态管理系统

        Args:
            performance_metrics: 性能指标数据
        """
        try:
            if self.current_daily_state:
                # 将性能指标添加到当前日级状态
                current_state = self.current_daily_state.get_current_state()
                current_state["performance_metrics"] = performance_metrics

                # 更新状态
                self.current_daily_state.current_state.update(current_state)

                self.logger.debug(f"性能指标已存储到动态状态管理系统")

        except Exception as e:
            self.logger.error(f"存储性能指标失败: {e}")

    def record_agent_execution(self,
                             agent_id: str,
                             input_state: Dict[str, Any],
                             output_data: Dict[str, Any],
                             processing_time: float,
                             success: bool = True,
                             error_message: Optional[str] = None) -> None:
        """
        记录智能体执行结果
        
        Args:
            agent_id: 智能体ID
            input_state: 输入状态
            output_data: 输出数据
            processing_time: 处理时间
            success: 是否成功
            error_message: 错误信息
        """
        if not self.current_daily_state or not self.current_date:
            self.logger.error("当前日级状态未初始化，无法记录智能体执行")
            return
        
        # 过滤输入状态，只保留关键信息以减少存储空间
        filtered_input = self._filter_input_for_storage(input_state)
        
        # 记录执行结果
        self.state_manager.record_agent_execution(
            date=self.current_date,
            agent_id=agent_id,
            input_data=filtered_input,
            output_data=output_data,
            processing_time=processing_time,
            success=success,
            error_message=error_message
        )
        
        if self.enable_detailed_logging:
            status = "成功" if success else "失败"
            self.logger.info(f"记录 {agent_id} 执行结果: {status}, 处理时间: {processing_time:.3f}秒")
            if not success and error_message:
                self.logger.error(f"  错误信息: {error_message}")
    
    def finalize_daily_trading(self) -> Optional[Dict[str, Any]]:
        """
        完成日级交易并创建快照
        
        Returns:
            Dict: 日级交易摘要
        """
        if not self.current_daily_state or not self.current_date:
            self.logger.warning("当前日级状态未初始化，无法完成日级交易")
            return None
        
        # 创建日级快照
        snapshot = self.state_manager.finalize_daily_state(self.current_date)
        
        # 获取执行摘要
        execution_summary = self.current_daily_state.get_execution_summary()
        
        if self.enable_detailed_logging:
            self.logger.info(f"完成日级交易: {self.current_date}")
            self.logger.info(f"  总执行次数: {execution_summary['total_executions']}")
            self.logger.info(f"  成功次数: {execution_summary['successful_executions']}")
            self.logger.info(f"  总处理时间: {execution_summary['total_processing_time']:.3f}秒")
        
        # 重置当前状态
        self.current_daily_state = None
        self.current_date = None
        
        return execution_summary
    
    def finalize_weekly_trading(self, week_start_date: str) -> Dict[str, Any]:
        """
        完成周级交易并持久化状态
        
        Args:
            week_start_date: 周开始日期
            
        Returns:
            Dict: 周级交易摘要
        """
        weekly_summary = self.state_manager.finalize_weekly_state(week_start_date)
        
        if self.enable_detailed_logging:
            self.logger.info(f"完成周级交易: {week_start_date}")
            if weekly_summary:
                self.logger.info(f"  交易天数: {weekly_summary.get('trading_days', 0)}")
                self.logger.info(f"  总执行次数: {weekly_summary.get('total_agent_executions', 0)}")
                self.logger.info(f"  成功率: {weekly_summary.get('success_rate', 0):.2%}")
        
        return weekly_summary
    
    def get_opro_training_data(self, agent_id: str, weeks: int = 4) -> Dict[str, Any]:
        """
        获取OPRO训练数据
        
        Args:
            agent_id: 智能体ID
            weeks: 获取最近几周的数据
            
        Returns:
            Dict: OPRO训练数据
        """
        training_data = self.state_manager.get_opro_training_data(agent_id, weeks)
        
        if self.enable_detailed_logging:
            io_count = len(training_data.get("io_history", []))
            failure_count = len(training_data.get("failure_cases", []))
            success_count = len(training_data.get("success_cases", []))
            
            self.logger.info(f"获取 {agent_id} 的OPRO训练数据:")
            self.logger.info(f"  输入输出记录: {io_count} 条")
            self.logger.info(f"  失败案例: {failure_count} 个")
            self.logger.info(f"  成功案例: {success_count} 个")
        
        return training_data
    
    def _filter_input_for_storage(self, input_state: Dict[str, Any]) -> Dict[str, Any]:
        """
        过滤输入状态以减少存储空间
        
        保留关键信息，移除冗余或过大的数据
        """
        filtered_input = {}
        
        # 保留的关键字段
        key_fields = [
            "current_date", "date", "cash", "positions", "position_values",
            "analyst_outputs", "outlook_outputs", "trading_day_info"
        ]
        
        for field in key_fields:
            if field in input_state:
                filtered_input[field] = input_state[field]
        
        # 对大数据字段进行摘要处理
        if "news_history" in input_state:
            news_history = input_state["news_history"]
            filtered_input["news_summary"] = {
                "total_dates": len(news_history),
                "total_news": sum(
                    len(news_list) for date_news in news_history.values()
                    for news_list in date_news.values() if isinstance(news_list, list)
                ),
                "latest_date": max(news_history.keys()) if news_history else None
            }
        
        if "price_history" in input_state:
            price_history = input_state["price_history"]
            filtered_input["price_summary"] = {
                "symbols": list(price_history.keys()),
                "total_points": sum(len(prices) for prices in price_history.values() if isinstance(prices, list)),
                "latest_prices": {
                    symbol: prices[-1] if isinstance(prices, list) and prices else None
                    for symbol, prices in price_history.items()
                }
            }
        
        if "fundamental_data" in input_state:
            fundamental_data = input_state["fundamental_data"]
            filtered_input["fundamental_summary"] = {
                "symbols": list(fundamental_data.keys()),
                "available_metrics": list(set(
                    key for data in fundamental_data.values() 
                    if isinstance(data, dict) for key in data.keys()
                ))
            }
        
        return filtered_input
    
    def create_agent_execution_wrapper(self, agent_process_func: Callable) -> Callable:
        """
        创建智能体执行包装器
        
        Args:
            agent_process_func: 原始的智能体处理函数
            
        Returns:
            Callable: 包装后的处理函数
        """
        def wrapped_process(agent_id: str, state: Dict[str, Any]) -> Dict[str, Any]:
            """包装后的智能体处理函数"""
            start_time = time.time()
            
            try:
                # 准备输入状态
                input_state = self.prepare_agent_input(agent_id, state)
                
                # 执行原始处理函数
                output_data = agent_process_func(input_state)
                
                # 计算处理时间
                processing_time = time.time() - start_time
                
                # 记录执行结果
                self.record_agent_execution(
                    agent_id=agent_id,
                    input_state=input_state,
                    output_data=output_data,
                    processing_time=processing_time,
                    success=True
                )
                
                return output_data
                
            except Exception as e:
                # 计算处理时间
                processing_time = time.time() - start_time
                
                # 记录执行失败
                self.record_agent_execution(
                    agent_id=agent_id,
                    input_state=state,
                    output_data={},
                    processing_time=processing_time,
                    success=False,
                    error_message=str(e)
                )
                
                # 重新抛出异常
                raise e
        
        return wrapped_process
    
    def get_current_state_summary(self) -> Dict[str, Any]:
        """获取当前状态摘要"""
        if not self.current_daily_state:
            return {"status": "no_active_state"}
        
        return {
            "status": "active",
            "date": self.current_date,
            "execution_summary": self.current_daily_state.get_execution_summary()
        }
