"""
每日状态管理器 - 基于现有数据库基础设施

提供每日完整state字典的持久化和历史查询功能，作为现有StateManager的轻量级扩展
"""

import json
import sqlite3
import threading
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class DailyStateManager:
    """
    每日状态管理器
    
    基于现有state_manager.db数据库，提供每日完整state的保存和查询功能。
    专门用于支持CG-OPO优化时的历史数据分析。
    """
    
    def __init__(self, storage_path: Optional[str] = None):
        """
        初始化每日状态管理器
        
        Args:
            storage_path: 数据库存储路径，默认使用现有的state_manager.db
        """
        self.storage_path = storage_path or "data/state_manager.db" 
        
        # 确保存储目录存在
        Path(self.storage_path).parent.mkdir(parents=True, exist_ok=True)
        
        # 线程锁，确保并发安全
        self._lock = threading.RLock()
        
        # 初始化daily_states表
        self._init_daily_states_table()
        
        logger.info(f"DailyStateManager initialized with storage: {self.storage_path}")
    
    def _init_daily_states_table(self) -> None:
        """初始化daily_states表结构"""
        try:
            with sqlite3.connect(self.storage_path) as conn:
                cursor = conn.cursor()
                
                # 创建daily_states表
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS daily_states (
                        date TEXT PRIMARY KEY,
                        state_data TEXT NOT NULL,
                        agent_count INTEGER,
                        trading_return REAL,
                        analyst_agents TEXT,
                        outlook_agents TEXT,
                        trading_agents TEXT,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # 创建索引
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_daily_states_date ON daily_states(date)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_daily_states_return ON daily_states(trading_return)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_daily_states_agents ON daily_states(agent_count)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_daily_states_created_at ON daily_states(created_at)')
                
                conn.commit()
                logger.debug("Daily states table initialized successfully")
                
        except Exception as e:
            logger.error(f"Failed to initialize daily_states table: {str(e)}")
            raise RuntimeError(f"Daily states table initialization failed: {str(e)}")
    
    def save_daily_state(self, date: str, complete_state: Dict[str, Any]) -> bool:
        """
        保存指定日期的完整state到数据库
        
        Args:
            date: 日期字符串 (格式: YYYY-MM-DD)
            complete_state: 完整的state字典，应包含所有agent输出
            
        Returns:
            bool: 保存是否成功
        """
        try:
            with self._lock:
                # 验证state完整性
                if not self._validate_state_completeness(complete_state):
                    logger.warning(f"State for {date} may be incomplete, saving anyway")
                
                # 提取统计信息
                analyst_outputs = complete_state.get("analyst_outputs", {})
                outlook_outputs = complete_state.get("outlook_outputs", {})
                trading_outputs = complete_state.get("trading_outputs", {})
                
                agent_count = len(analyst_outputs) + len(outlook_outputs) + len(trading_outputs)
                trading_return = complete_state.get("previous_day_return", None)
                
                # 序列化state数据
                state_data_json = json.dumps(complete_state, ensure_ascii=False, default=str)
                analyst_agents_json = json.dumps(list(analyst_outputs.keys()), ensure_ascii=False)
                outlook_agents_json = json.dumps(list(outlook_outputs.keys()), ensure_ascii=False)
                trading_agents_json = json.dumps(list(trading_outputs.keys()), ensure_ascii=False)
                
                with sqlite3.connect(self.storage_path) as conn:
                    cursor = conn.cursor()
                    
                    # 检查是否已存在该日期的记录
                    cursor.execute('SELECT date FROM daily_states WHERE date = ?', (date,))
                    existing = cursor.fetchone()
                    
                    current_timestamp = datetime.now().isoformat()
                    
                    if existing:
                        # 更新现有记录
                        cursor.execute('''
                            UPDATE daily_states 
                            SET state_data = ?, agent_count = ?, trading_return = ?,
                                analyst_agents = ?, outlook_agents = ?, trading_agents = ?,
                                updated_at = ?
                            WHERE date = ?
                        ''', (
                            state_data_json, agent_count, trading_return,
                            analyst_agents_json, outlook_agents_json, trading_agents_json,
                            current_timestamp, date
                        ))
                        logger.debug(f"Updated daily state for {date}")
                    else:
                        # 插入新记录
                        cursor.execute('''
                            INSERT INTO daily_states 
                            (date, state_data, agent_count, trading_return, 
                             analyst_agents, outlook_agents, trading_agents, created_at, updated_at)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            date, state_data_json, agent_count, trading_return,
                            analyst_agents_json, outlook_agents_json, trading_agents_json,
                            current_timestamp, current_timestamp
                        ))
                        logger.debug(f"Saved new daily state for {date}")
                    
                    conn.commit()
                
                logger.info(f"Daily state saved successfully: {date} (agents: {agent_count}, return: {trading_return})")
                return True
                
        except Exception as e:
            logger.error(f"Failed to save daily state for {date}: {str(e)}")
            return False
    
    def load_daily_state(self, date: str) -> Optional[Dict[str, Any]]:
        """
        从数据库加载指定日期的完整state
        
        Args:
            date: 日期字符串 (格式: YYYY-MM-DD)
            
        Returns:
            Optional[Dict[str, Any]]: 完整的state字典，如果不存在则返回None
        """
        try:
            with self._lock:
                with sqlite3.connect(self.storage_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        SELECT state_data, agent_count, trading_return, created_at, updated_at
                        FROM daily_states 
                        WHERE date = ?
                    ''', (date,))
                    
                    result = cursor.fetchone()
                    if result:
                        state_data = json.loads(result[0])
                        
                        # 添加元数据信息
                        state_data['_daily_metadata'] = {
                            'date': date,
                            'agent_count': result[1],
                            'trading_return': result[2],
                            'created_at': result[3],
                            'updated_at': result[4]
                        }
                        
                        logger.debug(f"Loaded daily state for {date} (agents: {result[1]})")
                        return state_data
                    
                    logger.debug(f"No daily state found for {date}")
                    return None
                    
        except Exception as e:
            logger.error(f"Failed to load daily state for {date}: {str(e)}")
            return None
    
    def query_agent_history(self, agent_id: str, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """
        查询指定智能体在日期范围内的历史表现数据
        
        Args:
            agent_id: 智能体ID (如 "NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA")
            start_date: 开始日期 (格式: YYYY-MM-DD)
            end_date: 结束日期 (格式: YYYY-MM-DD)
            
        Returns:
            List[Dict[str, Any]]: 历史数据列表，每个元素包含日期和该agent的输出
        """
        try:
            with self._lock:
                with sqlite3.connect(self.storage_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        SELECT date, state_data, trading_return
                        FROM daily_states 
                        WHERE date >= ? AND date <= ?
                        ORDER BY date ASC
                    ''', (start_date, end_date))
                    
                    results = []
                    for row in cursor.fetchall():
                        date, state_data_json, trading_return = row
                        
                        try:
                            state_data = json.loads(state_data_json)
                            agent_output = self._extract_agent_output(state_data, agent_id)
                            
                            if agent_output is not None:
                                results.append({
                                    'date': date,
                                    'agent_id': agent_id,
                                    'output': agent_output,
                                    'trading_return': trading_return
                                })
                        except json.JSONDecodeError as e:
                            logger.warning(f"Failed to parse state data for {date}: {str(e)}")
                            continue
                    
                    logger.debug(f"Found {len(results)} history records for {agent_id} from {start_date} to {end_date}")
                    return results
                    
        except Exception as e:
            logger.error(f"Failed to query agent history for {agent_id}: {str(e)}")
            return []
    
    def get_loss_days_for_agent(self, agent_id: str, days_back: int = 30) -> List[Dict[str, Any]]:
        """
        获取智能体在亏损日期的数据 (previous_day_return < 0)
        
        Args:
            agent_id: 智能体ID
            days_back: 向前查询的天数
            
        Returns:
            List[Dict[str, Any]]: 亏损日期的数据列表
        """
        try:
            # 计算查询日期范围
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
            
            with self._lock:
                with sqlite3.connect(self.storage_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute('''
                        SELECT date, state_data, trading_return
                        FROM daily_states 
                        WHERE date >= ? AND date <= ? AND trading_return < 0
                        ORDER BY date DESC
                    ''', (start_date, end_date))
                    
                    results = []
                    for row in cursor.fetchall():
                        date, state_data_json, trading_return = row
                        
                        try:
                            state_data = json.loads(state_data_json)
                            agent_output = self._extract_agent_output(state_data, agent_id)
                            
                            if agent_output is not None:
                                results.append({
                                    'date': date,
                                    'agent_id': agent_id,
                                    'output': agent_output,
                                    'trading_return': trading_return,
                                    'loss_type': 'negative_return'
                                })
                        except json.JSONDecodeError as e:
                            logger.warning(f"Failed to parse state data for {date}: {str(e)}")
                            continue
                    
                    logger.debug(f"Found {len(results)} loss days for {agent_id} in last {days_back} days")
                    return results
                    
        except Exception as e:
            logger.error(f"Failed to get loss days for {agent_id}: {str(e)}")
            return []
    
    def get_performance_trend(self, agent_id: str, days_back: int = 30) -> Dict[str, Any]:
        """
        分析智能体表现趋势
        
        Args:
            agent_id: 智能体ID
            days_back: 分析的天数范围
            
        Returns:
            Dict[str, Any]: 表现趋势分析结果
        """
        try:
            # 获取历史数据
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=days_back)).strftime('%Y-%m-%d')
            
            history = self.query_agent_history(agent_id, start_date, end_date)
            
            if not history:
                return {
                    'agent_id': agent_id,
                    'days_analyzed': 0,
                    'average_return': 0.0,
                    'positive_days': 0,
                    'negative_days': 0,
                    'confidence_trend': None,
                    'recommendation': 'No data available'
                }
            
            # 分析数据
            total_days = len(history)
            returns = [record['trading_return'] for record in history if record['trading_return'] is not None]
            positive_days = len([r for r in returns if r > 0])
            negative_days = len([r for r in returns if r < 0])
            average_return = sum(returns) / len(returns) if returns else 0.0
            
            # 分析信心度趋势（如果有confidence字段）
            confidence_values = []
            for record in history:
                output = record['output']
                if isinstance(output, dict) and 'confidence' in output:
                    confidence_values.append(output['confidence'])
            
            confidence_trend = None
            if len(confidence_values) >= 2:
                recent_confidence = sum(confidence_values[-5:]) / min(5, len(confidence_values))
                early_confidence = sum(confidence_values[:5]) / min(5, len(confidence_values))
                confidence_trend = recent_confidence - early_confidence
            
            # 生成建议
            recommendation = self._generate_performance_recommendation(
                average_return, positive_days / total_days if total_days > 0 else 0, confidence_trend
            )
            
            return {
                'agent_id': agent_id,
                'days_analyzed': total_days,
                'average_return': average_return,
                'positive_days': positive_days,
                'negative_days': negative_days,
                'win_rate': positive_days / total_days if total_days > 0 else 0.0,
                'confidence_trend': confidence_trend,
                'recent_performance': returns[-5:] if len(returns) >= 5 else returns,
                'recommendation': recommendation
            }
            
        except Exception as e:
            logger.error(f"Failed to analyze performance trend for {agent_id}: {str(e)}")
            return {'agent_id': agent_id, 'error': str(e)}
    
    def _validate_state_completeness(self, state: Dict[str, Any]) -> bool:
        """验证state的完整性"""
        required_fields = ["date", "cash", "positions"]
        agent_output_fields = ["analyst_outputs", "outlook_outputs"]
        
        # 检查基础字段
        for field in required_fields:
            if field not in state:
                logger.warning(f"Missing required field: {field}")
                return False
        
        # 检查至少有一个agent输出字段
        has_agent_output = any(field in state and state[field] for field in agent_output_fields)
        if not has_agent_output:
            logger.warning("No agent outputs found in state")
            return False
        
        return True
    
    def _extract_agent_output(self, state_data: Dict[str, Any], agent_id: str) -> Optional[Dict[str, Any]]:
        """从state数据中提取指定agent的输出"""
        # 检查分析层输出
        analyst_outputs = state_data.get("analyst_outputs", {})
        if agent_id in analyst_outputs:
            return analyst_outputs[agent_id]
        
        # 检查展望层输出
        outlook_outputs = state_data.get("outlook_outputs", {})
        if agent_id in outlook_outputs:
            return outlook_outputs[agent_id]
        
        # 检查交易层输出
        trading_outputs = state_data.get("trading_outputs", {})
        if agent_id in trading_outputs:
            return trading_outputs[agent_id]
        
        return None
    
    def _generate_performance_recommendation(self, avg_return: float, win_rate: float, confidence_trend: Optional[float]) -> str:
        """生成表现分析建议"""
        recommendations = []
        
        if avg_return > 0.01:
            recommendations.append("表现良好，平均收益率为正")
        elif avg_return < -0.01:
            recommendations.append("表现需要改进，平均收益率为负")
        else:
            recommendations.append("表现中性，收益率接近零")
        
        if win_rate > 0.6:
            recommendations.append("胜率较高")
        elif win_rate < 0.4:
            recommendations.append("胜率较低，需要优化决策逻辑")
        
        if confidence_trend is not None:
            if confidence_trend > 0.05:
                recommendations.append("信心度呈上升趋势")
            elif confidence_trend < -0.05:
                recommendations.append("信心度呈下降趋势，可能需要调整")
        
        return "; ".join(recommendations) if recommendations else "数据不足以生成建议"
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取每日状态管理器统计信息"""
        try:
            with self._lock:
                with sqlite3.connect(self.storage_path) as conn:
                    cursor = conn.cursor()
                    
                    # 统计总记录数
                    cursor.execute('SELECT COUNT(*) FROM daily_states')
                    total_records = cursor.fetchone()[0]
                    
                    # 统计最新记录日期
                    cursor.execute('SELECT MAX(date) FROM daily_states')
                    latest_date = cursor.fetchone()[0]
                    
                    # 统计平均agent数量
                    cursor.execute('SELECT AVG(agent_count) FROM daily_states WHERE agent_count IS NOT NULL')
                    avg_agent_count = cursor.fetchone()[0] or 0
                    
                    # 统计正收益天数
                    cursor.execute('SELECT COUNT(*) FROM daily_states WHERE trading_return > 0')
                    positive_days = cursor.fetchone()[0]
                    
                    return {
                        'total_daily_records': total_records,
                        'latest_date': latest_date,
                        'average_agent_count': round(avg_agent_count, 2),
                        'positive_return_days': positive_days,
                        'storage_path': self.storage_path
                    }
                    
        except Exception as e:
            logger.error(f"Failed to get statistics: {str(e)}")
            return {}