"""
状态管理器 - 负责状态的存储、加载和管理

提供日级和周级状态的持久化功能，支持OPRO优化数据提取
"""

import os
import json
import pickle
import gzip
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from pathlib import Path
import logging

from .dynamic_state import DynamicState, DailyStateSnapshot, AgentExecutionRecord


class StateManager:
    """
    状态管理器
    
    功能：
    1. 管理日级状态的创建和更新
    2. 周级状态的持久化存储
    3. 为OPRO优化提供数据接口
    4. 状态查询和分析功能
    """
    
    def __init__(self, 
                 storage_path: str = "data/state_storage",
                 compression: bool = True,
                 max_daily_states: int = 100):
        """
        初始化状态管理器
        
        Args:
            storage_path: 存储路径
            compression: 是否压缩存储
            max_daily_states: 内存中保持的最大日级状态数量
        """
        self.storage_path = Path(storage_path)
        self.compression = compression
        self.max_daily_states = max_daily_states
        
        # 创建存储目录
        self.storage_path.mkdir(parents=True, exist_ok=True)
        self.daily_storage_path = self.storage_path / "daily"
        self.weekly_storage_path = self.storage_path / "weekly"
        self.daily_storage_path.mkdir(exist_ok=True)
        self.weekly_storage_path.mkdir(exist_ok=True)
        
        # 内存中的状态缓存
        self.daily_states: Dict[str, DynamicState] = {}
        self.current_week_states: List[DailyStateSnapshot] = []
        
        # 日志记录器
        self.logger = logging.getLogger("StateManager")
        
        self.logger.info(f"状态管理器初始化完成，存储路径: {self.storage_path}")
    
    def create_daily_state(self, date: str, initial_state: Dict[str, Any]) -> DynamicState:
        """
        创建新的日级状态
        
        Args:
            date: 日期 (YYYY-MM-DD格式)
            initial_state: 初始状态数据
            
        Returns:
            DynamicState: 动态状态实例
        """
        if date in self.daily_states:
            self.logger.warning(f"日期 {date} 的状态已存在，将被覆盖")
        
        daily_state = DynamicState(initial_state, date)
        self.daily_states[date] = daily_state
        
        # 限制内存中的状态数量
        if len(self.daily_states) > self.max_daily_states:
            oldest_date = min(self.daily_states.keys())
            self._archive_daily_state(oldest_date)
            del self.daily_states[oldest_date]
        
        self.logger.info(f"创建日级状态: {date}")
        return daily_state
    
    def get_daily_state(self, date: str) -> Optional[DynamicState]:
        """获取指定日期的状态"""
        return self.daily_states.get(date)
    
    def record_agent_execution(self,
                             date: str,
                             agent_id: str,
                             input_data: Dict[str, Any],
                             output_data: Dict[str, Any],
                             processing_time: float,
                             success: bool = True,
                             error_message: Optional[str] = None) -> None:
        """
        记录智能体执行结果
        
        Args:
            date: 日期
            agent_id: 智能体ID
            input_data: 输入数据
            output_data: 输出数据
            processing_time: 处理时间
            success: 是否成功
            error_message: 错误信息
        """
        daily_state = self.daily_states.get(date)
        if not daily_state:
            self.logger.error(f"日期 {date} 的状态不存在，无法记录智能体执行")
            return
        
        daily_state.record_agent_execution(
            agent_id=agent_id,
            input_data=input_data,
            output_data=output_data,
            processing_time=processing_time,
            success=success,
            error_message=error_message
        )
        
        self.logger.debug(f"记录智能体执行: {date} - {agent_id}")
    
    def finalize_daily_state(self, date: str) -> DailyStateSnapshot:
        """
        完成日级状态并创建快照
        
        Args:
            date: 日期
            
        Returns:
            DailyStateSnapshot: 日级状态快照
        """
        daily_state = self.daily_states.get(date)
        if not daily_state:
            raise ValueError(f"日期 {date} 的状态不存在")
        
        # 创建快照
        snapshot = daily_state.create_daily_snapshot()
        
        # 保存到当前周状态列表
        self.current_week_states.append(snapshot)
        
        # 保存到磁盘
        self._save_daily_snapshot(snapshot)
        
        self.logger.info(f"完成日级状态: {date}")
        return snapshot
    
    def finalize_weekly_state(self, week_start_date: str) -> Dict[str, Any]:
        """
        完成周级状态并持久化存储
        
        Args:
            week_start_date: 周开始日期
            
        Returns:
            Dict: 周级状态摘要
        """
        if not self.current_week_states:
            self.logger.warning("当前周没有状态数据")
            return {}
        
        # 创建周级状态数据
        weekly_state = {
            "week_start_date": week_start_date,
            "week_end_date": self.current_week_states[-1].date,
            "daily_snapshots": [snapshot.to_dict() for snapshot in self.current_week_states],
            "weekly_summary": self._create_weekly_summary(),
            "opro_data": self._extract_opro_data(),
            "created_at": datetime.now().isoformat(),
            "total_trading_days": len(self.current_week_states)
        }
        
        # 保存周级状态
        self._save_weekly_state(week_start_date, weekly_state)
        
        # 清空当前周状态
        weekly_summary = weekly_state["weekly_summary"]
        self.current_week_states.clear()
        
        self.logger.info(f"完成周级状态: {week_start_date}")
        return weekly_summary
    
    def _create_weekly_summary(self) -> Dict[str, Any]:
        """创建周级摘要"""
        if not self.current_week_states:
            return {}
        
        total_executions = sum(len(snapshot.agent_executions) for snapshot in self.current_week_states)
        successful_executions = sum(
            len([exec_rec for exec_rec in snapshot.agent_executions if exec_rec.success])
            for snapshot in self.current_week_states
        )
        total_processing_time = sum(
            sum(exec_rec.processing_time for exec_rec in snapshot.agent_executions)
            for snapshot in self.current_week_states
        )
        
        return {
            "trading_days": len(self.current_week_states),
            "total_agent_executions": total_executions,
            "successful_executions": successful_executions,
            "success_rate": successful_executions / total_executions if total_executions > 0 else 0,
            "total_processing_time": total_processing_time,
            "average_daily_processing_time": total_processing_time / len(self.current_week_states),
            "agents_used": list(set(
                exec_rec.agent_id 
                for snapshot in self.current_week_states 
                for exec_rec in snapshot.agent_executions
            ))
        }
    
    def _extract_opro_data(self) -> Dict[str, Any]:
        """提取OPRO优化所需的数据"""
        opro_data = {
            "agent_io_history": {},
            "performance_data": {},
            "failure_cases": [],
            "success_cases": []
        }
        
        # 按智能体组织输入输出历史
        for snapshot in self.current_week_states:
            for exec_rec in snapshot.agent_executions:
                agent_id = exec_rec.agent_id
                
                if agent_id not in opro_data["agent_io_history"]:
                    opro_data["agent_io_history"][agent_id] = []
                
                io_record = {
                    "date": snapshot.date,
                    "input": exec_rec.input_data,
                    "output": exec_rec.output_data,
                    "processing_time": exec_rec.processing_time,
                    "success": exec_rec.success
                }
                
                opro_data["agent_io_history"][agent_id].append(io_record)
                
                # 收集失败和成功案例
                if exec_rec.success:
                    opro_data["success_cases"].append({
                        "agent_id": agent_id,
                        "date": snapshot.date,
                        "execution_record": exec_rec.to_dict()
                    })
                else:
                    opro_data["failure_cases"].append({
                        "agent_id": agent_id,
                        "date": snapshot.date,
                        "execution_record": exec_rec.to_dict()
                    })
        
        return opro_data
    
    def _save_daily_snapshot(self, snapshot: DailyStateSnapshot) -> None:
        """保存日级快照到磁盘"""
        filename = f"daily_state_{snapshot.date}.json"
        if self.compression:
            filename += ".gz"
        
        filepath = self.daily_storage_path / filename
        
        try:
            data = snapshot.to_dict()
            if self.compression:
                with gzip.open(filepath, 'wt', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
            else:
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(data, f, indent=2, ensure_ascii=False)
            
            self.logger.debug(f"保存日级快照: {filepath}")
        except Exception as e:
            self.logger.error(f"保存日级快照失败: {e}")
    
    def _save_weekly_state(self, week_start_date: str, weekly_state: Dict[str, Any]) -> None:
        """保存周级状态到磁盘"""
        filename = f"weekly_state_{week_start_date}.json"
        if self.compression:
            filename += ".gz"
        
        filepath = self.weekly_storage_path / filename
        
        try:
            if self.compression:
                with gzip.open(filepath, 'wt', encoding='utf-8') as f:
                    json.dump(weekly_state, f, indent=2, ensure_ascii=False)
            else:
                with open(filepath, 'w', encoding='utf-8') as f:
                    json.dump(weekly_state, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"保存周级状态: {filepath}")
        except Exception as e:
            self.logger.error(f"保存周级状态失败: {e}")
    
    def _archive_daily_state(self, date: str) -> None:
        """归档日级状态（从内存移除但保持磁盘文件）"""
        self.logger.debug(f"归档日级状态: {date}")
    
    def load_weekly_state(self, week_start_date: str) -> Optional[Dict[str, Any]]:
        """加载周级状态"""
        filename = f"weekly_state_{week_start_date}.json"
        if self.compression:
            filename += ".gz"
        
        filepath = self.weekly_storage_path / filename
        
        if not filepath.exists():
            return None
        
        try:
            if self.compression:
                with gzip.open(filepath, 'rt', encoding='utf-8') as f:
                    return json.load(f)
            else:
                with open(filepath, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.error(f"加载周级状态失败: {e}")
            return None
    
    def get_opro_training_data(self, agent_id: str, weeks: int = 4) -> Dict[str, Any]:
        """
        获取OPRO训练数据
        
        Args:
            agent_id: 智能体ID
            weeks: 获取最近几周的数据
            
        Returns:
            Dict: OPRO训练数据
        """
        training_data = {
            "agent_id": agent_id,
            "io_history": [],
            "failure_cases": [],
            "success_cases": [],
            "performance_trends": []
        }
        
        # 获取最近几周的文件
        weekly_files = sorted([
            f for f in self.weekly_storage_path.glob("weekly_state_*.json*")
        ], reverse=True)[:weeks]
        
        for filepath in weekly_files:
            weekly_state = self._load_file(filepath)
            if weekly_state and "opro_data" in weekly_state:
                opro_data = weekly_state["opro_data"]
                
                # 提取该智能体的数据
                if agent_id in opro_data.get("agent_io_history", {}):
                    training_data["io_history"].extend(
                        opro_data["agent_io_history"][agent_id]
                    )
                
                # 提取失败和成功案例
                for case in opro_data.get("failure_cases", []):
                    if case["agent_id"] == agent_id:
                        training_data["failure_cases"].append(case)
                
                for case in opro_data.get("success_cases", []):
                    if case["agent_id"] == agent_id:
                        training_data["success_cases"].append(case)
        
        return training_data
    
    def _load_file(self, filepath: Path) -> Optional[Dict[str, Any]]:
        """加载文件"""
        try:
            if filepath.suffix == '.gz':
                with gzip.open(filepath, 'rt', encoding='utf-8') as f:
                    return json.load(f)
            else:
                with open(filepath, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.error(f"加载文件失败 {filepath}: {e}")
            return None
