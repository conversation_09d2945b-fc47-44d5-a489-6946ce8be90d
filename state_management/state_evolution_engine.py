"""
状态演进引擎 (State Evolution Engine)

管理多智能体系统的严格执行顺序和状态演进流程，确保：
1. 分析层 → 展望层 → 交易层的严格执行顺序
2. 智能体输出正确传递到下一层级
3. 完整的状态演进追踪
4. 性能评估数据的实时计算和存储
5. 每日完整state的持久化和历史数据管理
"""

import time
import logging
import json
from typing import Dict, Any, List, Optional, Set, Tuple
from datetime import datetime

from .unified_state_interface import UnifiedStateInterface
from .logging_modes import LoggingMode, LoggingContext, PhaseDetector, get_global_logging_context
from .daily_state_manager import DailyStateManager


class StateEvolutionEngine:
    """
    状态演进引擎

    负责管理智能体的严格执行顺序和状态演进流程
    """

    def __init__(self, unified_state: UnifiedStateInterface, daily_state_manager: Optional[DailyStateManager] = None):
        """
        初始化状态演进引擎

        Args:
            unified_state: 统一状态接口实例
            daily_state_manager: 每日状态管理器实例，用于历史数据持久化
        """
        self.state = unified_state
        self.logger = logging.getLogger("StateEvolutionEngine")

        # 智能体执行层级定义
        self.execution_layers = [
            ["NAA", "TAA", "FAA"],  # 分析层：并行执行，数据隔离
            ["BOA", "BeOA", "NOA"], # 展望层：可以访问分析层输出
            ["TRA"]                 # 交易层：可以访问展望层输出
        ]

        # 执行统计
        self.execution_stats = {
            "total_agents_executed": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "execution_times": {},
            "layer_completion_times": {}
        }

        # 初始化日志上下文和阶段检测器
        self.logging_context = LoggingContext()
        self.phase_detector = PhaseDetector()

        # 当前运行的联盟信息（用于阶段检测）
        self.current_coalition = None

        # 每日状态管理器 - 用于历史数据持久化
        self.daily_state_manager = daily_state_manager or DailyStateManager()

        self.logger.info("状态演进引擎初始化完成 (with daily state persistence)")

    def _is_trading_day(self, date_str: str) -> bool:
        """
        检查指定日期是否为交易日

        Args:
            date_str: 日期字符串 (YYYY-MM-DD)

        Returns:
            bool: 如果是交易日返回True，否则返回False
        """
        try:
            # 尝试从交易日历服务获取交易日信息
            from contribution_assessment.services.trading_calendar_service import TradingCalendarService
            calendar_service = TradingCalendarService()
            return calendar_service.is_trading_day(date_str)
        except Exception as e:
            self.logger.warning(f"交易日历服务不可用，使用备用检测方法: {e}")

            # 备用方法：检查是否为周末
            try:
                from datetime import datetime
                date_obj = datetime.strptime(date_str, '%Y-%m-%d')
                # 周一到周五为工作日 (0-4)，周六周日为周末 (5-6)
                is_weekday = date_obj.weekday() < 5

                # 简单的节假日检测（可以扩展）
                known_holidays = {
                    "2025-01-01",  # 新年
                    "2025-01-20",  # 马丁·路德·金纪念日
                    "2025-02-17",  # 总统节
                    "2025-05-26",  # 阵亡将士纪念日
                    "2025-07-04",  # 独立日
                    "2025-09-01",  # 劳动节
                    "2025-11-27",  # 感恩节
                    "2025-12-25",  # 圣诞节
                }

                is_not_holiday = date_str not in known_holidays

                return is_weekday and is_not_holiday

            except Exception as backup_e:
                self.logger.error(f"备用交易日检测也失败: {backup_e}")
                # 最后的备用方案：假设所有日期都是交易日（保持原有行为）
                return True

    def execute_daily_cycle(self,
                          coalition: Set[str],
                          agents: Dict[str, Any],
                          logging_mode: Optional[LoggingMode] = None) -> Dict[str, Any]:
        """
        执行完整的日级智能体循环

        Args:
            coalition: 智能体联盟
            agents: 智能体实例字典
            logging_mode: 日志记录模式，如果为None则自动检测

        Returns:
            执行结果，包含交易动作和性能指标
        """
        cycle_start_time = time.time()
        current_date = self.state.state.get("current_date", "unknown")

        # 首先检查是否为交易日
        if not self._is_trading_day(current_date):
            self.logger.info(f"跳过非交易日: {current_date}")
            return {
                "trading_action": {"__HOLD__": 1.0},
                "performance_metrics": {},
                "execution_stats": self.execution_stats.copy(),
                "total_execution_time": time.time() - cycle_start_time,
                "success": True,
                "skipped": True,
                "skip_reason": "非交易日",
                "agent_outputs": {}
            }

        # 设置当前联盟和日志模式
        self.current_coalition = coalition
        if logging_mode is None:
            # 自动检测日志模式
            logging_mode = self.phase_detector.get_logging_mode_for_coalition(coalition)

        # 设置日志上下文
        self.logging_context.set_mode(logging_mode)

        # 根据日志模式调整日志输出
        if logging_mode == LoggingMode.DETAILED:
            self.logger.info(f"开始执行日级循环: {current_date}, 联盟: {coalition} [详细日志模式]")
        else:
            self.logger.info(f"开始执行日级循环: {current_date}, 联盟大小: {len(coalition)} [简洁日志模式]")

        try:
            # 按层级执行智能体
            for layer_idx, layer in enumerate(self.execution_layers):
                layer_start_time = time.time()
                layer_name = self._get_layer_name(layer_idx)

                # 过滤出当前层级中联盟包含的智能体
                layer_agents = [agent_id for agent_id in layer if agent_id in coalition and agent_id in agents]

                if not layer_agents:
                    self.logger.debug(f"跳过 {layer_name}：无可执行智能体")
                    continue

                self.logger.info(f"执行 {layer_name}: {layer_agents}")

                # 执行当前层级的智能体
                layer_success = self._execute_layer(layer_agents, agents, layer_name)

                # 记录层级完成时间
                layer_time = time.time() - layer_start_time
                self.execution_stats["layer_completion_times"][layer_name] = layer_time

                if not layer_success:
                    self.logger.error(f"{layer_name} 执行失败，停止后续层级执行")
                    break

                self.logger.info(f"{layer_name} 执行完成，耗时: {layer_time:.3f}秒")

            # 提取交易决策
            trading_action = self._extract_trading_action(coalition)

            # 计算性能指标（如果可能）
            performance_metrics = self._calculate_performance_metrics(trading_action)

            # 存储性能指标到状态
            if performance_metrics:
                self.state.store_performance_metrics(performance_metrics)

            # 总执行时间
            total_time = time.time() - cycle_start_time

            # 获取完整的当前状态
            complete_state = self.state.get_current_state()
            
            # 验证state完整性并添加agent输出字段
            complete_state = self._ensure_agent_outputs_completeness(complete_state)
            
            result = {
                "trading_action": trading_action,
                "performance_metrics": performance_metrics,
                "execution_stats": self.execution_stats.copy(),
                "total_execution_time": total_time,
                "success": True,
                "agent_outputs": {
                    "analyst_outputs": complete_state.get("analyst_outputs", {}),
                    "outlook_outputs": complete_state.get("outlook_outputs", {}),
                    "trading_outputs": complete_state.get("trading_outputs", {})
                }
            }

            # 持久化每日完整state (异步执行，不影响返回时间)
            self._persist_daily_state(current_date, complete_state)

            if logging_mode == LoggingMode.DETAILED:
                self.logger.info(f"日级循环执行完成: {current_date}, 总耗时: {total_time:.3f}秒")
            else:
                self.logger.debug(f"日级循环执行完成: {current_date}, 总耗时: {total_time:.3f}秒")

            # 根据日志模式决定是否打印详细状态信息
            # self._log_daily_state_summary(current_date, logging_mode)

            return result

        except Exception as e:
            self.logger.error(f"日级循环执行失败: {current_date}, 错误: {e}")
            return {
                "trading_action": {"__HOLD__": 1.0},
                "performance_metrics": {},
                "execution_stats": self.execution_stats.copy(),
                "total_execution_time": time.time() - cycle_start_time,
                "success": False,
                "error": str(e),
                "agent_outputs": {}
            }

    def _execute_layer(self,
                      layer_agents: List[str],
                      agents: Dict[str, Any],
                      layer_name: str) -> bool:
        """
        执行单个层级的智能体

        Args:
            layer_agents: 当前层级的智能体列表
            agents: 智能体实例字典
            layer_name: 层级名称

        Returns:
            是否执行成功
        """
        layer_success = True

        for agent_id in layer_agents:
            agent_start_time = time.time()

            try:
                self.logger.debug(f"执行智能体: {agent_id}")

                # 1. 获取智能体输入（应用严格的数据访问控制）
                agent_input = self.state.get_agent_input(agent_id)

                # 2. 执行智能体
                agent_output = agents[agent_id].process(agent_input)

                # 3. 存储智能体输出到状态
                self.state.store_agent_output(agent_id, agent_output)

                # 4. 记录执行统计
                execution_time = time.time() - agent_start_time
                self.execution_stats["execution_times"][agent_id] = execution_time
                self.execution_stats["successful_executions"] += 1
                self.execution_stats["total_agents_executed"] += 1

                self.logger.info(f"✅ {agent_id} 执行成功，耗时: {execution_time:.3f}秒")

            except Exception as e:
                execution_time = time.time() - agent_start_time
                self.execution_stats["execution_times"][agent_id] = execution_time
                self.execution_stats["failed_executions"] += 1
                self.execution_stats["total_agents_executed"] += 1

                self.logger.error(f"❌ {agent_id} 执行失败: {e}")
                layer_success = False

                # 对于分析层，单个智能体失败不影响其他智能体
                # 对于展望层和交易层，失败可能影响后续执行
                if layer_name != "分析层":
                    break

        return layer_success

    def _get_layer_name(self, layer_idx: int) -> str:
        """获取层级名称"""
        layer_names = ["分析层", "展望层", "交易层"]
        return layer_names[layer_idx] if layer_idx < len(layer_names) else f"层级{layer_idx}"

    def _extract_trading_action(self, coalition: Set[str]) -> Dict[str, float]:
        """
        从TRA智能体输出中提取交易动作

        Args:
            coalition: 智能体联盟

        Returns:
            交易动作字典
        """
        # 检查是否有TRA智能体且已执行
        if "TRA" not in coalition:
            self.logger.debug("联盟中无TRA智能体，返回持有动作")
            return {"__HOLD__": 1.0}

        trading_outputs = self.state.state.get("trading_outputs", {})
        if "TRA" not in trading_outputs:
            self.logger.warning("TRA智能体未执行或执行失败，返回持有动作")
            return {"__HOLD__": 1.0}

        tra_output = trading_outputs["TRA"]

        # 尝试从不同字段提取交易动作
        if isinstance(tra_output, dict):
            # 优先使用trading_actions字段
            if "trading_actions" in tra_output:
                actions = tra_output["trading_actions"]
                if isinstance(actions, dict) and actions:
                    self.logger.debug(f"从trading_actions提取交易动作: {actions}")
                    return actions

            # 尝试从action字段构建交易动作
            if "action" in tra_output:
                action = tra_output["action"]

                if action == "buy":
                    # 从状态中获取主要交易股票，支持多种字段名
                    symbols = (self.state.state.get("symbols") or
                              self.state.state.get("stocks") or
                              [self.state.state.get("symbol")] if self.state.state.get("symbol") else
                              ["AAPL"])  # 最后的默认值
                    main_symbol = symbols[0] if symbols and symbols[0] else "AAPL"
                    return {main_symbol: 1.0}  # 全仓买入
                elif action == "sell":
                    # 从状态中获取主要交易股票，支持多种字段名
                    symbols = (self.state.state.get("symbols") or
                              self.state.state.get("stocks") or
                              [self.state.state.get("symbol")] if self.state.state.get("symbol") else
                              ["AAPL"])  # 最后的默认值
                    main_symbol = symbols[0] if symbols and symbols[0] else "AAPL"
                    return {main_symbol: -1.0}  # 全仓卖出
                else:  # hold
                    return {"__HOLD__": 1.0}

        self.logger.warning("无法从TRA输出中提取有效交易动作，返回持有动作")
        return {"__HOLD__": 1.0}

    def _calculate_performance_metrics(self, trading_action: Dict[str, float]) -> Dict[str, Any]:
        """
        计算性能指标

        Args:
            trading_action: 交易动作

        Returns:
            性能指标字典
        """
        current_date = self.state.state.get("current_date", "unknown")

        # 基础性能指标
        metrics = {
            "date": current_date,
            "trading_action": trading_action.copy(),
            "execution_summary": self.state.get_execution_summary(),
            "calculation_timestamp": datetime.now().isoformat()
        }

        # 如果有价格数据，尝试计算更详细的指标
        price_history = self.state.state.get("price_history", {})
        if price_history:
            metrics["price_based_metrics"] = self._calculate_price_based_metrics(
                trading_action, price_history
            )

        # 添加智能体协调指标
        metrics["coordination_metrics"] = self._calculate_coordination_metrics()

        return metrics

    def _calculate_price_based_metrics(self,
                                     trading_action: Dict[str, float],
                                     price_history: Dict[str, Any]) -> Dict[str, Any]:
        """计算基于价格的指标"""
        metrics = {}

        for symbol, action_value in trading_action.items():
            if symbol == "__HOLD__":
                continue

            if symbol in price_history:
                prices = price_history[symbol]
                if isinstance(prices, list) and len(prices) > 0:
                    latest_price = prices[-1]
                    if isinstance(latest_price, dict):
                        current_price = latest_price.get("close", latest_price.get("price", 0))
                        metrics[f"{symbol}_current_price"] = current_price
                        metrics[f"{symbol}_action_value"] = action_value
                        metrics[f"{symbol}_exposure"] = abs(action_value) * current_price

        return metrics

    def _calculate_coordination_metrics(self) -> Dict[str, Any]:
        """计算智能体协调指标"""
        analyst_outputs = self.state.state.get("analyst_outputs", {})
        outlook_outputs = self.state.state.get("outlook_outputs", {})

        metrics = {
            "analyst_consensus": self._calculate_consensus(analyst_outputs),
            "outlook_consensus": self._calculate_consensus(outlook_outputs),
            "layer_completion": {
                "analyst_layer": len(analyst_outputs),
                "outlook_layer": len(outlook_outputs),
                "trading_layer": len(self.state.state.get("trading_outputs", {}))
            }
        }

        return metrics

    def _calculate_consensus(self, agent_outputs: Dict[str, Any]) -> Dict[str, Any]:
        """计算智能体间的共识度"""
        if not agent_outputs:
            return {"consensus_score": 0.0, "agent_count": 0}

        # 提取信心度分数
        confidence_scores = []
        for agent_id, output in agent_outputs.items():
            if isinstance(output, dict) and "confidence" in output:
                confidence = output["confidence"]
                if isinstance(confidence, (int, float)):
                    confidence_scores.append(confidence)

        if not confidence_scores:
            return {"consensus_score": 0.0, "agent_count": len(agent_outputs)}

        # 计算平均信心度作为共识指标
        avg_confidence = sum(confidence_scores) / len(confidence_scores)

        return {
            "consensus_score": avg_confidence,
            "agent_count": len(agent_outputs),
            "confidence_scores": confidence_scores,
            "confidence_std": self._calculate_std(confidence_scores) if len(confidence_scores) > 1 else 0.0
        }

    def _calculate_std(self, values: List[float]) -> float:
        """计算标准差"""
        if len(values) <= 1:
            return 0.0

        mean = sum(values) / len(values)
        variance = sum((x - mean) ** 2 for x in values) / len(values)
        return variance ** 0.5

    def set_logging_mode(self, mode: LoggingMode) -> None:
        """
        设置日志记录模式

        Args:
            mode: 新的日志记录模式
        """
        self.logging_context.set_mode(mode)
        self.logger.info(f"日志模式已切换为: {mode.value}")

    def get_current_logging_mode(self) -> LoggingMode:
        """获取当前日志记录模式"""
        return self.logging_context.current_mode

    def is_detailed_logging_enabled(self) -> bool:
        """检查是否启用详细日志记录"""
        return self.logging_context.is_detailed_mode()

    def detect_phase_for_coalition(self, coalition: Set[str]) -> tuple[str, LoggingMode]:
        """
        为指定联盟检测运行阶段

        Args:
            coalition: 智能体联盟

        Returns:
            tuple: (阶段名称, 建议的日志模式)
        """
        return self.phase_detector.detect_phase(coalition)

    def _log_daily_state_summary(self, current_date: str, logging_mode: Optional[LoggingMode] = None) -> None:
        """
        根据日志模式打印每日状态数据

        Args:
            current_date: 当前日期
            logging_mode: 日志记录模式，如果为None则使用当前上下文模式
        """
        # 确定日志模式
        if logging_mode is None:
            logging_mode = self.logging_context.current_mode

        # 简洁模式：完全跳过详细状态日志记录
        if logging_mode == LoggingMode.CONCISE:
            self.logger.debug(f"📊 日期 {current_date} 状态更新完成 [简洁模式]")
            return

        # 详细模式：显示完整的原始状态数据
        self.logger.info("=" * 120)
        self.logger.info(f"📊 日期 {current_date} 完整原始状态数据")
        self.logger.info("=" * 120)
        
        # 获取完整状态
        current_state = self.state.get_current_state()
        
        # 1. 完整分析层智能体原始输出
        analyst_outputs = current_state.get("analyst_outputs", {})
        self.logger.info(f"🔍 分析层智能体原始输出 ({len(analyst_outputs)} 个):")
        for agent_id, output in analyst_outputs.items():
            self.logger.info(f"  ┌─ {agent_id} 完整输出:")
            try:
                output_json = json.dumps(output, indent=4, ensure_ascii=False, default=str)
                for line in output_json.split('\n'):
                    self.logger.info(f"  │ {line}")
            except Exception as e:
                self.logger.info(f"  │ {str(output)}")
            self.logger.info(f"  └─ {agent_id} 输出结束")
        
        # 2. 完整展望层智能体原始输出
        outlook_outputs = current_state.get("outlook_outputs", {})
        self.logger.info(f"🔮 展望层智能体原始输出 ({len(outlook_outputs)} 个):")
        for agent_id, output in outlook_outputs.items():
            self.logger.info(f"  ┌─ {agent_id} 完整输出:")
            try:
                output_json = json.dumps(output, indent=4, ensure_ascii=False, default=str)
                for line in output_json.split('\n'):
                    self.logger.info(f"  │ {line}")
            except Exception as e:
                self.logger.info(f"  │ {str(output)}")
            self.logger.info(f"  └─ {agent_id} 输出结束")
        
        # 3. 完整交易层智能体原始输出
        trading_outputs = current_state.get("trading_outputs", {})
        self.logger.info(f"💼 交易层智能体原始输出 ({len(trading_outputs)} 个):")
        for agent_id, output in trading_outputs.items():
            self.logger.info(f"  ┌─ {agent_id} 完整输出:")
            try:
                output_json = json.dumps(output, indent=4, ensure_ascii=False, default=str)
                for line in output_json.split('\n'):
                    self.logger.info(f"  │ {line}")
            except Exception as e:
                self.logger.info(f"  │ {str(output)}")
            self.logger.info(f"  └─ {agent_id} 输出结束")
        
        # 4. 完整市场原始数据
        self.logger.info(f"📈 完整市场原始数据:")
        market_data_keys = ["price_history", "current_price", "volume", "fundamental_data"]
        for key in market_data_keys:
            if key in current_state:
                self.logger.info(f"  ┌─ {key}:")
                try:
                    data_json = json.dumps(current_state[key], indent=4, ensure_ascii=False, default=str)
                    for line in data_json.split('\n'):
                        self.logger.info(f"  │ {line}")
                except Exception as e:
                    self.logger.info(f"  │ {str(current_state[key])}")
                self.logger.info(f"  └─ {key} 数据结束")
        
        # 5. 完整新闻原始数据
        news_history = current_state.get("news_history", {})
        self.logger.info(f"📰 完整新闻原始数据:")
        self.logger.info(f"  ┌─ news_history:")
        try:
            news_json = json.dumps(news_history, indent=4, ensure_ascii=False, default=str)
            for line in news_json.split('\n'):
                self.logger.info(f"  │ {line}")
        except Exception as e:
            self.logger.info(f"  │ {str(news_history)}")
        self.logger.info(f"  └─ news_history 数据结束")
        
        # 6. 完整性能指标原始数据
        performance_metrics = current_state.get("performance_metrics", {})
        self.logger.info(f"📊 完整性能指标原始数据:")
        self.logger.info(f"  ┌─ performance_metrics:")
        try:
            metrics_json = json.dumps(performance_metrics, indent=4, ensure_ascii=False, default=str)
            for line in metrics_json.split('\n'):
                self.logger.info(f"  │ {line}")
        except Exception as e:
            self.logger.info(f"  │ {str(performance_metrics)}")
        self.logger.info(f"  └─ performance_metrics 数据结束")
        
        # 7. 完整交易状态原始数据
        trading_data_keys = ["cash", "positions", "position_values", "symbols"]
        self.logger.info(f"💰 完整交易状态原始数据:")
        for key in trading_data_keys:
            if key in current_state:
                self.logger.info(f"  ┌─ {key}:")
                try:
                    data_json = json.dumps(current_state[key], indent=4, ensure_ascii=False, default=str)
                    for line in data_json.split('\n'):
                        self.logger.info(f"  │ {line}")
                except Exception as e:
                    self.logger.info(f"  │ {str(current_state[key])}")
                self.logger.info(f"  └─ {key} 数据结束")
        
        # 8. 完整状态字典键值对
        self.logger.info(f"🔑 完整状态字典所有键:")
        state_keys = list(current_state.keys())
        self.logger.info(f"  可用键: {state_keys}")
        
        # 9. 其他所有原始数据
        other_keys = [k for k in current_state.keys() if k not in [
            "analyst_outputs", "outlook_outputs", "trading_outputs", 
            "price_history", "current_price", "volume", "fundamental_data",
            "news_history", "performance_metrics", "cash", "positions", 
            "position_values", "symbols"
        ]]
        
        if other_keys:
            self.logger.info(f"🗂️ 其他原始数据:")
            for key in other_keys:
                self.logger.info(f"  ┌─ {key}:")
                try:
                    data_json = json.dumps(current_state[key], indent=4, ensure_ascii=False, default=str)
                    for line in data_json.split('\n'):
                        self.logger.info(f"  │ {line}")
                except Exception as e:
                    self.logger.info(f"  │ {str(current_state[key])}")
                self.logger.info(f"  └─ {key} 数据结束")
        
        self.logger.info("=" * 120)
    
    def _ensure_agent_outputs_completeness(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        确保state包含完整的agent输出字段，符合Framework.md规范
        
        Args:
            state: 当前状态字典
            
        Returns:
            Dict[str, Any]: 包含完整agent输出字段的状态字典
        """
        # 确保存在所有必需的agent输出字段
        if "analyst_outputs" not in state:
            state["analyst_outputs"] = {}
        if "outlook_outputs" not in state:
            state["outlook_outputs"] = {}
        if "trading_outputs" not in state:
            state["trading_outputs"] = {}
        
        # 验证并记录state完整性
        analyst_count = len(state["analyst_outputs"])
        outlook_count = len(state["outlook_outputs"])
        trading_count = len(state["trading_outputs"])
        
        total_agents = analyst_count + outlook_count + trading_count
        expected_agents = len(self.current_coalition) if self.current_coalition else 0
        
        if total_agents < expected_agents:
            self.logger.warning(
                f"State completeness check: found {total_agents} agent outputs, "
                f"expected {expected_agents} from coalition {self.current_coalition}"
            )
        else:
            self.logger.debug(
                f"State completeness verified: {analyst_count} analyst, "
                f"{outlook_count} outlook, {trading_count} trading agents"
            )
        
        return state
    
    def _persist_daily_state(self, date: str, complete_state: Dict[str, Any]) -> None:
        """
        持久化每日完整state到数据库
        
        Args:
            date: 日期字符串
            complete_state: 完整的状态字典
        """
        try:
            # 异步保存，不阻塞主流程
            success = self.daily_state_manager.save_daily_state(date, complete_state)
            
            if success:
                # 计算统计信息用于日志
                analyst_count = len(complete_state.get("analyst_outputs", {}))
                outlook_count = len(complete_state.get("outlook_outputs", {}))
                trading_count = len(complete_state.get("trading_outputs", {}))
                previous_day_return = complete_state.get("previous_day_return", "N/A")
                
                self.logger.info(
                    f"Daily state persisted successfully: {date} "
                    f"(A:{analyst_count}, O:{outlook_count}, T:{trading_count}, "
                    f"Return:{previous_day_return})"
                )
            else:
                self.logger.error(f"Failed to persist daily state for {date}")
                
        except Exception as e:
            # 持久化失败不应影响主流程
            self.logger.error(f"Exception during daily state persistence for {date}: {str(e)}")
    
    def get_daily_state_manager(self) -> DailyStateManager:
        """
        获取每日状态管理器实例
        
        Returns:
            DailyStateManager: 每日状态管理器实例
        """
        return self.daily_state_manager