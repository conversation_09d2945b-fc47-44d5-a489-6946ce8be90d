"""
日志模式定义

定义多智能体系统中不同运行阶段的日志记录模式，
支持根据运行阶段动态调整日志详细程度。
"""

from enum import Enum
from typing import Dict, Any, Optional


class LoggingMode(Enum):
    """
    日志记录模式枚举
    
    定义系统在不同运行阶段的日志记录详细程度：
    - DETAILED: 详细日志模式，用于阶段一（完整联盟运行）
    - CONCISE: 简洁日志模式，用于阶段二（子集联盟运行）
    """
    DETAILED = "detailed"  # 详细日志：完整的智能体输出、状态数据等
    CONCISE = "concise"    # 简洁日志：仅基本执行信息，无详细状态数据


class LoggingContext:
    """
    日志上下文管理器
    
    管理当前的日志记录模式和相关配置，
    提供线程安全的日志模式切换功能。
    """
    
    def __init__(self, initial_mode: LoggingMode = LoggingMode.DETAILED):
        """
        初始化日志上下文
        
        Args:
            initial_mode: 初始日志模式，默认为详细模式
        """
        self._current_mode = initial_mode
        self._mode_stack = []  # 用于支持嵌套的模式切换
        
    @property
    def current_mode(self) -> LoggingMode:
        """获取当前日志模式"""
        return self._current_mode
    
    def set_mode(self, mode: LoggingMode) -> None:
        """
        设置日志模式
        
        Args:
            mode: 新的日志模式
        """
        self._current_mode = mode
    
    def push_mode(self, mode: LoggingMode) -> None:
        """
        推入新的日志模式（支持嵌套）
        
        Args:
            mode: 新的日志模式
        """
        self._mode_stack.append(self._current_mode)
        self._current_mode = mode
    
    def pop_mode(self) -> LoggingMode:
        """
        弹出当前日志模式，恢复到之前的模式
        
        Returns:
            恢复的日志模式
        """
        if self._mode_stack:
            self._current_mode = self._mode_stack.pop()
        return self._current_mode
    
    def is_detailed_mode(self) -> bool:
        """检查是否为详细日志模式"""
        return self._current_mode == LoggingMode.DETAILED
    
    def is_concise_mode(self) -> bool:
        """检查是否为简洁日志模式"""
        return self._current_mode == LoggingMode.CONCISE


class PhaseDetector:
    """
    阶段检测器
    
    负责检测当前运行是完整联盟（阶段一）还是子集联盟（阶段二），
    并提供相应的日志模式建议。
    """
    
    def __init__(self, all_agents: Optional[set] = None):
        """
        初始化阶段检测器
        
        Args:
            all_agents: 系统中所有智能体的集合
        """
        self.all_agents = all_agents or {"NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"}
    
    def detect_phase(self, coalition: set) -> tuple[str, LoggingMode]:
        """
        检测当前联盟所属的运行阶段
        
        Args:
            coalition: 当前运行的智能体联盟
            
        Returns:
            tuple: (阶段名称, 建议的日志模式)
        """
        if self.is_full_coalition(coalition):
            return ("阶段一（完整联盟）", LoggingMode.DETAILED)
        else:
            return ("阶段二（子集联盟）", LoggingMode.CONCISE)
    
    def is_full_coalition(self, coalition: set) -> bool:
        """
        检查是否为完整联盟
        
        Args:
            coalition: 智能体联盟
            
        Returns:
            是否为完整联盟
        """
        return len(coalition) == len(self.all_agents) and coalition.issubset(self.all_agents)
    
    def get_logging_mode_for_coalition(self, coalition: set) -> LoggingMode:
        """
        为指定联盟获取建议的日志模式
        
        Args:
            coalition: 智能体联盟
            
        Returns:
            建议的日志模式
        """
        _, mode = self.detect_phase(coalition)
        return mode


# 全局日志上下文实例
_global_logging_context = LoggingContext()


def get_global_logging_context() -> LoggingContext:
    """获取全局日志上下文实例"""
    return _global_logging_context


def set_global_logging_mode(mode: LoggingMode) -> None:
    """设置全局日志模式"""
    _global_logging_context.set_mode(mode)


def get_current_logging_mode() -> LoggingMode:
    """获取当前全局日志模式"""
    return _global_logging_context.current_mode


def is_detailed_logging_enabled() -> bool:
    """检查是否启用详细日志记录"""
    return _global_logging_context.is_detailed_mode()
