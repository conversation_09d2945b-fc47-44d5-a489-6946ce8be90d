"""
智能体状态管理器 (Agent State Manager)

负责智能体状态的跨周保存和恢复，支持：
- 智能体工作状态的持久化
- OPRO优化状态的保存和恢复
- 状态版本管理和验证
- 与现有投资组合状态管理协同工作

设计原则:
- 高内聚: 专注智能体状态管理的单一职责
- 低耦合: 通过标准接口与其他组件交互
- 容错性: 状态损坏时优雅降级到默认行为
"""

import json
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, Any, Optional, List, Type
from dataclasses import dataclass


@dataclass
class AgentStateSnapshot:
    """智能体状态快照数据类"""
    agent_id: str
    agent_class: str
    snapshot_data: Dict[str, Any]
    creation_timestamp: str
    week_number: int


class AgentStateManager:
    """智能体状态管理器"""
    
    def __init__(self, 
                 state_dir: str = "data/agent_states",
                 logger: Optional[logging.Logger] = None):
        """
        初始化智能体状态管理器
        
        Args:
            state_dir: 状态文件存储目录
            logger: 日志记录器
        """
        self.state_dir = Path(state_dir)
        self.logger = logger or logging.getLogger(__name__)
        
        # 确保状态目录存在
        self.state_dir.mkdir(parents=True, exist_ok=True)
        
        # 状态管理配置
        self.max_versions_per_agent = 10  # 保留的历史版本数
        self.state_file_prefix = "agent_states_week_"
        
        self.logger.info(f"📁 智能体状态管理器初始化完成，状态目录: {self.state_dir}")
    
    def save_agents_state(self, 
                         agents: Dict[str, Any], 
                         week_number: int,
                         is_full_coalition: bool = False) -> bool:
        """
        保存所有智能体的状态
        
        Args:
            agents: 智能体字典 {agent_id: agent_instance}
            week_number: 周数
            is_full_coalition: 是否为完整联盟的状态，只有True时才保存
            
        Returns:
            是否保存成功
        """
        try:
            # 检查是否为完整联盟状态
            if not is_full_coalition:
                self.logger.info(f"🚫 跳过保存第{week_number}周智能体状态：非完整联盟")
                self.logger.info("💡 只有完整联盟的智能体状态才会被保存以供跨周传递")
                return False
            
            # 创建状态快照
            snapshots = {}
            successful_saves = 0
            total_agents = len(agents)
            
            for agent_id, agent_instance in agents.items():
                try:
                    # 创建智能体快照
                    if hasattr(agent_instance, 'create_serializable_snapshot'):
                        snapshot_data = agent_instance.create_serializable_snapshot()
                        
                        # 创建快照对象
                        snapshot = AgentStateSnapshot(
                            agent_id=agent_id,
                            agent_class=f"{agent_instance.__class__.__module__}.{agent_instance.__class__.__name__}",
                            snapshot_data=snapshot_data,
                            creation_timestamp=datetime.now().isoformat(),
                            week_number=week_number
                        )
                        
                        snapshots[agent_id] = {
                            "agent_id": snapshot.agent_id,
                            "agent_class": snapshot.agent_class,
                            "snapshot_data": snapshot.snapshot_data,
                            "creation_timestamp": snapshot.creation_timestamp,
                            "week_number": snapshot.week_number
                        }
                        
                        successful_saves += 1
                        self.logger.debug(f"  ✅ 智能体 {agent_id} 状态快照创建成功")
                        
                    else:
                        self.logger.warning(f"  ⚠️ 智能体 {agent_id} 不支持状态快照功能")
                        
                except Exception as e:
                    self.logger.error(f"  ❌ 智能体 {agent_id} 状态快照创建失败: {e}")
                    continue
            
            if successful_saves == 0:
                self.logger.error("❌ 没有智能体状态被成功创建快照")
                return False
            
            # 保存到文件
            state_file = self.state_dir / f"{self.state_file_prefix}{week_number}.json"
            
            # 添加元数据
            state_data = {
                "metadata": {
                    "week_number": week_number,
                    "save_timestamp": datetime.now().isoformat(),
                    "total_agents": total_agents,
                    "successful_saves": successful_saves,
                    "is_full_coalition": is_full_coalition,
                    "version": "1.0.0"
                },
                "agent_snapshots": snapshots
            }
            
            # 写入文件
            with open(state_file, 'w', encoding='utf-8') as f:
                json.dump(state_data, f, indent=2, ensure_ascii=False)
            
            # 清理旧版本
            self._cleanup_old_versions(week_number)
            
            self.logger.info(f"✅ 第{week_number}周智能体状态保存成功 (完整联盟):")
            self.logger.info(f"   - 保存路径: {state_file}")
            self.logger.info(f"   - 成功保存: {successful_saves}/{total_agents} 个智能体")
            self.logger.info(f"   - 联盟类型: {'完整联盟' if is_full_coalition else '子集联盟'}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ 保存第{week_number}周智能体状态失败: {e}")
            return False
    
    def restore_agents_state(self, 
                           week_number: int,
                           agent_creation_callback=None) -> Optional[Dict[str, Any]]:
        """
        恢复指定周数的智能体状态
        
        Args:
            week_number: 要恢复的周数
            agent_creation_callback: 智能体创建回调函数
            
        Returns:
            恢复的智能体字典，失败返回None
        """
        try:
            # 查找状态文件
            state_file = self.state_dir / f"{self.state_file_prefix}{week_number}.json"
            
            if not state_file.exists():
                self.logger.warning(f"⚠️ 第{week_number}周的智能体状态文件不存在: {state_file}")
                return None
            
            # 读取状态数据
            with open(state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
            
            # 验证数据格式
            if not self._validate_state_data(state_data):
                self.logger.error(f"❌ 第{week_number}周智能体状态数据格式无效")
                return None
            
            # 检查是否为完整联盟状态
            metadata = state_data["metadata"]
            is_full_coalition = metadata.get("is_full_coalition", False)
            
            if not is_full_coalition:
                self.logger.warning(f"🚫 第{week_number}周智能体状态不是完整联盟状态，跳过恢复")
                self.logger.info("💡 只有完整联盟的智能体状态才会被恢复用于跨周传递")
                return None
            
            agent_snapshots = state_data["agent_snapshots"]
            
            self.logger.info(f"🔄 开始恢复第{week_number}周智能体状态 (完整联盟):")
            self.logger.info(f"   - 保存时间: {metadata.get('save_timestamp', '未知')}")
            self.logger.info(f"   - 智能体数量: {len(agent_snapshots)}")
            self.logger.info(f"   - 联盟类型: 完整联盟 ✓")
            
            # 恢复智能体
            restored_agents = {}
            successful_restores = 0
            
            for agent_id, snapshot_info in agent_snapshots.items():
                try:
                    # 获取智能体类
                    agent_class_path = snapshot_info["agent_class"]
                    agent_class = self._import_agent_class(agent_class_path)
                    
                    if not agent_class:
                        self.logger.error(f"  ❌ 无法导入智能体类: {agent_class_path}")
                        continue
                    
                    # 恢复智能体实例
                    snapshot_data = snapshot_info["snapshot_data"]
                    
                    # 使用agent_creation_callback提供LLM接口和logger
                    llm_interface = None
                    logger = self.logger
                    
                    if agent_creation_callback:
                        creation_params = agent_creation_callback(agent_id)
                        llm_interface = creation_params.get("llm_interface")
                        logger = creation_params.get("logger", self.logger)
                    
                    # 恢复智能体实例
                    if hasattr(agent_class, 'restore_from_snapshot'):
                        restored_agent = agent_class.restore_from_snapshot(
                            snapshot=snapshot_data,
                            llm_interface=llm_interface,
                            logger=logger
                        )
                        
                        restored_agents[agent_id] = restored_agent
                        successful_restores += 1
                        self.logger.debug(f"  ✅ 智能体 {agent_id} 状态恢复成功")
                        
                    else:
                        self.logger.error(f"  ❌ 智能体类 {agent_class_path} 不支持状态恢复")
                        
                except Exception as e:
                    self.logger.error(f"  ❌ 智能体 {agent_id} 状态恢复失败: {e}")
                    continue
            
            if successful_restores == 0:
                self.logger.error("❌ 没有智能体状态被成功恢复")
                return None
            
            self.logger.info(f"✅ 第{week_number}周智能体状态恢复完成 (完整联盟):")
            self.logger.info(f"   - 成功恢复: {successful_restores}/{len(agent_snapshots)} 个智能体")
            self.logger.info(f"   - 状态来源: 完整联盟 ✓")
            
            return restored_agents
            
        except Exception as e:
            self.logger.error(f"❌ 恢复第{week_number}周智能体状态失败: {e}")
            return None
    
    def has_state_for_week(self, week_number: int) -> bool:
        """
        检查是否存在指定周数的状态文件
        
        Args:
            week_number: 周数
            
        Returns:
            是否存在状态文件
        """
        state_file = self.state_dir / f"{self.state_file_prefix}{week_number}.json"
        return state_file.exists()
    
    def get_available_weeks(self) -> List[int]:
        """
        获取所有可用的状态周数
        
        Returns:
            可用周数列表，按降序排列
        """
        available_weeks = []
        
        for state_file in self.state_dir.glob(f"{self.state_file_prefix}*.json"):
            try:
                # 从文件名提取周数
                week_str = state_file.stem.replace(self.state_file_prefix, "")
                week_number = int(week_str)
                available_weeks.append(week_number)
            except ValueError:
                continue
        
        return sorted(available_weeks, reverse=True)
    
    def _validate_state_data(self, state_data: Dict[str, Any]) -> bool:
        """
        验证状态数据格式
        
        Args:
            state_data: 状态数据
            
        Returns:
            是否格式正确
        """
        try:
            # 检查必需字段
            if "metadata" not in state_data or "agent_snapshots" not in state_data:
                return False
            
            metadata = state_data["metadata"]
            required_metadata = ["week_number", "save_timestamp", "total_agents", "successful_saves"]
            
            for field in required_metadata:
                if field not in metadata:
                    return False
            
            # 检查快照数据
            agent_snapshots = state_data["agent_snapshots"]
            if not isinstance(agent_snapshots, dict):
                return False
            
            for agent_id, snapshot_info in agent_snapshots.items():
                required_fields = ["agent_id", "agent_class", "snapshot_data", "creation_timestamp"]
                for field in required_fields:
                    if field not in snapshot_info:
                        return False
            
            return True
            
        except Exception:
            return False
    
    def _import_agent_class(self, class_path: str) -> Optional[Type]:
        """
        动态导入智能体类
        
        Args:
            class_path: 类路径，格式为 "module.class"
            
        Returns:
            智能体类，失败返回None
        """
        try:
            # 分离模块和类名
            module_path, class_name = class_path.rsplit('.', 1)
            
            # 动态导入模块
            import importlib
            module = importlib.import_module(module_path)
            
            # 获取类
            agent_class = getattr(module, class_name)
            
            return agent_class
            
        except Exception as e:
            self.logger.error(f"导入智能体类失败 {class_path}: {e}")
            return None
    
    def _cleanup_old_versions(self, current_week: int) -> None:
        """
        清理旧版本的状态文件
        
        Args:
            current_week: 当前周数
        """
        try:
            available_weeks = self.get_available_weeks()
            
            # 保留最近的版本
            weeks_to_keep = available_weeks[:self.max_versions_per_agent]
            weeks_to_remove = [w for w in available_weeks if w not in weeks_to_keep and w < current_week]
            
            for week_to_remove in weeks_to_remove:
                state_file = self.state_dir / f"{self.state_file_prefix}{week_to_remove}.json"
                if state_file.exists():
                    state_file.unlink()
                    self.logger.debug(f"🗑️ 清理旧状态文件: 第{week_to_remove}周")
            
            if weeks_to_remove:
                self.logger.info(f"🗑️ 清理了 {len(weeks_to_remove)} 个旧状态文件")
                
        except Exception as e:
            self.logger.warning(f"⚠️ 清理旧状态文件失败: {e}")
    
    def get_state_info(self, week_number: int) -> Optional[Dict[str, Any]]:
        """
        获取指定周数的状态信息
        
        Args:
            week_number: 周数
            
        Returns:
            状态信息字典，失败返回None
        """
        try:
            state_file = self.state_dir / f"{self.state_file_prefix}{week_number}.json"
            
            if not state_file.exists():
                return None
            
            with open(state_file, 'r', encoding='utf-8') as f:
                state_data = json.load(f)
            
            if not self._validate_state_data(state_data):
                return None
            
            metadata = state_data["metadata"]
            agent_snapshots = state_data["agent_snapshots"]
            
            # 构建状态信息
            agent_info = {}
            for agent_id, snapshot_info in agent_snapshots.items():
                snapshot_data = snapshot_info["snapshot_data"]
                agent_info[agent_id] = {
                    "agent_class": snapshot_info["agent_class"],
                    "analysis_count": snapshot_data.get("analysis_count", 0),
                    "opro_enabled": snapshot_data.get("opro_enabled", False),
                    "prompt_version": snapshot_data.get("prompt_version", "unknown"),
                    "optimization_count": snapshot_data.get("opro_stats", {}).get("total_optimizations", 0)
                }
            
            return {
                "metadata": metadata,
                "agent_info": agent_info,
                "file_path": str(state_file),
                "file_size": state_file.stat().st_size
            }
            
        except Exception as e:
            self.logger.error(f"获取第{week_number}周状态信息失败: {e}")
            return None