#!/usr/bin/env python3
"""
收益率曲线分析模块 (Returns Curve Analyzer)

本模块提供收益率曲线分析和可视化功能，用于分析多智能体交易系统的投资表现。
主要功能包括：
1. 收益率曲线计算和累积收益分析
2. 多智能体/联盟收益率对比
3. 风险调整后收益指标计算
4. 可视化图表生成
5. 详细的统计报告

使用方法：
    analyzer = ReturnsAnalyzer()
    analyzer.analyze_coalition_returns(coalition_results)
    analyzer.generate_performance_report()
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import logging
import json
import os
from pathlib import Path

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
plt.style.use('seaborn-v0_8')


class ReturnsAnalyzer:
    """
    收益率曲线分析器
    
    提供全面的收益率分析功能，包括累积收益、风险指标、
    对比分析和可视化图表生成。
    """
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        """
        初始化收益率分析器
        
        参数:
            logger: 日志记录器
        """
        self.logger = logger or self._create_default_logger()
        
        # 分析结果存储
        self.coalition_results = {}
        self.performance_metrics = {}
        self.benchmark_data = {}
        
        # 风险参数
        self.risk_free_rate = 0.02  # 2%年化无风险收益率
        self.trading_days_per_year = 252
        
        # 输出路径
        self.output_dir = Path("results/returns_analysis")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        self.logger.info("收益率分析器初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"{__name__}.ReturnsAnalyzer")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def analyze_coalition_returns(self, 
                                coalition_results: Dict[str, Dict[str, Any]],
                                shapley_values: Optional[Dict[str, float]] = None) -> Dict[str, Any]:
        """
        分析联盟收益率数据
        
        参数:
            coalition_results: 联盟结果字典，格式为 {coalition_id: simulation_result}
            shapley_values: Shapley值字典，用于标注各智能体贡献
            
        返回:
            分析结果字典
        """
        self.logger.info(f"开始分析 {len(coalition_results)} 个联盟的收益率数据")
        
        self.coalition_results = coalition_results
        analysis_results = {}
        
        for i, (coalition_id, result) in enumerate(coalition_results.items(), 1):
            # 只在特定间隔显示进度，避免日志过多
            if i % 10 == 0 or i == len(coalition_results):
                self.logger.info(f"分析进度: {i}/{len(coalition_results)} 联盟")
            
            # 提取收益率数据
            daily_returns = result.get("daily_returns", [])
            if not daily_returns:
                self.logger.warning(f"联盟 {coalition_id} 没有收益率数据")
                continue
            
            # 计算收益率曲线
            coalition_analysis = self._analyze_single_coalition(
                coalition_id, daily_returns, result
            )
            
            # 如果有Shapley值，添加贡献度信息
            if shapley_values:
                coalition_analysis["shapley_contribution"] = self._calculate_coalition_contribution(
                    coalition_id, shapley_values
                )
            
            analysis_results[coalition_id] = coalition_analysis
        
        # 计算整体统计和排名
        analysis_results["overall_statistics"] = self._calculate_overall_statistics(analysis_results)
        
        # 存储分析结果
        self.performance_metrics = analysis_results
        
        self.logger.info("联盟收益率分析完成")
        return analysis_results
    
    def _analyze_single_coalition(self, 
                                coalition_id: str, 
                                daily_returns: List[float],
                                simulation_result: Dict[str, Any]) -> Dict[str, Any]:
        """
        分析单个联盟的收益率表现
        
        参数:
            coalition_id: 联盟ID
            daily_returns: 每日收益率列表
            simulation_result: 模拟结果
            
        返回:
            联盟分析结果
        """
        returns_array = np.array(daily_returns)
        
        # 1. 基本统计指标
        basic_stats = self._calculate_basic_statistics(returns_array)
        
        # 2. 收益率曲线
        cumulative_returns = self._calculate_cumulative_returns(returns_array)
        
        # 3. 风险指标
        risk_metrics = self._calculate_risk_metrics(returns_array)
        
        # 4. 时间序列分析
        time_series_analysis = self._analyze_time_series(returns_array)
        
        # 5. 回撤分析
        drawdown_analysis = self._calculate_drawdown_analysis(cumulative_returns)
        
        # 6. 周期性分析
        if len(daily_returns) >= 20:  # 至少4周数据
            periodic_analysis = self._analyze_periodic_performance(returns_array)
        else:
            periodic_analysis = {}
        
        return {
            "coalition_id": coalition_id,
            "coalition_members": self._parse_coalition_members(coalition_id),
            "simulation_info": {
                "total_days": len(daily_returns),
                "simulation_time": simulation_result.get("simulation_time", 0),
                "sharpe_ratio": simulation_result.get("sharpe_ratio", 0)
            },
            "basic_statistics": basic_stats,
            "cumulative_returns": cumulative_returns.tolist(),
            "risk_metrics": risk_metrics,
            "time_series_analysis": time_series_analysis,
            "drawdown_analysis": drawdown_analysis,
            "periodic_analysis": periodic_analysis,
            "daily_returns": daily_returns
        }
    
    def _calculate_basic_statistics(self, returns: np.ndarray) -> Dict[str, float]:
        """计算基本统计指标"""
        return {
            "total_return": np.sum(returns),
            "annualized_return": np.mean(returns) * self.trading_days_per_year,
            "volatility": np.std(returns) * np.sqrt(self.trading_days_per_year),
            "sharpe_ratio": self._calculate_sharpe_ratio(returns),
            "sortino_ratio": self._calculate_sortino_ratio(returns),
            "calmar_ratio": self._calculate_calmar_ratio(returns),
            "max_return": np.max(returns),
            "min_return": np.min(returns),
            "positive_days": np.sum(returns > 0),
            "negative_days": np.sum(returns < 0),
            "win_rate": np.mean(returns > 0),
            "avg_win": np.mean(returns[returns > 0]) if np.any(returns > 0) else 0,
            "avg_loss": np.mean(returns[returns < 0]) if np.any(returns < 0) else 0,
            "profit_factor": abs(np.sum(returns[returns > 0]) / np.sum(returns[returns < 0])) if np.any(returns < 0) else float('inf')
        }
    
    def _calculate_cumulative_returns(self, returns: np.ndarray) -> np.ndarray:
        """计算累积收益率"""
        return np.cumsum(returns)
    
    def _calculate_risk_metrics(self, returns: np.ndarray) -> Dict[str, float]:
        """计算风险指标"""
        return {
            "var_95": np.percentile(returns, 5),  # 95% VaR
            "var_99": np.percentile(returns, 1),  # 99% VaR
            "cvar_95": np.mean(returns[returns <= np.percentile(returns, 5)]),  # 95% CVaR
            "skewness": self._calculate_skewness(returns),
            "kurtosis": self._calculate_kurtosis(returns),
            "beta": self._calculate_beta(returns),  # 相对于市场基准
            "tracking_error": np.std(returns - np.mean(returns)),
            "information_ratio": self._calculate_information_ratio(returns)
        }
    
    def _analyze_time_series(self, returns: np.ndarray) -> Dict[str, Any]:
        """时间序列分析"""
        # 趋势分析
        x = np.arange(len(returns))
        trend_coef = np.polyfit(x, np.cumsum(returns), 1)[0]
        
        # 自相关分析
        autocorr_1 = np.corrcoef(returns[:-1], returns[1:])[0, 1] if len(returns) > 1 else 0
        
        # 波动率聚类检测
        volatility_clustering = self._detect_volatility_clustering(returns)
        
        return {
            "trend_coefficient": trend_coef,
            "autocorrelation_lag1": autocorr_1,
            "volatility_clustering": volatility_clustering,
            "regime_changes": self._detect_regime_changes(returns)
        }
    
    def _calculate_drawdown_analysis(self, cumulative_returns: np.ndarray) -> Dict[str, float]:
        """回撤分析"""
        # 计算回撤
        peak = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - peak) / (peak + 1e-10)  # 避免除零
        
        return {
            "max_drawdown": np.min(drawdown),
            "avg_drawdown": np.mean(drawdown[drawdown < 0]),
            "drawdown_duration": self._calculate_drawdown_duration(drawdown),
            "recovery_time": self._calculate_recovery_time(drawdown),
            "underwater_time": np.sum(drawdown < -0.01) / len(drawdown) * 100  # 水下时间百分比
        }
    
    def _analyze_periodic_performance(self, returns: np.ndarray) -> Dict[str, Any]:
        """周期性表现分析"""
        # 假设每5天为一周
        weeks = len(returns) // 5
        if weeks < 2:
            return {}
        
        weekly_returns = []
        for i in range(weeks):
            start_idx = i * 5
            end_idx = min((i + 1) * 5, len(returns))
            week_return = np.sum(returns[start_idx:end_idx])
            weekly_returns.append(week_return)
        
        weekly_returns = np.array(weekly_returns)
        
        return {
            "weekly_returns": weekly_returns.tolist(),
            "weekly_volatility": np.std(weekly_returns),
            "weekly_sharpe": self._calculate_sharpe_ratio(weekly_returns, period_scale=52),
            "best_week": np.max(weekly_returns),
            "worst_week": np.min(weekly_returns),
            "consistent_weeks": np.sum(weekly_returns > 0),
            "weekly_win_rate": np.mean(weekly_returns > 0)
        }
    
    def _calculate_sharpe_ratio(self, returns: np.ndarray, period_scale: int = 252) -> float:
        """计算夏普比率"""
        if len(returns) == 0 or np.std(returns) == 0:
            return 0.0
        
        excess_returns = np.mean(returns) * period_scale - self.risk_free_rate
        volatility = np.std(returns) * np.sqrt(period_scale)
        return excess_returns / volatility
    
    def _calculate_sortino_ratio(self, returns: np.ndarray) -> float:
        """计算索提诺比率"""
        if len(returns) == 0:
            return 0.0
        
        excess_returns = np.mean(returns) * self.trading_days_per_year - self.risk_free_rate
        downside_deviation = np.std(returns[returns < 0]) * np.sqrt(self.trading_days_per_year)
        
        return excess_returns / downside_deviation if downside_deviation > 0 else 0.0
    
    def _calculate_calmar_ratio(self, returns: np.ndarray) -> float:
        """计算卡玛比率"""
        if len(returns) == 0:
            return 0.0
        
        annualized_return = np.mean(returns) * self.trading_days_per_year
        cumulative_returns = np.cumsum(returns)
        peak = np.maximum.accumulate(cumulative_returns)
        drawdown = (cumulative_returns - peak) / (peak + 1e-10)
        max_drawdown = abs(np.min(drawdown))
        
        return annualized_return / max_drawdown if max_drawdown > 0 else 0.0
    
    def _calculate_skewness(self, returns: np.ndarray) -> float:
        """计算偏度"""
        if len(returns) < 3:
            return 0.0
        
        mean_return = np.mean(returns)
        std_return = np.std(returns)
        
        if std_return == 0:
            return 0.0
        
        return np.mean(((returns - mean_return) / std_return) ** 3)
    
    def _calculate_kurtosis(self, returns: np.ndarray) -> float:
        """计算峰度"""
        if len(returns) < 4:
            return 0.0
        
        mean_return = np.mean(returns)
        std_return = np.std(returns)
        
        if std_return == 0:
            return 0.0
        
        return np.mean(((returns - mean_return) / std_return) ** 4) - 3
    
    def _calculate_beta(self, returns: np.ndarray) -> float:
        """计算贝塔值（相对于简单基准）"""
        # 这里使用简单的市场基准（假设市场收益率为0.0004/天）
        market_returns = np.full(len(returns), 0.0004)
        
        if len(returns) < 2:
            return 1.0
        
        covariance = np.cov(returns, market_returns)[0, 1]
        market_variance = np.var(market_returns)
        
        return covariance / market_variance if market_variance > 0 else 1.0
    
    def _calculate_information_ratio(self, returns: np.ndarray) -> float:
        """计算信息比率"""
        # 相对于简单基准的信息比率
        benchmark_return = 0.0004  # 简单基准
        excess_returns = returns - benchmark_return
        
        if len(excess_returns) == 0 or np.std(excess_returns) == 0:
            return 0.0
        
        return np.mean(excess_returns) / np.std(excess_returns) * np.sqrt(self.trading_days_per_year)
    
    def _detect_volatility_clustering(self, returns: np.ndarray) -> Dict[str, float]:
        """检测波动率聚类"""
        if len(returns) < 10:
            return {"clustering_coefficient": 0.0}
        
        # 计算移动波动率
        window_size = min(10, len(returns) // 2)
        rolling_vol = []
        
        for i in range(window_size, len(returns)):
            window_returns = returns[i-window_size:i]
            rolling_vol.append(np.std(window_returns))
        
        if len(rolling_vol) < 2:
            return {"clustering_coefficient": 0.0}
        
        # 计算波动率的自相关性
        vol_autocorr = np.corrcoef(rolling_vol[:-1], rolling_vol[1:])[0, 1]
        
        return {
            "clustering_coefficient": vol_autocorr,
            "avg_rolling_volatility": np.mean(rolling_vol),
            "max_rolling_volatility": np.max(rolling_vol),
            "min_rolling_volatility": np.min(rolling_vol)
        }
    
    def _detect_regime_changes(self, returns: np.ndarray) -> int:
        """检测制度变化点"""
        if len(returns) < 20:
            return 0
        
        # 简单的制度变化检测：基于波动率变化
        window_size = 10
        regime_changes = 0
        
        for i in range(window_size, len(returns) - window_size):
            before_vol = np.std(returns[i-window_size:i])
            after_vol = np.std(returns[i:i+window_size])
            
            # 如果波动率变化超过50%，认为是制度变化
            if abs(before_vol - after_vol) / (before_vol + 1e-10) > 0.5:
                regime_changes += 1
        
        return regime_changes
    
    def _calculate_drawdown_duration(self, drawdown: np.ndarray) -> float:
        """计算平均回撤持续时间"""
        in_drawdown = False
        durations = []
        current_duration = 0
        
        for dd in drawdown:
            if dd < -0.01:  # 进入回撤
                if not in_drawdown:
                    in_drawdown = True
                    current_duration = 1
                else:
                    current_duration += 1
            else:  # 退出回撤
                if in_drawdown:
                    durations.append(current_duration)
                    in_drawdown = False
                    current_duration = 0
        
        # 如果最后还在回撤中
        if in_drawdown:
            durations.append(current_duration)
        
        return np.mean(durations) if durations else 0.0
    
    def _calculate_recovery_time(self, drawdown: np.ndarray) -> float:
        """计算平均恢复时间"""
        # 简化版本：计算从最大回撤到恢复的时间
        min_drawdown_idx = np.argmin(drawdown)
        
        # 从最大回撤点开始，找到恢复点
        recovery_idx = None
        for i in range(min_drawdown_idx, len(drawdown)):
            if drawdown[i] >= -0.01:  # 认为已经恢复
                recovery_idx = i
                break
        
        if recovery_idx is not None:
            return recovery_idx - min_drawdown_idx
        else:
            return len(drawdown) - min_drawdown_idx  # 还未恢复
    
    def _calculate_coalition_contribution(self, 
                                       coalition_id: str, 
                                       shapley_values: Dict[str, float]) -> Dict[str, float]:
        """计算联盟的Shapley贡献度"""
        members = self._parse_coalition_members(coalition_id)
        contribution = {}
        
        for member in members:
            if member in shapley_values:
                contribution[member] = shapley_values[member]
        
        return contribution
    
    def _parse_coalition_members(self, coalition_id: str) -> List[str]:
        """解析联盟成员"""
        # 假设coalition_id格式为 "frozenset({'NAA', 'TAA', 'TRA'})"
        # 或者简单格式如 "NAA_TAA_TRA"
        
        if coalition_id.startswith("frozenset"):
            # 提取frozenset中的成员
            import re
            matches = re.findall(r"'([^']+)'", coalition_id)
            return matches
        else:
            # 简单格式
            return coalition_id.split("_")
    
    def _calculate_overall_statistics(self, analysis_results: Dict[str, Any]) -> Dict[str, Any]:
        """计算整体统计"""
        if not analysis_results:
            return {}
        
        # 过滤掉非联盟结果
        coalition_results = {k: v for k, v in analysis_results.items() 
                           if k != "overall_statistics"}
        
        if not coalition_results:
            return {}
        
        # 收集所有联盟的关键指标
        all_sharpe_ratios = []
        all_returns = []
        all_volatilities = []
        
        for result in coalition_results.values():
            basic_stats = result.get("basic_statistics", {})
            all_sharpe_ratios.append(basic_stats.get("sharpe_ratio", 0))
            all_returns.append(basic_stats.get("annualized_return", 0))
            all_volatilities.append(basic_stats.get("volatility", 0))
        
        # 找出最佳表现联盟
        best_sharpe_idx = np.argmax(all_sharpe_ratios)
        best_return_idx = np.argmax(all_returns)
        
        coalition_names = list(coalition_results.keys())
        
        return {
            "total_coalitions": len(coalition_results),
            "best_sharpe_coalition": coalition_names[best_sharpe_idx],
            "best_sharpe_ratio": all_sharpe_ratios[best_sharpe_idx],
            "best_return_coalition": coalition_names[best_return_idx],
            "best_return": all_returns[best_return_idx],
            "avg_sharpe_ratio": np.mean(all_sharpe_ratios),
            "avg_return": np.mean(all_returns),
            "avg_volatility": np.mean(all_volatilities),
            "sharpe_ratio_range": np.max(all_sharpe_ratios) - np.min(all_sharpe_ratios),
            "return_range": np.max(all_returns) - np.min(all_returns),
            "strategy_diversification": np.std(all_sharpe_ratios) / np.mean(all_sharpe_ratios) if np.mean(all_sharpe_ratios) > 0 else 0
        }
    
    def generate_visualization(self, 
                             save_plots: bool = True,
                             plot_types: List[str] = None) -> Dict[str, Any]:
        """
        生成可视化图表
        
        参数:
            save_plots: 是否保存图表
            plot_types: 要生成的图表类型列表
            
        返回:
            图表路径字典
        """
        if not self.performance_metrics:
            self.logger.warning("没有性能指标数据，无法生成图表")
            return {}
        
        if plot_types is None:
            plot_types = ["cumulative_returns", "risk_return_scatter", "performance_comparison", 
                         "drawdown_analysis", "returns_distribution"]
        
        self.logger.info(f"开始生成 {len(plot_types)} 种类型的图表")
        
        plot_paths = {}
        
        for plot_type in plot_types:
            try:
                if plot_type == "cumulative_returns":
                    plot_paths[plot_type] = self._plot_cumulative_returns(save_plots)
                elif plot_type == "risk_return_scatter":
                    plot_paths[plot_type] = self._plot_risk_return_scatter(save_plots)
                elif plot_type == "performance_comparison":
                    plot_paths[plot_type] = self._plot_performance_comparison(save_plots)
                elif plot_type == "drawdown_analysis":
                    plot_paths[plot_type] = self._plot_drawdown_analysis(save_plots)
                elif plot_type == "returns_distribution":
                    plot_paths[plot_type] = self._plot_returns_distribution(save_plots)
                else:
                    self.logger.warning(f"未知的图表类型: {plot_type}")
                    
            except Exception as e:
                self.logger.error(f"生成图表 {plot_type} 时出错: {e}")
        
        self.logger.info(f"图表生成完成，共生成 {len(plot_paths)} 个图表")
        return plot_paths
    
    def _plot_cumulative_returns(self, save_plot: bool) -> str:
        """绘制累积收益率曲线"""
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 过滤掉非联盟结果
        coalition_results = {k: v for k, v in self.performance_metrics.items() 
                           if k != "overall_statistics"}
        
        for coalition_id, result in coalition_results.items():
            cumulative_returns = result.get("cumulative_returns", [])
            if cumulative_returns:
                days = range(len(cumulative_returns))
                ax.plot(days, cumulative_returns, label=coalition_id, linewidth=2)
        
        ax.set_xlabel('交易日')
        ax.set_ylabel('累积收益率')
        ax.set_title('智能体联盟累积收益率曲线对比')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_plot:
            plot_path = self.output_dir / "cumulative_returns.png"
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"累积收益率图表已保存: {plot_path}")
            return str(plot_path)
        
        return ""
    
    def _plot_risk_return_scatter(self, save_plot: bool) -> str:
        """绘制风险-收益散点图"""
        fig, ax = plt.subplots(figsize=(10, 8))
        
        # 收集数据
        returns = []
        volatilities = []
        labels = []
        sharpe_ratios = []
        
        coalition_results = {k: v for k, v in self.performance_metrics.items() 
                           if k != "overall_statistics"}
        
        for coalition_id, result in coalition_results.items():
            basic_stats = result.get("basic_statistics", {})
            returns.append(basic_stats.get("annualized_return", 0))
            volatilities.append(basic_stats.get("volatility", 0))
            sharpe_ratios.append(basic_stats.get("sharpe_ratio", 0))
            labels.append(coalition_id)
        
        # 创建散点图，颜色基于夏普比率
        scatter = ax.scatter(volatilities, returns, c=sharpe_ratios, 
                           cmap='RdYlGn', s=100, alpha=0.7)
        
        # 添加颜色条
        cbar = plt.colorbar(scatter)
        cbar.set_label('夏普比率')
        
        # 添加标签
        for i, label in enumerate(labels):
            ax.annotate(label, (volatilities[i], returns[i]), 
                       xytext=(5, 5), textcoords='offset points', 
                       fontsize=8, alpha=0.8)
        
        ax.set_xlabel('年化波动率')
        ax.set_ylabel('年化收益率')
        ax.set_title('联盟风险-收益分布图')
        ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_plot:
            plot_path = self.output_dir / "risk_return_scatter.png"
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"风险-收益散点图已保存: {plot_path}")
            return str(plot_path)
        
        return ""
    
    def _plot_performance_comparison(self, save_plot: bool) -> str:
        """绘制性能指标对比图"""
        coalition_results = {k: v for k, v in self.performance_metrics.items() 
                           if k != "overall_statistics"}
        
        if not coalition_results:
            return ""
        
        # 准备数据
        coalitions = list(coalition_results.keys())
        metrics = ['sharpe_ratio', 'sortino_ratio', 'calmar_ratio', 'win_rate']
        metric_names = ['夏普比率', '索提诺比率', '卡玛比率', '胜率']
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.flatten()
        
        for i, (metric, metric_name) in enumerate(zip(metrics, metric_names)):
            values = []
            for coalition_id in coalitions:
                basic_stats = coalition_results[coalition_id].get("basic_statistics", {})
                values.append(basic_stats.get(metric, 0))
            
            ax = axes[i]
            bars = ax.bar(range(len(coalitions)), values, alpha=0.7)
            ax.set_xlabel('联盟')
            ax.set_ylabel(metric_name)
            ax.set_title(f'{metric_name}对比')
            ax.set_xticks(range(len(coalitions)))
            ax.set_xticklabels(coalitions, rotation=45, ha='right')
            ax.grid(True, alpha=0.3)
            
            # 添加数值标签
            for bar, value in zip(bars, values):
                ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                       f'{value:.3f}', ha='center', va='bottom', fontsize=8)
        
        plt.tight_layout()
        
        if save_plot:
            plot_path = self.output_dir / "performance_comparison.png"
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"性能对比图已保存: {plot_path}")
            return str(plot_path)
        
        return ""
    
    def _plot_drawdown_analysis(self, save_plot: bool) -> str:
        """绘制回撤分析图"""
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
        
        coalition_results = {k: v for k, v in self.performance_metrics.items() 
                           if k != "overall_statistics"}
        
        # 上图：回撤曲线
        for coalition_id, result in coalition_results.items():
            cumulative_returns = np.array(result.get("cumulative_returns", []))
            if len(cumulative_returns) > 0:
                # 计算回撤
                peak = np.maximum.accumulate(cumulative_returns)
                drawdown = (cumulative_returns - peak) / (peak + 1e-10)
                
                days = range(len(drawdown))
                ax1.plot(days, drawdown * 100, label=coalition_id, linewidth=2)
        
        ax1.set_xlabel('交易日')
        ax1.set_ylabel('回撤 (%)')
        ax1.set_title('联盟回撤曲线')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.fill_between(range(len(drawdown)), drawdown * 100, 0, alpha=0.3)
        
        # 下图：回撤统计对比
        coalitions = list(coalition_results.keys())
        max_drawdowns = []
        avg_drawdowns = []
        
        for coalition_id in coalitions:
            drawdown_analysis = coalition_results[coalition_id].get("drawdown_analysis", {})
            max_drawdowns.append(abs(drawdown_analysis.get("max_drawdown", 0)) * 100)
            avg_drawdowns.append(abs(drawdown_analysis.get("avg_drawdown", 0)) * 100)
        
        x = np.arange(len(coalitions))
        width = 0.35
        
        ax2.bar(x - width/2, max_drawdowns, width, label='最大回撤', alpha=0.7)
        ax2.bar(x + width/2, avg_drawdowns, width, label='平均回撤', alpha=0.7)
        
        ax2.set_xlabel('联盟')
        ax2.set_ylabel('回撤 (%)')
        ax2.set_title('回撤统计对比')
        ax2.set_xticks(x)
        ax2.set_xticklabels(coalitions, rotation=45, ha='right')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_plot:
            plot_path = self.output_dir / "drawdown_analysis.png"
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"回撤分析图已保存: {plot_path}")
            return str(plot_path)
        
        return ""
    
    def _plot_returns_distribution(self, save_plot: bool) -> str:
        """绘制收益率分布图"""
        coalition_results = {k: v for k, v in self.performance_metrics.items() 
                           if k != "overall_statistics"}
        
        if not coalition_results:
            return ""
        
        n_coalitions = len(coalition_results)
        fig, axes = plt.subplots(n_coalitions, 1, figsize=(10, 4 * n_coalitions))
        
        if n_coalitions == 1:
            axes = [axes]
        
        for i, (coalition_id, result) in enumerate(coalition_results.items()):
            daily_returns = result.get("daily_returns", [])
            if daily_returns:
                ax = axes[i]
                
                # 绘制直方图
                ax.hist(daily_returns, bins=30, alpha=0.7, density=True, 
                       edgecolor='black', linewidth=0.5)
                
                # 添加统计线
                mean_return = np.mean(daily_returns)
                std_return = np.std(daily_returns)
                
                ax.axvline(mean_return, color='red', linestyle='--', 
                          label=f'均值: {mean_return:.4f}')
                ax.axvline(mean_return + std_return, color='orange', 
                          linestyle='--', alpha=0.7, label=f'+1σ: {mean_return + std_return:.4f}')
                ax.axvline(mean_return - std_return, color='orange', 
                          linestyle='--', alpha=0.7, label=f'-1σ: {mean_return - std_return:.4f}')
                
                ax.set_xlabel('日收益率')
                ax.set_ylabel('概率密度')
                ax.set_title(f'{coalition_id} 收益率分布')
                ax.legend()
                ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if save_plot:
            plot_path = self.output_dir / "returns_distribution.png"
            plt.savefig(plot_path, dpi=300, bbox_inches='tight')
            self.logger.info(f"收益率分布图已保存: {plot_path}")
            return str(plot_path)
        
        return ""
    
    def generate_performance_report(self, 
                                  save_report: bool = True,
                                  include_detailed_stats: bool = True) -> Dict[str, Any]:
        """
        生成详细的性能报告
        
        参数:
            save_report: 是否保存报告
            include_detailed_stats: 是否包含详细统计
            
        返回:
            性能报告字典
        """
        if not self.performance_metrics:
            self.logger.warning("没有性能指标数据，无法生成报告")
            return {}
        
        self.logger.info("开始生成性能报告")
        
        report = {
            "report_metadata": {
                "generation_time": datetime.now().isoformat(),
                "analyzer_version": "1.0.0",
                "total_coalitions": len(self.performance_metrics) - 1  # 减去overall_statistics
            },
            "executive_summary": self._generate_executive_summary(),
            "detailed_analysis": self.performance_metrics if include_detailed_stats else {},
            "recommendations": self._generate_recommendations(),
            "risk_assessment": self._generate_risk_assessment()
        }
        
        if save_report:
            report_path = self.output_dir / f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False, default=self._json_default)
            
            self.logger.info(f"性能报告已保存: {report_path}")
            report["report_file"] = str(report_path)
        
        return report
    
    def _generate_executive_summary(self) -> Dict[str, Any]:
        """生成执行摘要"""
        overall_stats = self.performance_metrics.get("overall_statistics", {})
        
        if not overall_stats:
            return {}
        
        return {
            "key_findings": [
                f"共分析了 {overall_stats.get('total_coalitions', 0)} 个智能体联盟",
                f"最佳夏普比率联盟: {overall_stats.get('best_sharpe_coalition', 'N/A')} "
                f"(夏普比率: {overall_stats.get('best_sharpe_ratio', 0):.3f})",
                f"最佳收益率联盟: {overall_stats.get('best_return_coalition', 'N/A')} "
                f"(年化收益率: {overall_stats.get('best_return', 0):.2%})",
                f"平均夏普比率: {overall_stats.get('avg_sharpe_ratio', 0):.3f}",
                f"策略多样化指标: {overall_stats.get('strategy_diversification', 0):.3f}"
            ],
            "performance_metrics": {
                "best_sharpe_ratio": overall_stats.get('best_sharpe_ratio', 0),
                "best_return": overall_stats.get('best_return', 0),
                "avg_sharpe_ratio": overall_stats.get('avg_sharpe_ratio', 0),
                "avg_return": overall_stats.get('avg_return', 0),
                "strategy_diversification": overall_stats.get('strategy_diversification', 0)
            }
        }
    
    def _generate_recommendations(self) -> List[str]:
        """生成投资建议"""
        recommendations = []
        
        overall_stats = self.performance_metrics.get("overall_statistics", {})
        
        # 基于整体表现给出建议
        if overall_stats.get('avg_sharpe_ratio', 0) > 1.0:
            recommendations.append("整体策略表现优异，建议继续当前投资策略")
        elif overall_stats.get('avg_sharpe_ratio', 0) > 0.5:
            recommendations.append("策略表现良好，可考虑适当增加投资规模")
        else:
            recommendations.append("策略表现需要改进，建议重新评估智能体配置")
        
        # 基于策略多样化给出建议
        diversification = overall_stats.get('strategy_diversification', 0)
        if diversification > 0.5:
            recommendations.append("策略多样化程度高，有助于分散风险")
        else:
            recommendations.append("建议增加策略多样化，考虑不同智能体组合")
        
        # 基于最佳表现联盟给出建议
        best_coalition = overall_stats.get('best_sharpe_coalition', '')
        if best_coalition:
            recommendations.append(f"重点关注最佳表现联盟 {best_coalition}，考虑增加类似配置")
        
        return recommendations
    
    def _generate_risk_assessment(self) -> Dict[str, Any]:
        """生成风险评估"""
        coalition_results = {k: v for k, v in self.performance_metrics.items() 
                           if k != "overall_statistics"}
        
        if not coalition_results:
            return {}
        
        # 收集风险指标
        all_max_drawdowns = []
        all_volatilities = []
        all_vars = []
        
        for result in coalition_results.values():
            basic_stats = result.get("basic_statistics", {})
            drawdown_analysis = result.get("drawdown_analysis", {})
            risk_metrics = result.get("risk_metrics", {})
            
            all_max_drawdowns.append(abs(drawdown_analysis.get("max_drawdown", 0)))
            all_volatilities.append(basic_stats.get("volatility", 0))
            all_vars.append(abs(risk_metrics.get("var_95", 0)))
        
        # 计算风险统计
        avg_max_drawdown = np.mean(all_max_drawdowns)
        avg_volatility = np.mean(all_volatilities)
        avg_var_95 = np.mean(all_vars)
        
        # 风险等级评估
        if avg_max_drawdown > 0.2:
            risk_level = "高风险"
        elif avg_max_drawdown > 0.1:
            risk_level = "中风险"
        else:
            risk_level = "低风险"
        
        return {
            "risk_level": risk_level,
            "average_max_drawdown": avg_max_drawdown,
            "average_volatility": avg_volatility,
            "average_var_95": avg_var_95,
            "risk_warnings": self._generate_risk_warnings(all_max_drawdowns, all_volatilities)
        }
    
    def _generate_risk_warnings(self, max_drawdowns: List[float], volatilities: List[float]) -> List[str]:
        """生成风险警告"""
        warnings = []
        
        if max(max_drawdowns) > 0.3:
            warnings.append("存在超过30%的最大回撤，需要关注风险控制")
        
        if max(volatilities) > 0.4:
            warnings.append("存在超过40%的年化波动率，投资风险较高")
        
        if np.std(max_drawdowns) > 0.1:
            warnings.append("不同联盟的回撤差异较大，需要仔细选择投资组合")
        
        return warnings
    
    def get_summary_statistics(self) -> Dict[str, Any]:
        """获取汇总统计信息"""
        return {
            "performance_metrics": self.performance_metrics,
            "analysis_timestamp": datetime.now().isoformat(),
            "total_coalitions_analyzed": len(self.performance_metrics) - 1 if self.performance_metrics else 0
        }
    
    def _json_default(self, obj):
        """JSON序列化的默认处理函数"""
        if isinstance(obj, (np.integer, np.int64)):
            return int(obj)
        elif isinstance(obj, (np.floating, np.float64)):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return str(obj)


def main():
    """主函数，用于测试收益率分析器"""
    # 创建示例数据
    sample_coalition_results = {
        "NAA_TAA_TRA": {
            "daily_returns": np.random.normal(0.001, 0.02, 100).tolist(),
            "sharpe_ratio": 0.8,
            "simulation_time": 15.2
        },
        "TAA_FAA_TRA": {
            "daily_returns": np.random.normal(0.0008, 0.018, 100).tolist(),
            "sharpe_ratio": 0.9,
            "simulation_time": 14.8
        }
    }
    
    sample_shapley_values = {
        "NAA": 0.3,
        "TAA": 0.4,
        "FAA": 0.2,
        "TRA": 0.1
    }
    
    # 创建分析器
    analyzer = ReturnsAnalyzer()
    
    # 分析收益率
    analysis_results = analyzer.analyze_coalition_returns(
        sample_coalition_results, sample_shapley_values
    )
    
    # 生成可视化
    plot_paths = analyzer.generate_visualization()
    
    # 生成报告
    report = analyzer.generate_performance_report()
    
    print("收益率分析完成！")
    print(f"分析了 {len(sample_coalition_results)} 个联盟")
    print(f"生成了 {len(plot_paths)} 个图表")
    print(f"报告已保存")


if __name__ == "__main__":
    main()