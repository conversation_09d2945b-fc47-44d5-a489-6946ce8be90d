#!/usr/bin/env python3
"""
新闻数据缺失诊断工具

分析新闻数据缺失的根本原因，包括：
1. API限制和错误
2. 数据源覆盖问题
3. 数据库存储问题
4. 时间范围和格式问题
"""

import os
import sys
import sqlite3
import requests
import json
from datetime import datetime, timedelta
import pandas as pd

# 添加项目路径
current_script_path = os.path.abspath(__file__)
project_root = os.path.dirname(current_script_path)
sys.path.append(project_root)

from config import ALPHAVANTAGE_API_KEY, DATA_DIR

class NewsGapDiagnostic:
    """新闻数据缺失诊断器"""
    
    def __init__(self, ticker):
        self.ticker = ticker.upper()
        self.database_path = self._get_database_path()
        
    def _get_database_path(self):
        """获取数据库路径"""
        ticker_dir = os.path.join(DATA_DIR, "tickers", self.ticker)
        return os.path.join(ticker_dir, f"{self.ticker}_data.db")
    
    def check_api_connectivity(self):
        """检查API连接性和限制"""
        print(f"🔗 检查 Alpha Vantage API 连接性...")
        
        # 测试API连接
        test_params = {
            "function": "NEWS_SENTIMENT",
            "tickers": self.ticker,
            "time_from": "20241201T0000",
            "time_to": "20241201T2359",
            "apikey": ALPHAVANTAGE_API_KEY,
            "limit": "10"
        }
        
        try:
            response = requests.get("https://www.alphavantage.co/query", params=test_params, timeout=30)
            data = response.json()
            
            print(f"  📊 API 响应状态: {response.status_code}")
            
            if "Error Message" in data:
                print(f"  ❌ API 错误: {data['Error Message']}")
                return False
            elif "Note" in data:
                print(f"  ⚠️ API 限制: {data['Note']}")
                return False
            elif "Information" in data and "rate limit" in data["Information"].lower():
                print(f"  🚫 每日限制: {data['Information']}")
                return False
            elif "feed" in data:
                news_count = len(data["feed"])
                print(f"  ✅ API 正常，测试返回 {news_count} 条新闻")
                return True
            else:
                print(f"  ⚠️ 未知响应格式: {list(data.keys())}")
                return False
                
        except Exception as e:
            print(f"  ❌ API 连接失败: {e}")
            return False
    
    def analyze_database_content(self, start_date, end_date):
        """分析数据库内容"""
        print(f"\n📊 分析数据库内容 ({start_date} 到 {end_date})...")
        
        if not os.path.exists(self.database_path):
            print(f"  ❌ 数据库文件不存在: {self.database_path}")
            return
        
        try:
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            # 检查新闻表是否存在
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='news'")
            if not cursor.fetchone():
                print(f"  ❌ 新闻表不存在")
                return
            
            # 统计总新闻数量
            cursor.execute("SELECT COUNT(*) FROM news WHERE ticker = ?", (self.ticker,))
            total_news = cursor.fetchone()[0]
            print(f"  📈 总新闻数量: {total_news}")
            
            # 统计日期范围内的新闻
            cursor.execute("""
                SELECT COUNT(*) FROM news 
                WHERE ticker = ? AND DATE(time_published) BETWEEN ? AND ?
            """, (self.ticker, start_date, end_date))
            range_news = cursor.fetchone()[0]
            print(f"  📅 指定期间新闻: {range_news}")
            
            # 按日期统计
            cursor.execute("""
                SELECT DATE(time_published) as news_date, COUNT(*) as count
                FROM news 
                WHERE ticker = ? AND DATE(time_published) BETWEEN ? AND ?
                GROUP BY DATE(time_published)
                ORDER BY news_date
            """, (self.ticker, start_date, end_date))
            
            daily_stats = cursor.fetchall()
            
            if daily_stats:
                print(f"  📊 有数据的日期数: {len(daily_stats)}")
                counts = [row[1] for row in daily_stats]
                print(f"  📊 平均每日新闻: {sum(counts)/len(counts):.1f}")
                print(f"  📊 最多/最少: {max(counts)}/{min(counts)}")
                
                # 显示前几个和后几个日期
                print(f"  📅 最早日期: {daily_stats[0][0]} ({daily_stats[0][1]}条)")
                print(f"  📅 最晚日期: {daily_stats[-1][0]} ({daily_stats[-1][1]}条)")
            else:
                print(f"  ❌ 指定期间无新闻数据")
            
            conn.close()
            
        except Exception as e:
            print(f"  ❌ 数据库分析失败: {e}")
    
    def check_business_days_coverage(self, start_date, end_date):
        """检查工作日覆盖情况"""
        print(f"\n📅 检查工作日覆盖情况...")
        
        try:
            # 生成工作日列表
            business_days = pd.bdate_range(start=start_date, end=end_date)
            expected_dates = [date.strftime('%Y-%m-%d') for date in business_days]
            
            print(f"  📊 预期工作日: {len(expected_dates)} 天")
            
            # 查询数据库中的日期
            if not os.path.exists(self.database_path):
                print(f"  ❌ 数据库不存在")
                return
            
            conn = sqlite3.connect(self.database_path)
            cursor = conn.cursor()
            
            existing_dates = []
            missing_dates = []
            
            for date_str in expected_dates:
                cursor.execute("""
                    SELECT COUNT(*) FROM news 
                    WHERE ticker = ? AND DATE(time_published) = ?
                """, (self.ticker, date_str))
                
                count = cursor.fetchone()[0]
                if count > 0:
                    existing_dates.append(date_str)
                else:
                    missing_dates.append(date_str)
            
            print(f"  ✅ 有数据日期: {len(existing_dates)} 天 ({len(existing_dates)/len(expected_dates)*100:.1f}%)")
            print(f"  ❌ 缺失日期: {len(missing_dates)} 天 ({len(missing_dates)/len(expected_dates)*100:.1f}%)")
            
            if missing_dates:
                print(f"  🚨 缺失日期模式分析:")
                
                # 分析缺失日期的连续性
                consecutive_gaps = []
                current_gap = []
                
                for i, date in enumerate(missing_dates):
                    if i == 0:
                        current_gap = [date]
                    else:
                        prev_date = datetime.strptime(missing_dates[i-1], '%Y-%m-%d')
                        curr_date = datetime.strptime(date, '%Y-%m-%d')
                        
                        # 如果日期连续（考虑工作日）
                        if (curr_date - prev_date).days <= 3:  # 允许跨周末
                            current_gap.append(date)
                        else:
                            if current_gap:
                                consecutive_gaps.append(current_gap)
                            current_gap = [date]
                
                if current_gap:
                    consecutive_gaps.append(current_gap)
                
                print(f"    - 连续缺失段数: {len(consecutive_gaps)}")
                for i, gap in enumerate(consecutive_gaps[:5], 1):  # 显示前5个
                    if len(gap) == 1:
                        print(f"    - 缺失段{i}: {gap[0]} (1天)")
                    else:
                        print(f"    - 缺失段{i}: {gap[0]} 到 {gap[-1]} ({len(gap)}天)")
            
            conn.close()
            
        except Exception as e:
            print(f"  ❌ 工作日分析失败: {e}")
    
    def check_api_data_availability(self, start_date, end_date):
        """检查API数据可用性"""
        print(f"\n🔍 检查 API 数据可用性...")
        
        # 测试几个随机日期的数据可用性
        test_dates = []
        start_dt = datetime.strptime(start_date, '%Y-%m-%d')
        end_dt = datetime.strptime(end_date, '%Y-%m-%d')
        
        # 选择几个测试日期
        total_days = (end_dt - start_dt).days
        for i in [0, total_days//4, total_days//2, total_days*3//4, total_days-1]:
            test_date = start_dt + timedelta(days=i)
            test_dates.append(test_date.strftime('%Y-%m-%d'))
        
        print(f"  📅 测试日期: {', '.join(test_dates)}")
        
        for date in test_dates:
            time_from = date.replace('-', '') + 'T0000'
            time_to = date.replace('-', '') + 'T2359'
            
            params = {
                "function": "NEWS_SENTIMENT",
                "tickers": self.ticker,
                "time_from": time_from,
                "time_to": time_to,
                "apikey": ALPHAVANTAGE_API_KEY,
                "limit": "50"
            }
            
            try:
                response = requests.get("https://www.alphavantage.co/query", params=params, timeout=30)
                data = response.json()
                
                if "feed" in data:
                    news_count = len(data["feed"])
                    print(f"    {date}: {news_count} 条新闻可用")
                elif "Error Message" in data:
                    print(f"    {date}: API错误 - {data['Error Message']}")
                elif "Note" in data:
                    print(f"    {date}: API限制 - {data['Note']}")
                    break  # 遇到限制就停止
                else:
                    print(f"    {date}: 未知响应")
                
                # 避免触发API限制
                import time
                time.sleep(1)
                
            except Exception as e:
                print(f"    {date}: 请求失败 - {e}")
    
    def suggest_solutions(self):
        """建议解决方案"""
        print(f"\n💡 问题解决建议:")
        print(f"  1. 🔑 API问题:")
        print(f"     - 检查API密钥是否有效且未过期")
        print(f"     - 确认API计划限制（免费/付费）")
        print(f"     - 检查每日请求限制是否用完")
        print(f"")
        print(f"  2. 📅 数据覆盖问题:")
        print(f"     - Alpha Vantage可能对某些日期/股票没有新闻")
        print(f"     - 节假日和周末通常没有新闻")
        print(f"     - 某些股票的新闻覆盖可能不完整")
        print(f"")
        print(f"  3. 🔧 技术问题:")
        print(f"     - 网络连接问题导致下载失败")
        print(f"     - 数据库写入问题")
        print(f"     - 时间格式解析问题")
        print(f"")
        print(f"  4. 🛠️ 解决方案:")
        print(f"     - 使用更小的时间窗口重试下载")
        print(f"     - 检查API响应日志确认实际返回数据")
        print(f"     - 考虑升级到付费API计划")
        print(f"     - 使用多个数据源补充")

def main():
    """主函数"""
    if len(sys.argv) < 4:
        print("Usage: python diagnose_news_gaps.py <ticker> <start_date> <end_date>")
        print("Example: python diagnose_news_gaps.py AAPL 2024-08-01 2024-12-30")
        sys.exit(1)
    
    ticker = sys.argv[1]
    start_date = sys.argv[2] 
    end_date = sys.argv[3]
    
    print(f"🔍 新闻数据缺失诊断: {ticker} ({start_date} 到 {end_date})")
    print("=" * 60)
    
    diagnostic = NewsGapDiagnostic(ticker)
    
    # 执行诊断
    api_ok = diagnostic.check_api_connectivity()
    diagnostic.analyze_database_content(start_date, end_date)
    diagnostic.check_business_days_coverage(start_date, end_date)
    
    if api_ok:
        diagnostic.check_api_data_availability(start_date, end_date)
    
    diagnostic.suggest_solutions()
    
    print(f"\n✅ 诊断完成")

if __name__ == "__main__":
    main()