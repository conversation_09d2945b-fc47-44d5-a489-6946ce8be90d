"""
展望层智能体 (Outlook Agents)

包含看涨、看跌和中性展望智能体的具体实现
"""

from typing import Dict, Any, List, Optional
import logging
from .opro_base_agent import OPROBaseAgent


class BullishOutlookAgent(OPROBaseAgent):
    """看涨展望智能体 (BOA)"""
    
    def __init__(self, llm_interface=None, logger=None, opro_enabled=False, opro_optimizer=None):
        super().__init__("BOA", llm_interface, logger, opro_enabled, opro_optimizer)

    def get_required_data_fields(self) -> List[str]:
        """BOA只需要分析层智能体的输出"""
        return ["analyst_outputs", "current_date", "date"]

    def get_default_prompt_template(self) -> str:
        """获取默认的静态提示词模板"""
        return """你是一个看涨展望智能体（BOA），在多智能体金融分析系统的展望层工作。

层级定位：
- 你属于展望层，可以访问分析层智能体（NAA、TAA、FAA）的输出
- 你不能访问同层其他展望智能体（BeOA、NOA）的输出
- 你的输出将被决策层智能体（TRA）使用

你的任务是：
1. 基于前序分析层智能体的结果，寻找和强调积极因素
2. 构建看涨的市场叙述和逻辑
3. 评估上涨潜力和目标价位
4. 分析看涨情景下的风险因素

请提供看涨展望结果，包含：
- 市场展望（看涨）
- 看涨因素列表
- 目标价位
- 上涨潜力（百分比）
- 时间框架
- 风险因素
- 分析信心度（0到1）"""
    
    def process(self, state: Dict[str, Any], is_full_coalition: bool = False) -> Dict[str, Any]:
        """处理看涨展望分析"""
        prompt = self.get_prompt_template()
        return self.call_llm(prompt, state, is_full_coalition=is_full_coalition)


class BearishOutlookAgent(OPROBaseAgent):
    """看跌展望智能体 (BeOA)"""
    
    def __init__(self, llm_interface=None, logger=None, opro_enabled=False, opro_optimizer=None):
        super().__init__("BeOA", llm_interface, logger, opro_enabled, opro_optimizer)

    def get_required_data_fields(self) -> List[str]:
        """BeOA只需要分析层智能体的输出"""
        return ["analyst_outputs", "current_date", "date"]

    def get_default_prompt_template(self) -> str:
        """获取默认的静态提示词模板"""
        return """你是一个看跌展望智能体（BeOA），在多智能体金融分析系统的展望层工作。

层级定位：
- 你属于展望层，可以访问分析层智能体（NAA、TAA、FAA）的输出
- 你不能访问同层其他展望智能体（BOA、NOA）的输出
- 你的输出将被决策层智能体（TRA）使用

你的任务是：
1. 基于前序分析层智能体的结果，识别和强调风险因素
2. 构建看跌的市场叙述和逻辑
3. 评估下跌风险和支撑位
4. 分析看跌情景下的防御策略

请提供看跌展望结果，包含：
- 市场展望（看跌）
- 看跌因素列表
- 下跌目标
- 下跌风险（百分比）
- 关键支撑位
- 防御策略
- 分析信心度（0到1）"""
    
    def process(self, state: Dict[str, Any], is_full_coalition: bool = False) -> Dict[str, Any]:
        """处理看跌展望分析"""
        prompt = self.get_prompt_template()
        return self.call_llm(prompt, state, is_full_coalition=is_full_coalition)


class NeutralObserverAgent(OPROBaseAgent):
    """中性观察智能体 (NOA)"""
    
    def __init__(self, llm_interface=None, logger=None, opro_enabled=False, opro_optimizer=None):
        super().__init__("NOA", llm_interface, logger, opro_enabled, opro_optimizer)

    def get_required_data_fields(self) -> List[str]:
        """NOA只需要分析层智能体的输出"""
        return ["analyst_outputs", "current_date", "date"]

    def get_default_prompt_template(self) -> str:
        """获取默认的静态提示词模板"""
        return """你是一个中性展望智能体（NOA），在多智能体金融分析系统的展望层工作。

层级定位：
- 你属于展望层，可以访问分析层智能体（NAA、TAA、FAA）的输出
- 你不能访问同层其他展望智能体（BOA、BeOA）的输出
- 你的输出将被决策层智能体（TRA）使用

你的任务是：
1. 基于前序分析层智能体的结果，客观平衡地分析看涨和看跌因素
2. 识别市场中的不确定性和矛盾信号
3. 提供中性的观察和等待策略
4. 分析何时市场可能出现明确方向

请提供中性展望结果，包含：
- 市场展望（中性）
- 平衡分析
- 不确定性因素
- 关键催化剂
- 观望策略
- 市场无效性
- 分析信心度（0到1）"""
    
    def process(self, state: Dict[str, Any], is_full_coalition: bool = False) -> Dict[str, Any]:
        """处理中性观察分析"""
        prompt = self.get_prompt_template()
        return self.call_llm(prompt, state, is_full_coalition=is_full_coalition)