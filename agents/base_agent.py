"""
基础智能体类 (Base Agent Class)

定义所有智能体的基础接口和通用功能
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
import logging
from datetime import datetime


class BaseAgent(ABC):
    """
    基础智能体抽象类
    
    所有智能体都必须继承此类并实现process方法
    """
    
    def __init__(self, agent_id: str, llm_interface=None, logger: Optional[logging.Logger] = None):
        """
        初始化基础智能体
        
        参数:
            agent_id: 智能体唯一标识符
            llm_interface: LLM接口实例
            logger: 日志记录器
        """
        self.agent_id = agent_id
        self.llm_interface = llm_interface
        self.logger = logger or self._create_default_logger()
        
        # 智能体状态
        self.last_analysis = None
        self.analysis_count = 0
        self.total_processing_time = 0.0

        # 详细日志模式控制
        self.detailed_logging = False

        # self.logger.info(f"智能体 {self.agent_id} 初始化完成")

    def set_detailed_logging(self, detailed: bool) -> None:
        """
        设置详细日志模式

        参数:
            detailed: True为详细模式，False为简洁模式
        """
        self.detailed_logging = detailed
        # mode_str = "详细" if detailed else "简洁"
        # self.logger.debug(f"🔧 智能体 {self.agent_id} 日志模式设置为: {mode_str}模式")

    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(f"agents.{self.agent_id}")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    @abstractmethod
    def get_prompt_template(self) -> str:
        """
        获取该智能体的提示词模板
        
        返回:
            提示词模板字符串
        """
        pass
    
    @abstractmethod
    def process(self, state: Dict[str, Any], is_full_coalition: bool = False) -> Dict[str, Any]:
        """
        处理输入状态并生成分析结果
        
        参数:
            state: 包含市场数据、历史状态等信息的状态字典
            
        返回:
            分析结果字典
        """
        pass
    
    def format_state_for_llm(self, state: Dict[str, Any], detailed: bool = False) -> str:
        """
        将状态信息格式化为LLM可读的文本

        参数:
            state: 状态字典
            detailed: 是否显示详细信息（包括完整的价格历史、所有新闻、完整基本面数据）

        返回:
            格式化的状态描述
        """
        formatted_parts = []
        
        # 基础信息
        current_date = state.get("current_date", state.get("date", "未知"))
        formatted_parts.append(f"📅 分析日期: {current_date}")
        
        # 交易日信息
        if "trading_day_info" in state:
            trading_info = state["trading_day_info"]
            is_trading_day = trading_info.get("is_trading_day", "未知")
            has_price_data = trading_info.get("has_price_data", "未知")
            trading_day_status = "✅ 交易日" if is_trading_day else "❌ 非交易日"
            price_data_status = "✅ 有价格数据" if has_price_data else "❌ 无价格数据"
            formatted_parts.append(f"🏢 交易日状态: {trading_day_status} | {price_data_status}")
            
            if trading_info.get("next_trading_day"):
                formatted_parts.append(f"📅 下一交易日: {trading_info['next_trading_day']}")
        
        # 跳过日期信息
        if state.get("skip_day", False):
            skip_reason = state.get("skip_reason", "未知原因")
            formatted_parts.append(f"⚠️ 建议跳过当前日期: {skip_reason}")
        
        # 分析期间信息
        if "analysis_period" in state:
            period = state["analysis_period"]
            start_date = period.get("start_date", "未知")
            end_date = period.get("end_date", "未知")
            formatted_parts.append(f"📊 分析期间: {start_date} 至 {end_date}")
        
        # 投资组合信息
        if "cash" in state:
            formatted_parts.append(f"💵 可用现金: ${state['cash']:,.2f}")
        
        if "positions" in state and state["positions"]:
            formatted_parts.append(f"💰 持仓: {state['positions']}")
        
        # 股票价格历史信息 (真实数据)
        if "price_history" in state and state["price_history"]:
            formatted_parts.append("\n📈 股票价格历史:")
            for symbol, price_data in state["price_history"].items():
                if price_data:
                    if detailed and isinstance(price_data, list):
                        # 详细模式：显示所有历史价格数据
                        formatted_parts.append(f"  • {symbol} (共{len(price_data)}个交易日):")
                        for i, data_point in enumerate(price_data):
                            if isinstance(data_point, dict):
                                date_str = data_point.get('date', f'第{i+1}天')
                                if hasattr(date_str, 'strftime'):
                                    date_str = date_str.strftime('%Y-%m-%d')
                                close = data_point.get('close', 'N/A')
                                open_price = data_point.get('open', 'N/A')
                                high = data_point.get('high', 'N/A')
                                low = data_point.get('low', 'N/A')
                                volume = data_point.get('volume', 'N/A')
                                formatted_parts.append(f"    {date_str}: 收盘${close}, 开盘${open_price}, 最高${high}, 最低${low}, 成交量{volume}")
                    else:
                        # 简洁模式：只显示最新数据
                        latest_data = price_data[-1] if isinstance(price_data, list) else price_data
                        if isinstance(latest_data, dict):
                            current_price = latest_data.get('close', 'N/A')
                            open_price = latest_data.get('open', 'N/A')
                            high_price = latest_data.get('high', 'N/A')
                            low_price = latest_data.get('low', 'N/A')
                            volume = latest_data.get('volume', 'N/A')
                            formatted_parts.append(f"  • {symbol}: 收盘${current_price}, 开盘${open_price}, 最高${high_price}, 最低${low_price}, 成交量{volume}")
                        else:
                            formatted_parts.append(f"  • {symbol}: {len(price_data)} 个历史数据点")
        
        # 新闻历史信息 (真实数据)
        if "news_history" in state and state["news_history"]:
            formatted_parts.append("\n📰 相关新闻:")
            total_news = 0
            for date, stocks_news in state["news_history"].items():
                if isinstance(stocks_news, dict):
                    # stocks_news是 {stock: [news_list]} 格式
                    date_total = 0
                    date_news_items = []

                    # 统计该日期所有股票的新闻
                    for stock, news_list in stocks_news.items():
                        if isinstance(news_list, list) and news_list:
                            date_total += len(news_list)
                            # 收集前几条新闻用于显示
                            for news in news_list[:2]:  # 每个股票最多显示2条
                                if isinstance(news, dict):
                                    date_news_items.append(news)

                    if date_total > 0:
                        total_news += date_total
                        formatted_parts.append(f"  📅 {date}: {date_total} 条新闻")

                        if detailed:
                            # 详细模式：显示所有新闻的完整信息
                            for stock, news_list in stocks_news.items():
                                if isinstance(news_list, list) and news_list:
                                    formatted_parts.append(f"    📈 {stock} ({len(news_list)}条):")
                                    for i, news in enumerate(news_list, 1):
                                        if isinstance(news, dict):
                                            title = news.get('title', '无标题')
                                            sentiment = news.get('overall_sentiment_label') or news.get('sentiment', '未知')
                                            time_info = news.get('time', news.get('full_timestamp', ''))
                                            summary = news.get('summary', news.get('content', ''))
                                            url = news.get('url', '')

                                            formatted_parts.append(f"      {i}. 标题: {title}")
                                            formatted_parts.append(f"         时间: {time_info}")
                                            formatted_parts.append(f"         情绪: {sentiment}")
                                            if summary:
                                                summary_short = summary[:200] + "..." if len(summary) > 200 else summary
                                                formatted_parts.append(f"         摘要: {summary_short}")
                                            if url:
                                                formatted_parts.append(f"         链接: {url}")
                                            formatted_parts.append("")  # 空行分隔
                        else:
                            # 简洁模式：显示前几条新闻标题
                            for news in date_news_items[:3]:  # 最多显示3条
                                title = news.get('title', '无标题')
                                if title and title != '无标题':
                                    # 截断过长的标题
                                    if len(title) > 80:
                                        title = title[:80] + "..."
                                    sentiment = news.get('overall_sentiment_label') or news.get('sentiment', '未知')
                                    formatted_parts.append(f"    - {title} (情绪: {sentiment})")
                                else:
                                    # 如果标题为空，尝试使用摘要的前部分
                                    summary = news.get('summary', '') or news.get('content', '')
                                    if summary:
                                        title = summary[:50] + "..." if len(summary) > 50 else summary
                                        sentiment = news.get('overall_sentiment_label') or news.get('sentiment', '未知')
                                        formatted_parts.append(f"    - {title} (情绪: {sentiment})")
                elif isinstance(stocks_news, list):
                    # 兼容旧格式：直接是新闻列表
                    if stocks_news:
                        total_news += len(stocks_news)
                        formatted_parts.append(f"  📅 {date}: {len(stocks_news)} 条新闻")
                        # 显示前几条新闻标题
                        for news in stocks_news[:2]:  # 只显示前2条
                            if isinstance(news, dict):
                                title = news.get('title', '无标题')[:80] + "..." if len(news.get('title', '')) > 80 else news.get('title', '无标题')
                                sentiment = news.get('overall_sentiment_label') or news.get('sentiment', '未知')
                                formatted_parts.append(f"    - {title} (情绪: {sentiment})")
            formatted_parts.append(f"  📊 总计: {total_news} 条新闻")
        
        # 基本面数据信息 (真实数据)
        if "fundamental_data" in state:
            formatted_parts.append("\n📊 基本面数据:")
            if not state["fundamental_data"]:
                formatted_parts.append("  ⚠️ 基本面数据为空，无法进行财务分析")
            else:
                for symbol, fund_data in state["fundamental_data"].items():
                    if fund_data and isinstance(fund_data, dict):
                        # 从实际的数据结构中提取报告日期
                        report_date = '未知'
                        current_data = fund_data.get('current', {})
                        quarterly_history = fund_data.get('quarterly_history', [])
                        
                        if current_data and current_data.get('fiscal_date'):
                            report_date = current_data.get('fiscal_date')
                        elif current_data and current_data.get('report_date'):
                            report_date = current_data.get('report_date')
                        elif quarterly_history and quarterly_history[0].get('data', {}).get('fiscal_date'):
                            report_date = quarterly_history[0]['data']['fiscal_date']
                        elif quarterly_history and quarterly_history[0].get('data', {}).get('report_date'):
                            report_date = quarterly_history[0]['data']['report_date']
                        
                        # 评估数据质量
                        data_quality = self._assess_fundamental_data_quality(fund_data)
                        formatted_parts.append(f"  • {symbol} (报告日期: {report_date}, 数据质量: {data_quality}):")

                        # 检查是否有综合财务数据（从数据库加载的）
                        if 'current' in fund_data or 'quarterly_history' in fund_data or 'annual_history' in fund_data:
                            # 检查是否有实际的财务数据
                            has_actual_data = False
                            current_data = fund_data.get('current', {})
                            quarterly_data = fund_data.get('quarterly_history', [])
                            
                            if current_data and any(current_data.get(key) not in [None, 'N/A', '', 0] for key in ['revenue', 'net_income', 'total_assets']):
                                has_actual_data = True
                            elif quarterly_data and any(q.get('data', {}).get(key) not in [None, 'N/A', '', 0] for q in quarterly_data for key in ['revenue', 'net_income', 'total_assets']):
                                has_actual_data = True
                            
                            if has_actual_data:
                                self._format_comprehensive_financial_data(formatted_parts, fund_data, detailed)
                            else:
                                formatted_parts.append("    ⚠️ 可用数据字段: current, quarterly_history, annual_history, growth_metrics")
                                formatted_parts.append("    ⚠️ 但缺少具体财务指标数据，无法进行详细分析")
                        # 提取关键财务指标（原有格式）
                        elif 'data' in fund_data and isinstance(fund_data['data'], dict):
                            financial_data = fund_data['data']

                            # 检查是否有实际的财务数据
                            has_financial_data = any(
                                financial_data.get(key) not in [None, 'N/A', '', 0]
                                for key in ['totalRevenue', 'netIncome', 'grossProfit', 'totalAssets']
                            )

                            if not has_financial_data:
                                formatted_parts.append("    ⚠️ 缺少具体财务指标数据，无法进行详细分析")
                            else:
                                # 有财务数据，进行格式化显示
                                if detailed:
                                    # 详细模式：显示所有可用的财务指标
                                    formatted_parts.append("    📈 收入指标:")
                                    revenue = financial_data.get('totalRevenue', 'N/A')
                                    gross_profit = financial_data.get('grossProfit', 'N/A')
                                    operating_income = financial_data.get('operatingIncome', 'N/A')
                                    formatted_parts.append(f"      - 总收入: {revenue}")
                                    formatted_parts.append(f"      - 毛利润: {gross_profit}")
                                    formatted_parts.append(f"      - 营业收入: {operating_income}")

                                    formatted_parts.append("    💰 盈利指标:")
                                    net_income = financial_data.get('netIncome', 'N/A')
                                    ebitda = financial_data.get('ebitda', 'N/A')
                                    eps = financial_data.get('basicEPS', 'N/A')
                                    formatted_parts.append(f"      - 净收入: {net_income}")
                                    formatted_parts.append(f"      - EBITDA: {ebitda}")
                                    formatted_parts.append(f"      - 每股收益: {eps}")

                                    formatted_parts.append("    🏦 资产负债:")
                                    total_assets = financial_data.get('totalAssets', 'N/A')
                                    total_debt = financial_data.get('totalDebt', 'N/A')
                                    cash = financial_data.get('cashAndCashEquivalents', 'N/A')
                                    formatted_parts.append(f"      - 总资产: {total_assets}")
                                    formatted_parts.append(f"      - 总债务: {total_debt}")
                                    formatted_parts.append(f"      - 现金及等价物: {cash}")

                                    formatted_parts.append("    📊 比率指标:")
                                    roe = financial_data.get('returnOnEquity', 'N/A')
                                    roa = financial_data.get('returnOnAssets', 'N/A')
                                    debt_ratio = financial_data.get('debtToEquity', 'N/A')
                                    formatted_parts.append(f"      - 净资产收益率: {roe}")
                                    formatted_parts.append(f"      - 总资产收益率: {roa}")
                                    formatted_parts.append(f"      - 债务权益比: {debt_ratio}")
                                else:
                                    # 简洁模式：只显示关键指标
                                    revenue = financial_data.get('totalRevenue', 'N/A')
                                    net_income = financial_data.get('netIncome', 'N/A')
                                    gross_profit = financial_data.get('grossProfit', 'N/A')

                                    formatted_parts.append(f"    - 总收入: {revenue}")
                                    formatted_parts.append(f"    - 净收入: {net_income}")
                                    formatted_parts.append(f"    - 毛利润: {gross_profit}")
                        else:
                            # 检查是否有其他可用的数据字段
                            available_fields = [key for key in fund_data.keys() if key not in ['report_date', 'data_quality', 'analysis_date', 'time_filtered']]
                            if available_fields:
                                formatted_parts.append(f"    ℹ️ 可用数据字段: {', '.join(available_fields)}")
                                # 如果有时间过滤信息，显示它
                                if fund_data.get('time_filtered'):
                                    formatted_parts.append(f"    🕒 已应用时间过滤，截至: {fund_data.get('analysis_date', '未知')}")
                            else:
                                formatted_parts.append("    ⚠️ 基本面数据结构不完整，缺少财务数据字段")
        
        # 前序智能体分析输出
        if "analyst_outputs" in state and state["analyst_outputs"]:
            formatted_parts.append("\n🤖 分析层智能体输出:")
            for agent_id, output in state["analyst_outputs"].items():
                if isinstance(output, dict):
                    summary = output.get("summary", output.get("reasoning", output.get("analysis", str(output)[:100] + "...")))
                    confidence = output.get("confidence", "未知")
                    formatted_parts.append(f"  • {agent_id}: {summary} (信心度: {confidence})")
        
        # 展望层智能体输出
        if "outlook_outputs" in state and state["outlook_outputs"]:
            formatted_parts.append("\n🔮 展望层智能体输出:")
            for agent_id, output in state["outlook_outputs"].items():
                if isinstance(output, dict):
                    outlook = output.get("outlook", output.get("reasoning", str(output)[:100] + "..."))
                    confidence = output.get("confidence", "未知")
                    formatted_parts.append(f"  • {agent_id}: {outlook} (信心度: {confidence})")
        
        # 兼容旧格式的前序智能体输出
        if "previous_outputs" in state and state["previous_outputs"]:
            formatted_parts.append("\n🤖 前序智能体分析:")
            for agent_id, output in state["previous_outputs"].items():
                if isinstance(output, dict):
                    summary = output.get("summary", output.get("reasoning", str(output)[:100] + "..."))
                    formatted_parts.append(f"  • {agent_id}: {summary}")
        
        return "\n".join(formatted_parts)

    def _format_comprehensive_financial_data(self, formatted_parts: List[str], comprehensive_data: Dict[str, Any], detailed: bool = False):
        """
        格式化综合财务数据（包含年报、季报和增长率）

        参数:
            formatted_parts: 格式化输出列表
            comprehensive_data: 综合财务数据
            detailed: 是否显示详细信息
        """
        try:
            # 最新季报数据 - 从 current 或 quarterly_history 获取
            latest_quarterly = comprehensive_data.get('current') or (
                comprehensive_data.get('quarterly_history', [{}])[0] if comprehensive_data.get('quarterly_history') else {}
            )
            if latest_quarterly:
                fiscal_date = latest_quarterly.get('fiscal_date', latest_quarterly.get('report_date', '未知'))
                formatted_parts.append(f"    📈 最新季报 ({fiscal_date}):")

                # 关键财务指标 - 使用实际的数据库字段名
                revenue = latest_quarterly.get('revenue', 'N/A')
                net_income = latest_quarterly.get('net_income', 'N/A')
                total_assets = latest_quarterly.get('total_assets', 'N/A')
                eps = latest_quarterly.get('eps', 'N/A')
                profit_margin = latest_quarterly.get('profit_margin', 'N/A')

                formatted_parts.append(f"      - 总收入: {revenue}")
                formatted_parts.append(f"      - 净收入: {net_income}")
                formatted_parts.append(f"      - 总资产: {total_assets}")
                formatted_parts.append(f"      - 每股收益: {eps}")
                if profit_margin != 'N/A' and profit_margin is not None:
                    try:
                        formatted_parts.append(f"      - 利润率: {float(profit_margin):.2%}")
                    except (ValueError, TypeError):
                        formatted_parts.append(f"      - 利润率: {profit_margin}")

                # 显示更多财务指标（总是显示详细信息给FAA）
                # 资产负债相关
                total_debt = latest_quarterly.get('total_debt', 'N/A')
                cash_equivalents = latest_quarterly.get('cash_and_cash_equivalents', 'N/A')
                current_assets = latest_quarterly.get('current_assets', 'N/A')
                current_liabilities = latest_quarterly.get('current_liabilities', 'N/A')
                stockholders_equity = latest_quarterly.get('stockholders_equity', 'N/A')

                if any(x != 'N/A' for x in [total_debt, cash_equivalents, current_assets, current_liabilities, stockholders_equity]):
                    formatted_parts.append("      💰 资产负债指标:")
                    if total_debt != 'N/A':
                        formatted_parts.append(f"        - 总债务: {total_debt}")
                    if cash_equivalents != 'N/A':
                        formatted_parts.append(f"        - 现金及等价物: {cash_equivalents}")
                    if current_assets != 'N/A':
                        formatted_parts.append(f"        - 流动资产: {current_assets}")
                    if current_liabilities != 'N/A':
                        formatted_parts.append(f"        - 流动负债: {current_liabilities}")
                    if stockholders_equity != 'N/A':
                        formatted_parts.append(f"        - 股东权益: {stockholders_equity}")

                # 计算财务比率
                if revenue != 'N/A' and net_income != 'N/A' and total_assets != 'N/A':
                    try:
                        revenue_val = float(revenue) if revenue != 'N/A' else 0
                        net_income_val = float(net_income) if net_income != 'N/A' else 0
                        total_assets_val = float(total_assets) if total_assets != 'N/A' else 0
                        equity_val = float(stockholders_equity) if stockholders_equity != 'N/A' else 0
                        debt_val = float(total_debt) if total_debt != 'N/A' else 0

                        formatted_parts.append("      📊 计算财务比率:")
                        if revenue_val > 0:
                            net_margin = (net_income_val / revenue_val) * 100
                            formatted_parts.append(f"        - 净利润率: {net_margin:.2f}%")
                        if total_assets_val > 0:
                            roa = (net_income_val / total_assets_val) * 100
                            formatted_parts.append(f"        - 总资产收益率(ROA): {roa:.2f}%")
                            asset_turnover = revenue_val / total_assets_val
                            formatted_parts.append(f"        - 资产周转率: {asset_turnover:.2f}")
                        if equity_val > 0:
                            roe = (net_income_val / equity_val) * 100
                            formatted_parts.append(f"        - 净资产收益率(ROE): {roe:.2f}%")
                        if total_assets_val > 0 and debt_val > 0:
                            debt_ratio = (debt_val / total_assets_val) * 100
                            formatted_parts.append(f"        - 债务比率: {debt_ratio:.2f}%")
                        if equity_val > 0 and debt_val > 0:
                            debt_to_equity = debt_val / equity_val
                            formatted_parts.append(f"        - 债务权益比: {debt_to_equity:.2f}")
                    except (ValueError, TypeError, ZeroDivisionError):
                        pass

                # 环比增长率
                if 'qoq_growth' in latest_quarterly and latest_quarterly['qoq_growth']:
                    formatted_parts.append("      📊 环比增长率:")
                    qoq = latest_quarterly['qoq_growth']
                    for metric, growth in qoq.items():
                        if growth is not None:
                            formatted_parts.append(f"        - {metric}: {growth}%")

                # 同比增长率
                if 'yoy_growth' in latest_quarterly and latest_quarterly['yoy_growth']:
                    formatted_parts.append("      📈 同比增长率:")
                    yoy = latest_quarterly['yoy_growth']
                    for metric, growth in yoy.items():
                        if growth is not None:
                            formatted_parts.append(f"        - {metric}: {growth}%")

            # 最新年报数据
            latest_annual = comprehensive_data.get('annual_history', [{}])[0] if comprehensive_data.get('annual_history') else {}
            if latest_annual:
                # 年度数据使用年份信息
                year = latest_annual.get('year', '未知')
                fiscal_date = f"{year}年度" if year != '未知' else '未知'
                formatted_parts.append(f"    📊 最新年报 ({fiscal_date}):")

                revenue = latest_annual.get('revenue_sum', 'N/A')  # 年度数据使用 sum 字段
                net_income = latest_annual.get('net_income_sum', 'N/A')
                total_assets = latest_annual.get('total_assets', 'N/A')

                formatted_parts.append(f"      - 年度总收入: {revenue}")
                formatted_parts.append(f"      - 年度净收入: {net_income}")
                formatted_parts.append(f"      - 总资产: {total_assets}")

                # 年度增长率
                if 'yoy_growth' in latest_annual and latest_annual['yoy_growth']:
                    formatted_parts.append("      📈 年度同比增长率:")
                    growth = latest_annual['yoy_growth']
                    for metric, rate in growth.items():
                        if rate is not None:
                            formatted_parts.append(f"        - {metric}: {rate}%")

            # 历史趋势分析（总是显示给FAA）
            quarterly_history = comprehensive_data.get('quarterly_history', [])
            if len(quarterly_history) >= 2:
                formatted_parts.append("    📈 季度趋势分析:")
                formatted_parts.append(f"      - 可用季度数据: {len(quarterly_history)} 个季度")

                # 显示最近几个季度的收入趋势
                recent_quarters = quarterly_history[:4]  # 最近4个季度
                revenue_trend = []
                for q in recent_quarters:
                    q_date = q.get('fiscal_date', q.get('report_date', '未知'))
                    q_revenue = q.get('revenue', 'N/A')
                    if q_revenue != 'N/A':
                        try:
                            revenue_val = float(q_revenue)
                            if revenue_val >= 1e9:
                                revenue_str = f"{revenue_val/1e9:.1f}B"
                            elif revenue_val >= 1e6:
                                revenue_str = f"{revenue_val/1e6:.1f}M"
                            else:
                                revenue_str = str(q_revenue)
                            revenue_trend.append(f"{q_date}: {revenue_str}")
                        except (ValueError, TypeError):
                            revenue_trend.append(f"{q_date}: {q_revenue}")

                if revenue_trend:
                    formatted_parts.append("      - 最近4季度收入:")
                    for trend in revenue_trend:
                        formatted_parts.append(f"        {trend}")

            annual_history = comprehensive_data.get('annual_history', [])
            if len(annual_history) >= 2:
                formatted_parts.append("    📊 年度趋势分析:")
                formatted_parts.append(f"      - 可用年度数据: {len(annual_history)} 年")

                # 显示最近几年的收入和净利润趋势
                recent_years = annual_history[:3]  # 最近3年
                for year_data in recent_years:
                    year = year_data.get('year', '未知')
                    revenue = year_data.get('revenue_sum', 'N/A')
                    net_income = year_data.get('net_income_sum', 'N/A')

                    # 格式化大数字
                    try:
                        if revenue != 'N/A':
                            revenue_val = float(revenue)
                            if revenue_val >= 1e9:
                                revenue_str = f"{revenue_val/1e9:.1f}B"
                            elif revenue_val >= 1e6:
                                revenue_str = f"{revenue_val/1e6:.1f}M"
                            else:
                                revenue_str = str(revenue)
                        else:
                            revenue_str = revenue

                        if net_income != 'N/A':
                            income_val = float(net_income)
                            if income_val >= 1e9:
                                income_str = f"{income_val/1e9:.1f}B"
                            elif income_val >= 1e6:
                                income_str = f"{income_val/1e6:.1f}M"
                            else:
                                income_str = str(net_income)
                        else:
                            income_str = net_income
                    except (ValueError, TypeError):
                        revenue_str = str(revenue)
                        income_str = str(net_income)

                    formatted_parts.append(f"      - {year}年: 收入 {revenue_str}, 净利润 {income_str}")

            # 数据统计
            annual_count = len(comprehensive_data.get('annual_history', []))
            quarterly_count = len(comprehensive_data.get('quarterly_history', []))
            formatted_parts.append(f"    📋 数据统计: {annual_count} 年报, {quarterly_count} 季报")

        except Exception as e:
            formatted_parts.append(f"    ⚠️ 格式化综合财务数据时出错: {e}")

    def get_required_data_fields(self) -> List[str]:
        """
        获取智能体所需的数据字段列表

        子类应该重写此方法来定义其特定的数据需求
        默认返回所有字段以保持向后兼容性

        返回:
            所需数据字段名称列表
        """
        # 默认返回所有可能的字段，保持向后兼容
        return [
            "current_date", "date", "cash", "positions", "position_values",
            "price_history", "news_history", "fundamental_data",
            "trading_day_info", "skip_day", "skip_reason"
        ]

    def _filter_state_data(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        根据智能体需求过滤状态数据

        参数:
            state: 完整的状态字典

        返回:
            过滤后的状态字典
        """
        required_fields = self.get_required_data_fields()
        filtered_state = {}

        for field in required_fields:
            if field in state:
                filtered_state[field] = state[field]

        # 记录过滤信息（包括详细的字段信息）
        if self.detailed_logging:
            original_fields = set(state.keys())
            filtered_fields = set(filtered_state.keys())
            excluded_fields = original_fields - filtered_fields

            # self.logger.debug(f"智能体 {self.agent_id} 数据过滤:")
            # self.logger.debug(f"  - 需要字段: {required_fields}")
            # self.logger.debug(f"  - 保留字段: {list(filtered_fields)}")
            # self.logger.debug(f"  - 排除字段: {list(excluded_fields)}")
            
            # 特别记录对于TAA的新闻数据过滤
            if self.agent_id == "TAA" and "news_history" in excluded_fields:
                self.logger.info(f"✅ TAA数据过滤成功: 已排除新闻数据")
            elif self.agent_id == "TAA" and "news_history" in filtered_fields:
                self.logger.error(f"❌ TAA数据过滤失败: 新闻数据未被正确排除")

        return filtered_state

    def call_llm(self, prompt: str, state: Dict[str, Any], is_full_coalition: bool = False) -> Dict[str, Any]:
        """
        调用LLM进行分析

        参数:
            prompt: 提示词
            state: 当前状态

        返回:
            LLM分析结果
        """
        if not self.llm_interface:
            self.logger.warning(f"智能体 {self.agent_id} 没有LLM接口，返回默认结果")
            return self._get_default_output(state)

        try:
            start_time = datetime.now()

            # 过滤状态数据，只保留该智能体需要的字段
            filtered_state = self._filter_state_data(state)

            # 格式化状态信息（使用详细模式以显示完整信息）
            state_info = self.format_state_for_llm(filtered_state, detailed=True)

            # 构建完整提示词
            full_prompt = f"{prompt}\n\n{state_info}\n\n请基于以上信息进行分析。"
            
            # 调用LLM
            response = self.llm_interface.analyze(prompt=full_prompt, model="glm-4-flash", temperature=0.0, top_p=0.5)
            
            # 验证数据和响应的完整性
            if self._validate_agent_response(response, filtered_state):
                # 数据验证通过，显示简洁完成状态
                if isinstance(response, dict) and "content" in response:
                    content = response.get("content", "")
            else:
                self.logger.warning(f"🤖 {self.agent_id}: 数据验证失败，使用默认响应")
            
            # 记录统计信息
            processing_time = (datetime.now() - start_time).total_seconds()
            self.total_processing_time += processing_time
            self.analysis_count += 1
            
            # 解析响应（避免重复字段）
            if isinstance(response, dict):
                # 如果是字典且包含content字段，说明是文本响应
                if "content" in response and "type" in response and response["type"] == "text_response":
                    result = {"analysis": response["content"]}  # 只保留analysis字段
                else:
                    result = response
            else:
                # 尝试解析JSON响应
                import json
                try:
                    result = json.loads(response)
                except json.JSONDecodeError:
                    result = {"analysis": response}  # 只保留analysis字段
            
            # 添加元数据
            result.update({
                "agent_id": self.agent_id,
                "timestamp": datetime.now().isoformat(),
                "processing_time": processing_time,
                "llm_used": True
            })
            
            self.last_analysis = result
            
            # 自动记录IO数据（如果是支持OPRO的智能体）
            self.logger.info(f"🔍 {self.agent_id}: 检查IO数据记录能力 - hasattr: {hasattr(self, 'record_weekly_io')}, is_full_coalition: {is_full_coalition}")
            if hasattr(self, 'record_weekly_io') and callable(getattr(self, 'record_weekly_io')):
                try:
                    self.logger.info(f"📝 {self.agent_id}: 开始记录IO数据 (is_full_coalition={is_full_coalition})")
                    
                    # 准备完整的输入状态副本
                    input_state_copy = state.copy()
                    
                    # 构建LLM原始响应数据结构
                    llm_raw_response = {
                        "raw_llm_output": response,  # 原始LLM响应
                        "processed_result": result,  # 处理后的结果
                        "processing_time": processing_time,
                        "timestamp": datetime.now().isoformat(),
                        "prompt": full_prompt,
                        "success": True
                    }
                    
                    # 增强IO记录验证机制
                    io_validation_result = self._validate_io_recording_data(input_state_copy, llm_raw_response)
                    self.logger.info(f"🔍 {self.agent_id}: IO数据验证结果 - {io_validation_result}")
                    
                    # 调用记录方法，传递is_full_coalition参数
                    record_method = getattr(self, 'record_weekly_io')
                    record_success = record_method(input_state_copy, llm_raw_response, is_full_coalition=is_full_coalition)
                    
                    # 验证记录结果并记录详细诊断日志
                    if record_success is not None and record_success is False:
                        self.logger.error(f"❌ {self.agent_id}: IO数据记录方法返回失败状态")
                    else:
                        self.logger.info(f"✅ {self.agent_id}: IO数据记录完成")
                        self._log_io_recording_diagnostics(input_state_copy, llm_raw_response)
                    
                except Exception as record_e:
                    # 记录失败不应该影响主要功能
                    self.logger.error(f"❌ {self.agent_id}: IO数据自动记录失败: {record_e}")
                    import traceback
                    self.logger.error(f"异常详情: {traceback.format_exc()}")
            else:
                self.logger.warning(f"⚠️ {self.agent_id}: 不支持IO数据记录 - hasattr: {hasattr(self, 'record_weekly_io')}")
            
            return result
            
        except Exception as e:
            self.logger.error(f"智能体 {self.agent_id} LLM调用失败: {e}")
            return self._get_default_output(state)
    
    def _get_default_output(self, state: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取默认输出（当LLM不可用时）
        
        参数:
            state: 当前状态
            
        返回:
            默认分析结果
        """
        return {
            "agent_id": self.agent_id,
            "analysis": f"默认分析 - {self.agent_id}",
            "confidence": 0.5,
            "reasoning": "LLM不可用，使用默认分析",
            "timestamp": datetime.now().isoformat(),
            "llm_used": False
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取智能体统计信息

        返回:
            统计信息字典
        """
        avg_processing_time = (
            self.total_processing_time / self.analysis_count
            if self.analysis_count > 0 else 0.0
        )

        return {
            "agent_id": self.agent_id,
            "analysis_count": self.analysis_count,
            "total_processing_time": self.total_processing_time,
            "average_processing_time": avg_processing_time,
            "has_llm": self.llm_interface is not None,
            "last_analysis_time": (
                self.last_analysis.get("timestamp")
                if self.last_analysis else None
            )
        }

    def create_serializable_snapshot(self) -> Dict[str, Any]:
        """
        创建智能体的可序列化快照

        这个方法只包含可以安全序列化的属性，避免线程锁等不可序列化对象

        返回:
            可序列化的智能体状态字典
        """
        snapshot = {
            # 基本标识信息
            "agent_id": self.agent_id,
            "agent_type": type(self).__name__,
            "agent_class": f"{type(self).__module__}.{type(self).__name__}",

            # 状态信息
            "analysis_count": self.analysis_count,
            "total_processing_time": self.total_processing_time,
            "detailed_logging": getattr(self, 'detailed_logging', False),

            # 时间戳
            "snapshot_timestamp": datetime.now().isoformat(),

            # 标记这是一个序列化快照
            "_is_serialized_snapshot": True,
        }

        # 安全地添加最后分析结果（如果存在且可序列化）
        if self.last_analysis:
            try:
                # 只保存可序列化的部分
                serializable_analysis = {}
                for key, value in self.last_analysis.items():
                    if isinstance(value, (str, int, float, bool, list, dict, type(None))):
                        serializable_analysis[key] = value
                    elif hasattr(value, 'isoformat'):  # datetime对象
                        serializable_analysis[key] = value.isoformat()
                    else:
                        serializable_analysis[key] = str(value)

                snapshot["last_analysis"] = serializable_analysis
            except Exception as e:
                # 如果序列化失败，记录错误但不中断
                snapshot["last_analysis_error"] = str(e)

        # 尝试获取提示词模板（如果可用）
        try:
            if hasattr(self, 'get_prompt_template'):
                snapshot["prompt_template"] = self.get_prompt_template()
        except Exception:
            # 如果获取失败，跳过
            pass

        # 添加其他可序列化的自定义属性
        custom_attrs = self._get_custom_serializable_attributes()
        if custom_attrs:
            snapshot.update(custom_attrs)

        return snapshot

    def _get_custom_serializable_attributes(self) -> Dict[str, Any]:
        """
        获取子类特定的可序列化属性

        子类可以重写此方法来添加自己的可序列化属性

        返回:
            自定义可序列化属性字典
        """
        return {}

    @classmethod
    def restore_from_snapshot(cls, snapshot: Dict[str, Any], llm_interface=None, logger=None):
        """
        从序列化快照恢复智能体实例

        注意：这只是一个基础实现，子类可能需要重写此方法

        参数:
            snapshot: 序列化快照
            llm_interface: LLM接口实例
            logger: 日志记录器

        返回:
            恢复的智能体实例
        """
        if not snapshot.get("_is_serialized_snapshot"):
            raise ValueError("无效的序列化快照")

        # 创建新实例
        agent_id = snapshot["agent_id"]
        instance = cls(agent_id=agent_id, llm_interface=llm_interface, logger=logger)

        # 恢复状态
        instance.analysis_count = snapshot.get("analysis_count", 0)
        instance.total_processing_time = snapshot.get("total_processing_time", 0.0)
        instance.detailed_logging = snapshot.get("detailed_logging", False)

        # 恢复最后分析结果
        if "last_analysis" in snapshot:
            instance.last_analysis = snapshot["last_analysis"]

        return instance

    def __getstate__(self):
        """自定义pickle序列化行为"""
        # 创建状态字典，排除不可序列化的对象
        state = self.__dict__.copy()

        # 移除不可序列化的对象
        # LLM接口通常包含线程锁或其他不可序列化对象
        if 'llm_interface' in state:
            state['llm_interface'] = None

        # 移除logger（通常包含不可序列化的handler）
        if 'logger' in state:
            state['logger'] = None

        # 添加序列化标记
        state['_was_pickled'] = True

        return state

    def __setstate__(self, state):
        """自定义pickle反序列化行为"""
        # 恢复状态
        self.__dict__.update(state)

        # 重新创建不可序列化的对象
        if self.logger is None:
            self.logger = self._create_default_logger()

        # LLM接口需要在使用时重新设置
        # 这里不重新创建，因为需要外部传入

        # 移除序列化标记
        if hasattr(self, '_was_pickled'):
            delattr(self, '_was_pickled')

    def set_llm_interface(self, llm_interface):
        """设置LLM接口（用于反序列化后重新设置）"""
        self.llm_interface = llm_interface
    
    def _validate_agent_response(self, response: Any, state: Dict[str, Any]) -> bool:
        """
        验证智能体是否收到了真实数据并正确响应
        
        参数:
            response: 智能体响应
            state: 输入状态数据
            
        返回:
            bool: 验证通过返回True，否则返回False
        """
        try:
            # 验证输入数据的真实性
            if not self._validate_input_data(state):
                return False
            
            # 验证响应的完整性
            if not self._validate_response_completeness(response):
                return False
            
            return True
            
        except Exception as e:
            self.logger.warning(f"智能体 {self.agent_id} 数据验证异常: {e}")
            return False
    
    def _validate_input_data(self, state: Dict[str, Any]) -> bool:
        """
        验证输入数据的真实性
        
        参数:
            state: 输入状态数据
            
        返回:
            bool: 数据验证通过返回True
        """
        # 检查基本字段
        if not state.get("current_date"):
            return False
        
        # 根据智能体类型验证相应数据
        if self.agent_id == "NAA":
            # 新闻分析智能体：检查新闻数据，但允许无新闻的情况
            news_history = state.get("news_history", {})
            if not news_history:
                # 没有新闻历史数据，但这是可以接受的
                self.logger.debug(f"NAA: 无新闻历史数据，将提供基于无新闻的分析")
                return True
            # 检查是否有实际新闻内容
            has_news = any(
                isinstance(news_data, dict) and any(
                    isinstance(news_list, list) and len(news_list) > 0
                    for news_list in news_data.values()
                )
                for news_data in news_history.values()
            )
            # 无论是否有新闻，都允许通过验证
            if not has_news:
                self.logger.debug(f"NAA: 新闻数据为空，将提供基于无新闻的分析")
            return True
            
        elif self.agent_id == "TAA":
            # 技术分析智能体需要价格数据
            price_history = state.get("price_history", {})
            if not price_history:
                return False
            # 检查是否有实际价格数据
            has_prices = any(
                isinstance(prices, list) and len(prices) > 0
                for prices in price_history.values()
            )
            return has_prices
            
        elif self.agent_id == "FAA":
            # 基本面分析智能体：检查基本面数据，但允许无数据的情况
            fundamental_data = state.get("fundamental_data", {})
            if not fundamental_data:
                # 没有基本面数据，但这是可以接受的
                self.logger.debug(f"FAA: 无基本面数据，将提供基于无数据的分析")
                return True
            # 检查是否有实际基本面数据
            has_fundamentals = any(
                isinstance(fund_data, dict) and fund_data
                for fund_data in fundamental_data.values()
            )
            # 无论是否有基本面数据，都允许通过验证
            if not has_fundamentals:
                self.logger.debug(f"FAA: 基本面数据为空，将提供基于无数据的分析")
            return True
            
        elif self.agent_id == "TRA":
            # 交易智能体需要现金和持仓信息
            return "cash" in state and "positions" in state
            
        # 其他智能体的基本验证
        return True
    
    def _validate_response_completeness(self, response: Any) -> bool:
        """
        验证响应的完整性

        参数:
            response: 智能体响应

        返回:
            bool: 响应完整性验证通过返回True
        """
        if not response:
            return False

        # 检查响应格式
        if isinstance(response, dict):
            # 检查是否有内容字段（通用文本响应）
            if "content" in response:
                content = response["content"]
                # 检查内容是否非空且有意义
                if not content or len(str(content).strip()) < 10:
                    return False
            elif "analysis" in response:
                analysis = response["analysis"]
                if not analysis or len(str(analysis).strip()) < 10:
                    return False
            else:
                # 检查是否为结构化响应（如FAA的JSON格式响应）
                # 对于结构化响应，检查是否有足够的有效字段
                valid_fields = 0
                for key, value in response.items():
                    # 跳过元数据字段
                    if key in ['agent_id', 'timestamp', 'processing_time', 'llm_used']:
                        continue
                    # 检查字段值是否有效
                    if value is not None and str(value).strip():
                        valid_fields += 1

                # 如果有至少3个有效字段，认为是有效的结构化响应
                if valid_fields < 3:
                    return False
        elif isinstance(response, str):
            # 字符串响应需要有足够的内容
            if len(response.strip()) < 10:
                return False
        else:
            return False

        return True
    
    def _assess_fundamental_data_quality(self, fund_data: Dict[str, Any]) -> str:
        """
        评估基本面数据质量
        
        参数:
            fund_data: 基本面数据字典
            
        返回:
            数据质量评估 ("完整", "部分", "缺失")
        """
        if not fund_data:
            return "缺失"
        
        # 检查主要数据结构是否存在
        current_data = fund_data.get('current', {})
        quarterly_history = fund_data.get('quarterly_history', [])
        annual_history = fund_data.get('annual_history', [])
        
        # 评估当前数据质量
        current_score = 0
        if current_data:
            key_metrics = ['revenue', 'net_income', 'total_assets', 'eps']
            available_metrics = sum(1 for metric in key_metrics 
                                 if current_data.get(metric) not in [None, 'N/A', '', 0])
            current_score = available_metrics / len(key_metrics)
        
        # 评估历史数据质量
        quarterly_score = min(len(quarterly_history) / 4, 1.0)  # 至少4个季度算部分完整
        annual_score = min(len(annual_history) / 3, 1.0)  # 至少3年算部分完整
        
        # 综合评分
        total_score = (current_score * 0.5 + quarterly_score * 0.3 + annual_score * 0.2)
        
        if total_score >= 0.8:
            return "完整"
        elif total_score >= 0.3:
            return "部分"
        else:
            return "缺失"
    
    def _validate_io_recording_data(self, input_state: Dict[str, Any], llm_response: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证IO记录数据的完整性和有效性
        
        参数:
            input_state: 输入状态数据
            llm_response: LLM响应数据
            
        返回:
            验证结果字典
        """
        validation_result = {
            "input_valid": False,
            "response_valid": False,
            "input_size": 0,
            "response_size": 0,
            "data_types": {},
            "validation_timestamp": datetime.now().isoformat()
        }
        
        try:
            # 验证输入状态数据
            if input_state:
                validation_result["input_valid"] = True
                validation_result["input_size"] = len(str(input_state))
                validation_result["data_types"]["input_keys"] = list(input_state.keys())
                
                # 检查关键数据字段
                key_fields = ["current_date", "cash", "positions"]
                available_key_fields = [field for field in key_fields if field in input_state]
                validation_result["data_types"]["available_key_fields"] = available_key_fields
            
            # 验证LLM响应数据
            if llm_response:
                validation_result["response_valid"] = True
                validation_result["response_size"] = len(str(llm_response))
                validation_result["data_types"]["response_keys"] = list(llm_response.keys())
                
                # 检查响应数据的核心组件
                if "raw_llm_output" in llm_response:
                    raw_output = llm_response["raw_llm_output"]
                    validation_result["data_types"]["raw_output_type"] = type(raw_output).__name__
                    if isinstance(raw_output, str):
                        validation_result["data_types"]["raw_output_length"] = len(raw_output)
                    elif isinstance(raw_output, dict):
                        validation_result["data_types"]["raw_output_keys"] = list(raw_output.keys())
                
        except Exception as e:
            validation_result["validation_error"] = str(e)
        
        return validation_result
    
    def _log_io_recording_diagnostics(self, input_state: Dict[str, Any], llm_response: Dict[str, Any]) -> None:
        """
        记录IO数据记录的详细诊断日志
        
        参数:
            input_state: 输入状态数据
            llm_response: LLM响应数据
        """
        try:
            # 记录数据量统计
            input_size = len(str(input_state)) if input_state else 0
            response_size = len(str(llm_response)) if llm_response else 0
            
            self.logger.info(f"📊 {self.agent_id}: IO数据统计 - 输入大小: {input_size:,} 字符, 响应大小: {response_size:,} 字符")
            
            # 记录数据类型信息
            if input_state:
                input_keys_count = len(input_state.keys())
                main_data_fields = [key for key in input_state.keys() 
                                  if key in ["price_history", "news_history", "fundamental_data"]]
                self.logger.info(f"📋 {self.agent_id}: 输入数据类型 - 总字段数: {input_keys_count}, 主要数据字段: {main_data_fields}")
            
            if llm_response:
                response_components = list(llm_response.keys())
                has_raw_output = "raw_llm_output" in llm_response
                has_processed_result = "processed_result" in llm_response
                self.logger.info(f"🔍 {self.agent_id}: 响应组件 - {response_components}, 原始输出: {has_raw_output}, 处理结果: {has_processed_result}")
            
            # 记录成功状态
            self.logger.info(f"✅ {self.agent_id}: IO记录诊断完成 - 数据写入验证通过")
            
        except Exception as e:
            self.logger.error(f"❌ {self.agent_id}: IO记录诊断日志失败: {e}")