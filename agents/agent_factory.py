"""
智能体工厂 (Agent Factory)

负责创建和管理所有智能体实例
"""

from typing import Dict, Any, Optional, List
import logging
from .analyst_agents import NewsAnalystAgent, TechnicalAnalystAgent, FundamentalAnalystAgent
from .outlook_agents import BullishOutlookAgent, BearishOutlookAgent, NeutralObserverAgent
from .trader_agent import TraderAgent


class AgentFactory:
    """智能体工厂类"""
    
    def __init__(self, llm_interface=None, logger: Optional[logging.Logger] = None, opro_enabled: bool = False):
        """
        初始化智能体工厂

        参数:
            llm_interface: LLM接口实例
            logger: 日志记录器
            opro_enabled: 是否启用OPRO优化功能
        """
        self.llm_interface = llm_interface
        self.logger = logger or self._create_default_logger()
        self.opro_enabled = opro_enabled
        
        # 智能体类映射
        self.agent_classes = {
            "NAA": NewsAnalystAgent,
            "TAA": TechnicalAnalystAgent,
            "FAA": FundamentalAnalystAgent,
            "BOA": BullishOutlookAgent,
            "BeOA": BearishOutlookAgent,
            "NOA": NeutralObserverAgent,
            "TRA": TraderAgent
        }
        
        # self.logger.info("智能体工厂初始化完成")
    
    def _create_default_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger("agents.AgentFactory")
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger
    
    def create_agent(self, agent_id: str):
        """
        创建单个智能体

        参数:
            agent_id: 智能体ID

        返回:
            智能体实例
        """
        if agent_id not in self.agent_classes:
            raise ValueError(f"未知的智能体ID: {agent_id}")

        agent_class = self.agent_classes[agent_id]
        agent = agent_class(
            llm_interface=self.llm_interface,
            logger=self.logger,
            opro_enabled=self.opro_enabled
        )

        self.logger.info(f"创建智能体: {agent_id} (OPRO: {'启用' if self.opro_enabled else '禁用'})")
        return agent
    
    def create_all_agents(self) -> Dict[str, Any]:
        """
        创建所有智能体
        
        返回:
            智能体字典 {agent_id: agent_instance}
        """
        agents = {}
        
        for agent_id in self.agent_classes.keys():
            try:
                agents[agent_id] = self.create_agent(agent_id)
                self.logger.info(f"✅ 智能体 {agent_id} 创建成功")
            except Exception as e:
                self.logger.error(f"❌ 智能体 {agent_id} 创建失败: {e}")
        
        self.logger.info(f"创建完成，共 {len(agents)} 个智能体")
        return agents
    
    def create_agents_subset(self, agent_ids: List[str]) -> Dict[str, Any]:
        """
        创建指定的智能体子集
        
        参数:
            agent_ids: 要创建的智能体ID列表
            
        返回:
            智能体字典
        """
        agents = {}
        
        for agent_id in agent_ids:
            try:
                agents[agent_id] = self.create_agent(agent_id)
                self.logger.info(f"✅ 智能体 {agent_id} 创建成功")
            except Exception as e:
                self.logger.error(f"❌ 智能体 {agent_id} 创建失败: {e}")
        
        self.logger.info(f"创建完成，共 {len(agents)} 个智能体")
        return agents
    
    def get_available_agents(self) -> List[str]:
        """
        获取可用的智能体ID列表
        
        返回:
            智能体ID列表
        """
        return list(self.agent_classes.keys())
    
    def validate_agents(self, agents: Dict[str, Any]) -> Dict[str, bool]:
        """
        验证智能体是否正常工作
        
        参数:
            agents: 智能体字典
            
        返回:
            验证结果字典 {agent_id: is_valid}
        """
        validation_results = {}
        
        for agent_id, agent in agents.items():
            try:
                # 简单的验证：检查智能体是否有必要的方法
                if hasattr(agent, 'process') and hasattr(agent, 'get_prompt_template'):
                    validation_results[agent_id] = True
                    self.logger.info(f"✅ 智能体 {agent_id} 验证通过")
                else:
                    validation_results[agent_id] = False
                    self.logger.warning(f"⚠️ 智能体 {agent_id} 缺少必要方法")
            except Exception as e:
                validation_results[agent_id] = False
                self.logger.error(f"❌ 智能体 {agent_id} 验证失败: {e}")
        
        return validation_results
    
    # 周期性优化和热更新支持
    def create_optimizable_agents(self, agent_ids: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        创建支持优化的智能体集合
        
        这些智能体具有提示词热更新能力，支持运行时优化
        
        参数:
            agent_ids: 要创建的智能体ID列表，如果为None则创建所有智能体
            
        返回:
            支持优化的智能体字典
        """
        target_ids = agent_ids or list(self.agent_classes.keys())
        agents = {}
        
        for agent_id in target_ids:
            try:
                agent = self.create_agent(agent_id)
                
                # 为智能体添加优化支持功能
                self._enhance_agent_for_optimization(agent, agent_id)
                
                agents[agent_id] = agent
                self.logger.info(f"✅ 可优化智能体 {agent_id} 创建成功")
                
            except Exception as e:
                self.logger.error(f"❌ 可优化智能体 {agent_id} 创建失败: {e}")
        
        self.logger.info(f"可优化智能体创建完成，共 {len(agents)} 个")
        return agents
    
    def _enhance_agent_for_optimization(self, agent: Any, agent_id: str):
        """
        为智能体增强优化支持功能
        
        参数:
            agent: 智能体实例
            agent_id: 智能体ID
        """
        # 添加优化历史跟踪
        agent._optimization_history = []
        agent._current_prompt_version = 1
        agent._original_prompt = getattr(agent, 'prompt_template', '')
        agent._agent_id = agent_id
        
        # 添加热更新方法
        agent.update_prompt = self._create_update_prompt_method(agent)
        agent.get_prompt_history = self._create_get_prompt_history_method(agent)
        agent.rollback_prompt = self._create_rollback_prompt_method(agent)
        agent.get_optimization_stats = self._create_get_optimization_stats_method(agent)
        
        # 标记为支持优化
        agent._supports_optimization = True
        
        self.logger.debug(f"智能体 {agent_id} 已增强优化支持")
    
    def _create_update_prompt_method(self, agent: Any):
        """
        为智能体创建更新提示词的方法
        
        参数:
            agent: 智能体实例
            
        返回:
            update_prompt 方法
        """
        def update_prompt(new_prompt: str, version_note: str = ""):
            """
            更新智能体提示词
            
            参数:
                new_prompt: 新的提示词
                version_note: 版本说明
            """
            if not new_prompt or new_prompt.strip() == "":
                self.logger.warning(f"智能体 {agent._agent_id} 尝试更新为空提示词，忽略更新")
                return False
            
            # 记录旧提示词
            old_prompt = getattr(agent, 'prompt_template', '')
            
            # 更新版本历史
            agent._optimization_history.append({
                "version": agent._current_prompt_version,
                "old_prompt": old_prompt,
                "new_prompt": new_prompt,
                "timestamp": self._get_timestamp(),
                "note": version_note,
                "change_summary": self._calculate_prompt_diff_summary(old_prompt, new_prompt)
            })
            
            # 更新提示词
            agent.prompt_template = new_prompt
            agent._current_prompt_version += 1
            
            self.logger.info(f"🔄 智能体 {agent._agent_id} 提示词已更新到版本 {agent._current_prompt_version}")
            if version_note:
                self.logger.info(f"    更新说明: {version_note}")
            
            return True
        
        return update_prompt
    
    def _create_get_prompt_history_method(self, agent: Any):
        """
        为智能体创建获取提示词历史的方法
        
        参数:
            agent: 智能体实例
            
        返回:
            get_prompt_history 方法
        """
        def get_prompt_history():
            """
            获取提示词更新历史
            
            返回:
                提示词历史列表
            """
            return agent._optimization_history.copy()
        
        return get_prompt_history
    
    def _create_rollback_prompt_method(self, agent: Any):
        """
        为智能体创建回滚提示词的方法
        
        参数:
            agent: 智能体实例
            
        返回:
            rollback_prompt 方法
        """
        def rollback_prompt(target_version: Optional[int] = None):
            """
            回滚提示词到指定版本
            
            参数:
                target_version: 目标版本号，如果为None则回滚到上一版本
                
            返回:
                是否回滚成功
            """
            if not agent._optimization_history:
                self.logger.warning(f"智能体 {agent._agent_id} 没有可回滚的历史版本")
                return False
            
            if target_version is None:
                # 回滚到上一版本
                if len(agent._optimization_history) > 0:
                    last_record = agent._optimization_history[-1]
                    target_prompt = last_record["old_prompt"]
                    target_version = last_record["version"]
                else:
                    target_prompt = agent._original_prompt
                    target_version = 0
            else:
                # 回滚到指定版本
                target_record = None
                for record in agent._optimization_history:
                    if record["version"] == target_version:
                        target_record = record
                        break
                
                if target_record is None:
                    self.logger.error(f"智能体 {agent._agent_id} 找不到版本 {target_version}")
                    return False
                
                target_prompt = target_record["old_prompt"]
            
            # 执行回滚
            agent.prompt_template = target_prompt
            
            # 记录回滚操作
            agent._optimization_history.append({
                "version": agent._current_prompt_version,
                "old_prompt": getattr(agent, 'prompt_template', ''),
                "new_prompt": target_prompt,
                "timestamp": self._get_timestamp(),
                "note": f"回滚到版本 {target_version}",
                "change_summary": "rollback",
                "is_rollback": True,
                "rollback_target": target_version
            })
            
            agent._current_prompt_version += 1
            
            self.logger.info(f"↩️ 智能体 {agent._agent_id} 已回滚到版本 {target_version}")
            return True
        
        return rollback_prompt
    
    def _create_get_optimization_stats_method(self, agent: Any):
        """
        为智能体创建获取优化统计的方法
        
        参数:
            agent: 智能体实例
            
        返回:
            get_optimization_stats 方法
        """
        def get_optimization_stats():
            """
            获取智能体优化统计信息
            
            返回:
                优化统计字典
            """
            history = agent._optimization_history
            
            stats = {
                "agent_id": agent._agent_id,
                "current_version": agent._current_prompt_version,
                "total_updates": len(history),
                "rollback_count": len([h for h in history if h.get("is_rollback", False)]),
                "first_update": history[0]["timestamp"] if history else None,
                "last_update": history[-1]["timestamp"] if history else None,
                "supports_optimization": getattr(agent, '_supports_optimization', False),
                "has_original_prompt": bool(getattr(agent, '_original_prompt', '')),
                "current_prompt_length": len(getattr(agent, 'prompt_template', '')),
                "original_prompt_length": len(getattr(agent, '_original_prompt', ''))
            }
            
            return stats
        
        return get_optimization_stats
    
    def _get_timestamp(self) -> str:
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().isoformat()
    
    def _calculate_prompt_diff_summary(self, old_prompt: str, new_prompt: str) -> str:
        """
        计算提示词差异摘要
        
        参数:
            old_prompt: 旧提示词
            new_prompt: 新提示词
            
        返回:
            差异摘要字符串
        """
        if not old_prompt and new_prompt:
            return "initial_prompt"
        
        if old_prompt == new_prompt:
            return "no_change"
        
        old_len = len(old_prompt)
        new_len = len(new_prompt)
        
        if new_len > old_len:
            return f"expanded (+{new_len - old_len} chars)"
        elif new_len < old_len:
            return f"shortened (-{old_len - new_len} chars)"
        else:
            return "modified (same length)"
    
    def update_multiple_agents(self, 
                             agents: Dict[str, Any], 
                             prompt_updates: Dict[str, str],
                             batch_note: str = "") -> Dict[str, bool]:
        """
        批量更新多个智能体的提示词
        
        参数:
            agents: 智能体字典
            prompt_updates: 提示词更新字典 {agent_id: new_prompt}
            batch_note: 批次更新说明
            
        返回:
            更新结果字典 {agent_id: success}
        """
        results = {}
        
        self.logger.info(f"🔄 开始批量更新 {len(prompt_updates)} 个智能体")
        if batch_note:
            self.logger.info(f"批次说明: {batch_note}")
        
        for agent_id, new_prompt in prompt_updates.items():
            if agent_id not in agents:
                self.logger.warning(f"智能体 {agent_id} 不存在，跳过更新")
                results[agent_id] = False
                continue
            
            agent = agents[agent_id]
            
            # 检查智能体是否支持优化
            if not getattr(agent, '_supports_optimization', False):
                self.logger.warning(f"智能体 {agent_id} 不支持优化，跳过更新")
                results[agent_id] = False
                continue
            
            try:
                success = agent.update_prompt(new_prompt, f"批量更新: {batch_note}")
                results[agent_id] = success
            except Exception as e:
                self.logger.error(f"智能体 {agent_id} 更新失败: {e}")
                results[agent_id] = False
        
        successful_updates = sum(1 for success in results.values() if success)
        self.logger.info(f"✅ 批量更新完成: {successful_updates}/{len(prompt_updates)} 成功")
        
        return results
    
    def get_agents_optimization_summary(self, agents: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取智能体优化摘要
        
        参数:
            agents: 智能体字典
            
        返回:
            优化摘要字典
        """
        summary = {
            "total_agents": len(agents),
            "optimization_enabled_agents": 0,
            "agents_with_updates": 0,
            "total_updates": 0,
            "total_rollbacks": 0,
            "agent_details": {}
        }
        
        for agent_id, agent in agents.items():
            if getattr(agent, '_supports_optimization', False):
                summary["optimization_enabled_agents"] += 1
                
                stats = agent.get_optimization_stats()
                summary["agent_details"][agent_id] = stats
                
                if stats["total_updates"] > 0:
                    summary["agents_with_updates"] += 1
                
                summary["total_updates"] += stats["total_updates"]
                summary["total_rollbacks"] += stats["rollback_count"]
            else:
                summary["agent_details"][agent_id] = {
                    "supports_optimization": False,
                    "reason": "not_enhanced_for_optimization"
                }
        
        return summary
    
    def export_agents_configuration(self, 
                                  agents: Dict[str, Any], 
                                  output_path: str) -> bool:
        """
        导出智能体配置
        
        参数:
            agents: 智能体字典
            output_path: 输出文件路径
            
        返回:
            是否导出成功
        """
        try:
            export_data = {
                "export_timestamp": self._get_timestamp(),
                "optimization_summary": self.get_agents_optimization_summary(agents),
                "agent_configurations": {}
            }
            
            for agent_id, agent in agents.items():
                agent_config = {
                    "agent_id": agent_id,
                    "current_prompt": getattr(agent, 'prompt_template', ''),
                    "supports_optimization": getattr(agent, '_supports_optimization', False)
                }
                
                if getattr(agent, '_supports_optimization', False):
                    agent_config.update({
                        "original_prompt": getattr(agent, '_original_prompt', ''),
                        "optimization_history": getattr(agent, '_optimization_history', []),
                        "optimization_stats": agent.get_optimization_stats()
                    })
                
                export_data["agent_configurations"][agent_id] = agent_config
            
            import json
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"📁 智能体配置已导出: {output_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出智能体配置失败: {e}")
            return False