"""
分析层智能体 (Analyst Agents)

包含新闻分析、技术分析和基本面分析智能体的具体实现
"""

from typing import Dict, Any, List, Optional
import logging
from datetime import datetime
from .opro_base_agent import OPROBaseAgent


class NewsAnalystAgent(OPROBaseAgent):
    """新闻分析智能体 (NAA)"""
    
    def __init__(self, llm_interface=None, logger=None, opro_enabled=False, opro_optimizer=None):
        super().__init__("NAA", llm_interface, logger, opro_enabled, opro_optimizer)

    def get_required_data_fields(self) -> List[str]:
        """NAA只需要新闻相关数据"""
        return ["news_history", "current_date", "date"]

    def get_default_prompt_template(self) -> str:
        return """你是一个新闻分析智能体（NAA），在多智能体金融分析系统的分析层工作。

层级定位：
- 你属于分析层，只能访问原始市场数据（新闻数据）
- 你不能访问其他智能体的分析结果
- 你的输出将被展望层智能体（BOA、BeOA、NOA）使用

你的任务是：
1. 分析当前的市场新闻和舆论情绪
2. 评估新闻对目标股票的影响程度
3. 量化市场情绪（-1到1，-1极度悲观，1极度乐观）
4. 提供新闻摘要和关键事件

请提供分析结果，包含：
- 情绪评分（-1到1）
- 新闻摘要
- 关键事件列表
- 影响评估
- 分析信心度（0到1）"""
    
    def process(self, state: Dict[str, Any], is_full_coalition: bool = False) -> Dict[str, Any]:
        """处理新闻分析"""
        prompt = self.get_prompt_template()
        return self.call_llm(prompt, state, is_full_coalition=is_full_coalition)


class TechnicalAnalystAgent(OPROBaseAgent):
    """技术分析智能体 (TAA) - 作为交易日检测的第一道防线"""
    
    def __init__(self, llm_interface=None, logger=None, opro_enabled=False, opro_optimizer=None):
        super().__init__("TAA", llm_interface, logger, opro_enabled, opro_optimizer)

    def get_required_data_fields(self) -> List[str]:
        """TAA只需要价格数据"""
        return ["price_history", "current_date", "date"]

    def get_default_prompt_template(self) -> str:
        return """你是一个技术分析智能体（TAA），在多智能体金融分析系统的分析层工作。

层级定位：
- 你属于分析层，只能访问原始市场数据（价格数据、持仓信息）
- 你不能访问其他智能体的分析结果
- 你的输出将被展望层智能体（BOA、BeOA、NOA）使用

你的首要任务是验证交易日的有效性：
1. 检查当前日期是否为有效交易日（是否有价格数据）
2. 如果没有价格数据，请返回 skip_day: true

如果是有效交易日，请进行技术分析：
1. 分析股票的价格趋势和动量
2. 识别关键的支撑位和阻力位
3. 评估技术指标信号（RSI、MACD、移动平均线等）
4. 判断当前技术面是看涨、看跌还是中性

请提供分析结果，包含：
- 是否跳过该日（true/false）
- 如果跳过，说明原因
- 趋势方向（bullish/bearish/neutral）
- 支撑位价格
- 阻力位价格
- 技术评分（-1到1）
- 关键技术指标分析
- 分析信心度（0到1）"""
    
    def process(self, state: Dict[str, Any], is_full_coalition: bool = False) -> Dict[str, Any]:
        """处理技术分析并检测交易日有效性"""
        
        # 首先检查是否有价格数据
        price_history = state.get('price_history', {})
        has_price_data = False
        
        for stock, price_data in price_history.items():
            if price_data and len(price_data) > 0:
                has_price_data = True
                break
        
        # 如果没有价格数据，返回跳过标记
        if not has_price_data:
            self.logger.info(f"TAA检测到无价格数据，建议跳过当前日期: {state.get('current_date', '未知')}")
            return {
                "agent_id": self.agent_id,
                "skip_day": True,
                "reason": "无价格数据，可能为休市日",
                "analysis": "无法进行技术分析，建议跳过",
                "confidence": 1.0,
                "timestamp": datetime.now().isoformat(),
                "llm_used": False
            }
        
        # 有价格数据，进行正常的技术分析
        prompt = self.get_prompt_template()
        result = self.call_llm(prompt, state, is_full_coalition=is_full_coalition)
        
        # 确保返回结果包含skip_day标记
        if isinstance(result, dict):
            result["skip_day"] = False
        
        return result


class FundamentalAnalystAgent(OPROBaseAgent):
    """基本面分析智能体 (FAA)"""
    
    def __init__(self, llm_interface=None, logger=None, opro_enabled=False, opro_optimizer=None):
        super().__init__("FAA", llm_interface, logger, opro_enabled, opro_optimizer)

    def get_required_data_fields(self) -> List[str]:
        """FAA只需要基本面数据"""
        return ["fundamental_data", "current_date", "date"]

    def get_default_prompt_template(self) -> str:
        return """你是一个基本面分析智能体（FAA），在多智能体金融分析系统的分析层工作。

层级定位：
- 你属于分析层，只能访问原始市场数据（基本面数据）
- 你不能访问其他智能体的分析结果
- 你的输出将被展望层智能体（BOA、BeOA、NOA）使用

**重要提醒**：如果基本面数据部分为空或缺少具体财务指标，请明确指出数据不足，不要基于假设进行分析。

你的任务是：
1. **财务健康状况评估**：基于提供的财务数据分析公司的财务稳健性
2. **行业地位分析**：评估公司在所属行业中的竞争地位和优势
3. **估值水平判断**：根据财务指标确定当前股价是高估、低估还是合理
4. **长期投资价值**：分析公司的长期增长潜力和投资价值

**输出格式要求**（必须严格按照JSON格式返回）：
```json
{
  "valuation_assessment": "高估/低估/合理",
  "financial_health_score": 数值(0-10),
  "competitive_position": "领先/中等/落后",
  "long_term_outlook": "正面/中性/负面",
  "intrinsic_value_estimate": "具体数值或N/A",
  "confidence_level": 数值(0.0-1.0),
  "data_quality": "完整/部分/缺失",
  "analysis_reasoning": "详细分析理由",
  "key_financial_metrics": {
    "revenue": "收入数据或N/A",
    "profit_margin": "利润率或N/A",
    "debt_ratio": "负债比率或N/A",
    "roe": "净资产收益率或N/A"
  }
}
```

**数据质量要求**：
- 如果基本面数据完整（包含收入、利润、资产负债等关键指标），进行详细分析
- 如果数据部分缺失，基于可用数据进行有限分析并标注不确定性
- 如果数据完全缺失或为空，返回"数据不足，无法进行有效分析"

请严格基于提供的实际财务数据进行分析，避免基于一般假设或历史印象进行推测。"""
    
    def process(self, state: Dict[str, Any], is_full_coalition: bool = False) -> Dict[str, Any]:
        """处理基本面分析"""
        prompt = self.get_prompt_template()
        return self.call_llm(prompt, state, is_full_coalition=is_full_coalition)