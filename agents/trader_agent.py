"""
交易决策智能体 (Trader Agent)

最终的交易决策制定者，综合所有分析做出具体的交易行动
"""

from typing import Dict, Any, List, Optional
import logging
from datetime import datetime
from .opro_base_agent import OPROBaseAgent
from .base_agent import BaseAgent


class TraderAgent(OPROBaseAgent):
    """交易决策智能体 (TRA)"""
    
    def __init__(self, llm_interface=None, logger=None, opro_enabled=False, opro_optimizer=None):
        super().__init__("TRA", llm_interface, logger, opro_enabled, opro_optimizer)

    def get_required_data_fields(self) -> List[str]:
        """TRA需要前序层级的输出来做交易决策"""
        # 交易智能体只需要前序层级的输出
        return ["current_date", "date", "cash", "positions", "position_values",
                "analyst_outputs", "outlook_outputs", "trading_day_info", "skip_day", "skip_reason"]

    def get_default_prompt_template(self) -> str:
        return """你是一个交易决策智能体（TRA），在多智能体金融分析系统的决策层工作。

层级定位：
- 你属于决策层，可以访问所有前序层级的输出
- 分析层智能体：NAA（新闻分析）、TAA（技术分析）、FAA（基本面分析）
- 展望层智能体：BOA（看涨展望）、BeOA（看跌展望）、NOA（中性展望）
- 你的输出是最终的交易决策

**核心决策逻辑：多维度综合分析，权衡各层级信息**

## 综合决策框架：

### 1. **基础面评估**
- **FAA基本面分析**：评估公司财务健康度、估值合理性
- **NAA新闻分析**：识别重大事件影响、市场情绪变化

### 2. **技术面分析**
- **TAA技术分析**：趋势方向、支撑阻力、动量指标

### 3. **展望层综合**
- **BOA看涨信号强度**：上涨概率和目标空间
- **BeOA看跌信号强度**：下跌风险和幅度预期  
- **NOA中性观点**：市场不确定性和观望理由


## 综合决策规则：

### **买入条件**（需同时满足多个条件）：
1. 基础面：FAA显示基本面稳健 + NAA无重大负面消息
2. 技术面：TAA显示上升趋势或突破信号
3. 展望层：BOA信号强于BeOA，或NOA认为上涨概率较高
4. 风险控制：各层级信号基本一致，分歧度较低

### **卖出条件**（满足以下任一组合）：
1. 基础面恶化：FAA显示基本面问题 + NAA出现重大负面
2. 技术面破位：TAA显示下降趋势 + BeOA强烈看跌
3. 全面看跌：BeOA信号明显强于BOA + 其他层级支持

### **观望条件**：
1. 各层级信号分歧较大，缺乏明确方向
2. NOA建议观望且有充分理由
3. 基础面和技术面方向相反
4. 市场环境不确定性较高

**重要：分析过程要求**
你必须按以下步骤进行分析：
1. **基础面评估**：综合FAA和NAA的分析结果
2. **技术面分析**：评估TAA提供的技术信号
3. **展望层综合**：权衡BOA、BeOA、NOA的观点
4. **风险评估**：评估信号一致性和潜在风险
5. **最终决策**：根据现有持仓,基于综合分析得出结论

你的回答必须包含两个部分：
1. **analysis**: 详细的分析过程和理由（必须在最后包含上述最终决策声明）
2. **action**: 具体的交易行动（必须是 "buy", "sell", 或 "hold"）

请严格按照以下JSON格式回答：
{
  "analysis": "详细的分析过程，包括展望层智能体信号分析、决策理由、风险评估等，最后必须明确声明：**最终决策：买入/卖出/保持不变**",
  "action": "buy/sell/hold"
}

**关键一致性约束：analysis字段中的最终决策声明必须与action字段完全一致！**

"""
    
    def process(self, state: Dict[str, Any], is_full_coalition: bool = False) -> Dict[str, Any]:
        """处理交易决策"""
        if not self.llm_interface:
            self.logger.warning(f"智能体 {self.agent_id} 没有LLM接口，返回默认结果")
            return {
                "analysis": "无LLM接口可用",
                "action": "hold",
                "trading_actions": {"__HOLD__": 1.0}
            }
        
        try:
            # 过滤状态数据，只保留该智能体需要的字段
            filtered_state = self._filter_state_data(state)
            
            # 格式化状态信息
            state_info = self.format_state_for_llm(filtered_state, detailed=True)
            
            # 构建完整提示词
            prompt = self.get_prompt_template()
            full_prompt = f"{prompt}\n\n{state_info}\n\n请严格按照JSON格式回答。"
            
            # 调试：打印提示词长度和开头
            # self.logger.info(f"🔍 发送给LLM的提示词长度: {len(full_prompt)}")
            # self.logger.info(f"🔍 提示词开头200字符: {full_prompt[:200]}...")
            # self.logger.info(f"🔍 提示词结尾200字符: ...{full_prompt[-200:]}")
            
            # 直接调用LLM获取原始响应
            response = self.llm_interface.analyze(
                prompt=full_prompt, 
                model="glm-4-flash", 
                temperature=0.3,  # 提高温度以增加决策多样性
                top_p=0.8         # 提高top_p以增加决策变化
            )
            
            # 解析响应
            # self.logger.info(f"🔍 LLM原始响应类型: {type(response)}")
            # if isinstance(response, dict) and "content" in response:
            #     self.logger.info(f"🔍 LLM响应内容前200字符: {response['content'][:200]}...")
            # else:
            #     self.logger.info(f"🔍 LLM响应前200字符: {str(response)[:200]}...")
            result = self._parse_llm_response(response)
            # self.logger.info(f"🔍 解析后结果: {result}")
            
            # 确保包含必要字段
            if "action" not in result:
                result["action"] = "hold"
            if "analysis" not in result:
                result["analysis"] = "交易决策分析"
            
            # 验证一致性并修正
            result = self._validate_consistency(result)
            
            # 自动记录IO数据（如果是支持OPRO的智能体）
            self.logger.info(f"🔍 {self.agent_id}: 检查IO数据记录能力 - hasattr: {hasattr(self, 'record_weekly_io')}, is_full_coalition: {is_full_coalition}")
            if hasattr(self, 'record_weekly_io') and callable(getattr(self, 'record_weekly_io')):
                try:
                    self.logger.info(f"📝 {self.agent_id}: 开始记录IO数据 (is_full_coalition={is_full_coalition})")
                    
                    # 准备完整的输入状态副本
                    input_state_copy = state.copy()
                    
                    # 构建LLM原始响应数据结构
                    llm_raw_response = {
                        "raw_llm_output": response,  # 原始LLM响应
                        "processed_result": result,  # 处理后的结果
                        "processing_time": 0,  # TraderAgent没有计算处理时间，使用0
                        "timestamp": datetime.now().isoformat(),
                        "prompt": full_prompt,
                        "success": True
                    }
                    
                    # 调用记录方法，传递is_full_coalition参数
                    record_method = getattr(self, 'record_weekly_io')
                    record_method(input_state_copy, llm_raw_response, is_full_coalition=is_full_coalition)
                    
                    self.logger.info(f"✅ {self.agent_id}: IO数据记录完成")
                    
                except Exception as record_e:
                    # 记录失败不应该影响主要功能
                    self.logger.error(f"❌ {self.agent_id}: IO数据自动记录失败: {record_e}")
                    import traceback
                    self.logger.error(f"异常详情: {traceback.format_exc()}")
            else:
                self.logger.warning(f"⚠️ {self.agent_id}: 不支持IO数据记录 - hasattr: {hasattr(self, 'record_weekly_io')}")
            
            # 将LLM的决策转换为交易环境可理解的格式
            action = result.get("action", "hold").lower()
            
            # 转换为交易行动字典（简化版：只考虑买入/卖出/保持不变）
            # 从状态中获取当前交易的股票代码
            symbol = state.get("symbol", "AAPL")  # 如果没有symbol字段则默认为AAPL
            
            if action == "buy":
                result["trading_actions"] = {symbol: 1.0}  # 全仓买入
            elif action == "sell":
                result["trading_actions"] = {symbol: -1.0}  # 全仓卖出
            else:  # hold
                result["trading_actions"] = {"__HOLD__": 0.0}
            
            return result
            
        except Exception as e:
            self.logger.error(f"TRA处理异常: {e}")
            return {
                "analysis": f"处理异常: {e}",
                "action": "hold",
                "trading_actions": {"__HOLD__": 1.0}
            }
    
    def _parse_llm_response(self, response) -> Dict[str, Any]:
        """解析LLM响应"""
        import json
        import re
        
        # 如果响应是字典且包含content字段
        if isinstance(response, dict) and "content" in response:
            content = response["content"]
        elif isinstance(response, dict) and "analysis" in response:
            # 如果响应已经是分析格式，直接使用
            content = response["analysis"]
        else:
            content = str(response)
        
        # 尝试多种方式解析JSON
        json_patterns = [
            r'\{[^{}]*"action"[^{}]*\}',  # 简单的单层JSON
            r'\{(?:[^{}]|\{[^}]*\})*"action"(?:[^{}]|\{[^}]*\})*\}',  # 带有嵌套的JSON
            r'\{.*?"action".*?\}',  # 最宽松的匹配，使用DOTALL模式
        ]
        
        # 1. 尝试直接解析整个内容为JSON
        try:
            data = json.loads(content.strip())
            if isinstance(data, dict) and "action" in data:
                return data
        except json.JSONDecodeError:
            pass
        
        # 2. 使用正则表达式查找JSON块
        for pattern in json_patterns:
            json_match = re.search(pattern, content, re.DOTALL | re.MULTILINE)
            if json_match:
                try:
                    json_str = json_match.group()
                    # 清理JSON字符串
                    json_str = json_str.strip()
                    data = json.loads(json_str)
                    if isinstance(data, dict) and "action" in data:
                        return data
                except json.JSONDecodeError:
                    continue
        
        # 3. 特殊处理：如果content本身包含嵌套的JSON字符串
        # 这个情况在我们的日志中出现了
        try:
            # 查找形如 "{'analysis': '...', 'action': 'buy', 'position_size': 0.5}" 的模式
            nested_json_pattern = r"(\{[^}]*'action'[^}]*\})"
            nested_match = re.search(nested_json_pattern, content)
            if nested_match:
                # 将单引号替换为双引号，然后解析
                json_str = nested_match.group(1).replace("'", '"')
                data = json.loads(json_str)
                if isinstance(data, dict) and "action" in data:
                    return data
        except (json.JSONDecodeError, AttributeError):
            pass
        
        # 4. 尝试手动解析关键字段
        result = {"analysis": content}
        
        # 查找action字段，支持单引号和双引号
        action_patterns = [
            r'"action"\s*:\s*"([^"]+)"',
            r"'action'\s*:\s*'([^']+)'",
            r'"action"\s*:\s*\'([^\']+)\'',
            r"'action'\s*:\s*\"([^\"]+)\""
        ]
        
        for pattern in action_patterns:
            action_match = re.search(pattern, content)
            if action_match:
                result["action"] = action_match.group(1)
                break
        
        # 不解析position_size，使用固定的全仓交易逻辑
        
        return result
    
    def _validate_consistency(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """验证分析内容与动作字段的一致性，并进行必要的修正"""
        analysis = result.get("analysis", "")
        action = result.get("action", "hold").lower()
        
        # 只检查明确的最终决策声明（兼容有无双星号的格式）
        final_decision_patterns = {
            "buy": ["**最终决策：买入**", "**最终决策: 买入**", "最终决策：买入", "最终决策: 买入"],
            "sell": ["**最终决策：卖出**", "**最终决策: 卖出**", "最终决策：卖出", "最终决策: 卖出"], 
            "hold": ["**最终决策：保持不变**", "**最终决策: 保持不变**", "最终决策：保持不变", "最终决策: 保持不变"]
        }
        
        # 查找明确的最终决策声明
        declared_action = None
        for decision, patterns in final_decision_patterns.items():
            if any(pattern in analysis for pattern in patterns):
                declared_action = decision
                self.logger.info(f"🎯 找到最终决策声明: {decision}")
                break
        
        # 如果找到明确声明但与action不符，进行修正
        original_action = action
        if declared_action and declared_action != action:
            self.logger.warning(f"修正决策不一致: 声明{declared_action} vs 字段{action}")
            result["action"] = declared_action
        
        # 如果没有找到明确声明，发出警告但不修正
        elif not declared_action:
            self.logger.warning(f"未找到明确的最终决策声明，保持action={action}")
        
        if original_action != result["action"]:
            self.logger.info(f"一致性修正：{original_action} -> {result['action']}")
            
        return result