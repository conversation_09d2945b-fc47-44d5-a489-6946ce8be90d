"""
并行LLM调用配置示例

展示如何在系统中启用分析层和展望层的并行执行
"""

# 系统配置示例
parallel_config = {
    # 基础系统配置
    "start_date": "2025-01-01",
    "end_date": "2025-02-15", 
    "stocks": ["AAPL"],
    "starting_cash": 1000000,
    
    # 并行执行配置
    "enable_parallel_execution": True,        # 启用并行执行
    "max_concurrent_agents": 3,               # 最大并发智能体数
    
    # 原有配置保持不变
    "enable_concurrent_execution": True,      # 子集并行（保持现有）
    "max_concurrent_api_calls": 60,           # API并发限制
    "max_concurrent_simulations": 60,         # 模拟并发限制
    
    # 其他配置...
    "verbose": True,
    "trading_days_per_week": 5
}

def create_trading_simulator_with_parallel():
    """创建启用并行的交易模拟器示例"""
    from contribution_assessment.trading_simulator import TradingSimulator
    import logging
    
    # 创建日志器
    logger = logging.getLogger("ParallelExample")
    logger.setLevel(logging.INFO)
    
    # 创建交易模拟器
    simulator = TradingSimulator(
        base_config=parallel_config,
        logger=logger
    )
    
    print("🚀 交易模拟器已创建，并行配置:")
    print(f"   - 分析层并行: {'✅ 启用' if simulator.enable_parallel_execution else '❌ 禁用'}")
    print(f"   - 展望层并行: {'✅ 启用' if simulator.enable_parallel_execution else '❌ 禁用'}")
    print(f"   - 最大并发数: {parallel_config['max_concurrent_agents']}")
    
    return simulator

if __name__ == "__main__":
    # 演示配置
    print("📋 并行LLM调用配置示例:")
    print("=" * 50)
    
    print("🔧 配置参数:")
    for key, value in parallel_config.items():
        if 'parallel' in key or 'concurrent' in key:
            print(f"   {key}: {value}")
    
    print("\n🏗️ 系统架构:")
    print("   Layer 0 (分析层): NAA, TAA, FAA -> 可并行")
    print("   Layer 1 (展望层): BOA, BeOA, NOA -> 可并行 (依赖分析层)")
    print("   Layer 2 (决策层): TRA -> 串行 (依赖展望层)")
    
    print("\n⚡ 性能提升:")
    print("   - 分析层: 3个智能体并行 -> 3倍速度提升")
    print("   - 展望层: 3个智能体并行 -> 3倍速度提升")
    print("   - 总体预期提升: ~2倍 (考虑依赖关系)")
    
    # 可选：创建实际的模拟器实例
    # simulator = create_trading_simulator_with_parallel()