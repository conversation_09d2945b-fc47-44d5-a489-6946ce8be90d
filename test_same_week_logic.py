#!/usr/bin/env python3
"""
测试同一周内联盟状态继承的正确性
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_state_tracker import PortfolioStateTracker
from stock_trading_env import StockTradingEnv

def test_first_week_scenario():
    """测试第一周场景：所有联盟都应该从0开始"""
    print("🧪 测试第一周场景...")
    
    tracker = PortfolioStateTracker()
    
    # 模拟完整联盟先运行，产生了一些收益
    tracker.add_daily_record(
        date_str="2025-01-01",
        net_worth=1050000,
        daily_return=0.02,
        cumulative_return=0.05,  # 完整联盟获得了5%收益
        weekly_return=0.05,
        coalition_info={"coalition_size": 7}
    )
    
    # 现在子集联盟要在第1周运行，应该从0开始，不继承完整联盟的状态
    clean_state = tracker.get_clean_state_for_new_phase(1)  # 第1周
    
    if clean_state:
        cum_return = clean_state.get("cumulative_return", 0.0)
        weekly_return = clean_state.get("weekly_return", 0.0)
        
        if cum_return == 0.0 and weekly_return == 0.0:
            print("✅ 第一周子集联盟正确从0开始")
            return True
        else:
            print(f"❌ 第一周子集联盟错误继承状态: 累计={cum_return:.6f}, 周={weekly_return:.6f}")
            return False
    else:
        print("❌ 无法获取第一周清洁状态")
        return False

def test_second_week_scenario():
    """测试第二周场景：应该继承上一周的累计收益率"""
    print("\n🧪 测试第二周场景...")
    
    tracker = PortfolioStateTracker()
    
    # 首先模拟第1周完整联盟的最终状态（设置week_number为1）
    tracker._current_week = 1  # 确保记录为第1周
    tracker.add_daily_record(
        date_str="2025-01-05",  # 第1周最后一天
        net_worth=1100000,
        cumulative_return=0.10,  # 第1周结束时10%累计收益
        weekly_return=0.08,      # 第1周获得8%周收益
        coalition_info={"coalition_size": 7}
    )
    
    # 验证第1周数据正确保存
    latest_state = tracker.get_latest_state()
    print(f"第1周最终状态: 周={latest_state.get('week_number')}, 累计={latest_state.get('cumulative_return'):.6f}")
    
    # 现在开始第2周，设置tracker为第2周
    tracker.start_new_week(2)
    
    # 现在第2周的完整联盟开始运行（这是跨周的情况）
    clean_state_full = tracker.get_clean_state_for_new_phase(2)
    
    if clean_state_full:
        cum_return = clean_state_full.get("cumulative_return", 0.0)
        weekly_return = clean_state_full.get("weekly_return", 0.0)
        last_week_return = clean_state_full.get("last_week_return", 0.0)
        
        if cum_return == 0.10 and weekly_return == 0.0 and last_week_return == 0.08:
            print("✅ 第二周完整联盟正确继承累计收益率，周收益率重置")
            
            # 现在模拟完整联盟运行并产生一些收益
            tracker.add_daily_record(
                date_str="2025-01-08",
                net_worth=1120000,
                cumulative_return=0.12,
                weekly_return=0.018,  # 第2周目前获得1.8%收益
                coalition_info={"coalition_size": 7}
            )
            
            # 现在第2周的子集联盟开始运行，应该从第1周基础开始，不继承完整联盟的第2周状态
            clean_state_subset = tracker.get_clean_state_for_new_phase(2)
            
            subset_cum = clean_state_subset.get("cumulative_return", 0.0)
            subset_weekly = clean_state_subset.get("weekly_return", 0.0)
            
            if subset_cum == 0.10 and subset_weekly == 0.0:  # 应该从第1周基础开始
                print("✅ 第二周子集联盟正确从上一周基础开始，不继承同周完整联盟状态")
                return True
            else:
                print(f"❌ 第二周子集联盟错误继承状态: 累计={subset_cum:.6f}, 周={subset_weekly:.6f}")
                return False
        else:
            print(f"❌ 第二周完整联盟状态错误: 累计={cum_return:.6f}, 周={weekly_return:.6f}, 上周={last_week_return:.6f}")
            return False
    else:
        print("❌ 无法获取第二周清洁状态")
        return False

def test_trading_env_consistency():
    """测试交易环境的一致性"""
    print("\n🧪 测试交易环境一致性...")
    
    tracker = PortfolioStateTracker()
    
    # 模拟第1周完整联盟结束状态
    tracker.add_daily_record(
        date_str="2025-01-05",
        net_worth=1080000,
        cumulative_return=0.08,
        weekly_return=0.06,
        coalition_info={"coalition_size": 7}
    )
    
    # 创建第1周的子集联盟环境
    config_subset_week1 = {
        "start_date": "2025-01-01",
        "end_date": "2025-01-05",
        "stocks": ["AAPL"],
        "starting_cash": 1000000,
        "portfolio_tracker": tracker,
        "current_week_number": 1,
        "coalition_size": 3  # 子集联盟
    }
    
    env_subset = StockTradingEnv(config_subset_week1)
    subset_cum = getattr(env_subset, 'cumulative_return', 0.0)
    subset_weekly = getattr(env_subset, 'weekly_return', 0.0)
    
    if subset_cum == 0.0 and subset_weekly == 0.0:
        print("✅ 第1周子集联盟交易环境正确从0开始")
        return True
    else:
        print(f"❌ 第1周子集联盟交易环境错误: 累计={subset_cum:.6f}, 周={subset_weekly:.6f}")
        return False

if __name__ == "__main__":
    print("🚀 测试同一周内联盟状态继承逻辑...")
    
    test_results = []
    
    # 测试1: 第一周场景
    test_results.append(("第一周场景", test_first_week_scenario()))
    
    # 测试2: 第二周场景
    test_results.append(("第二周场景", test_second_week_scenario()))
    
    # 测试3: 交易环境一致性
    test_results.append(("交易环境一致性", test_trading_env_consistency()))
    
    # 总结结果
    print(f"\n{'='*60}")
    print("📊 测试结果总结")
    print('='*60)
    
    all_passed = True
    for test_name, success in test_results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if not success:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！同一周内状态继承逻辑修正成功！")
        print("📝 修正要点:")
        print("  - 第一周：所有联盟都从0开始")
        print("  - 同一周内：子集联盟不继承完整联盟状态，都从上一周基础开始")
        print("  - 跨周：正确继承上一周的累计收益率")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
        sys.exit(1)