#!/usr/bin/env python3
"""
日志功能正确性测试脚本

验证各种日志模式的功能正确性，包括：
1. 日志配置语法正确性
2. setup_logging 函数工作正常
3. _suppress_third_party_logs 函数运行正确
4. 所有日志级别配置正确
5. 文件和控制台处理器设置正确
"""

import logging
import tempfile
import os
import sys
import unittest
from io import StringIO
from contextlib import redirect_stderr, redirect_stdout
from typing import List, Optional

# 导入要测试的模块
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
from state_management.logging_modes import (
    LoggingMode, 
    LoggingContext, 
    PhaseDetector,
    get_global_logging_context,
    set_global_logging_mode,
    get_current_logging_mode,
    is_detailed_logging_enabled
)
from utils.comprehensive_logger import SimpleLogger


class TestLoggingModes(unittest.TestCase):
    """测试日志模式功能"""
    
    def setUp(self):
        """测试前准备"""
        # 重置全局日志上下文
        set_global_logging_mode(LoggingMode.DETAILED)
    
    def test_logging_mode_enum(self):
        """测试日志模式枚举定义"""
        self.assertEqual(LoggingMode.DETAILED.value, "detailed")
        self.assertEqual(LoggingMode.CONCISE.value, "concise")
        
    def test_logging_context_initialization(self):
        """测试日志上下文初始化"""
        # 测试默认初始化
        context = LoggingContext()
        self.assertEqual(context.current_mode, LoggingMode.DETAILED)
        
        # 测试指定初始化模式
        context_concise = LoggingContext(LoggingMode.CONCISE)
        self.assertEqual(context_concise.current_mode, LoggingMode.CONCISE)
    
    def test_logging_context_mode_switching(self):
        """测试日志上下文模式切换"""
        context = LoggingContext()
        
        # 测试设置模式
        context.set_mode(LoggingMode.CONCISE)
        self.assertEqual(context.current_mode, LoggingMode.CONCISE)
        
        # 测试推入模式
        context.push_mode(LoggingMode.DETAILED)
        self.assertEqual(context.current_mode, LoggingMode.DETAILED)
        
        # 测试弹出模式
        restored_mode = context.pop_mode()
        self.assertEqual(restored_mode, LoggingMode.CONCISE)
        self.assertEqual(context.current_mode, LoggingMode.CONCISE)
    
    def test_logging_context_mode_checking(self):
        """测试日志上下文模式检查方法"""
        context = LoggingContext(LoggingMode.DETAILED)
        self.assertTrue(context.is_detailed_mode())
        self.assertFalse(context.is_concise_mode())
        
        context.set_mode(LoggingMode.CONCISE)
        self.assertFalse(context.is_detailed_mode())
        self.assertTrue(context.is_concise_mode())
    
    def test_global_logging_context(self):
        """测试全局日志上下文"""
        # 测试获取全局上下文
        global_context = get_global_logging_context()
        self.assertIsInstance(global_context, LoggingContext)
        
        # 测试设置全局模式
        set_global_logging_mode(LoggingMode.CONCISE)
        self.assertEqual(get_current_logging_mode(), LoggingMode.CONCISE)
        self.assertFalse(is_detailed_logging_enabled())
        
        set_global_logging_mode(LoggingMode.DETAILED)
        self.assertEqual(get_current_logging_mode(), LoggingMode.DETAILED)
        self.assertTrue(is_detailed_logging_enabled())


class TestPhaseDetector(unittest.TestCase):
    """测试阶段检测器功能"""
    
    def setUp(self):
        """测试前准备"""
        self.detector = PhaseDetector()
        self.all_agents = {"NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"}
    
    def test_phase_detector_initialization(self):
        """测试阶段检测器初始化"""
        # 测试默认初始化
        detector_default = PhaseDetector()
        self.assertEqual(detector_default.all_agents, self.all_agents)
        
        # 测试自定义初始化
        custom_agents = {"Agent1", "Agent2"}
        detector_custom = PhaseDetector(custom_agents)
        self.assertEqual(detector_custom.all_agents, custom_agents)
    
    def test_full_coalition_detection(self):
        """测试完整联盟检测"""
        # 测试完整联盟
        self.assertTrue(self.detector.is_full_coalition(self.all_agents))
        
        # 测试部分联盟
        partial_coalition = {"NAA", "TAA", "FAA"}
        self.assertFalse(self.detector.is_full_coalition(partial_coalition))
        
        # 测试超集（不应该发生，但测试健壮性）
        superset_coalition = self.all_agents | {"ExtraAgent"}
        self.assertFalse(self.detector.is_full_coalition(superset_coalition))
    
    def test_phase_detection(self):
        """测试阶段检测"""
        # 测试完整联盟阶段检测
        phase_name, mode = self.detector.detect_phase(self.all_agents)
        self.assertEqual(phase_name, "阶段一（完整联盟）")
        self.assertEqual(mode, LoggingMode.DETAILED)
        
        # 测试部分联盟阶段检测
        partial_coalition = {"NAA", "TAA"}
        phase_name, mode = self.detector.detect_phase(partial_coalition)
        self.assertEqual(phase_name, "阶段二（子集联盟）")
        self.assertEqual(mode, LoggingMode.CONCISE)
    
    def test_get_logging_mode_for_coalition(self):
        """测试为联盟获取日志模式"""
        # 测试完整联盟
        mode = self.detector.get_logging_mode_for_coalition(self.all_agents)
        self.assertEqual(mode, LoggingMode.DETAILED)
        
        # 测试部分联盟
        partial_coalition = {"NAA", "TAA"}
        mode = self.detector.get_logging_mode_for_coalition(partial_coalition)
        self.assertEqual(mode, LoggingMode.CONCISE)


class TestSimpleLogger(unittest.TestCase):
    """测试简单日志记录器功能"""
    
    def setUp(self):
        """测试前准备"""
        # 清理现有的日志处理器
        logging.getLogger().handlers.clear()
        for logger_name in logging.Logger.manager.loggerDict:
            logging.getLogger(logger_name).handlers.clear()
    
    def test_simple_logger_initialization(self):
        """测试简单日志记录器初始化"""
        logger = SimpleLogger("TestLogger", logging.DEBUG)
        self.assertEqual(logger.name, "TestLogger")
        self.assertEqual(logger.logger.level, logging.DEBUG)
        self.assertTrue(len(logger.logger.handlers) > 0)
    
    def test_simple_logger_methods(self):
        """测试简单日志记录器方法"""
        with tempfile.NamedTemporaryFile(mode='w+', delete=False) as f:
            log_file = f.name
        
        try:
            # 捕获日志输出
            log_capture = StringIO()
            
            # 创建日志记录器
            logger = SimpleLogger("TestLogger", logging.DEBUG)
            
            # 添加StringIO处理器用于测试
            handler = logging.StreamHandler(log_capture)
            formatter = logging.Formatter('%(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.logger.handlers.clear()  # 清除默认处理器
            logger.logger.addHandler(handler)
            
            # 测试各级别日志方法
            logger.debug("Debug message")
            logger.info("Info message")
            logger.warning("Warning message")
            logger.error("Error message")
            logger.critical("Critical message")
            
            # 检查输出
            log_output = log_capture.getvalue()
            self.assertIn("DEBUG - Debug message", log_output)
            self.assertIn("INFO - Info message", log_output)
            self.assertIn("WARNING - Warning message", log_output)
            self.assertIn("ERROR - Error message", log_output)
            self.assertIn("CRITICAL - Critical message", log_output)
            
        finally:
            # 清理临时文件
            if os.path.exists(log_file):
                os.unlink(log_file)
    
    def test_logger_handler_deduplication(self):
        """测试日志处理器去重功能"""
        logger1 = SimpleLogger("DuplicateTest")
        initial_handler_count = len(logger1.logger.handlers)
        
        # 创建同名日志记录器，应该不会重复添加处理器
        logger2 = SimpleLogger("DuplicateTest")
        final_handler_count = len(logger2.logger.handlers)
        
        self.assertEqual(initial_handler_count, final_handler_count)


class TestLoggingConfiguration(unittest.TestCase):
    """测试日志配置功能"""
    
    def test_logging_levels_configuration(self):
        """测试所有日志级别配置正确"""
        levels = [
            logging.DEBUG,
            logging.INFO,
            logging.WARNING,
            logging.ERROR,
            logging.CRITICAL
        ]
        
        for level in levels:
            logger = SimpleLogger("LevelTest", level)
            self.assertEqual(logger.logger.level, level)
    
    def test_file_and_console_handlers(self):
        """测试文件和控制台处理器设置"""
        with tempfile.NamedTemporaryFile(mode='w+', delete=False) as f:
            log_file = f.name
        
        try:
            # 创建logger
            logger = SimpleLogger("HandlerTest")
            
            # 清除默认处理器
            logger.logger.handlers.clear()
            
            # 添加控制台处理器
            console_handler = logging.StreamHandler(sys.stdout)
            console_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            logger.logger.addHandler(console_handler)
            
            # 添加文件处理器
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            file_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            file_handler.setFormatter(file_formatter)
            logger.logger.addHandler(file_handler)
            
            # 测试日志输出
            logger.info("Test message for handlers")
            
            # 验证处理器数量
            self.assertEqual(len(logger.logger.handlers), 2)
            
            # 验证文件输出
            with open(log_file, 'r', encoding='utf-8') as f:
                file_content = f.read()
                self.assertIn("Test message for handlers", file_content)
                
        finally:
            # 清理临时文件
            if os.path.exists(log_file):
                os.unlink(log_file)


class TestThirdPartyLogging(unittest.TestCase):
    """测试第三方库日志抑制功能"""
    
    def test_third_party_log_suppression(self):
        """测试第三方库日志抑制功能"""
        # 模拟第三方库日志抑制函数
        def suppress_third_party_logs():
            """抑制第三方库的调试日志"""
            third_party_loggers = [
                'httpcore', 'httpx', 'urllib3', 'requests',
                'zhipuai', 'openai', 'asyncio', 'concurrent.futures',
                'multiprocessing', 'threading', 'sqlalchemy', 'sqlite3'
            ]
            
            for logger_name in third_party_loggers:
                third_party_logger = logging.getLogger(logger_name)
                third_party_logger.setLevel(logging.WARNING)
        
        # 测试抑制功能
        suppress_third_party_logs()
        
        # 验证第三方库日志级别
        test_loggers = ['httpcore', 'httpx', 'urllib3', 'requests']
        for logger_name in test_loggers:
            logger = logging.getLogger(logger_name)
            self.assertGreaterEqual(logger.level, logging.WARNING)


class TestLoggingSyntax(unittest.TestCase):
    """测试日志配置语法正确性"""
    
    def test_import_syntax(self):
        """测试导入语法正确性"""
        try:
            from state_management.logging_modes import LoggingMode, LoggingContext
            from utils.comprehensive_logger import SimpleLogger
            # 如果能成功导入，说明语法正确
            self.assertTrue(True)
        except SyntaxError as e:
            self.fail(f"语法错误: {e}")
        except ImportError as e:
            self.fail(f"导入错误: {e}")
    
    def test_enum_syntax(self):
        """测试枚举定义语法"""
        # 测试LoggingMode枚举
        self.assertIsInstance(LoggingMode.DETAILED, LoggingMode)
        self.assertIsInstance(LoggingMode.CONCISE, LoggingMode)
        
        # 测试枚举值
        self.assertEqual(str(LoggingMode.DETAILED.value), "detailed")
        self.assertEqual(str(LoggingMode.CONCISE.value), "concise")
    
    def test_class_definitions_syntax(self):
        """测试类定义语法正确性"""
        # 测试LoggingContext类
        context = LoggingContext()
        self.assertIsInstance(context, LoggingContext)
        
        # 测试PhaseDetector类
        detector = PhaseDetector()
        self.assertIsInstance(detector, PhaseDetector)
        
        # 测试SimpleLogger类
        logger = SimpleLogger()
        self.assertIsInstance(logger, SimpleLogger)


def run_comprehensive_logging_tests():
    """运行综合日志测试"""
    print("=" * 60)
    print("开始日志功能正确性验证测试")
    print("=" * 60)
    
    # 创建测试套件
    test_suite = unittest.TestSuite()
    
    # 添加测试用例
    test_classes = [
        TestLoggingModes,
        TestPhaseDetector,
        TestSimpleLogger,
        TestLoggingConfiguration,
        TestThirdPartyLogging,
        TestLoggingSyntax
    ]
    
    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)
    
    print("\n" + "=" * 60)
    print("测试结果总结:")
    print(f"总计运行测试: {result.testsRun}")
    print(f"成功: {result.testsRun - len(result.failures) - len(result.errors)}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"  - {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"  - {test}: {traceback}")
    
    print("=" * 60)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_comprehensive_logging_tests()
    sys.exit(0 if success else 1)