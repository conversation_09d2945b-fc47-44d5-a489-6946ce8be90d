#!/usr/bin/env python3
"""
测试基本功能是否受修复影响
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_trading_env import StockTradingEnv
from portfolio_state_tracker import PortfolioStateTracker

def test_basic_trading_env():
    """测试基本的交易环境功能"""
    print("🧪 测试基本交易环境功能...")
    
    try:
        config = {
            "start_date": "2025-01-01",
            "end_date": "2025-01-05", 
            "stocks": ["AAPL"],
            "starting_cash": 1000000,
            "coalition_size": 7
        }
        
        env = StockTradingEnv(config)
        
        # 测试基本的step操作
        initial_state = env.get_state()
        action = {"__HOLD__": 1.0}  # 持有操作
        
        state, reward, done, info = env.step(action)
        
        print(f"✅ 基本step操作成功: 奖励={reward:.6f}, 完成={done}")
        return True
        
    except Exception as e:
        print(f"❌ 基本交易环境测试失败: {e}")
        return False

def test_portfolio_tracker_basic():
    """测试portfolio tracker的基本功能"""
    print("🧪 测试Portfolio Tracker基本功能...")
    
    try:
        tracker = PortfolioStateTracker()
        
        # 添加记录
        tracker.add_daily_record(
            date_str="2025-01-01",
            net_worth=1000000,
            daily_return=0.0,
            cumulative_return=0.0,
            coalition_info={"coalition_size": 7}
        )
        
        # 获取状态
        state = tracker.get_latest_state()
        if state and state["net_worth"] == 1000000:
            print("✅ Portfolio Tracker基本功能正常")
            return True
        else:
            print("❌ Portfolio Tracker状态获取异常")
            return False
            
    except Exception as e:
        print(f"❌ Portfolio Tracker测试失败: {e}")
        return False

def test_subcoalition_handling():
    """测试子集联盟的处理是否正常"""
    print("🧪 测试子集联盟处理...")
    
    try:
        tracker = PortfolioStateTracker()
        
        # 先添加完整联盟记录
        tracker.add_daily_record(
            date_str="2025-01-01",
            net_worth=1050000,
            cumulative_return=0.05,
            coalition_info={"coalition_size": 7}
        )
        
        # 添加子集联盟记录（应该被跳过）
        tracker.add_daily_record(
            date_str="2025-01-01",
            net_worth=1020000,
            cumulative_return=0.02,
            coalition_info={"coalition_size": 3}  # 子集联盟
        )
        
        # 验证只保存了完整联盟的数据
        state = tracker.get_latest_state()
        if state and state["cumulative_return"] == 0.05:
            print("✅ 子集联盟处理正常，保持数据纯净")
            return True
        else:
            print(f"❌ 子集联盟处理异常: 累计收益={state['cumulative_return'] if state else 'None'}")
            return False
            
    except Exception as e:
        print(f"❌ 子集联盟测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始基本功能验证测试...")
    
    tests = [
        ("基本交易环境", test_basic_trading_env),
        ("Portfolio Tracker基本功能", test_portfolio_tracker_basic), 
        ("子集联盟处理", test_subcoalition_handling)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"测试: {test_name}")
        print('='*50)
        
        success = test_func()
        results.append((test_name, success))
    
    # 总结结果
    print(f"\n{'='*50}")
    print("📊 功能验证结果总结")
    print('='*50)
    
    all_passed = True
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if not success:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有基本功能测试通过！修复未影响现有功能！")
        sys.exit(0)
    else:
        print("\n❌ 部分功能测试失败，需要检查修复是否引入问题")
        sys.exit(1)