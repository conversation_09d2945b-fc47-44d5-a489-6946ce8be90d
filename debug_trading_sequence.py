import sqlite3
import pandas as pd

# 根据日志重现交易序列
prices = {
    '2025-01-02': 243.85,
    '2025-01-03': 243.36, 
    '2025-01-06': 245.00,
    '2025-01-07': 242.21,
    '2025-01-08': 242.70
}

print('=== 重现系统的交易序列 ===')
cash = 1000000.0
positions = 0
trading_fee_rate = 0.001

print(f'初始状态: 现金=${cash:.2f}, 持仓={positions}股')
print()

# 第1天：买入操作 (action=0.7)
print('第1天 (2025-01-02): 买入操作')
price = prices['2025-01-02']
cash_to_spend = cash * 0.99
shares_to_buy = int(cash_to_spend / price)
buy_cost = shares_to_buy * price * (1 + trading_fee_rate)
cash -= buy_cost
positions += shares_to_buy

position_value = positions * price
net_worth = cash + position_value
print(f'  价格: ${price:.2f}')
print(f'  买入: {shares_to_buy}股')
print(f'  成本: ${buy_cost:.2f}')
print(f'  剩余现金: ${cash:.2f}')
print(f'  持仓价值: ${position_value:.2f}')
print(f'  净值: ${net_worth:.2f}')
print()

# 第2天：持有操作
print('第2天 (2025-01-03): 持有操作')
price = prices['2025-01-03']
position_value = positions * price
net_worth = cash + position_value
print(f'  价格: ${price:.2f}')
print(f'  持仓价值: ${position_value:.2f}')
print(f'  净值: ${net_worth:.2f}')
print()

# 第3天：又一次买入操作！
print('第3天 (2025-01-06): 又买入操作 - 问题所在!')
price = prices['2025-01-06']
cash_to_spend = cash * 0.99  # 用剩余现金
shares_to_buy = int(cash_to_spend / price)
buy_cost = shares_to_buy * price * (1 + trading_fee_rate)
cash -= buy_cost
positions += shares_to_buy  # 累加持仓！

position_value = positions * price
net_worth = cash + position_value
print(f'  价格: ${price:.2f}')
print(f'  又买入: {shares_to_buy}股')
print(f'  总持仓: {positions}股')
print(f'  成本: ${buy_cost:.2f}')
print(f'  剩余现金: ${cash:.2f}')
print(f'  持仓价值: ${position_value:.2f}')
print(f'  净值: ${net_worth:.2f}')
print()

print('=== 问题分析 ===')
print('1. 系统在第1天和第3天都执行了"全仓买入"')
print('2. 但实际上是用剩余现金继续买入，导致持仓累积')
print('3. 即使股价下跌，持股数量大幅增加仍可能推高净值')
print()
print('正确的全仓买入逻辑应该是:')
print('1. 卖出所有现有持仓（如果有）')
print('2. 用全部资产买入新股票')
print('3. 这样每次买入都是真正的"全仓"操作')