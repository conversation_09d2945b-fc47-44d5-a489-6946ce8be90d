#!/usr/bin/env python3
"""
交易回测系统
基于真实价格数据和AI交易决策进行回测分析
支持多空交易和详细的风险指标计算
"""

import sqlite3
import json
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple
from datetime import datetime, date
import matplotlib.pyplot as plt
import seaborn as sns

plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class TradingBacktester:
    def __init__(self, initial_capital: float = 1000000.0):
        self.initial_capital = initial_capital
        self.data_dir = Path('/Users/<USER>/Code/Multi_Agent_Optimization/data')
        
        # 加载交易动作数据
        with open('/Users/<USER>/Code/Multi_Agent_Optimization/trading_actions_80days.json', 'r') as f:
            self.trading_actions = json.load(f)
        
        # 初始化结果存储
        self.price_data = {}
        self.backtest_results = {}
        
    def load_price_data(self, ticker: str) -> pd.DataFrame:
        """从SQLite数据库加载价格数据"""
        db_path = self.data_dir / 'tickers' / ticker / f'{ticker}_data.db'
        
        if not db_path.exists():
            raise FileNotFoundError(f"数据库文件不存在: {db_path}")
        
        conn = sqlite3.connect(db_path)
        query = """
        SELECT trade_date, Open, High, Low, Close, Adj_Close, Volume 
        FROM ohlcv 
        WHERE trade_date >= '2025-01-02' AND trade_date <= '2025-04-22'
        ORDER BY trade_date
        """
        
        df = pd.read_sql_query(query, conn)
        conn.close()
        
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        df.set_index('trade_date', inplace=True)
        
        return df
    
    def load_all_price_data(self):
        """加载所有股票的价格数据"""
        stocks = ['AAPL', 'GOOG', 'NVDA', 'META']
        
        for stock in stocks:
            try:
                self.price_data[stock] = self.load_price_data(stock)
                print(f"✅ 成功加载 {stock} 价格数据: {len(self.price_data[stock])} 天")
            except Exception as e:
                print(f"❌ 加载 {stock} 价格数据失败: {e}")
                
        print(f"\n📊 总共加载 {len(self.price_data)} 只股票的价格数据")
    
    def backtest_single_stock(self, ticker: str) -> Dict:
        """单只股票回测"""
        if ticker not in self.price_data:
            raise ValueError(f"未找到 {ticker} 的价格数据")
        
        if ticker not in self.trading_actions:
            raise ValueError(f"未找到 {ticker} 的交易动作数据")
        
        price_df = self.price_data[ticker]
        actions = self.trading_actions[ticker]
        
        # 初始化回测变量
        portfolio_value = self.initial_capital
        position = 0  # 持仓数量（正数做多，负数做空）
        cash = self.initial_capital
        trade_history = []
        daily_returns = []
        portfolio_values = []
        drawdowns = []
        max_portfolio_value = self.initial_capital
        
        print(f"\n🔄 开始回测 {ticker}...")
        
        for action_data in actions:
            trade_date = action_data['date']
            direction = action_data['direction']
            trading_dict = action_data['trading_dict']
            
            # 转换日期格式
            try:
                trade_date_dt = pd.to_datetime(trade_date)
            except:
                print(f"⚠️  日期格式错误: {trade_date}")
                continue
            
            # 查找对应的价格数据
            if trade_date_dt not in price_df.index:
                print(f"⚠️  未找到 {trade_date} 的价格数据")
                continue
            
            close_price = price_df.loc[trade_date_dt, 'Close']
            
            # 执行交易
            if direction == 'buy':
                # 买入操作：用所有现金买入
                if cash > 0:
                    shares_to_buy = cash / close_price
                    position += shares_to_buy
                    cash = 0
                    
                    trade_history.append({
                        'date': trade_date,
                        'action': 'BUY',
                        'price': close_price,
                        'shares': shares_to_buy,
                        'position': position,
                        'cash': cash
                    })
                    
            elif direction == 'sell':
                # 卖出操作：如果有持仓则卖出，否则做空
                if position > 0:
                    # 平多仓
                    cash += position * close_price
                    shares_sold = position
                    position = 0
                else:
                    # 做空：借入股票卖出
                    shares_to_short = self.initial_capital / close_price
                    position -= shares_to_short
                    cash += shares_to_short * close_price
                    shares_sold = -shares_to_short
                
                trade_history.append({
                    'date': trade_date,
                    'action': 'SELL',
                    'price': close_price,
                    'shares': shares_sold,
                    'position': position,
                    'cash': cash
                })
            
            # 计算当前组合价值
            if position > 0:
                # 做多
                portfolio_value = cash + position * close_price
            elif position < 0:
                # 做空
                portfolio_value = cash - abs(position) * close_price
            else:
                # 空仓
                portfolio_value = cash
            
            # 计算收益率
            daily_return = (portfolio_value - self.initial_capital) / self.initial_capital
            daily_returns.append(daily_return)
            portfolio_values.append(portfolio_value)
            
            # 计算回撤
            max_portfolio_value = max(max_portfolio_value, portfolio_value)
            drawdown = (portfolio_value - max_portfolio_value) / max_portfolio_value
            drawdowns.append(drawdown)
        
        # 计算最终指标
        if len(daily_returns) == 0:
            return self._empty_results(ticker)
        
        total_return = (portfolio_values[-1] - self.initial_capital) / self.initial_capital
        
        # 年化收益率（假设252个交易日）
        trading_days = len(daily_returns)
        annual_return = (1 + total_return) ** (252 / trading_days) - 1
        
        # 计算日收益率序列
        pct_returns = []
        for i in range(1, len(portfolio_values)):
            pct_return = (portfolio_values[i] - portfolio_values[i-1]) / portfolio_values[i-1]
            pct_returns.append(pct_return)
        
        if len(pct_returns) > 1:
            # 年化波动率
            daily_volatility = np.std(pct_returns)
            annual_volatility = daily_volatility * np.sqrt(252)
            
            # 夏普比率（假设无风险利率为3%）
            risk_free_rate = 0.03
            sharpe_ratio = (annual_return - risk_free_rate) / annual_volatility if annual_volatility > 0 else 0
        else:
            annual_volatility = 0
            sharpe_ratio = 0
        
        # 最大回撤
        max_drawdown = min(drawdowns) if drawdowns else 0
        
        results = {
            'ticker': ticker,
            'total_trades': len(trade_history),
            'initial_capital': self.initial_capital,
            'final_value': portfolio_values[-1],
            'total_return': total_return,
            'annual_return': annual_return,
            'annual_volatility': annual_volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'trade_history': trade_history,
            'portfolio_values': portfolio_values,
            'dates': [action['date'] for action in actions[:len(portfolio_values)]],
            'pct_returns': pct_returns
        }
        
        return results
    
    def _empty_results(self, ticker: str) -> Dict:
        """返回空结果"""
        return {
            'ticker': ticker,
            'total_trades': 0,
            'initial_capital': self.initial_capital,
            'final_value': self.initial_capital,
            'total_return': 0,
            'annual_return': 0,
            'annual_volatility': 0,
            'sharpe_ratio': 0,
            'max_drawdown': 0,
            'trade_history': [],
            'portfolio_values': [],
            'dates': [],
            'pct_returns': []
        }
    
    def run_backtest(self):
        """运行完整回测"""
        print("🚀 开始加载价格数据...")
        self.load_all_price_data()
        
        print("\n📈 开始执行回测...")
        stocks = ['AAPL', 'GOOG', 'NVDA', 'META']
        
        for stock in stocks:
            if stock in self.price_data:
                try:
                    result = self.backtest_single_stock(stock)
                    self.backtest_results[stock] = result
                    print(f"✅ {stock} 回测完成")
                except Exception as e:
                    print(f"❌ {stock} 回测失败: {e}")
    
    def print_results(self):
        """打印回测结果"""
        print("\n" + "="*80)
        print("📊 回测结果汇总")
        print("="*80)
        
        summary_data = []
        
        for ticker, result in self.backtest_results.items():
            print(f"\n🏷️  {ticker} 股票:")
            print(f"   初始资金: ${result['initial_capital']:,.2f}")
            print(f"   最终价值: ${result['final_value']:,.2f}")
            print(f"   总收益率: {result['total_return']:.2%}")
            print(f"   年化收益率: {result['annual_return']:.2%}")
            print(f"   年化波动率: {result['annual_volatility']:.2%}")
            print(f"   夏普比率: {result['sharpe_ratio']:.3f}")
            print(f"   最大回撤: {result['max_drawdown']:.2%}")
            print(f"   总交易次数: {result['total_trades']}")
            
            summary_data.append({
                'Stock': ticker,
                'Total Return': f"{result['total_return']:.2%}",
                'Annual Return': f"{result['annual_return']:.2%}",
                'Annual Volatility': f"{result['annual_volatility']:.2%}",
                'Sharpe Ratio': f"{result['sharpe_ratio']:.3f}",
                'Max Drawdown': f"{result['max_drawdown']:.2%}",
                'Total Trades': result['total_trades']
            })
        
        # 创建汇总表格
        if summary_data:
            print(f"\n📋 汇总表格:")
            summary_df = pd.DataFrame(summary_data)
            print(summary_df.to_string(index=False))
    
    def plot_results(self):
        """绘制回测结果图表"""
        if not self.backtest_results:
            print("⚠️  无回测结果可绘制")
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('交易回测结果分析', fontsize=16, fontweight='bold')
        
        # 1. 组合价值走势
        ax1 = axes[0, 0]
        for ticker, result in self.backtest_results.items():
            if result['portfolio_values']:
                dates = pd.to_datetime(result['dates'])
                ax1.plot(dates, result['portfolio_values'], label=ticker, linewidth=2)
        
        ax1.axhline(y=self.initial_capital, color='red', linestyle='--', alpha=0.7, label='初始资金')
        ax1.set_title('组合价值走势')
        ax1.set_ylabel('组合价值 ($)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 累计收益率
        ax2 = axes[0, 1]
        for ticker, result in self.backtest_results.items():
            if result['portfolio_values']:
                dates = pd.to_datetime(result['dates'])
                cumulative_returns = [(v - self.initial_capital) / self.initial_capital for v in result['portfolio_values']]
                ax2.plot(dates, cumulative_returns, label=ticker, linewidth=2)
        
        ax2.axhline(y=0, color='red', linestyle='--', alpha=0.7)
        ax2.set_title('累计收益率')
        ax2.set_ylabel('收益率 (%)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        ax2.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1%}'.format(y)))
        
        # 3. 收益率对比
        ax3 = axes[1, 0]
        tickers = list(self.backtest_results.keys())
        annual_returns = [self.backtest_results[t]['annual_return'] for t in tickers]
        bars = ax3.bar(tickers, annual_returns, color=['blue', 'green', 'orange', 'red'])
        ax3.set_title('年化收益率对比')
        ax3.set_ylabel('年化收益率 (%)')
        ax3.grid(True, alpha=0.3)
        ax3.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1%}'.format(y)))
        
        # 添加数值标签
        for bar, return_val in zip(bars, annual_returns):
            ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                    f'{return_val:.1%}', ha='center', va='bottom')
        
        # 4. 风险收益散点图
        ax4 = axes[1, 1]
        volatilities = [self.backtest_results[t]['annual_volatility'] for t in tickers]
        returns = [self.backtest_results[t]['annual_return'] for t in tickers]
        
        scatter = ax4.scatter(volatilities, returns, s=100, alpha=0.7, c=['blue', 'green', 'orange', 'red'])
        
        for i, ticker in enumerate(tickers):
            ax4.annotate(ticker, (volatilities[i], returns[i]), 
                        xytext=(5, 5), textcoords='offset points', fontsize=10)
        
        ax4.set_xlabel('年化波动率 (%)')
        ax4.set_ylabel('年化收益率 (%)')
        ax4.set_title('风险-收益散点图')
        ax4.grid(True, alpha=0.3)
        ax4.xaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1%}'.format(y)))
        ax4.yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.1%}'.format(y)))
        
        plt.tight_layout()
        plt.savefig('/Users/<USER>/Code/Multi_Agent_Optimization/backtest_results.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("📊 图表已保存为 backtest_results.png")

def main():
    """主函数"""
    print("🤖 AI交易策略回测系统")
    print("基于80个交易日的真实数据进行回测分析")
    print("-" * 50)
    
    # 创建回测器
    backtester = TradingBacktester(initial_capital=1000000.0)
    
    # 运行回测
    backtester.run_backtest()
    
    # 打印结果
    backtester.print_results()
    
    # 绘制图表
    backtester.plot_results()
    
    print("\n✅ 回测分析完成!")

if __name__ == "__main__":
    main()