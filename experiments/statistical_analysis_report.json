{"analysis_metadata": {"significance_level": 0.05, "analysis_date": "2025-07-30T12:32:32.188255", "data_source": "../daily_returns_csv"}, "shapley_normality_analysis": {"agent_results": {"NAA": {"mean": 0.16479776529418683, "std": 0.035627829404314866, "cv": 0.21619121679662498, "shapiro_statistic": 0.8703254704369222, "shapiro_p_value": 0.06597377292431206, "ks_statistic": 0.22886457542764738, "ks_p_value": 0.4859406902155876, "is_normal_shapiro": true, "is_normal_ks": true}, "TAA": {"mean": 0.12043840661445027, "std": 0.047856754156498135, "cv": 0.3973545939518952, "shapiro_statistic": 0.9532308169650314, "shapiro_p_value": 0.6845867199939355, "ks_statistic": 0.13784381065265844, "ks_p_value": 0.953220803417969, "is_normal_shapiro": true, "is_normal_ks": true}, "FAA": {"mean": 0.14033323561297434, "std": 0.04294099760138689, "cv": 0.305993070093631, "shapiro_statistic": 0.9177209020239906, "shapiro_p_value": 0.26759659734703417, "ks_statistic": 0.1586404748269864, "ks_p_value": 0.8776882175903461, "is_normal_shapiro": true, "is_normal_ks": true}, "BOA": {"mean": 0.0700226719945013, "std": 0.026084629925234734, "cv": 0.3725169174818564, "shapiro_statistic": 0.965109488756722, "shapiro_p_value": 0.8534801544425092, "ks_statistic": 0.13557091373621388, "ks_p_value": 0.9590815622230888, "is_normal_shapiro": true, "is_normal_ks": true}, "BeOA": {"mean": 0.08143748103195834, "std": 0.02461110663481649, "cv": 0.3022085939170737, "shapiro_statistic": 0.9222842509388565, "shapiro_p_value": 0.305403632972936, "ks_statistic": 0.21513849641150912, "ks_p_value": 0.5636394508236229, "is_normal_shapiro": true, "is_normal_ks": true}, "NOA": {"mean": 0.08437215564274449, "std": 0.02629504749893991, "cv": 0.3116555135829474, "shapiro_statistic": 0.9577316974599849, "shapiro_p_value": 0.7510257382336564, "ks_statistic": 0.10992628769239221, "ks_p_value": 0.9951435760464282, "is_normal_shapiro": true, "is_normal_ks": true}, "TRA": {"mean": 0.4414684740455903, "std": 0.09391030807549487, "cv": 0.21272256932620015, "shapiro_statistic": 0.9171212879694174, "shapiro_p_value": 0.26296013466420254, "ks_statistic": 0.19625703805867065, "ks_p_value": 0.6754570932478432, "is_normal_shapiro": true, "is_normal_ks": true}}, "overall_cv": 0.8217835795139391, "interpretation": "Shapley values show sufficient variation for bottleneck identification"}, "optimization_effectiveness": {"n_optimizations": 23, "pre_optimization_mean": 0.6361832355062154, "post_optimization_mean": 0.8227109591763464, "mean_improvement": 0.186527723670131, "t_statistic": 8.386987434067745, "p_value": 2.6709607022309587e-08, "is_significant": true, "cohens_d": 1.2037415270564016, "effect_size_interpretation": "large", "confidence_interval_95": [0.1404045089189543, 0.2326509384213077], "statistical_power": 0.9993735925859131}, "return_distribution_analysis": {"AAPL": {"n_observations": 60, "mean_return": 0.04483333333333331, "std_return": 1.3064219477471875, "skewness": -0.3681506622277138, "kurtosis": 1.7547988188566608, "var_95": -2.4205, "cvar_95": -3.283333333333333, "shapiro_p_value": 0.005241725718710875, "jarque_bera_p_value": 0.010814978918964843, "is_normal_shapiro": false, "is_normal_jarque_bera": false, "return_distribution_type": "heavy_tailed"}, "MSFT": {"n_observations": 60, "mean_return": 0.047333333333333345, "std_return": 1.2754343922322655, "skewness": 0.2752048737389412, "kurtosis": 1.8461508691382003, "var_95": -1.7505, "cvar_95": -2.8966666666666665, "shapiro_p_value": 0.01456822694639808, "jarque_bera_p_value": 0.009667071084870275, "is_normal_shapiro": false, "is_normal_jarque_bera": false, "return_distribution_type": "heavy_tailed"}, "NVDA": {"n_observations": 60, "mean_return": -0.0931666666666666, "std_return": 3.4040086621837817, "skewness": -1.6468744423002395, "kurtosis": 8.509547378925754, "var_95": -3.3059999999999996, "cvar_95": -9.123333333333333, "shapiro_p_value": 4.179231170855627e-06, "jarque_bera_p_value": 6.3119293561516126e-46, "is_normal_shapiro": false, "is_normal_jarque_bera": false, "return_distribution_type": "left_skewed"}, "META": {"n_observations": 60, "mean_return": 0.30683333333333335, "std_return": 1.8073548735344946, "skewness": 0.11954141944079101, "kurtosis": -0.34802458829648053, "var_95": -2.31, "cvar_95": -3.0525, "shapiro_p_value": 0.6349993057510593, "jarque_bera_p_value": 0.8002331571145564, "is_normal_shapiro": true, "is_normal_jarque_bera": true, "return_distribution_type": "approximately_normal"}}, "performance_claims_validation": {"AAPL_learning_trend": {"slope": -0.007276170297304868, "r_squared": 0.0005853251851227332, "p_value": 0.8544162182485274, "is_significant_trend": false, "trend_direction": "negative"}, "MSFT_learning_trend": {"slope": -0.08496343817727152, "r_squared": 0.24228639231750781, "p_value": 6.479189574799112e-05, "is_significant_trend": true, "trend_direction": "negative"}, "NVDA_learning_trend": {"slope": -0.2378568889166363, "r_squared": 0.5657986923987646, "p_value": 4.262581078083881e-12, "is_significant_trend": true, "trend_direction": "negative"}, "META_learning_trend": {"slope": 0.18807314884205073, "r_squared": 0.40976717546400504, "p_value": 3.6465386046796764e-08, "is_significant_trend": true, "trend_direction": "positive"}, "META_win_rate_test": {"observed_win_rate": 0.5, "n_trades": 57, "successes": 30, "binomial_p_value": 0.7913664277791806, "significantly_different_from_random": false, "better_than_random": false}, "MSFT_win_rate_test": {"observed_win_rate": 0.4, "n_trades": 50, "successes": 24, "binomial_p_value": 0.887724827340783, "significantly_different_from_random": false, "better_than_random": false}, "AAPL_win_rate_test": {"observed_win_rate": 0.43333333333333335, "n_trades": 45, "successes": 26, "binomial_p_value": 0.3712980344710104, "significantly_different_from_random": false, "better_than_random": false}, "NVDA_win_rate_test": {"observed_win_rate": 0.43333333333333335, "n_trades": 53, "successes": 26, "binomial_p_value": 1.0, "significantly_different_from_random": false, "better_than_random": false}}, "statistical_conclusions": ["Shapley values show sufficient variation (CV = 0.822) for reliable bottleneck identification.", "CG-OPO optimization shows statistically significant improvement (p = 0.0000, effect size = large).", "1 stocks show statistically significant positive learning trends."]}