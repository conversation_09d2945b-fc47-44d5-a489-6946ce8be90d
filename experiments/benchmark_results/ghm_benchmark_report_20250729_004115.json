{"experiment_summary": {"total_experiments": 19, "successful_experiments": 19, "failed_experiments": 0}, "performance_improvements": {"time_reduction": {"mean": -252.0932534942097, "min": -348.6754966887417, "max": -226.46276595744678, "std": 31.230427903029373}, "execution_reduction": {"mean": 82.46419946323083, "min": 69.6969696969697, "max": 87.6470588235294, "std": 6.554871822605754}, "speedup_factor": {"mean": 0.28586484484522506, "min": 0.22287822878228783, "max": 0.30631364562118124, "std": 0.022102519128006974}}, "detailed_results": [{"config_name": "Standard_3-3-1_Architecture", "agent_architecture": [3, 3, 1], "coalition_count": 38, "coalition_size_avg": 4.473684210526316, "ghm_execution_time": 0.0005550384521484375, "ghm_total_executions": 21, "ghm_cache_hit_rate": 92.8082191780822, "ghm_memory_usage": 1344, "naive_execution_time": 0.0001590251922607422, "naive_total_executions": 170, "time_improvement_percent": -249.02548725637183, "execution_reduction_percent": 87.6470588235294, "speedup_factor": 0.2865120274914089, "results_match": true, "error_message": ""}, {"config_name": "Standard_3-3-1_Architecture", "agent_architecture": [3, 3, 1], "coalition_count": 38, "coalition_size_avg": 4.473684210526316, "ghm_execution_time": 0.0005171298980712891, "ghm_total_executions": 21, "ghm_cache_hit_rate": 92.8082191780822, "ghm_memory_usage": 1344, "naive_execution_time": 0.00015306472778320312, "naive_total_executions": 170, "time_improvement_percent": -237.8504672897196, "execution_reduction_percent": 87.6470588235294, "speedup_factor": 0.2959889349930844, "results_match": true, "error_message": ""}, {"config_name": "Standard_3-3-1_Architecture", "agent_architecture": [3, 3, 1], "coalition_count": 38, "coalition_size_avg": 4.473684210526316, "ghm_execution_time": 0.0005068778991699219, "ghm_total_executions": 21, "ghm_cache_hit_rate": 92.8082191780822, "ghm_memory_usage": 1344, "naive_execution_time": 0.00014901161193847656, "naive_total_executions": 170, "time_improvement_percent": -240.16000000000003, "execution_reduction_percent": 87.6470588235294, "speedup_factor": 0.2939793038570085, "results_match": true, "error_message": ""}, {"config_name": "Scalability_Test", "agent_architecture": [2, 2, 1], "coalition_count": 9, "coalition_size_avg": 3.6666666666666665, "ghm_execution_time": 0.00011515617370605469, "ghm_total_executions": 10, "ghm_cache_hit_rate": 77.27272727272727, "ghm_memory_usage": 640, "naive_execution_time": 2.9087066650390625e-05, "naive_total_executions": 33, "time_improvement_percent": -295.9016393442623, "execution_reduction_percent": 69.6969696969697, "speedup_factor": 0.2525879917184265, "results_match": true, "error_message": ""}, {"config_name": "Scalability_Test", "agent_architecture": [2, 2, 1], "coalition_count": 9, "coalition_size_avg": 3.6666666666666665, "ghm_execution_time": 0.00011086463928222656, "ghm_total_executions": 10, "ghm_cache_hit_rate": 77.27272727272727, "ghm_memory_usage": 640, "naive_execution_time": 2.7894973754882812e-05, "naive_total_executions": 33, "time_improvement_percent": -297.43589743589746, "execution_reduction_percent": 69.6969696969697, "speedup_factor": 0.25161290322580643, "results_match": true, "error_message": ""}, {"config_name": "Scalability_Test", "agent_architecture": [3, 3, 1], "coalition_count": 38, "coalition_size_avg": 4.473684210526316, "ghm_execution_time": 0.0005040168762207031, "ghm_total_executions": 21, "ghm_cache_hit_rate": 92.8082191780822, "ghm_memory_usage": 1344, "naive_execution_time": 0.00015306472778320312, "naive_total_executions": 170, "time_improvement_percent": -229.2834890965732, "execution_reduction_percent": 87.6470588235294, "speedup_factor": 0.30368968779564803, "results_match": true, "error_message": ""}, {"config_name": "Scalability_Test", "agent_architecture": [3, 3, 1], "coalition_count": 38, "coalition_size_avg": 4.473684210526316, "ghm_execution_time": 0.0005161762237548828, "ghm_total_executions": 21, "ghm_cache_hit_rate": 92.8082191780822, "ghm_memory_usage": 1344, "naive_execution_time": 0.00015497207641601562, "naive_total_executions": 170, "time_improvement_percent": -233.0769230769231, "execution_reduction_percent": 87.6470588235294, "speedup_factor": 0.3002309468822171, "results_match": true, "error_message": ""}, {"config_name": "Scalability_Test", "agent_architecture": [4, 4, 1], "coalition_count": 41, "coalition_size_avg": 4.951219512195122, "ghm_execution_time": 0.0006473064422607422, "ghm_total_executions": 36, "ghm_cache_hit_rate": 90.57591623036649, "ghm_memory_usage": 2304, "naive_execution_time": 0.00019407272338867188, "naive_total_executions": 203, "time_improvement_percent": -233.53808353808353, "execution_reduction_percent": 82.26600985221675, "speedup_factor": 0.2998158379373849, "results_match": true, "error_message": ""}, {"config_name": "Scalability_Test", "agent_architecture": [4, 4, 1], "coalition_count": 41, "coalition_size_avg": 4.951219512195122, "ghm_execution_time": 0.0006201267242431641, "ghm_total_executions": 36, "ghm_cache_hit_rate": 90.57591623036649, "ghm_memory_usage": 2304, "naive_execution_time": 0.00017905235290527344, "naive_total_executions": 203, "time_improvement_percent": -246.3382157123835, "execution_reduction_percent": 82.26600985221675, "speedup_factor": 0.2887351018838908, "results_match": true, "error_message": ""}, {"config_name": "Scalability_Test", "agent_architecture": [5, 3, 1], "coalition_count": 27, "coalition_size_avg": 4.62962962962963, "ghm_execution_time": 0.0004050731658935547, "ghm_total_executions": 35, "ghm_cache_hit_rate": 84.375, "ghm_memory_usage": 2240, "naive_execution_time": 0.00011372566223144531, "naive_total_executions": 125, "time_improvement_percent": -256.1844863731656, "execution_reduction_percent": 72.0, "speedup_factor": 0.2807533843437316, "results_match": true, "error_message": ""}, {"config_name": "Scalability_Test", "agent_architecture": [5, 3, 1], "coalition_count": 27, "coalition_size_avg": 4.62962962962963, "ghm_execution_time": 0.0003960132598876953, "ghm_total_executions": 35, "ghm_cache_hit_rate": 84.375, "ghm_memory_usage": 2240, "naive_execution_time": 0.00010800361633300781, "naive_total_executions": 125, "time_improvement_percent": -266.66666666666663, "execution_reduction_percent": 72.0, "speedup_factor": 0.2727272727272727, "results_match": true, "error_message": ""}, {"config_name": "Layer_Width_Impact", "agent_architecture": [3, 2, 1], "coalition_count": 21, "coalition_size_avg": 4.0476190476190474, "ghm_execution_time": 0.0003230571746826172, "ghm_total_executions": 15, "ghm_cache_hit_rate": 88.63636363636364, "ghm_memory_usage": 960, "naive_execution_time": 7.200241088867188e-05, "naive_total_executions": 85, "time_improvement_percent": -348.6754966887417, "execution_reduction_percent": 82.35294117647058, "speedup_factor": 0.22287822878228783, "results_match": true, "error_message": ""}, {"config_name": "Layer_Width_Impact", "agent_architecture": [3, 2, 1], "coalition_count": 21, "coalition_size_avg": 4.0476190476190474, "ghm_execution_time": 0.00025391578674316406, "ghm_total_executions": 15, "ghm_cache_hit_rate": 88.63636363636364, "ghm_memory_usage": 960, "naive_execution_time": 7.081031799316406e-05, "naive_total_executions": 85, "time_improvement_percent": -258.5858585858586, "execution_reduction_percent": 82.35294117647058, "speedup_factor": 0.27887323943661974, "results_match": true, "error_message": ""}, {"config_name": "Layer_Width_Impact", "agent_architecture": [3, 3, 1], "coalition_count": 38, "coalition_size_avg": 4.473684210526316, "ghm_execution_time": 0.0004990100860595703, "ghm_total_executions": 21, "ghm_cache_hit_rate": 92.8082191780822, "ghm_memory_usage": 1344, "naive_execution_time": 0.0001461505889892578, "naive_total_executions": 170, "time_improvement_percent": -241.43556280587276, "execution_reduction_percent": 87.6470588235294, "speedup_factor": 0.2928810320114668, "results_match": true, "error_message": ""}, {"config_name": "Layer_Width_Impact", "agent_architecture": [3, 3, 1], "coalition_count": 38, "coalition_size_avg": 4.473684210526316, "ghm_execution_time": 0.0004870891571044922, "ghm_total_executions": 21, "ghm_cache_hit_rate": 92.8082191780822, "ghm_memory_usage": 1344, "naive_execution_time": 0.00014495849609375, "naive_total_executions": 170, "time_improvement_percent": -236.01973684210526, "execution_reduction_percent": 87.6470588235294, "speedup_factor": 0.2976015663240333, "results_match": true, "error_message": ""}, {"config_name": "Layer_Width_Impact", "agent_architecture": [3, 4, 1], "coalition_count": 44, "coalition_size_avg": 4.659090909090909, "ghm_execution_time": 0.00060272216796875, "ghm_total_executions": 27, "ghm_cache_hit_rate": 92.64305177111717, "ghm_memory_usage": 1728, "naive_execution_time": 0.0001819133758544922, "naive_total_executions": 205, "time_improvement_percent": -231.32372214941023, "execution_reduction_percent": 86.82926829268293, "speedup_factor": 0.30181962025316456, "results_match": true, "error_message": ""}, {"config_name": "Layer_Width_Impact", "agent_architecture": [3, 4, 1], "coalition_count": 44, "coalition_size_avg": 4.659090909090909, "ghm_execution_time": 0.0005853176116943359, "ghm_total_executions": 27, "ghm_cache_hit_rate": 92.64305177111717, "ghm_memory_usage": 1728, "naive_execution_time": 0.000179290771484375, "naive_total_executions": 205, "time_improvement_percent": -226.46276595744678, "execution_reduction_percent": 86.82926829268293, "speedup_factor": 0.30631364562118124, "results_match": true, "error_message": ""}, {"config_name": "Layer_Width_Impact", "agent_architecture": [3, 5, 1], "coalition_count": 38, "coalition_size_avg": 5.2631578947368425, "ghm_execution_time": 0.0005917549133300781, "ghm_total_executions": 33, "ghm_cache_hit_rate": 91.58163265306123, "ghm_memory_usage": 2112, "naive_execution_time": 0.0001800060272216797, "naive_total_executions": 200, "time_improvement_percent": -228.74172185430464, "execution_reduction_percent": 83.5, "speedup_factor": 0.3041901692183723, "results_match": true, "error_message": ""}, {"config_name": "Layer_Width_Impact", "agent_architecture": [3, 5, 1], "coalition_count": 38, "coalition_size_avg": 5.2631578947368425, "ghm_execution_time": 0.0005931854248046875, "ghm_total_executions": 33, "ghm_cache_hit_rate": 91.58163265306123, "ghm_memory_usage": 2112, "naive_execution_time": 0.0001780986785888672, "naive_total_executions": 200, "time_improvement_percent": -233.06559571619815, "execution_reduction_percent": 83.5, "speedup_factor": 0.3002411575562701, "results_match": true, "error_message": ""}], "timestamp": "2025-07-29T00:41:15.071820"}