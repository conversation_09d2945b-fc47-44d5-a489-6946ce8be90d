#!/usr/bin/env python3
"""
Simplified metrics extraction for DAG-Shapley paper.
Generates only essential tables: trading performance and B&H comparison.
"""

import pandas as pd
import numpy as np
import json
from pathlib import Path

class SimplifiedMetricsExtractor:
    """Extract essential metrics for paper."""
    
    def __init__(self, data_dir: str = "../daily_returns_csv"):
        self.data_dir = Path(data_dir)
        
    def load_summary_statistics(self) -> pd.DataFrame:
        """Load summary statistics."""
        summary_file = self.data_dir / "stocks_summary_statistics.csv"
        return pd.read_csv(summary_file)
    
    def calculate_buy_and_hold_returns(self) -> dict:
        """Calculate Buy & Hold returns for comparison."""
        # Load daily returns to get price data
        daily_file = self.data_dir / "all_stocks_daily_returns.csv"
        daily_df = pd.read_csv(daily_file)
        
        bh_results = {}
        
        for stock in daily_df['main_stock'].unique():
            stock_data = daily_df[daily_df['main_stock'] == stock].copy()
            stock_data = stock_data.sort_values('global_day')
            
            if len(stock_data) == 0:
                continue
                
            # Calculate B&H return (first to last net value, assuming stock price movement)
            # Approximate from return data
            returns = stock_data['return_percentage'].values
            returns = returns[~np.isnan(returns)]
            
            # Calculate cumulative return for B&H
            cumulative_return = np.prod(1 + returns/100) - 1
            
            # Calculate statistics
            daily_returns = returns
            mean_return = np.mean(daily_returns)
            std_return = np.std(daily_returns)
            
            # Annualized metrics
            trading_days = len(returns)
            annualized_return = (1 + cumulative_return) ** (252/trading_days) - 1
            annualized_vol = std_return * np.sqrt(252)
            sharpe_ratio = (mean_return * 252) / annualized_vol if annualized_vol > 0 else 0
            
            # Max drawdown approximation
            cumulative_values = np.cumprod(1 + daily_returns/100)
            running_max = np.maximum.accumulate(cumulative_values)
            drawdowns = (cumulative_values - running_max) / running_max
            max_drawdown = abs(np.min(drawdowns)) * 100
            
            bh_results[stock] = {
                'total_return_pct': cumulative_return * 100,
                'annualized_return_pct': annualized_return * 100,
                'annualized_sharpe': sharpe_ratio,
                'max_drawdown_pct': max_drawdown,
                'trading_days': trading_days
            }
        
        return bh_results
    
    def generate_performance_table(self) -> dict:
        """Generate Table 1: Trading Performance Summary."""
        summary_df = self.load_summary_statistics()
        
        table_data = {
            'headers': ['Stock', 'Total Return', 'Annualized Return', 'Annualized Sharpe', 'Max Drawdown'],
            'rows': []
        }
        
        for _, row in summary_df.iterrows():
            # Calculate annualized metrics
            trading_days = row['trading_days']
            total_return = row['total_return_pct']
            annualized_return = (1 + total_return/100) ** (252/trading_days) - 1
            
            # Annualized Sharpe (already calculated correctly in source)
            annualized_sharpe = row['sharpe_ratio']
            
            # Max drawdown approximation from min daily return
            max_drawdown = abs(row['min_daily_return_pct'])
            
            table_data['rows'].append([
                row['stock'],
                f"{total_return:.2f}\\%",
                f"{annualized_return*100:.2f}\\%", 
                f"{annualized_sharpe:.2f}",
                f"{max_drawdown:.2f}\\%"
            ])
        
        return table_data
    
    def generate_comparison_table(self) -> dict:
        """Generate Table 2: DAG-Shapley vs Buy & Hold Comparison."""
        summary_df = self.load_summary_statistics()
        bh_results = self.calculate_buy_and_hold_returns()
        
        table_data = {
            'headers': ['Stock', 'Strategy', 'Annualized Return', 'Annualized Sharpe', 'Max Drawdown'],
            'rows': []
        }
        
        for _, row in summary_df.iterrows():
            stock = row['stock']
            
            # DAG-Shapley results
            trading_days = row['trading_days']
            total_return = row['total_return_pct']
            annualized_return_dag = (1 + total_return/100) ** (252/trading_days) - 1
            sharpe_dag = row['sharpe_ratio']
            max_dd_dag = abs(row['min_daily_return_pct'])
            
            # Add DAG-Shapley row
            table_data['rows'].append([
                stock,
                'DAG-Shapley',
                f"{annualized_return_dag*100:.2f}\\%",
                f"{sharpe_dag:.2f}",
                f"{max_dd_dag:.2f}\\%"
            ])
            
            # Add B&H row if available
            if stock in bh_results:
                bh = bh_results[stock]
                table_data['rows'].append([
                    '',  # Empty stock name for second row
                    'Buy \\& Hold',
                    f"{bh['annualized_return_pct']:.2f}\\%",
                    f"{bh['annualized_sharpe']:.2f}",
                    f"{bh['max_drawdown_pct']:.2f}\\%"
                ])
            
        return table_data
    
    def export_latex_tables(self, output_file: str = "paper_tables.json"):
        """Export tables in LaTeX-ready format."""
        
        tables = {
            'performance_table': self.generate_performance_table(),
            'comparison_table': self.generate_comparison_table()
        }
        
        output_path = self.data_dir.parent / "experiments" / output_file
        with open(output_path, 'w') as f:
            json.dump(tables, f, indent=2)
        
        print(f"Tables exported to: {output_path}")
        return tables
    
    def print_latex_tables(self):
        """Print LaTeX table code."""
        tables = self.export_latex_tables()
        
        print("\n=== TABLE 1: TRADING PERFORMANCE SUMMARY ===")
        print("\\begin{table}[h]")
        print("\\centering")
        print("\\caption{Trading Performance Summary}")
        print("\\label{tab:performance}")
        print("\\begin{tabular}{lcccc}")
        print("\\toprule")
        
        # Headers
        perf_table = tables['performance_table']
        print(" & ".join(perf_table['headers']) + " \\\\")
        print("\\midrule")
        
        # Rows
        for row in perf_table['rows']:
            print(" & ".join(row) + " \\\\")
        
        print("\\bottomrule")
        print("\\end{tabular}")
        print("\\end{table}")
        
        print("\n=== TABLE 2: COMPARISON WITH BUY & HOLD ===")
        print("\\begin{table}[h]")
        print("\\centering") 
        print("\\caption{Performance Comparison: DAG-Shapley vs Buy \\& Hold}")
        print("\\label{tab:comparison}")
        print("\\begin{tabular}{llccc}")
        print("\\toprule")
        
        # Headers
        comp_table = tables['comparison_table']
        print(" & ".join(comp_table['headers']) + " \\\\")
        print("\\midrule")
        
        # Rows with grouping
        current_stock = ""
        for row in comp_table['rows']:
            if row[0] != "":  # New stock
                if current_stock != "":
                    print("\\midrule")
                current_stock = row[0]
            print(" & ".join(row) + " \\\\")
        
        print("\\bottomrule")
        print("\\end{tabular}")
        print("\\end{table}")

def main():
    """Main execution."""
    print("Generating simplified tables for DAG-Shapley paper...")
    
    extractor = SimplifiedMetricsExtractor()
    extractor.print_latex_tables()
    
    print("\nTable generation completed.")

if __name__ == "__main__":
    main()