#!/usr/bin/env python3
"""
多智能体交易系统时间配置使用示例

这个示例展示了如何正确配置和使用新的时间配置功能，
确保系统在启动时就有完整的时间结构配置。
"""

import sys
import os
import logging
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def setup_logger():
    """设置日志"""
    logger = logging.getLogger("time_config_example")
    logger.setLevel(logging.INFO)
    
    if not logger.handlers:
        handler = logging.StreamHandler()
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        handler.setFormatter(formatter)
        logger.addHandler(handler)
    
    return logger

def example_1_standard_configuration():
    """示例1：标准日期范围配置"""
    logger = setup_logger()
    logger.info("=" * 60)
    logger.info("示例1：标准日期范围配置")
    logger.info("=" * 60)
    
    try:
        from contribution_assessment.assessor import ContributionAssessor
        
        # 标准配置：使用日期范围自动计算交易天数
        config = {
            "start_date": "2025-01-01",
            "end_date": "2025-03-31",  # 3个月的数据
            "stocks": ["AAPL", "MSFT"],
            "starting_cash": 1000000,
            "trading_days_per_week": 5,
            "weekly_evaluation_enabled": True,
            "optimization_frequency": 5,
            "verbose": True
        }
        
        logger.info("📋 配置信息:")
        logger.info(f"  模拟期间: {config['start_date']} 至 {config['end_date']}")
        logger.info(f"  股票代码: {config['stocks']}")
        logger.info(f"  每周交易日数: {config['trading_days_per_week']}")
        
        # 创建评估器
        assessor = ContributionAssessor(
            config=config,
            logger=logger,
            enable_opro=False  # 为了简化示例，禁用OPRO
        )
        
        # 展示时间配置结果
        time_config = assessor.time_config
        logger.info("✅ 时间配置结果:")
        logger.info(f"  总交易天数: {time_config.get('total_trading_days')}")
        logger.info(f"  总周数: {time_config.get('total_weeks')}")
        logger.info(f"  实际交易日: {len(time_config.get('actual_trading_days', []))} 个")
        logger.info(f"  周划分: {len(time_config.get('trading_weeks', []))} 个周期")
        logger.info(f"  日历初始化: {time_config.get('calendar_initialized', False)}")
        
        # 展示前几个周期的详细信息
        trading_weeks = time_config.get('trading_weeks', [])
        if trading_weeks:
            logger.info("📅 前5个交易周期:")
            for i, week in enumerate(trading_weeks[:5]):
                if week and len(week) > 0:
                    start_date = week[0] if week else "N/A"
                    end_date = week[-1] if week else "N/A"
                    logger.info(f"  第{i+1}周: {len(week)}天 ({start_date} 到 {end_date})")
                else:
                    logger.info(f"  第{i+1}周: 空周期")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 示例1执行失败: {e}")
        return False

def example_2_specified_simulation_days():
    """示例2：指定模拟天数配置"""
    logger = setup_logger()
    logger.info("\n" + "=" * 60)
    logger.info("示例2：指定模拟天数配置")
    logger.info("=" * 60)
    
    try:
        from contribution_assessment.assessor import ContributionAssessor
        
        # 指定模拟天数配置：明确设置要模拟的天数
        config = {
            "start_date": "2025-01-01",
            "end_date": "2025-02-28",
            "stocks": ["AAPL"],
            "starting_cash": 1000000,
            "simulation_days": 30,  # 明确指定30个交易日
            "trading_days_per_week": 5,
            "weekly_evaluation_enabled": True,
            "verbose": True
        }
        
        logger.info("📋 配置信息:")
        logger.info(f"  模拟期间: {config['start_date']} 至 {config['end_date']}")
        logger.info(f"  指定模拟天数: {config['simulation_days']}")
        logger.info(f"  股票代码: {config['stocks']}")
        
        # 创建评估器
        assessor = ContributionAssessor(
            config=config,
            logger=logger,
            enable_opro=False
        )
        
        # 展示时间配置结果
        time_config = assessor.time_config
        logger.info("✅ 时间配置结果:")
        logger.info(f"  总交易天数: {time_config.get('total_trading_days')}")
        logger.info(f"  总周数: {time_config.get('total_weeks')}")
        logger.info(f"  实际交易日: {len(time_config.get('actual_trading_days', []))} 个")
        
        # 验证配置是否按预期工作
        expected_days = config["simulation_days"]
        actual_days = time_config.get('total_trading_days')
        logger.info(f"🔍 配置验证:")
        logger.info(f"  期望天数: {expected_days}")
        logger.info(f"  实际天数: {actual_days}")
        logger.info(f"  配置正确: {expected_days == actual_days}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 示例2执行失败: {e}")
        return False

def example_3_time_config_usage():
    """示例3：时间配置在实际使用中的应用"""
    logger = setup_logger()
    logger.info("\n" + "=" * 60)
    logger.info("示例3：时间配置在实际使用中的应用")
    logger.info("=" * 60)
    
    try:
        from contribution_assessment.assessor import ContributionAssessor
        
        # 创建配置
        config = {
            "start_date": "2025-01-01",
            "end_date": "2025-01-31",
            "stocks": ["AAPL"],
            "starting_cash": 1000000,
            "trading_days_per_week": 5,
            "verbose": True
        }
        
        # 创建评估器
        assessor = ContributionAssessor(
            config=config,
            logger=logger,
            enable_opro=False
        )
        
        # 演示如何使用时间配置
        time_config = assessor.time_config
        
        logger.info("📊 时间配置使用示例:")
        
        # 1. 获取总体信息
        total_weeks = time_config.get('total_weeks', 0)
        total_days = time_config.get('total_trading_days', 0)
        logger.info(f"  模拟将运行 {total_weeks} 周，共 {total_days} 个交易日")
        
        # 2. 计算优化频率
        optimization_frequency = config.get('optimization_frequency', 5)
        optimization_times = total_days // optimization_frequency
        logger.info(f"  每 {optimization_frequency} 天优化一次，预计优化 {optimization_times} 次")
        
        # 3. 检查周划分
        trading_weeks = time_config.get('trading_weeks', [])
        if trading_weeks:
            logger.info(f"  周划分详情:")
            for i, week in enumerate(trading_weeks):
                week_days = len(week)
                logger.info(f"    第{i+1}周: {week_days}天")
        
        # 4. 验证配置一致性
        days_per_week = time_config.get('trading_days_per_week', 5)
        expected_weeks = (total_days + days_per_week - 1) // days_per_week
        actual_weeks = len(trading_weeks) if trading_weeks else total_weeks
        
        logger.info(f"🔍 一致性检查:")
        logger.info(f"  期望周数: {expected_weeks}")
        logger.info(f"  实际周数: {actual_weeks}")
        logger.info(f"  配置一致: {expected_weeks == actual_weeks}")
        
        # 5. 演示方法调用
        calculated_days = assessor._calculate_actual_simulation_days()
        logger.info(f"📈 方法调用示例:")
        logger.info(f"  _calculate_actual_simulation_days() 返回: {calculated_days}")
        logger.info(f"  与配置一致: {calculated_days == total_days}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 示例3执行失败: {e}")
        return False

def example_4_error_handling():
    """示例4：错误处理和降级机制"""
    logger = setup_logger()
    logger.info("\n" + "=" * 60)
    logger.info("示例4：错误处理和降级机制")
    logger.info("=" * 60)
    
    try:
        from contribution_assessment.assessor import ContributionAssessor
        
        # 创建可能导致错误的配置
        config = {
            "start_date": "2025-01-01",
            "end_date": "2025-01-31",
            "stocks": ["INVALID_STOCK"],  # 无效股票代码
            "starting_cash": 1000000,
            "trading_days_per_week": 5,
            "verbose": True
        }
        
        logger.info("📋 测试错误处理配置:")
        logger.info(f"  使用无效股票代码: {config['stocks']}")
        
        # 创建评估器（应该能够处理错误并使用降级配置）
        assessor = ContributionAssessor(
            config=config,
            logger=logger,
            enable_opro=False
        )
        
        # 检查时间配置是否仍然可用
        time_config = assessor.time_config
        logger.info("✅ 错误处理结果:")
        logger.info(f"  配置存在: {time_config is not None}")
        logger.info(f"  日历初始化: {time_config.get('calendar_initialized', False)}")
        logger.info(f"  总交易天数: {time_config.get('total_trading_days', 'N/A')}")
        logger.info(f"  错误信息: {time_config.get('error', '无错误')}")
        
        # 验证系统仍能正常工作
        calculated_days = assessor._calculate_actual_simulation_days()
        logger.info(f"📈 降级机制验证:")
        logger.info(f"  系统仍能计算模拟天数: {calculated_days}")
        logger.info(f"  使用了合理默认值: {calculated_days > 0}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 示例4执行失败: {e}")
        return False

if __name__ == "__main__":
    print("多智能体交易系统 - 时间配置使用示例")
    print("=" * 80)
    
    # 运行所有示例
    examples = [
        ("标准日期范围配置", example_1_standard_configuration),
        ("指定模拟天数配置", example_2_specified_simulation_days),
        ("时间配置实际应用", example_3_time_config_usage),
        ("错误处理机制", example_4_error_handling)
    ]
    
    results = []
    for name, example_func in examples:
        try:
            success = example_func()
            results.append((name, success))
        except Exception as e:
            print(f"❌ {name} 执行异常: {e}")
            results.append((name, False))
    
    # 总结结果
    print("\n" + "=" * 80)
    print("示例执行结果总结:")
    print("=" * 80)
    
    for name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {name}: {status}")
    
    success_count = sum(1 for _, success in results if success)
    total_count = len(results)
    
    print(f"\n总体结果: {success_count}/{total_count} 个示例成功")
    
    if success_count == total_count:
        print("🎉 所有示例都成功执行！时间配置功能工作正常。")
    else:
        print("⚠️ 部分示例执行失败，请检查配置和环境。")
    
    print("=" * 80)
