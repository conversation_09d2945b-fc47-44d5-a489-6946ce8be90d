#!/usr/bin/env python3
"""
快速交易回测分析
基于真实价格数据计算关键指标
仅支持做多交易，不支持做空
"""

import sqlite3
import json
import pandas as pd
import numpy as np
from pathlib import Path
from datetime import datetime

class QuickBacktester:
    def __init__(self, initial_capital: float = 1000000.0):
        self.initial_capital = initial_capital
        self.data_dir = Path('/Users/<USER>/Code/Multi_Agent_Optimization/data')
        
        # 加载交易动作数据
        with open('/Users/<USER>/Code/Multi_Agent_Optimization/trading_actions_80days.json', 'r') as f:
            self.trading_actions = json.load(f)
    
    def load_price_data(self, ticker: str) -> dict:
        """快速加载价格数据到字典"""
        db_path = self.data_dir / 'tickers' / ticker / f'{ticker}_data.db'
        
        conn = sqlite3.connect(db_path)
        query = """
        SELECT trade_date, Close 
        FROM ohlcv 
        WHERE trade_date >= '2025-01-02' AND trade_date <= '2025-04-22'
        ORDER BY trade_date
        """
        
        cursor = conn.execute(query)
        price_dict = {row[0]: row[1] for row in cursor.fetchall()}
        conn.close()
        
        return price_dict
    
    def backtest_stock(self, ticker: str) -> dict:
        """单只股票快速回测"""
        print(f"🔄 回测 {ticker}...")
        
        # 加载价格数据
        prices = self.load_price_data(ticker)
        actions = self.trading_actions[ticker]
        
        # 初始化
        portfolio_value = self.initial_capital
        position = 0  # 持仓股数
        cash = self.initial_capital
        portfolio_values = []
        daily_returns = []
        
        # 执行交易
        for action_data in actions:
            trade_date = action_data['date']
            direction = action_data['direction']
            
            if trade_date not in prices:
                continue
                
            close_price = prices[trade_date]
            
            # 执行交易决策（不支持做空）
            if direction == 'buy':
                if cash > 0:
                    shares_to_buy = cash / close_price
                    position += shares_to_buy
                    cash = 0
                    
            elif direction == 'sell':
                if position > 0:
                    # 平多仓
                    cash += position * close_price
                    position = 0
                # 如果没有持仓，卖出信号被忽略（不做空）
            
            # 计算组合价值（只有多头或现金）
            portfolio_value = cash + position * close_price
            
            portfolio_values.append(portfolio_value)
        
        # 计算指标
        if len(portfolio_values) < 2:
            return self._empty_result(ticker)
        
        # 总收益率
        total_return = (portfolio_values[-1] - self.initial_capital) / self.initial_capital
        
        # 年化收益率
        trading_days = len(portfolio_values)
        annual_return = (1 + total_return) ** (252 / trading_days) - 1
        
        # 计算日收益率
        pct_returns = []
        for i in range(1, len(portfolio_values)):
            pct_return = (portfolio_values[i] - portfolio_values[i-1]) / portfolio_values[i-1]
            pct_returns.append(pct_return)
        
        # 年化波动率
        if len(pct_returns) > 1:
            daily_volatility = np.std(pct_returns)
            annual_volatility = daily_volatility * np.sqrt(252)
            # 夏普比率
            risk_free_rate = 0.03
            sharpe_ratio = (annual_return - risk_free_rate) / annual_volatility if annual_volatility > 0 else 0
        else:
            annual_volatility = 0
            sharpe_ratio = 0
        
        # 最大回撤
        peak = self.initial_capital
        max_drawdown = 0
        for value in portfolio_values:
            if value > peak:
                peak = value
            drawdown = (value - peak) / peak
            max_drawdown = min(max_drawdown, drawdown)
        
        return {
            'ticker': ticker,
            'final_value': portfolio_values[-1],
            'total_return': total_return,
            'annual_return': annual_return,
            'annual_volatility': annual_volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'total_trades': len([a for a in actions if a['direction'] != 'hold'])
        }
    
    def _empty_result(self, ticker: str) -> dict:
        return {
            'ticker': ticker,
            'final_value': self.initial_capital,
            'total_return': 0,
            'annual_return': 0,
            'annual_volatility': 0,
            'sharpe_ratio': 0,
            'max_drawdown': 0,
            'total_trades': 0
        }
    
    def run_all_backtests(self):
        """运行所有股票回测"""
        stocks = ['AAPL', 'GOOG', 'NVDA', 'META']
        results = {}
        
        print("🚀 开始快速回测分析...")
        print(f"💰 初始资金: ${self.initial_capital:,.2f}")
        print("-" * 60)
        
        for stock in stocks:
            try:
                result = self.backtest_stock(stock)
                results[stock] = result
                print(f"✅ {stock} 完成")
            except Exception as e:
                print(f"❌ {stock} 失败: {e}")
        
        # 打印结果汇总
        print("\n" + "="*80)
        print("📊 回测结果汇总")
        print("="*80)
        
        # 创建表格数据
        table_data = []
        for ticker, result in results.items():
            table_data.append([
                ticker,
                f"${result['final_value']:,.0f}",
                f"{result['total_return']:.2%}",
                f"{result['annual_return']:.2%}",
                f"{result['annual_volatility']:.2%}",
                f"{result['sharpe_ratio']:.3f}",
                f"{result['max_drawdown']:.2%}",
                result['total_trades']
            ])
        
        # 打印表格
        headers = ["股票", "最终价值", "总收益率", "年化收益", "年化波动", "夏普比率", "最大回撤", "交易次数"]
        
        # 打印表头
        print(f"{'股票':<6} {'最终价值':<12} {'总收益率':<10} {'年化收益':<10} {'年化波动':<10} {'夏普比率':<10} {'最大回撤':<10} {'交易次数':<8}")
        print("-" * 80)
        
        # 打印数据行
        for row in table_data:
            print(f"{row[0]:<6} {row[1]:<12} {row[2]:<10} {row[3]:<10} {row[4]:<10} {row[5]:<10} {row[6]:<10} {row[7]:<8}")
        
        print("\n📈 策略特征分析:")
        print("-" * 40)
        
        # 计算策略特征
        for ticker, result in results.items():
            actions = self.trading_actions[ticker]
            buy_count = sum(1 for a in actions if a['direction'] == 'buy')
            sell_count = sum(1 for a in actions if a['direction'] == 'sell') 
            hold_count = sum(1 for a in actions if a['direction'] == 'hold')
            
            total_actions = len(actions)
            buy_pct = buy_count / total_actions * 100
            sell_pct = sell_count / total_actions * 100
            hold_pct = hold_count / total_actions * 100
            
            if buy_pct > 60:
                strategy_type = "强多型"
            elif sell_pct > 30:
                strategy_type = "积极型"
            elif hold_pct > 60:
                strategy_type = "保守型"
            else:
                strategy_type = "均衡型"
            
            print(f"{ticker:<6}: {strategy_type:<8} (买入{buy_pct:.0f}% 卖出{sell_pct:.0f}% 持有{hold_pct:.0f}%)")
        
        print("\n💡 投资建议:")
        print("-" * 40)
        
        # 找出最佳表现
        best_return = max(results.values(), key=lambda x: x['annual_return'])
        best_sharpe = max(results.values(), key=lambda x: x['sharpe_ratio'])
        lowest_drawdown = min(results.values(), key=lambda x: x['max_drawdown'])
        
        print(f"🏆 最高年化收益: {best_return['ticker']} ({best_return['annual_return']:.2%})")
        print(f"🎯 最高夏普比率: {best_sharpe['ticker']} ({best_sharpe['sharpe_ratio']:.3f})")
        print(f"🛡️  最小回撤: {lowest_drawdown['ticker']} ({lowest_drawdown['max_drawdown']:.2%})")
        
        return results

def main():
    """主函数"""
    print("🤖 AI交易策略快速回测系统 (仅支持做多)")
    print("="*50)
    
    backtester = QuickBacktester(initial_capital=1000000.0)
    results = backtester.run_all_backtests()
    
    print("\n✅ 回测完成!")
    
    # 保存结果
    with open('/Users/<USER>/Code/Multi_Agent_Optimization/backtest_results.json', 'w') as f:
        # 转换numpy类型为Python原生类型以便JSON序列化
        json_results = {}
        for ticker, result in results.items():
            json_results[ticker] = {k: float(v) if isinstance(v, np.floating) else v 
                                  for k, v in result.items()}
        json.dump(json_results, f, indent=2)
    
    print("💾 结果已保存到 backtest_results.json")

if __name__ == "__main__":
    main()