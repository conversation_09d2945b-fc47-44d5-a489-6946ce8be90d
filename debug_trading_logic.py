import sqlite3
import pandas as pd

# 模拟第一周的交易逻辑
conn = sqlite3.connect('data/tickers/AAPL/AAPL_data.db')
query = '''
SELECT trade_date, Open, Close 
FROM ohlcv 
WHERE ticker = 'AAPL' 
AND trade_date >= '2025-01-02' 
AND trade_date <= '2025-01-08' 
ORDER BY trade_date
'''
df = pd.read_sql(query, conn)
conn.close()

print('苹果股票第一周数据:')
print(df)
print()

# 模拟系统的交易逻辑
initial_cash = 1000000.0
cash = initial_cash
positions = 0
position_values = 0.0

print('=== 系统当前的交易逻辑 ===')
for i, row in df.iterrows():
    date = row['trade_date']
    price = row['Close']  # 系统使用收盘价执行交易和计算净值
    
    if i == 0:  # 第一天买入
        # 买入操作：用收盘价买入
        shares_to_buy = int(cash * 0.99 * 0.7 / price)  # 70%仓位
        buy_cost = shares_to_buy * price * 1.001  # 包含手续费
        cash -= buy_cost
        positions = shares_to_buy
        
        # 计算持仓价值：也用收盘价
        position_values = positions * price
        net_worth = cash + position_values
        
        print(f'{date}: 买入{shares_to_buy}股，价格${price:.2f}')
        print(f'  花费: ${buy_cost:.2f}, 剩余现金: ${cash:.2f}')
        print(f'  持仓价值: ${position_values:.2f}, 净值: ${net_worth:.2f}')
        
        previous_net_worth = net_worth
    else:
        # 其他天：只更新持仓价值
        position_values = positions * price
        net_worth = cash + position_values
        daily_return = (net_worth / previous_net_worth) - 1
        
        print(f'{date}: 持有，价格${price:.2f}')
        print(f'  持仓价值: ${position_values:.2f}, 净值: ${net_worth:.2f}')
        print(f'  日收益率: {daily_return:.4%}')
        
        previous_net_worth = net_worth

print()
print('=== 正确的交易逻辑应该是 ===')
cash = initial_cash
positions = 0
position_values = 0.0

for i, row in df.iterrows():
    date = row['trade_date']
    open_price = row['Open']
    close_price = row['Close']
    
    if i == 0:  # 第一天买入
        # 正确的买入操作：用开盘价买入
        shares_to_buy = int(cash * 0.99 * 0.7 / open_price)  # 70%仓位
        buy_cost = shares_to_buy * open_price * 1.001  # 包含手续费
        cash -= buy_cost
        positions = shares_to_buy
        
        # 计算持仓价值：用收盘价
        position_values = positions * close_price
        net_worth = cash + position_values
        
        print(f'{date}: 开盘价${open_price:.2f}买入{shares_to_buy}股，收盘价${close_price:.2f}')
        print(f'  花费: ${buy_cost:.2f}, 剩余现金: ${cash:.2f}')
        print(f'  持仓价值: ${position_values:.2f}, 净值: ${net_worth:.2f}')
        
        # 计算首日收益
        cost_basis = shares_to_buy * open_price  # 成本基础
        market_value = shares_to_buy * close_price  # 市场价值
        daily_return = (close_price - open_price) / open_price
        print(f'  首日股价变化: {daily_return:.4%}')
        
        previous_net_worth = net_worth
    else:
        # 其他天：只更新持仓价值
        position_values = positions * close_price
        net_worth = cash + position_values
        daily_return = (net_worth / previous_net_worth) - 1
        
        print(f'{date}: 持有，收盘价${close_price:.2f}')
        print(f'  持仓价值: ${position_values:.2f}, 净值: ${net_worth:.2f}')
        print(f'  日收益率: {daily_return:.4%}')
        
        previous_net_worth = net_worth

print()
print('=== 结论 ===')
print('问题1: 系统用收盘价执行买入，然后立即用同样的收盘价计算净值')
print('问题2: 这导致买入当天的收益率为0（或很小），掩盖了真实的市场变化')
print('解决方案: 应该用开盘价执行交易，用收盘价计算净值')