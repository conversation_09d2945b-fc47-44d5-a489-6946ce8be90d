#!/usr/bin/env python3
"""
TRA Financial Metrics Calculator

Extract TRA input data from test_results files and calculate comprehensive financial metrics:
- Sharpe Ratio
- Annualized Return 
- Maximum Drawdown
- Violation Rate (percentage of negative return days)
- Additional risk metrics
"""

import json
import os
import re
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import matplotlib.pyplot as plt
from datetime import datetime
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TRAFinancialAnalyzer:
    def __init__(self, test_results_dir: str):
        self.test_results_dir = Path(test_results_dir)
        self.risk_free_rate = 0.02  # 2% annual risk-free rate
        
    def extract_tra_inputs_from_logs(self, log_file_path: str) -> List[Dict]:
        """
        Extract TRA input lines from log files.
        
        Returns:
            List of dictionaries with date, cumulative_return, weekly_return
        """
        tra_inputs = []
        tra_pattern = r'🤖 TRA 输入: 日期=([^,]+), 累计收益=([-+]?\d*\.?\d+), 周收益=([-+]?\d*\.?\d+)'
        
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    match = re.search(tra_pattern, line)
                    if match:
                        date_str = match.group(1)
                        cumulative_return = float(match.group(2))
                        weekly_return = float(match.group(3))
                        
                        tra_inputs.append({
                            'line_number': str(line_num),
                            'date': date_str,
                            'cumulative_return': cumulative_return,
                            'weekly_return': weekly_return,
                            'full_line': line.strip()
                        })
        except Exception as e:
            logger.error(f"Error processing {log_file_path}: {e}")
            
        return tra_inputs
    
    def load_tra_data_from_json(self, json_file_path: str) -> List[Dict]:
        """Load TRA data from existing JSON files if available."""
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # Convert string values to float
                for item in data:
                    item['cumulative_return'] = float(item['cumulative_return'])
                    item['weekly_return'] = float(item['weekly_return'])
                return data
        except Exception as e:
            logger.error(f"Error loading {json_file_path}: {e}")
            return []
    
    def find_continuous_segments(self, tra_data: List[Dict]) -> List[List[Dict]]:
        """
        Find continuous segments of data without resets.
        """
        if not tra_data:
            return []
            
        # Sort by date to ensure chronological order
        sorted_data = sorted(tra_data, key=lambda x: x['date'])
        
        segments = []
        current_segment = []
        
        for i, item in enumerate(sorted_data):
            curr_cum = item['cumulative_return']
            
            if i == 0:
                current_segment = [item]
            else:
                prev_cum = sorted_data[i-1]['cumulative_return']
                
                # Check for reset: if current is 0 and previous was non-zero
                # or if there's a dramatic decrease suggesting reset
                is_reset = (curr_cum == 0.0 and prev_cum != 0.0) or \
                          (curr_cum < prev_cum and abs(curr_cum - prev_cum) > 0.3)
                
                if is_reset:
                    # End current segment and start new one
                    if len(current_segment) > 1:  # Only keep segments with multiple points
                        segments.append(current_segment)
                    current_segment = [item]
                else:
                    current_segment.append(item)
        
        # Add the last segment
        if len(current_segment) > 1:
            segments.append(current_segment)
            
        return segments
    
    def calculate_daily_returns(self, tra_data: List[Dict]) -> np.ndarray:
        """
        Calculate daily returns from cumulative returns, using the best continuous segment.
        """
        if not tra_data:
            return np.array([])
        
        # Find continuous segments
        segments = self.find_continuous_segments(tra_data)
        
        if not segments:
            return np.array([])
        
        # Use the longest segment (most complete data)
        best_segment = max(segments, key=len)
        
        logger.info(f"Found {len(segments)} continuous segments, using segment with {len(best_segment)} days")
        
        cumulative_returns = np.array([item['cumulative_return'] for item in best_segment])
        
        daily_returns = []
        
        for i in range(len(cumulative_returns)):
            if i == 0:
                # First day return equals cumulative return
                daily_return = cumulative_returns[i]
            else:
                prev_cum = cumulative_returns[i-1]
                curr_cum = cumulative_returns[i]
                
                # Normal calculation: daily return from cumulative returns
                if prev_cum == -1.0:  # Avoid division by zero
                    daily_return = 0.0
                else:
                    daily_return = (1 + curr_cum) / (1 + prev_cum) - 1
            
            daily_returns.append(daily_return)
        
        return np.array(daily_returns)
    
    def calculate_sharpe_ratio(self, daily_returns: np.ndarray, 
                             risk_free_rate: float = None) -> float:
        """
        Calculate Sharpe ratio.
        
        Args:
            daily_returns: Array of daily returns
            risk_free_rate: Annual risk-free rate (default: self.risk_free_rate)
        
        Returns:
            Sharpe ratio (annualized)
        """
        if risk_free_rate is None:
            risk_free_rate = self.risk_free_rate
            
        if len(daily_returns) == 0:
            return 0.0
            
        # Convert annual risk-free rate to daily
        daily_rf_rate = risk_free_rate / 252  # 252 trading days per year
        
        # Calculate excess returns
        excess_returns = daily_returns - daily_rf_rate
        
        # Calculate mean and std of excess returns
        mean_excess_return = np.mean(excess_returns)
        std_excess_return = np.std(excess_returns, ddof=1)
        
        if std_excess_return == 0:
            return 0.0
            
        # Annualize Sharpe ratio
        sharpe_ratio = (mean_excess_return / std_excess_return) * np.sqrt(252)
        
        return sharpe_ratio
    
    def calculate_annualized_return(self, daily_returns: np.ndarray) -> float:
        """
        Calculate annualized return using compound return formula.
        """
        if len(daily_returns) == 0:
            return 0.0
            
        # Calculate total return
        total_return = np.prod(1 + daily_returns) - 1
        
        # Annualize based on number of trading days
        num_days = len(daily_returns)
        if num_days == 0:
            return 0.0
            
        # Assume 252 trading days per year
        annualized_return = (1 + total_return) ** (252 / num_days) - 1
        
        return annualized_return
    
    def calculate_maximum_drawdown(self, daily_returns: np.ndarray) -> Tuple[float, int, int]:
        """
        Calculate maximum drawdown and its duration.
        
        Returns:
            Tuple of (max_drawdown, start_index, end_index)
        """
        if len(daily_returns) == 0:
            return 0.0, 0, 0
            
        # Calculate cumulative returns
        cumulative_returns = np.cumprod(1 + daily_returns)
        
        # Calculate running maximum (peak)
        running_max = np.maximum.accumulate(cumulative_returns)
        
        # Calculate drawdown at each point
        drawdown = (cumulative_returns - running_max) / running_max
        
        # Find maximum drawdown
        max_drawdown_idx = np.argmin(drawdown)
        max_drawdown = abs(drawdown[max_drawdown_idx])
        
        # Find the peak before maximum drawdown
        peak_idx = np.argmax(running_max[:max_drawdown_idx + 1])
        
        return max_drawdown, peak_idx, max_drawdown_idx
    
    def calculate_violation_rate(self, daily_returns: np.ndarray) -> float:
        """
        Calculate violation rate (percentage of negative return days).
        """
        if len(daily_returns) == 0:
            return 0.0
            
        negative_days = np.sum(daily_returns < 0)
        violation_rate = negative_days / len(daily_returns)
        
        return violation_rate
    
    def calculate_additional_metrics(self, daily_returns: np.ndarray) -> Dict:
        """
        Calculate additional risk and return metrics.
        """
        if len(daily_returns) == 0:
            return {}
            
        metrics = {
            'volatility_annualized': np.std(daily_returns, ddof=1) * np.sqrt(252),
            'skewness': float(pd.Series(daily_returns).skew()),
            'kurtosis': float(pd.Series(daily_returns).kurtosis()),
            'var_95': float(np.percentile(daily_returns, 5)),  # 5% VaR
            'var_99': float(np.percentile(daily_returns, 1)),  # 1% VaR
            'max_daily_return': float(np.max(daily_returns)),
            'min_daily_return': float(np.min(daily_returns)),
            'positive_days_ratio': float(np.sum(daily_returns > 0) / len(daily_returns)),
        }
        
        return metrics
    
    def analyze_stock(self, stock_symbol: str) -> Dict:
        """
        Analyze a single stock's TRA data and calculate all metrics.
        """
        logger.info(f"Analyzing {stock_symbol}")
        
        # Try to load from JSON first, then from log, then from markdown
        json_path = self.test_results_dir / f"{stock_symbol}_phase1_tra_inputs.json"
        log_path = self.test_results_dir / f"{stock_symbol}_0101_0430.log"
        md_path = self.test_results_dir / f"{stock_symbol}_phase1.md"
        
        tra_data = []
        
        if json_path.exists():
            tra_data = self.load_tra_data_from_json(str(json_path))
            logger.info(f"Loaded {len(tra_data)} TRA entries from JSON for {stock_symbol}")
        elif log_path.exists():
            tra_data = self.extract_tra_inputs_from_logs(str(log_path))
            logger.info(f"Extracted {len(tra_data)} TRA entries from log for {stock_symbol}")
        elif md_path.exists():
            tra_data = self.extract_tra_inputs_from_logs(str(md_path))
            logger.info(f"Extracted {len(tra_data)} TRA entries from markdown for {stock_symbol}")
        else:
            logger.warning(f"No TRA data found for {stock_symbol}")
            return {}
        
        if not tra_data:
            return {}
        
        # Find continuous segments and get the best one
        segments = self.find_continuous_segments(tra_data)
        if not segments:
            return {}
        
        # Use the longest segment (most complete data)
        best_segment = max(segments, key=len)
        logger.info(f"Using segment with {len(best_segment)} days for {stock_symbol}")
        
        # Calculate daily returns from the best segment
        daily_returns = self.calculate_daily_returns(tra_data)
        
        if len(daily_returns) == 0:
            return {}
        
        # Calculate all metrics
        sharpe_ratio = self.calculate_sharpe_ratio(daily_returns)
        annualized_return = self.calculate_annualized_return(daily_returns)
        max_drawdown, dd_start, dd_end = self.calculate_maximum_drawdown(daily_returns)
        violation_rate = self.calculate_violation_rate(daily_returns)
        additional_metrics = self.calculate_additional_metrics(daily_returns)
        
        # Calculate cumulative return
        total_return = np.prod(1 + daily_returns) - 1
        
        results = {
            'stock_symbol': stock_symbol,
            'total_trading_days': len(daily_returns),
            'total_return': float(total_return),
            'annualized_return': float(annualized_return),
            'sharpe_ratio': float(sharpe_ratio),
            'maximum_drawdown': float(max_drawdown),
            'drawdown_start_day': int(dd_start),
            'drawdown_end_day': int(dd_end),
            'violation_rate': float(violation_rate),
            'mean_daily_return': float(np.mean(daily_returns)),
            **additional_metrics,
            'daily_returns': daily_returns.tolist(),
            'dates': [item['date'] for item in best_segment]
        }
        
        return results
    
    def analyze_all_stocks(self) -> Dict[str, Dict]:
        """
        Analyze all stocks in the test_results directory.
        """
        results = {}
        
        # Find all stock symbols from JSON files or log files
        stock_symbols = set()
        
        # From JSON files
        for json_file in self.test_results_dir.glob("*_phase1_tra_inputs.json"):
            stock_symbol = json_file.stem.replace("_phase1_tra_inputs", "")
            stock_symbols.add(stock_symbol)
        
        # From log files
        for log_file in self.test_results_dir.glob("*_0101_0430.log"):
            stock_symbol = log_file.stem.replace("_0101_0430", "")
            stock_symbols.add(stock_symbol)
        
        # From markdown files (like AAPL_phase1.md)
        for md_file in self.test_results_dir.glob("*_phase1.md"):
            stock_symbol = md_file.stem.replace("_phase1", "")
            stock_symbols.add(stock_symbol)
        
        logger.info(f"Found {len(stock_symbols)} stocks to analyze: {list(stock_symbols)}")
        
        for stock_symbol in sorted(stock_symbols):
            stock_results = self.analyze_stock(stock_symbol)
            if stock_results:
                results[stock_symbol] = stock_results
        
        return results
    
    def generate_summary_report(self, all_results: Dict[str, Dict]) -> pd.DataFrame:
        """
        Generate a summary report DataFrame.
        """
        summary_data = []
        
        for stock_symbol, results in all_results.items():
            summary_data.append({
                'Stock': stock_symbol,
                'Trading Days': results['total_trading_days'],
                'Total Return (%)': f"{results['total_return'] * 100:.2f}",
                'Annualized Return (%)': f"{results['annualized_return'] * 100:.2f}",
                'Sharpe Ratio': f"{results['sharpe_ratio']:.3f}",
                'Max Drawdown (%)': f"{results['maximum_drawdown'] * 100:.2f}",
                'Violation Rate (%)': f"{results['violation_rate'] * 100:.2f}",
                'Volatility (%)': f"{results['volatility_annualized'] * 100:.2f}",
                'Mean Daily Return (%)': f"{results['mean_daily_return'] * 100:.3f}",
                'Skewness': f"{results['skewness']:.3f}",
                'Kurtosis': f"{results['kurtosis']:.3f}",
                'VaR 95% (%)': f"{results['var_95'] * 100:.3f}",
                'VaR 99% (%)': f"{results['var_99'] * 100:.3f}",
                'Positive Days (%)': f"{results['positive_days_ratio'] * 100:.1f}"
            })
        
        return pd.DataFrame(summary_data)
    
    def create_visualizations(self, all_results: Dict[str, Dict], output_dir: str):
        """
        Create comprehensive visualizations for the analysis.
        """
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # 1. Cumulative returns comparison
        plt.figure(figsize=(14, 8))
        for stock_symbol, results in all_results.items():
            daily_returns = np.array(results['daily_returns'])
            cumulative_returns = np.cumprod(1 + daily_returns)
            plt.plot(range(len(cumulative_returns)), 
                    (cumulative_returns - 1) * 100, 
                    label=stock_symbol, linewidth=2)
        
        plt.title('Cumulative Returns Comparison', fontsize=16, fontweight='bold')
        plt.xlabel('Trading Days')
        plt.ylabel('Cumulative Return (%)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        plt.savefig(output_path / 'cumulative_returns_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 2. Risk-Return scatter plot
        plt.figure(figsize=(12, 8))
        for stock_symbol, results in all_results.items():
            plt.scatter(results['volatility_annualized'] * 100, 
                       results['annualized_return'] * 100,
                       s=100, label=stock_symbol, alpha=0.7)
            plt.annotate(stock_symbol, 
                        (results['volatility_annualized'] * 100, 
                         results['annualized_return'] * 100),
                        xytext=(5, 5), textcoords='offset points', fontsize=10)
        
        plt.title('Risk-Return Analysis', fontsize=16, fontweight='bold')
        plt.xlabel('Annualized Volatility (%)')
        plt.ylabel('Annualized Return (%)')
        plt.grid(True, alpha=0.3)
        plt.legend()
        plt.tight_layout()
        plt.savefig(output_path / 'risk_return_scatter.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        # 3. Metrics comparison radar chart (if more than one stock)
        if len(all_results) > 1:
            self._create_metrics_comparison_chart(all_results, output_path)
        
        logger.info(f"Visualizations saved to {output_path}")
    
    def _create_metrics_comparison_chart(self, all_results: Dict[str, Dict], output_path: Path):
        """Create a radar chart comparing key metrics across stocks."""
        try:
            import matplotlib.pyplot as plt
            
            metrics = ['annualized_return', 'sharpe_ratio', 'maximum_drawdown', 
                      'violation_rate', 'volatility_annualized']
            metric_labels = ['Ann. Return', 'Sharpe Ratio', 'Max Drawdown', 
                            'Violation Rate', 'Volatility']
            
            fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
            
            angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
            angles += angles[:1]  # Complete the circle
            
            for stock_symbol, results in all_results.items():
                values = []
                for metric in metrics:
                    val = results[metric]
                    # Normalize values for radar chart (0-1 scale)
                    if metric == 'annualized_return':
                        values.append(max(0, min(1, (val + 0.5) / 1.0)))  # -50% to +50%
                    elif metric == 'sharpe_ratio':
                        values.append(max(0, min(1, (val + 2) / 4)))  # -2 to +2
                    elif metric in ['maximum_drawdown', 'violation_rate', 'volatility_annualized']:
                        values.append(max(0, min(1, 1 - val)))  # Invert so lower is better
                    else:
                        values.append(max(0, min(1, val)))
                
                values += values[:1]  # Complete the circle
                
                ax.plot(angles, values, 'o-', linewidth=2, label=stock_symbol)
                ax.fill(angles, values, alpha=0.25)
            
            ax.set_xticks(angles[:-1])
            ax.set_xticklabels(metric_labels)
            ax.set_ylim(0, 1)
            ax.set_title('Financial Metrics Comparison', fontsize=16, fontweight='bold', pad=20)
            ax.legend(loc='upper right', bbox_to_anchor=(1.2, 1.0))
            
            plt.tight_layout()
            plt.savefig(output_path / 'metrics_radar_chart.png', dpi=300, bbox_inches='tight')
            plt.close()
            
        except Exception as e:
            logger.warning(f"Could not create radar chart: {e}")


def main():
    """Main execution function."""
    test_results_dir = "/Users/<USER>/Code/Multi_Agent_Optimization/test_results"
    output_dir = "/Users/<USER>/Code/Multi_Agent_Optimization/test_results/financial_metrics_analysis"
    
    logger.info("Starting TRA Financial Metrics Analysis")
    logger.info("=" * 60)
    
    # Initialize analyzer
    analyzer = TRAFinancialAnalyzer(test_results_dir)
    
    # Analyze all stocks
    all_results = analyzer.analyze_all_stocks()
    
    if not all_results:
        logger.error("No TRA data found to analyze")
        return
    
    logger.info(f"Successfully analyzed {len(all_results)} stocks")
    
    # Generate summary report
    summary_df = analyzer.generate_summary_report(all_results)
    
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Save detailed results as JSON
    detailed_results_path = os.path.join(output_dir, "detailed_financial_metrics.json")
    with open(detailed_results_path, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    # Save summary as CSV
    summary_csv_path = os.path.join(output_dir, "financial_metrics_summary.csv")
    summary_df.to_csv(summary_csv_path, index=False)
    
    # Create visualizations
    analyzer.create_visualizations(all_results, output_dir)
    
    # Print summary to console
    print("\n" + "=" * 80)
    print("FINANCIAL METRICS SUMMARY")
    print("=" * 80)
    print(summary_df.to_string(index=False))
    print("\n" + "=" * 80)
    print(f"Detailed results saved to: {detailed_results_path}")
    print(f"Summary CSV saved to: {summary_csv_path}")
    print(f"Visualizations saved to: {output_dir}")
    print("=" * 80)


if __name__ == "__main__":
    main()