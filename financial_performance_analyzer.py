#!/usr/bin/env python3
"""
Financial Performance Analyzer
从提取的日志段落中分析股票交易的财务表现

计算指标:
- 每日收益率序列
- 年化收益率
- 年化夏普率
- 最大回撤
"""
import re
import os
import glob
import json
import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional
from pathlib import Path
from dataclasses import dataclass
from datetime import datetime, timedelta


@dataclass
class DailyReturn:
    """每日收益率数据"""
    day: int
    date: str
    return_rate: float
    net_value: float
    action: str
    stock: str


@dataclass
class StockPerformance:
    """股票表现指标"""
    stock_symbol: str
    total_days: int
    total_weeks: int
    daily_returns: List[float]
    cumulative_returns: List[float]
    net_values: List[float]
    
    # 计算得出的指标
    annualized_return: float
    annualized_sharpe: float
    max_drawdown: float
    volatility: float
    
    # 额外统计
    win_rate: float
    best_day: float
    worst_day: float


class FinancialPerformanceAnalyzer:
    """财务表现分析器"""
    
    def __init__(self):
        self.return_pattern = re.compile(r'收益率=([+-]?\d+\.\d+), 净值=\$([0-9,]+\.\d+)')
        self.action_pattern = re.compile(r'交易动作: \{\'?([A-Z_]+)\'?: ([0-9.]+)\}')
        self.day_pattern = re.compile(r'第(\d+)天')
        self.risk_free_rate = 0.02  # 假设无风险利率为2%
    
    def parse_log_section(self, file_path: str) -> List[DailyReturn]:
        """解析单个日志段落文件"""
        returns = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
                lines = content.strip().split('\n')
            
            current_stock = None
            
            for line in lines:
                # 提取日期
                date_match = re.search(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                date_str = date_match.group(1) if date_match else ""
                
                # 提取交易动作
                action_match = self.action_pattern.search(line)
                if action_match:
                    current_stock = action_match.group(1)
                    if current_stock == '__HOLD__':
                        current_stock = 'HOLD'
                
                # 提取收益率和净值
                return_match = self.return_pattern.search(line)
                day_match = self.day_pattern.search(line)
                
                if return_match and day_match:
                    day = int(day_match.group(1))
                    return_rate = float(return_match.group(1))
                    net_value_str = return_match.group(2).replace(',', '')
                    net_value = float(net_value_str)
                    
                    returns.append(DailyReturn(
                        day=day,
                        date=date_str,
                        return_rate=return_rate,
                        net_value=net_value,
                        action=current_stock or 'UNKNOWN',
                        stock=current_stock or 'UNKNOWN'
                    ))
        
        except Exception as e:
            print(f"解析文件 {file_path} 时出错: {e}")
        
        return returns
    
    def parse_all_sections(self, sections_dir: str) -> Dict[str, List[DailyReturn]]:
        """解析所有日志段落文件"""
        all_returns = {}
        
        # 按文件名排序获取所有段落文件
        section_files = glob.glob(os.path.join(sections_dir, "*section_*.log"))
        section_files.sort()
        
        print(f"找到 {len(section_files)} 个段落文件")
        
        for file_path in section_files:
            filename = os.path.basename(file_path)
            # 提取基础文件名（去掉section部分）
            base_name = filename.split('_section_')[0]
            
            if base_name not in all_returns:
                all_returns[base_name] = []
            
            section_returns = self.parse_log_section(file_path)
            all_returns[base_name].extend(section_returns)
            
            print(f"从 {filename} 解析出 {len(section_returns)} 条收益率记录")
        
        return all_returns
    
    def calculate_performance_metrics(self, returns: List[DailyReturn]) -> StockPerformance:
        """计算股票表现指标"""
        if not returns:
            raise ValueError("收益率数据为空")
        
        # 排序确保按日期顺序
        returns.sort(key=lambda x: x.day)
        
        # 提取数据
        daily_returns = [r.return_rate for r in returns]
        net_values = [r.net_value for r in returns]
        
        # 计算累计收益率
        initial_value = net_values[0] / (1 + daily_returns[0])  # 估算初始值
        cumulative_returns = []
        for net_value in net_values:
            cum_return = (net_value - initial_value) / initial_value
            cumulative_returns.append(cum_return)
        
        # 基础统计
        daily_returns_array = np.array(daily_returns)
        mean_return = np.mean(daily_returns_array)
        std_return = np.std(daily_returns_array, ddof=1)
        
        # 年化指标（假设一年252个交易日）
        annualized_return = (1 + mean_return) ** 252 - 1
        annualized_volatility = std_return * np.sqrt(252)
        annualized_sharpe = (annualized_return - self.risk_free_rate) / annualized_volatility if annualized_volatility > 0 else 0
        
        # 最大回撤
        peak = net_values[0]
        max_drawdown = 0
        for value in net_values:
            if value > peak:
                peak = value
            drawdown = (peak - value) / peak
            if drawdown > max_drawdown:
                max_drawdown = drawdown
        
        # 胜率
        positive_days = sum(1 for r in daily_returns if r > 0)
        win_rate = positive_days / len(daily_returns)
        
        # 最佳和最差单日表现
        best_day = max(daily_returns)
        worst_day = min(daily_returns)
        
        # 获取主要交易股票
        stock_actions = [r.stock for r in returns if r.stock != 'HOLD']
        main_stock = max(set(stock_actions), key=stock_actions.count) if stock_actions else 'MIXED'
        
        return StockPerformance(
            stock_symbol=main_stock,
            total_days=len(returns),
            total_weeks=len(returns) // 5,  # 假设每周5个交易日
            daily_returns=daily_returns,
            cumulative_returns=cumulative_returns,
            net_values=net_values,
            annualized_return=annualized_return,
            annualized_sharpe=annualized_sharpe,
            max_drawdown=max_drawdown,
            volatility=annualized_volatility,
            win_rate=win_rate,
            best_day=best_day,
            worst_day=worst_day
        )
    
    def analyze_all_stocks(self, sections_dir: str) -> Dict[str, StockPerformance]:
        """分析所有股票的表现"""
        print("开始解析所有日志段落...")
        all_returns = self.parse_all_sections(sections_dir)
        
        stock_performances = {}
        
        for log_file, returns in all_returns.items():
            if returns:
                print(f"\n分析 {log_file} 的表现指标...")
                try:
                    performance = self.calculate_performance_metrics(returns)
                    stock_performances[log_file] = performance
                    print(f"  - 总交易日: {performance.total_days}")
                    print(f"  - 主要股票: {performance.stock_symbol}")
                    print(f"  - 年化收益率: {performance.annualized_return:.2%}")
                    print(f"  - 年化夏普率: {performance.annualized_sharpe:.3f}")
                    print(f"  - 最大回撤: {performance.max_drawdown:.2%}")
                except Exception as e:
                    print(f"分析 {log_file} 时出错: {e}")
        
        return stock_performances
    
    def generate_summary_report(self, performances: Dict[str, StockPerformance]) -> str:
        """生成总结报告"""
        report = []
        report.append("=" * 80)
        report.append("股票交易表现分析报告")
        report.append("=" * 80)
        report.append("")
        
        # 按年化收益率排序
        sorted_performances = sorted(
            performances.items(), 
            key=lambda x: x[1].annualized_return, 
            reverse=True
        )
        
        # 详细表现
        for i, (log_file, perf) in enumerate(sorted_performances, 1):
            report.append(f"{i}. {log_file}")
            report.append("-" * 60)
            report.append(f"主要交易股票: {perf.stock_symbol}")
            report.append(f"交易期间: {perf.total_days} 天 ({perf.total_weeks} 周)")
            report.append(f"年化收益率: {perf.annualized_return:.2%}")
            report.append(f"年化夏普率: {perf.annualized_sharpe:.3f}")
            report.append(f"年化波动率: {perf.volatility:.2%}")
            report.append(f"最大回撤: {perf.max_drawdown:.2%}")
            report.append(f"胜率: {perf.win_rate:.1%}")
            report.append(f"最佳单日: {perf.best_day:.2%}")
            report.append(f"最差单日: {perf.worst_day:.2%}")
            report.append(f"最终净值: ${perf.net_values[-1]:,.2f}")
            report.append("")
        
        # 汇总统计
        if performances:
            all_returns = [p.annualized_return for p in performances.values()]
            all_sharpes = [p.annualized_sharpe for p in performances.values()]
            all_drawdowns = [p.max_drawdown for p in performances.values()]
            
            report.append("汇总统计")
            report.append("-" * 40)
            report.append(f"平均年化收益率: {np.mean(all_returns):.2%}")
            report.append(f"平均年化夏普率: {np.mean(all_sharpes):.3f}")
            report.append(f"平均最大回撤: {np.mean(all_drawdowns):.2%}")
            report.append(f"最佳表现: {max(all_returns):.2%}")
            report.append(f"最差表现: {min(all_returns):.2%}")
        
        return "\n".join(report)
    
    def save_detailed_data(self, performances: Dict[str, StockPerformance], output_file: str):
        """保存详细数据到JSON文件"""
        data = {}
        
        for log_file, perf in performances.items():
            data[log_file] = {
                'stock_symbol': perf.stock_symbol,
                'total_days': perf.total_days,
                'total_weeks': perf.total_weeks,
                'annualized_return': perf.annualized_return,
                'annualized_sharpe': perf.annualized_sharpe,
                'max_drawdown': perf.max_drawdown,
                'volatility': perf.volatility,
                'win_rate': perf.win_rate,
                'best_day': perf.best_day,
                'worst_day': perf.worst_day,
                'final_net_value': perf.net_values[-1],
                'daily_returns': perf.daily_returns,
                'net_values': perf.net_values,
                'cumulative_returns': perf.cumulative_returns
            }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        print(f"详细数据已保存到: {output_file}")


def main():
    """主函数"""
    analyzer = FinancialPerformanceAnalyzer()
    
    # 设置路径
    sections_dir = "extracted_log_sections"
    output_report = "financial_performance_report.txt"
    output_data = "financial_performance_data.json"
    
    if not os.path.exists(sections_dir):
        print(f"错误: 找不到目录 {sections_dir}")
        print("请先运行 log_section_extractor.py 提取日志段落")
        return
    
    # 分析所有股票表现
    print("开始财务表现分析...")
    performances = analyzer.analyze_all_stocks(sections_dir)
    
    if not performances:
        print("未找到任何有效的交易数据")
        return
    
    # 生成报告
    print("\n生成分析报告...")
    report = analyzer.generate_summary_report(performances)
    
    # 保存报告
    with open(output_report, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"分析报告已保存到: {output_report}")
    
    # 保存详细数据
    analyzer.save_detailed_data(performances, output_data)
    
    # 显示报告
    print("\n" + "=" * 50)
    print("分析报告预览:")
    print("=" * 50)
    print(report)


if __name__ == "__main__":
    main()