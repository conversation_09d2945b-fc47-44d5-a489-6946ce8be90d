"""
轻量级投资组合状态管理器
用于跨周持仓状态传递的最小化实现
"""

from typing import Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)


class PortfolioStateManager:
    """简单的内存投资组合状态管理器"""
    
    # 类级别的状态存储，避免复杂的数据库操作
    _weekly_states: Dict[int, Dict[str, Any]] = {}
    
    @classmethod
    def save_week_end_state(cls, week_number: int, portfolio_state: Dict[str, Any]) -> None:
        """
        保存周末投资组合状态
        
        Args:
            week_number: 周数
            portfolio_state: 投资组合状态字典
        """
        try:
            cls._weekly_states[week_number] = portfolio_state.copy()
            logger.info(f"📊 保存第{week_number}周末投资组合状态: 现金=${portfolio_state.get('cash', 0):,.2f}")
        except Exception as e:
            logger.error(f"❌ 保存第{week_number}周状态失败: {e}")
    
    @classmethod
    def load_previous_week_state(cls, week_number: int) -> Optional[Dict[str, Any]]:
        """
        加载前一周的投资组合状态
        
        Args:
            week_number: 当前周数
            
        Returns:
            前一周的投资组合状态，如果不存在则返回None
        """
        previous_week = week_number - 1
        if previous_week < 1:
            return None
            
        previous_state = cls._weekly_states.get(previous_week)
        if previous_state:
            logger.info(f"🔄 加载第{previous_week}周投资组合状态: 现金=${previous_state.get('cash', 0):,.2f}")
            return previous_state.copy()
        else:
            logger.info(f"📝 第{previous_week}周无投资组合状态，将使用初始状态")
            return None
    
    @classmethod
    def extract_portfolio_state_from_trading_env(cls, trading_env) -> Dict[str, Any]:
        """
        从交易环境中提取投资组合状态（支持收益率跟踪）
        
        Args:
            trading_env: StockTradingEnv实例
            
        Returns:
            投资组合状态字典（包含收益率信息）
        """
        try:
            # 尝试使用新的简化收益率状态提取方法
            if hasattr(trading_env, 'get_simplified_return_state'):
                state = trading_env.get_simplified_return_state()
                logger.info(f"🔍 从交易环境提取简化收益率状态: 累计收益率={state.get('cumulative_return', 0):.4f}")
                return state
            
            # 回退到传统方法
            state = {
                "net_worth": getattr(trading_env, 'net_worth', 0),
                "current_day_index": getattr(trading_env, 'current_day_index', 0),
                # 收益率字段
                "cumulative_return": getattr(trading_env, 'cumulative_return', 0.0),
                "weekly_return": getattr(trading_env, 'weekly_return', 0.0),
                "last_week_return": getattr(trading_env, 'last_week_return', 0.0)
            }
            return state
        except Exception as e:
            logger.error(f"❌ 提取投资组合状态失败: {e}")
            return {}
    
    @classmethod
    def get_all_states(cls) -> Dict[int, Dict[str, Any]]:
        """获取所有保存的状态（用于调试）"""
        return cls._weekly_states.copy()
    
    @classmethod
    def clear_all_states(cls) -> None:
        """清除所有状态（用于测试）"""
        cls._weekly_states.clear()
        logger.info("🧹 清除所有投资组合状态")