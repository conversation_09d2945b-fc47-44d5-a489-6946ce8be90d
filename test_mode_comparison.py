#!/usr/bin/env python3
"""
对比正常模式和消融实验模式下的智能体LLM调用
"""

import sys
import os
import logging
import time
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

from contribution_assessment.service_factory import ServiceFactory
from contribution_assessment.refactored_assessor import RefactoredContributionAssessor

def test_agent_llm_calls(ablation_mode=False):
    """测试智能体LLM调用"""
    mode_name = "消融实验模式" if ablation_mode else "正常模式"
    print(f"\n=== 测试 {mode_name} ===")
    
    # 创建日志器
    logger = logging.getLogger(f"test_{mode_name}")
    logger.setLevel(logging.DEBUG)
    handler = logging.StreamHandler()
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    handler.setFormatter(formatter)
    logger.addHandler(handler)
    
    # 检查API key
    api_key = os.environ.get("ZHIPUAI_API_KEY")
    print(f"ZHIPUAI_API_KEY状态: {'已设置' if api_key else '未设置'}")
    if api_key:
        print(f"API Key长度: {len(api_key)}")
    
    try:
        # 创建配置
        config = {
            "stocks": ["NVDA"],
            "start_date": "2025-01-01",
            "end_date": "2025-01-05",  # 只测试几天
            "starting_cash": 1000000
        }
        
        print(f"创建 {mode_name} 的评估器...")
        
        # 创建评估器
        assessor = ServiceFactory.create_assessor_with_config(
            config=config,
            agents=None,
            logger=logger,
            llm_provider="zhipuai",
            enable_opro=False,
            ablation_mode=ablation_mode
        )
        
        print(f"评估器创建成功，ablation_mode={assessor.ablation_mode}")
        
        # 创建智能体并测试
        agents = assessor._create_agents()
        print(f"智能体创建成功: {list(agents.keys())}")
        
        # 检查智能体的LLM接口
        for agent_id, agent in agents.items():
            has_llm = hasattr(agent, 'llm_interface') and agent.llm_interface is not None
            if has_llm:
                has_client = hasattr(agent.llm_interface, 'client') and agent.llm_interface.client is not None
                provider = getattr(agent.llm_interface, 'provider', 'unknown')
                print(f"智能体 {agent_id}: LLM接口={has_llm}, 客户端={has_client}, provider={provider}")
            else:
                print(f"智能体 {agent_id}: 无LLM接口")
        
        # 测试单个智能体调用（选择TRA作为测试）
        if 'TRA' in agents:
            print(f"\n测试TRA智能体调用...")
            
            # 创建模拟状态
            test_state = {
                "current_date": "2025-01-02",
                "cash": 1000000,
                "positions": {},
                "cumulative_return": 0.0,
                "weekly_return": 0.0,
                "analyst_outputs": {
                    "NAA": {"analysis": "测试新闻分析"},
                    "TAA": {"analysis": "测试技术分析"},
                    "FAA": {"analysis": "测试基本面分析"}
                },
                "outlook_outputs": {
                    "BOA": {"outlook": "bullish", "confidence": 0.7},
                    "BeOA": {"outlook": "bearish", "confidence": 0.3},
                    "NOA": {"outlook": "neutral", "confidence": 0.5}
                }
            }
            
            start_time = time.time()
            try:
                result = agents['TRA'].process(test_state, is_full_coalition=True)
                execution_time = time.time() - start_time
                
                print(f"TRA执行结果:")
                print(f"  执行时间: {execution_time:.2f}秒")
                print(f"  结果类型: {type(result)}")
                print(f"  action: {result.get('action', 'N/A')}")
                print(f"  llm_used: {result.get('llm_used', 'N/A')}")
                
                if execution_time < 1.0:
                    print(f"  ⚠️ 执行时间过短，可能没有真正调用LLM")
                else:
                    print(f"  ✅ 执行时间正常，可能进行了真实的LLM调用")
                    
            except Exception as e:
                execution_time = time.time() - start_time
                print(f"TRA执行失败: {e}")
                print(f"执行时间: {execution_time:.2f}秒")
        
    except Exception as e:
        print(f"{mode_name} 测试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    print("开始对比测试...")
    
    # 测试正常模式
    test_agent_llm_calls(ablation_mode=False)
    
    # 测试消融实验模式
    test_agent_llm_calls(ablation_mode=True)
    
    print("\n测试完成!")

if __name__ == "__main__":
    main()