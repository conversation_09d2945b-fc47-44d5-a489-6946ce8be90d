#!/usr/bin/env python3
"""
批量检查多个股票的新闻数据完整性
用法: python check_news_data_batch.py [start_date] [end_date]
"""

import subprocess
import sys
import os
from datetime import datetime

def check_news_for_stocks(start_date="2024-08-01", end_date="2024-12-30"):
    """批量检查指定股票的新闻数据完整性"""
    
    # 股票列表
    stocks = ["GOOG", "NVDA", "META", "MSFT"]
    
    print(f"开始批量检查新闻数据完整性...")
    print(f"股票: {', '.join(stocks)}")
    print(f"日期范围: {start_date} 到 {end_date}")
    print("=" * 60)
    
    # 获取项目根目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    news_script_path = os.path.join(script_dir, "data", "get_news_data.py")
    
    if not os.path.exists(news_script_path):
        print(f"错误: 找不到新闻脚本 {news_script_path}")
        return False
    
    results = {}
    
    for i, stock in enumerate(stocks, 1):
        print(f"\n[{i}/{len(stocks)}] 检查 {stock} 的新闻数据...")
        print("-" * 40)
        
        try:
            # 运行检查命令
            result = subprocess.run([
                sys.executable, 
                news_script_path,
                stock,
                start_date,
                end_date,
                "--check"
            ], capture_output=True, text=True, timeout=300)  # 5分钟超时
            
            # 解析输出获取统计信息
            output = result.stderr
            missing_count = 0
            total_count = 0
            existing_count = 0
            
            # 简单解析统计信息
            for line in output.split('\n'):
                if "预期日期:" in line:
                    total_count = int(line.split()[-2])
                elif "已有数据:" in line:
                    existing_count = int(line.split()[2])
                elif "缺失数据:" in line:
                    missing_count = int(line.split()[2])
            
            results[stock] = {
                'total': total_count,
                'existing': existing_count,
                'missing': missing_count,
                'completeness': (existing_count / total_count * 100) if total_count > 0 else 0,
                'status': '✅ 完整' if missing_count == 0 else f'⚠️ 缺失{missing_count}天'
            }
            
            print(f"结果: {results[stock]['status']}")
            print(f"完整度: {results[stock]['completeness']:.1f}% ({existing_count}/{total_count})")
            
        except subprocess.TimeoutExpired:
            print(f"⏰ {stock} 检查超时")
            results[stock] = {'status': '⏰ 超时', 'completeness': 0}
        except Exception as e:
            print(f"❌ {stock} 检查异常: {e}")
            results[stock] = {'status': f'❌ 异常: {e}', 'completeness': 0}
    
    # 生成汇总报告
    print("\n" + "=" * 60)
    print("📊 汇总报告")
    print("=" * 60)
    
    print(f"{'股票':<8} {'状态':<12} {'完整度':<10} {'已有/总计':<12}")
    print("-" * 50)
    
    total_missing = 0
    total_stocks = len(stocks)
    complete_stocks = 0
    
    for stock, data in results.items():
        if isinstance(data.get('completeness'), (int, float)):
            completeness_str = f"{data['completeness']:.1f}%"
            existing = data.get('existing', 0)
            total = data.get('total', 0)
            ratio_str = f"{existing}/{total}"
            
            if data.get('missing', 0) == 0:
                complete_stocks += 1
            else:
                total_missing += data.get('missing', 0)
        else:
            completeness_str = "N/A"
            ratio_str = "N/A"
        
        print(f"{stock:<8} {data['status']:<12} {completeness_str:<10} {ratio_str:<12}")
    
    # 总体统计
    print("-" * 50)
    print(f"总体情况:")
    print(f"  - 完整股票: {complete_stocks}/{total_stocks} ({complete_stocks/total_stocks*100:.1f}%)")
    print(f"  - 总缺失天数: {total_missing}")
    
    if complete_stocks == total_stocks:
        print("🎉 所有股票数据都完整！")
    else:
        print(f"⚠️ 有 {total_stocks - complete_stocks} 只股票存在数据缺失")
    
    # 数据质量检查建议
    print(f"\n💡 建议:")
    if total_missing > 0:
        print(f"  - 对缺失数据的股票重新运行下载命令")
        print(f"  - 使用 --quality 参数检查数据质量")
        
        incomplete_stocks = [stock for stock, data in results.items() 
                           if data.get('missing', 0) > 0]
        if incomplete_stocks:
            print(f"\n📝 补充下载命令:")
            for stock in incomplete_stocks:
                print(f"    python data/get_news_data.py {stock} {start_date} {end_date}")
    
    return complete_stocks == total_stocks

def main():
    """主函数"""
    # 解析命令行参数
    if len(sys.argv) >= 3:
        start_date = sys.argv[1]
        end_date = sys.argv[2]
    else:
        start_date = "2024-08-01"
        end_date = "2024-12-30"
        print(f"使用默认日期范围: {start_date} 到 {end_date}")
        print("可以通过命令行参数指定: python check_news_data_batch.py <start_date> <end_date>")
    
    try:
        # 验证日期格式
        datetime.strptime(start_date, '%Y-%m-%d')
        datetime.strptime(end_date, '%Y-%m-%d')
        
        success = check_news_for_stocks(start_date, end_date)
        sys.exit(0 if success else 1)
        
    except ValueError as e:
        print(f"日期格式错误: {e}")
        print("请使用 YYYY-MM-DD 格式，例如: 2024-08-01")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n用户中断检查")
        sys.exit(1)
    except Exception as e:
        print(f"批量检查异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()