#!/usr/bin/env python3
"""
分析OPRO候选生成失败的根本原因并提供解决方案
"""

import json

def analyze_issue():
    """分析问题"""
    
    print("=== OPRO候选生成失败问题分析 ===\n")
    
    print("1. 问题现象:")
    print("   - 日志显示'生成 8 个候选提示词...'")
    print("   - 但结果是'成功生成 0 个候选提示词'")
    print("   - 导致'候选生成失败'错误\n")
    
    print("2. 调试发现:")
    print("   - LLM接口工作正常，可以成功调用")
    print("   - 简单的元提示词可以生成候选")
    print("   - 但实际的OPRO元提示词可能导致LLM返回JSON格式响应\n")
    
    print("3. 根本原因分析:")
    print("   a) LLM响应处理问题:")
    print("      - 当LLM返回JSON格式时，代码执行 str(response)")
    print("      - 这会生成很长的字符串，包含字典的字符串表示")
    print("      - 例如: \"{'key': 'value', ...}\" 格式")
    
    print("   b) 清理方法问题:")
    print("      - _clean_candidate_prompt 会移除所有 '[' 和 ']' 字符")
    print("      - 这会破坏JSON结构")
    print("      - 长度限制使用句号分割，但JSON字符串可能没有句号")
    
    print("   c) 验证方法问题:")
    print("      - _validate_candidate_prompt 要求长度 >= 10 字符")
    print("      - 可能过度清理后的内容长度不足")
    print("      - 或者内容格式不符合预期\n")
    
    print("4. 具体失败流程:")
    print("   1. LLM返回JSON格式: {'analyze_market_data': {...}}")
    print("   2. 代码转换为字符串: \"{'analyze_market_data': {...}}\"")
    print("   3. 清理方法移除括号: \"'analyze_market_data': ...\"")
    print("   4. 结果可能格式不正确或过短")
    print("   5. 验证失败，候选被拒绝\n")
    
    print("5. 解决方案:")
    print("   a) 改进LLM响应处理:")
    print("      - 检测JSON响应并智能处理")
    print("      - 提取有意义的文本内容而不是转换整个对象")
    
    print("   b) 改进清理方法:")
    print("      - 不要盲目移除所有括号")
    print("      - 更智能的长度限制处理")
    
    print("   c) 改进元提示词:")
    print("      - 明确要求LLM返回纯文本而不是JSON")
    print("      - 提供更明确的输出格式指导\n")

def demonstrate_issue():
    """演示问题"""
    
    print("=== 问题演示 ===\n")
    
    # 模拟LLM返回的JSON响应
    mock_llm_response = {
        'analyze_market_data': {
            'technical_indicators': ['moving_averages', 'rsi', 'bollinger_bands'],
            'fundamental_analysis': ['economic_indicators', 'financials'],
            'risk_assessment': 'medium',
            'decision_framework': 'balanced approach'
        }
    }
    
    print("1. 模拟LLM返回的JSON响应:")
    print(json.dumps(mock_llm_response, indent=2))
    
    # 当前代码的处理方式
    current_handling = str(mock_llm_response).strip()
    print(f"\n2. 当前代码处理 (str(response)):")
    print(f"长度: {len(current_handling)} 字符")
    print(f"内容: {current_handling}")
    
    # 清理后的结果
    cleaned = current_handling.replace("```", "").replace("[", "").replace("]", "")
    
    # 移除前缀
    prefixes_to_remove = [
        "好的，这是优化后的决策逻辑部分：",
        "优化后的决策逻辑：",
        "以下是优化的提示词：",
        "优化后的提示词：",
        "决策逻辑部分：",
        "这是优化后的",
        "根据分析，优化后的",
        "基于失败案例，优化的"
    ]
    
    for prefix in prefixes_to_remove:
        if cleaned.startswith(prefix):
            cleaned = cleaned[len(prefix):].strip()
    
    # 移除后缀
    suffixes_to_remove = [
        "以上是优化后的决策逻辑。",
        "这样可以提高决策质量。",
        "希望这个优化能够改善表现。"
    ]
    
    for suffix in suffixes_to_remove:
        if cleaned.endswith(suffix):
            cleaned = cleaned[:-len(suffix)].strip()
    
    # 移除多余的空白字符
    cleaned = " ".join(cleaned.split())
    
    # 限制长度
    max_length = 500
    if len(cleaned) > max_length:
        cleaned = cleaned[:max_length].rsplit("。", 1)[0] + "。"
    
    print(f"\n3. 清理后的结果:")
    print(f"长度: {len(cleaned)} 字符")
    print(f"内容: {cleaned}")
    
    # 验证
    is_valid = len(cleaned) >= 10
    print(f"\n4. 验证结果: {is_valid}")
    
    if not is_valid:
        print("   → 这就是候选生成失败的原因！")

def suggest_fixes():
    """建议修复方案"""
    
    print("\n=== 修复建议 ===\n")
    
    print("1. 立即修复 - 改进LLM响应处理:")
    print("""
def _handle_llm_response(self, response):
    if response and isinstance(response, dict):
        if "content" in response:
            return response.get("content", "").strip()
        else:
            # 智能处理JSON响应
            # 尝试提取有意义的文本内容
            if len(response) == 1:
                # 如果只有一个键值对，返回值
                return str(list(response.values())[0])
            else:
                # 返回JSON的文本描述
                return json.dumps(response, ensure_ascii=False)
    elif response:
        return str(response).strip()
    else:
        return ""
""")
    
    print("2. 改进清理方法:")
    print("""
def _clean_candidate_prompt(self, prompt: str) -> str:
    # 首先检查是否为JSON格式
    try:
        json_obj = json.loads(prompt)
        # 如果是JSON，提取有意义的内容
        if isinstance(json_obj, dict):
            # 可以根据具体需求提取内容
            return self._extract_meaningful_content(json_obj)
    except:
        pass
    
    # 原有的清理逻辑（但更保守）
    # 不要移除所有括号，只移除明显的格式标记
    prompt = prompt.replace("```json", "").replace("```", "")
    # ... 其他清理逻辑
""")
    
    print("3. 改进元提示词:")
    print("""
在元提示词末尾明确说明：
'请直接输出优化后的决策逻辑文本，不要使用JSON格式或代码块。
输出应该是可以直接使用的提示词文本。'
""")

if __name__ == "__main__":
    analyze_issue()
    demonstrate_issue()
    suggest_fixes()