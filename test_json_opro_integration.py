#!/usr/bin/env python3
"""
Test script for JSON reading and OPRO integration workflow validation.
Executes item #12 of the implementation checklist.
"""

import sys
import os
import json
import traceback
from typing import Dict, Any, List
from datetime import datetime

# Add project root to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """Test 1: Verify syntax correctness by testing all imports"""
    print("Test 1: Testing imports and syntax correctness...")
    
    try:
        from contribution_assessment.weekly_optimization_manager import WeeklyOptimizationManager
        print("✓ WeeklyOptimizationManager import successful")
    except Exception as e:
        print(f"✗ WeeklyOptimizationManager import failed: {e}")
        return False
    
    try:
        from contribution_assessment.services.simplified_opro_service import SimplifiedOproService
        print("✓ SimplifiedOproService import successful")
    except Exception as e:
        print(f"✗ SimplifiedOproService import failed: {e}")
        return False
    
    try:
        from state_management.opro_integration import OproIntegration
        print("✓ OproIntegration import successful")
    except Exception as e:
        print(f"✗ OproIntegration import failed: {e}")
        return False
    
    return True

def test_method_existence():
    """Test 2: Check completeness of newly implemented methods"""
    print("\nTest 2: Checking method existence and completeness...")
    
    try:
        from contribution_assessment.weekly_optimization_manager import WeeklyOptimizationManager
        from contribution_assessment.services.simplified_opro_service import SimplifiedOproService
        from state_management.opro_integration import OproIntegration
        
        # Test WeeklyOptimizationManager methods
        wom = WeeklyOptimizationManager({})
        required_methods = [
            'read_json_data_file',
            'convert_json_to_opro_format',
            'initialize_opro_with_json_data'
        ]
        
        for method in required_methods:
            if hasattr(wom, method):
                print(f"✓ WeeklyOptimizationManager.{method} exists")
            else:
                print(f"✗ WeeklyOptimizationManager.{method} missing")
                return False
        
        # Test SimplifiedOproService methods
        sos = SimplifiedOproService({})
        opro_methods = [
            'initialize_with_json_data'
        ]
        
        for method in opro_methods:
            if hasattr(sos, method):
                print(f"✓ SimplifiedOproService.{method} exists")
            else:
                print(f"✗ SimplifiedOproService.{method} missing")
                return False
        
        # Test OproIntegration methods
        oi = OproIntegration({})
        integration_methods = [
            'process_json_financial_data'
        ]
        
        for method in integration_methods:
            if hasattr(oi, method):
                print(f"✓ OproIntegration.{method} exists")
            else:
                print(f"✗ OproIntegration.{method} missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Method existence check failed: {e}")
        return False

def test_method_call_chains():
    """Test 3: Verify method call chain integrity"""
    print("\nTest 3: Testing method call chain integrity...")
    
    try:
        from contribution_assessment.weekly_optimization_manager import WeeklyOptimizationManager
        
        # Create test JSON data
        test_json_data = {
            "financial_data": {
                "portfolio_value": 100000,
                "cash_balance": 10000,
                "positions": [
                    {"symbol": "AAPL", "quantity": 100, "current_price": 150.0}
                ]
            },
            "market_data": {
                "timestamp": "2025-01-01T00:00:00Z",
                "volatility": 0.2
            }
        }
        
        # Write test JSON file
        test_file_path = "/tmp/test_financial_data.json"
        with open(test_file_path, 'w') as f:
            json.dump(test_json_data, f)
        
        # Test method chain
        wom = WeeklyOptimizationManager({})
        
        # Test read_json_data_file
        try:
            json_data = wom.read_json_data_file(test_file_path)
            print("✓ read_json_data_file method callable")
        except Exception as e:
            print(f"✗ read_json_data_file failed: {e}")
            return False
        
        # Test convert_json_to_opro_format
        try:
            opro_data = wom.convert_json_to_opro_format(json_data)
            print("✓ convert_json_to_opro_format method callable")
        except Exception as e:
            print(f"✗ convert_json_to_opro_format failed: {e}")
            return False
        
        # Test initialize_opro_with_json_data
        try:
            wom.initialize_opro_with_json_data(test_file_path)
            print("✓ initialize_opro_with_json_data method callable")
        except Exception as e:
            print(f"✗ initialize_opro_with_json_data failed: {e}")
            return False
        
        # Clean up
        os.remove(test_file_path)
        return True
        
    except Exception as e:
        print(f"✗ Method call chain test failed: {e}")
        traceback.print_exc()
        return False

def test_opro_integration():
    """Test 4: Confirm OPRO integration remains functional"""
    print("\nTest 4: Testing OPRO integration functionality...")
    
    try:
        from contribution_assessment.services.simplified_opro_service import SimplifiedOproService
        from state_management.opro_integration import OproIntegration
        
        # Test SimplifiedOproService initialization
        config = {"test": True}
        sos = SimplifiedOproService(config)
        print("✓ SimplifiedOproService instantiation successful")
        
        # Test OproIntegration initialization
        oi = OproIntegration(config)
        print("✓ OproIntegration instantiation successful")
        
        # Test method callability without breaking existing functionality
        test_data = {
            "financial_metrics": {
                "portfolio_value": 100000,
                "returns": 0.05
            }
        }
        
        try:
            sos.initialize_with_json_data(test_data)
            print("✓ SimplifiedOproService.initialize_with_json_data callable")
        except Exception as e:
            print(f"✗ SimplifiedOproService method failed: {e}")
            return False
        
        try:
            oi.process_json_financial_data(test_data)
            print("✓ OproIntegration.process_json_financial_data callable")
        except Exception as e:
            print(f"✗ OproIntegration method failed: {e}")
            return False
        
        return True
        
    except Exception as e:
        print(f"✗ OPRO integration test failed: {e}")
        traceback.print_exc()
        return False

def test_json_reading_logic():
    """Test 5: Validate JSON data reading and format conversion logic"""
    print("\nTest 5: Testing JSON reading and conversion logic...")
    
    try:
        from contribution_assessment.weekly_optimization_manager import WeeklyOptimizationManager
        
        # Create comprehensive test JSON data
        test_json_data = {
            "financial_data": {
                "portfolio_value": 150000.50,
                "cash_balance": 25000.75,
                "positions": [
                    {"symbol": "AAPL", "quantity": 100, "current_price": 150.25, "market_value": 15025.0},
                    {"symbol": "GOOGL", "quantity": 50, "current_price": 2800.00, "market_value": 140000.0}
                ],
                "total_equity": 155025.0
            },
            "market_data": {
                "timestamp": "2025-01-15T15:30:00Z",
                "market_volatility": 0.18,
                "sector_performance": {
                    "technology": 0.023,
                    "healthcare": -0.001
                }
            },
            "performance_metrics": {
                "daily_return": 0.0125,
                "weekly_return": 0.045,
                "sharpe_ratio": 1.25,
                "max_drawdown": -0.08
            }
        }
        
        # Write test JSON file
        test_file_path = "/tmp/test_comprehensive_data.json"
        with open(test_file_path, 'w') as f:
            json.dump(test_json_data, f, indent=2)
        
        wom = WeeklyOptimizationManager({})
        
        # Test JSON reading
        try:
            loaded_data = wom.read_json_data_file(test_file_path)
            print("✓ JSON file reading successful")
            
            # Verify data integrity
            if loaded_data == test_json_data:
                print("✓ JSON data integrity maintained")
            else:
                print("✗ JSON data integrity compromised")
                return False
                
        except Exception as e:
            print(f"✗ JSON reading failed: {e}")
            return False
        
        # Test format conversion
        try:
            opro_format = wom.convert_json_to_opro_format(loaded_data)
            print("✓ JSON to OPRO format conversion successful")
            
            # Verify conversion structure
            if isinstance(opro_format, dict) and 'financial_data' in opro_format:
                print("✓ OPRO format structure valid")
            else:
                print("✗ OPRO format structure invalid")
                return False
                
        except Exception as e:
            print(f"✗ Format conversion failed: {e}")
            return False
        
        # Clean up
        os.remove(test_file_path)
        return True
        
    except Exception as e:
        print(f"✗ JSON reading and conversion test failed: {e}")
        traceback.print_exc()
        return False

def run_comprehensive_test():
    """Execute all tests and report results"""
    print("="*60)
    print("JSON READING AND OPRO INTEGRATION WORKFLOW TEST")
    print("="*60)
    print(f"Test execution time: {datetime.now()}")
    print()
    
    tests = [
        ("Import and Syntax Validation", test_imports),
        ("Method Completeness Check", test_method_existence),
        ("Method Call Chain Integrity", test_method_call_chains),
        ("OPRO Integration Functionality", test_opro_integration),
        ("JSON Reading and Conversion Logic", test_json_reading_logic)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"Executing: {test_name}")
        print("-" * 40)
        
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✓ {test_name}: PASSED")
            else:
                print(f"✗ {test_name}: FAILED")
                
        except Exception as e:
            print(f"✗ {test_name}: ERROR - {e}")
            results.append((test_name, False))
            traceback.print_exc()
        
        print()
    
    # Summary
    print("="*60)
    print("TEST RESULTS SUMMARY")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("✓ ALL TESTS PASSED - Implementation verification successful")
        return True
    else:
        print("✗ SOME TESTS FAILED - Implementation requires attention")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)