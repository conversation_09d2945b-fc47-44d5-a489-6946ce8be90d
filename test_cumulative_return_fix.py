#!/usr/bin/env python3
"""
测试累计收益率跨周传递修复效果
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from portfolio_state_tracker import PortfolioStateTracker
from stock_trading_env import StockTradingEnv

def test_cumulative_return_continuity():
    """测试累计收益率在跨周传递中的连续性"""
    print("=" * 60)
    print("🧪 测试累计收益率跨周传递修复效果")
    print("=" * 60)
    
    # 创建portfolio tracker
    tracker = PortfolioStateTracker()
    
    # 模拟第1周完整联盟的状态保存
    print("\n📝 模拟第1周完整联盟状态保存...")
    tracker.add_daily_record(
        date_str="2025-01-01",
        net_worth=1050000,
        daily_return=0.01,
        cumulative_return=0.05,  # 第1周结束时5%累计收益
        weekly_return=0.03,
        last_week_return=0.0,
        coalition_info={"coalition_id": "full_coalition", "coalition_size": 7},
        positions={"AAPL": 100},
        position_values={"AAPL": 15000}
    )
    
    # 验证第1周状态保存
    latest_state = tracker.get_latest_state()
    print(f"✅ 第1周最新状态: 累计收益={latest_state['cumulative_return']:.6f}")
    
    # 测试第2周开始时的状态继承
    print("\n🔄 测试第2周状态继承...")
    clean_state = tracker.get_clean_state_for_new_phase(2)
    
    if clean_state:
        inherited_cum_return = clean_state.get("cumulative_return", 0.0)
        print(f"✅ 第2周继承状态: 累计收益={inherited_cum_return:.6f}")
        
        if inherited_cum_return == 0.05:
            print("🎉 累计收益率跨周传递成功！")
            return True
        else:
            print(f"❌ 累计收益率传递失败: 期望0.05, 实际{inherited_cum_return:.6f}")
            return False
    else:
        print("❌ 无法获取第2周继承状态")
        return False

def test_trading_env_state_inheritance():
    """测试交易环境的状态继承"""
    print("\n" + "=" * 60)
    print("🧪 测试交易环境状态继承")
    print("=" * 60)
    
    # 创建portfolio tracker并添加第1周数据
    tracker = PortfolioStateTracker()
    tracker.add_daily_record(
        date_str="2025-01-01",
        net_worth=1100000,  
        daily_return=0.02,
        cumulative_return=0.10,  # 10%累计收益
        weekly_return=0.05,
        last_week_return=0.0,
        coalition_info={"coalition_id": "full_coalition", "coalition_size": 7}
    )
    
    # 创建第2周的交易环境配置
    config = {
        "start_date": "2025-01-08",  # 第2周开始
        "end_date": "2025-01-12",
        "stocks": ["AAPL"],
        "starting_cash": 1000000,
        "portfolio_tracker": tracker,
        "current_week_number": 2,
        "coalition_size": 7  # 完整联盟
    }
    
    print("\n🏗️ 创建第2周交易环境...")
    try:
        env = StockTradingEnv(config)
        
        # 检查累计收益率是否正确继承
        cum_return = getattr(env, 'cumulative_return', 0.0)
        print(f"✅ 交易环境累计收益率: {cum_return:.6f}")
        
        if cum_return == 0.10:
            print("🎉 交易环境状态继承成功！") 
            return True
        else:
            print(f"❌ 交易环境状态继承失败: 期望0.10, 实际{cum_return:.6f}")
            return False
            
    except Exception as e:
        print(f"❌ 交易环境创建失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试累计收益率跨周传递修复效果...")
    
    # 测试1: Portfolio Tracker状态传递
    test1_success = test_cumulative_return_continuity()
    
    # 测试2: 交易环境状态继承  
    test2_success = test_trading_env_state_inheritance()
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    print(f"Portfolio Tracker状态传递: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"交易环境状态继承: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print("\n🎉 所有测试通过！累计收益率跨周传递修复成功！")
        sys.exit(0)
    else:
        print("\n❌ 部分测试失败，需要进一步调试")
        sys.exit(1)