{"opro_system": {"version": "1.0.0", "description": "OPRO优化系统配置文件", "created_at": "2025-01-03T00:00:00Z"}, "optimization": {"optimization_frequency": "weekly", "candidates_per_generation": 8, "historical_weeks_to_consider": 10, "temperature": 0.0, "max_optimization_iterations": 60, "convergence_threshold": 0.001, "prompt_length_limit": 500, "evaluation_timeout": 300, "min_improvement_threshold": 0.01, "auto_optimize_after_evaluation": true, "rollback_on_degradation": true}, "cg_opro": {"enabled": true, "description": "CG-OPRO (Cycle-Guided OPRO) 周期性引导优化配置", "cg_opro_frequency": 5, "min_days_for_optimization": 5, "max_agents_per_cycle": 1, "performance_degradation_threshold": -0.05, "optimization_timeout": 300, "optimize_worst_performers": true, "min_performance_for_optimization": -0.1, "historical_window_weeks": 4, "backup_enabled": true, "cleanup_old_data": true, "verbose_logging": true, "progress_reporting": true, "performance_tracking": true, "results_path": "results"}, "evaluation": {"enable_cache": true, "cache_ttl": 3600, "parallel_evaluation": true, "max_workers": 4, "quick_test_days": 3, "full_evaluation_trigger_threshold": 0.05}, "storage": {"results_base_path": "results/periodic_shapley", "opro_db_path": "results/opro_optimization.db", "export_directory": "results/opro_export", "backup_enabled": true, "backup_frequency": "daily", "data_retention_days": 90}, "agents": {"default_agents": ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"], "core_analyst_agents": ["NAA", "TAA", "FAA"], "outlook_agents": ["BOA", "BeOA", "NOA"], "trader_agent": "TRA", "max_prompt_history_per_agent": 50}, "llm_integration": {"primary_provider": "zhipuai", "fallback_provider": null, "model_config": {"zhipuai": {"model": "glm-4-flash", "temperature": 0.0, "top_p": 0.5, "max_tokens": 1000, "timeout": 30}, "openai": {"model": "gpt-3.5-turbo", "temperature": 0.0, "top_p": 0.5, "max_tokens": 1000, "timeout": 30}}, "rate_limiting": {"requests_per_minute": 60, "concurrent_requests": 10}}, "monitoring": {"enable_performance_tracking": true, "log_level": "INFO", "suppress_third_party_debug": true, "metrics_collection": true, "alert_thresholds": {"optimization_failure_rate": 0.3, "performance_degradation_threshold": -0.1, "system_error_rate": 0.05}, "dashboard_refresh_interval": 300}, "security": {"enable_prompt_validation": true, "max_prompt_length": 1000, "forbidden_patterns": ["ignore previous instructions", "system prompt", "override safety"], "enable_audit_logging": true}, "experimental": {"enable_multi_objective_optimization": false, "enable_cross_agent_collaboration": false, "enable_dynamic_coalition_optimization": false, "enable_reinforcement_learning": false}, "four_phase_system": {"description": "四阶段优化系统配置", "phase_config": {"target_agents": ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"], "max_coalitions": 20, "min_improvement_threshold": 0.01, "max_concurrent": 60, "detailed_logging": true, "cg_opro_frequency": 5, "min_days_for_optimization": 5, "max_agents_per_cycle": 1, "verbose_logging": true, "results_path": "results"}, "cycle_config": {"cycle_days": 5, "trading_days_per_cycle": 5, "max_cycles": 10, "enable_continuous_optimization": true}}}