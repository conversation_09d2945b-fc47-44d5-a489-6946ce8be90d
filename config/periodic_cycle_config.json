{"cycle_management": {"cycle_days": 5, "auto_transition": true, "max_cycles": null, "start_date": null, "end_date": null, "manual_trading_period": {"enabled": false, "start_date": "2023-01-01", "end_date": "2023-01-31", "trading_days": []}, "persistence_enabled": true, "storage_path": "results/periodic_cycles"}, "phase_execution": {"max_retries": 3, "retry_delay": 5.0, "skip_failed_phases": false, "detailed_logging": true, "max_concurrent": 60, "timeout_seconds": 3600}, "optimization": {"enable_recovery": true, "export_path": "results/periodic_cycles", "monitoring_interval": 30.0, "performance_thresholds": {"max_execution_time": 7200, "max_memory_usage": 2048, "min_success_rate": 0.8}}, "start_date": "2023-01-01", "end_date": "2024-12-31", "stocks": ["AAPL", "GOOGL", "MSFT", "AMZN", "TSLA"], "assessment": {"start_date": "2023-01-01", "end_date": "2024-12-31", "stocks": ["AAPL", "GOOGL", "MSFT", "AMZN", "TSLA"], "starting_cash": 100000, "risk_free_rate": 0.02, "simulation_days": null, "enable_concurrent_execution": true, "max_concurrent_api_calls": 60, "trading_days_per_week": 5}, "opro": {"optimization_frequency": "weekly", "candidates_per_generation": 1, "historical_weeks_to_consider": 4, "temperature": 0.7, "max_optimization_iterations": 10, "convergence_threshold": 0.01, "enable_cache": true, "parallel_evaluation": false, "max_workers": 1}, "logging": {"level": "INFO", "file_path": null, "enable_performance_logging": true, "enable_detailed_phase_logging": true, "suppress_third_party_logs": true}, "monitoring": {"enable_system_monitoring": true, "monitoring_interval": 30.0, "enable_memory_tracking": true, "enable_performance_alerts": true, "alert_thresholds": {"cpu_usage": 80.0, "memory_usage": 85.0, "execution_time": 3600.0}}, "export": {"enable_auto_export": true, "export_interval": "cycle_end", "export_formats": ["json", "csv"], "include_performance_metrics": true, "include_phase_details": true, "retention_days": 30}}