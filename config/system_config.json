{"system": {"name": "Multi-Agent Contribution Assessment System", "version": "2.0.0", "description": "Configuration-driven multi-agent trading system with contribution assessment", "environment": "development", "created_at": "2025-01-17T00:00:00Z"}, "assessment": {"start_date": "2024-01-01", "end_date": "2024-12-31", "stocks": ["AAPL", "GOOGL", "MSFT", "META"], "starting_cash": 100000, "risk_free_rate": 0.02, "simulation_days": null, "enable_concurrent_execution": true, "max_concurrent_api_calls": 60, "trading_days_per_week": 5}, "opro": {"enabled": true, "optimization_frequency": "weekly", "candidates_per_generation": 8, "historical_weeks_to_consider": 10, "temperature": 0.0, "max_optimization_iterations": 50, "convergence_threshold": 0.001, "enable_cache": true, "parallel_evaluation": true, "max_workers": 4, "auto_optimize_after_evaluation": true, "rollback_on_degradation": true}, "services": {"enabled": ["coalition_service", "simulation_service", "shapley_service", "opro_service", "state_manager", "phase_coordinator"], "coalition_service": {"implementation_class": "coalition_service.CoalitionService", "lifecycle": "singleton", "dependencies": [], "configuration": {"max_coalition_size": 7, "enable_pruning": true, "pruning_threshold": 0.1}, "enabled": true}, "simulation_service": {"implementation_class": "simulation_service.SimulationService", "lifecycle": "singleton", "dependencies": [], "configuration": {"enable_concurrent_execution": true, "max_concurrent_simulations": 60, "simulation_timeout": 300, "enable_detailed_logging": false}, "enabled": true}, "shapley_service": {"implementation_class": "shapley_service.ShapleyService", "lifecycle": "singleton", "dependencies": [], "configuration": {"calculation_mode": "periodic", "enable_caching": true, "fallback_mode": "approximation"}, "enabled": true}, "opro_service": {"implementation_class": "opro_service.OPROService", "lifecycle": "singleton", "dependencies": [], "configuration": {"enable_optimization": true, "optimization_mode": "adaptive", "enable_ab_testing": true}, "enabled": true}, "state_manager": {"implementation_class": "state_manager.StateManager", "lifecycle": "singleton", "dependencies": ["event_bus"], "configuration": {"enable_persistence": true, "state_file_path": "results/system_state.json", "backup_interval": 300}, "enabled": true}, "phase_coordinator": {"implementation_class": "phase_coordinator.PhaseCoordinator", "lifecycle": "singleton", "dependencies": ["coalition_service", "simulation_service", "shapley_service", "opro_service", "state_manager"], "configuration": {"enable_phase_monitoring": true, "phase_timeout": 600, "enable_rollback": true}, "enabled": true}}, "features": {"service.coalition_service": {"state": "enabled", "description": "Coalition generation service", "conditions": {}, "metadata": {"category": "core", "impact": "high"}}, "service.simulation_service": {"state": "enabled", "description": "Trading simulation service", "conditions": {}, "metadata": {"category": "core", "impact": "high"}}, "service.shapley_service": {"state": "enabled", "description": "Shapley value calculation service", "conditions": {}, "metadata": {"category": "core", "impact": "high"}}, "service.opro_service": {"state": "conditional", "description": "OPRO optimization service", "conditions": {"config_value": {"key": "opro.enabled", "value": true}}, "metadata": {"category": "optimization", "impact": "medium"}}, "concurrent_execution": {"state": "enabled", "description": "Concurrent execution for performance", "conditions": {}, "metadata": {"category": "performance", "impact": "medium"}}, "detailed_logging": {"state": "conditional", "description": "Detailed logging for debugging", "conditions": {"environment": "development"}, "metadata": {"category": "debugging", "impact": "low"}}, "experimental.multi_objective_optimization": {"state": "disabled", "description": "Multi-objective optimization (experimental)", "conditions": {}, "metadata": {"category": "experimental", "impact": "unknown"}}}, "agents": {"default_agents": ["NAA", "TAA", "FAA", "BOA", "BeOA", "NOA", "TRA"], "core_analyst_agents": ["NAA", "TAA", "FAA"], "outlook_agents": ["BOA", "BeOA", "NOA"], "trader_agent": "TRA", "max_prompt_history_per_agent": 50, "enable_agent_optimization": true}, "llm_integration": {"primary_provider": "zhipuai", "fallback_provider": null, "model_config": {"zhipuai": {"model": "glm-4-flash", "temperature": 0.0, "top_p": 0.5, "max_tokens": 1000, "timeout": 30}, "openai": {"model": "gpt-3.5-turbo", "temperature": 0.0, "top_p": 0.5, "max_tokens": 1000, "timeout": 30}, "lmstudio": {"model": "local-model", "temperature": 0.0, "top_p": 0.5, "max_tokens": 1000, "timeout": 30}}, "rate_limiting": {"requests_per_minute": 60, "concurrent_requests": 10}}, "storage": {"results_base_path": "results", "periodic_shapley_path": "results/periodic_shapley", "opro_db_path": "results/opro_optimization.db", "weekly_optimization_path": "results/weekly_optimization", "export_directory": "results/exports", "backup_enabled": true, "backup_frequency": "daily", "data_retention_days": 90}, "monitoring": {"enable_performance_tracking": true, "log_level": "INFO", "suppress_third_party_debug": true, "metrics_collection": true, "alert_thresholds": {"optimization_failure_rate": 0.3, "performance_degradation_threshold": -0.1, "system_error_rate": 0.05}, "dashboard_refresh_interval": 300}, "security": {"enable_prompt_validation": true, "max_prompt_length": 1000, "forbidden_patterns": ["ignore previous instructions", "system prompt", "override safety"], "enable_audit_logging": true}, "environments": {"development": {"assessment": {"simulation_days": 30, "enable_concurrent_execution": true, "max_concurrent_api_calls": 10}, "opro": {"max_optimization_iterations": 5, "enable_cache": true}, "monitoring": {"log_level": "DEBUG", "enable_performance_tracking": true}}, "production": {"assessment": {"simulation_days": null, "enable_concurrent_execution": true, "max_concurrent_api_calls": 60}, "opro": {"max_optimization_iterations": 50, "enable_cache": true}, "monitoring": {"log_level": "INFO", "enable_performance_tracking": true}}, "testing": {"assessment": {"simulation_days": 5, "enable_concurrent_execution": false, "max_concurrent_api_calls": 1}, "opro": {"max_optimization_iterations": 1, "enable_cache": false}, "monitoring": {"log_level": "WARNING", "enable_performance_tracking": false}}}}