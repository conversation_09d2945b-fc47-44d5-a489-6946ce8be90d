#!/usr/bin/env python3
"""
按天下载新闻数据 - 精确控制版本

相比按周下载，按天下载的优势：
1. 更精确的进度控制
2. 网络中断影响更小
3. API限制恢复后能精确继续
4. 更容易定位问题日期
"""

import subprocess
import sys
import os
from datetime import datetime, timedelta

def download_news_daily(stocks, start_date, end_date, auto_migrate=True):
    """
    按天下载多个股票的新闻数据
    
    Args:
        stocks: 股票代码列表
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
        auto_migrate: 是否自动运行时间格式迁移
    """
    
    print(f"🗓️ 按天下载新闻数据")
    print(f"股票: {', '.join(stocks)}")
    print(f"日期范围: {start_date} 到 {end_date}")
    print(f"自动迁移: {'是' if auto_migrate else '否'}")
    print("=" * 60)
    
    # 获取项目根目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    news_script_path = os.path.join(script_dir, "data", "get_news_data.py")
    
    if not os.path.exists(news_script_path):
        print(f"❌ 错误: 找不到新闻下载脚本 {news_script_path}")
        return False
    
    # 统计信息
    total_stocks = len(stocks)
    successful_stocks = 0
    failed_stocks = []
    
    for i, stock in enumerate(stocks, 1):
        print(f"\n[{i}/{total_stocks}] 🚀 开始下载 {stock} 的新闻数据...")
        print("-" * 50)
        
        try:
            # 构建下载命令
            cmd = [
                sys.executable, 
                news_script_path,
                stock,
                start_date,
                end_date
            ]
            
            # 添加控制参数
            if not auto_migrate:
                cmd.append("--no-migrate")
            
            print(f"📝 执行命令: {' '.join(cmd)}")
            
            # 运行下载命令
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=3600)  # 1小时超时
            
            if result.returncode == 0:
                print(f"✅ {stock} 下载完成")
                successful_stocks += 1
                
                # 解析输出获取统计信息
                output_lines = result.stderr.split('\n')
                for line in output_lines:
                    if "✅ Inserted" in line and "news articles" in line:
                        print(f"   📊 {line.split('✅ ')[-1]}")
                    elif "📛 API limit reached" in line:
                        print(f"   ⚠️ API限制: {line}")
                        break
                    elif "✅.*时间格式迁移完成" in line:
                        print(f"   🔄 时间格式迁移完成")
                
            else:
                print(f"❌ {stock} 下载失败")
                failed_stocks.append(stock)
                
                # 显示错误信息
                if result.stderr:
                    error_lines = result.stderr.split('\n')[-10:]  # 显示最后10行错误
                    print(f"   错误信息:")
                    for line in error_lines:
                        if line.strip():
                            print(f"     {line}")
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {stock} 下载超时 (1小时)")
            failed_stocks.append(stock)
        except Exception as e:
            print(f"❌ {stock} 下载异常: {e}")
            failed_stocks.append(stock)
    
    # 汇总报告
    print("\n" + "=" * 60)
    print("📊 下载汇总报告")
    print("=" * 60)
    
    print(f"总股票数: {total_stocks}")
    print(f"成功下载: {successful_stocks} ({successful_stocks/total_stocks*100:.1f}%)")
    print(f"下载失败: {len(failed_stocks)} ({len(failed_stocks)/total_stocks*100:.1f}%)")
    
    if successful_stocks > 0:
        print(f"\n✅ 成功下载的股票:")
        successful_list = [stock for stock in stocks if stock not in failed_stocks]
        for stock in successful_list:
            print(f"   - {stock}")
    
    if failed_stocks:
        print(f"\n❌ 失败的股票:")
        for stock in failed_stocks:
            print(f"   - {stock}")
        
        print(f"\n🔄 重试建议:")
        print(f"   如果是API限制，请等待24小时后重试")
        print(f"   如果是网络问题，可以直接重新运行")
        print(f"   脚本支持断点续传，会从中断处继续")
    
    # 下一步建议
    if successful_stocks == total_stocks:
        print(f"\n🎉 所有股票下载完成！")
        print(f"💡 建议运行数据检查:")
        for stock in stocks:
            print(f"   python data/get_news_data.py {stock} {start_date} {end_date} --check")
    
    return successful_stocks == total_stocks

def main():
    """主函数"""
    # 默认参数
    default_stocks = ["GOOG", "NVDA", "META", "MSFT"]
    default_start = "2024-08-01"
    default_end = "2024-12-30"
    
    # 解析命令行参数
    auto_migrate = True
    if "--no-migrate" in sys.argv:
        auto_migrate = False
        print("ℹ️ 禁用自动时间格式迁移")
    
    # 可以通过命令行参数自定义股票和日期
    if len(sys.argv) >= 4 and not sys.argv[1].startswith('--'):
        # 自定义模式: python download_news_daily.py AAPL,MSFT 2024-01-01 2024-12-31
        stocks = sys.argv[1].split(',')
        start_date = sys.argv[2]
        end_date = sys.argv[3]
    else:
        # 默认模式
        stocks = default_stocks
        start_date = default_start
        end_date = default_end
        print(f"使用默认参数 (股票: {','.join(stocks)}, 日期: {start_date} 到 {end_date})")
        print(f"自定义用法: python download_news_daily.py <STOCK1,STOCK2> <START_DATE> <END_DATE>")
    
    try:
        # 验证日期格式
        datetime.strptime(start_date, '%Y-%m-%d')
        datetime.strptime(end_date, '%Y-%m-%d')
        
        success = download_news_daily(stocks, start_date, end_date, auto_migrate)
        sys.exit(0 if success else 1)
        
    except ValueError as e:
        print(f"❌ 日期格式错误: {e}")
        print("请使用 YYYY-MM-DD 格式，例如: 2024-08-01")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断下载")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 下载异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()