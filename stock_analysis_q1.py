#!/usr/bin/env python3
"""
股票分析脚本：计算Q1期间（1月1日-3月30日）META, MSFT, NVDA, AAPL 的关键财务指标

计算指标：
- Buy & Hold 收益率
- 年化收益率
- 夏普率
- 最大回撤
"""

import sqlite3
import pandas as pd
import numpy as np
from typing import Dict, <PERSON><PERSON>
from datetime import datetime
import os


class StockAnalyzer:
    """股票分析器"""
    
    def __init__(self):
        self.tickers = ['META', 'MSFT', 'NVDA', 'AAPL']
        self.start_date = '2025-01-01'
        self.end_date = '2025-03-30'
        self.data_path = 'data/tickers'
        
    def load_stock_data(self, ticker: str) -> pd.DataFrame:
        """从SQLite数据库加载股票数据"""
        db_path = os.path.join(self.data_path, ticker, f'{ticker}_data.db')
        
        if not os.path.exists(db_path):
            raise FileNotFoundError(f"Database not found: {db_path}")
            
        conn = sqlite3.connect(db_path)
        
        query = '''
        SELECT trade_date, Open, High, Low, Close, Volume
        FROM ohlcv 
        WHERE ticker = ? 
        AND trade_date >= ? 
        AND trade_date <= ? 
        ORDER BY trade_date
        '''
        
        df = pd.read_sql(query, conn, params=(ticker, self.start_date, self.end_date))
        conn.close()
        
        # 转换日期格式
        df['trade_date'] = pd.to_datetime(df['trade_date'])
        df.set_index('trade_date', inplace=True)
        
        return df
    
    def calculate_returns(self, df: pd.DataFrame) -> pd.Series:
        """计算日收益率"""
        return df['Close'].pct_change().dropna()
    
    def calculate_buy_hold_return(self, df: pd.DataFrame) -> float:
        """计算买入持有收益率"""
        if len(df) < 2:
            return 0.0
        
        start_price = df.iloc[0]['Close']
        end_price = df.iloc[-1]['Close']
        
        return (end_price - start_price) / start_price
    
    def calculate_annualized_return(self, total_return: float, days: int) -> float:
        """计算年化收益率"""
        if days <= 0:
            return 0.0
        
        # 假设一年252个交易日
        return (1 + total_return) ** (252 / days) - 1
    
    def calculate_sharpe_ratio(self, returns: pd.Series, risk_free_rate: float = 0.02) -> float:
        """计算夏普率"""
        if len(returns) == 0 or returns.std() == 0:
            return 0.0
        
        # 日收益率的年化
        annual_return = returns.mean() * 252
        annual_volatility = returns.std() * np.sqrt(252)
        
        return (annual_return - risk_free_rate) / annual_volatility
    
    def calculate_max_drawdown(self, df: pd.DataFrame) -> float:
        """计算最大回撤"""
        prices = df['Close']
        
        # 计算累计最高点
        cummax = prices.expanding().max()
        
        # 计算回撤
        drawdown = (prices - cummax) / cummax
        
        # 返回最大回撤（绝对值）
        return abs(drawdown.min())
    
    def analyze_stock(self, ticker: str) -> Dict:
        """分析单只股票"""
        try:
            df = self.load_stock_data(ticker)
            
            if len(df) == 0:
                return {
                    'ticker': ticker,
                    'error': 'No data available'
                }
            
            returns = self.calculate_returns(df)
            buy_hold_return = self.calculate_buy_hold_return(df)
            trading_days = len(df)
            annualized_return = self.calculate_annualized_return(buy_hold_return, trading_days)
            sharpe_ratio = self.calculate_sharpe_ratio(returns)
            max_drawdown = self.calculate_max_drawdown(df)
            
            return {
                'ticker': ticker,
                'start_date': df.index[0].strftime('%Y-%m-%d'),
                'end_date': df.index[-1].strftime('%Y-%m-%d'),
                'trading_days': trading_days,
                'start_price': df.iloc[0]['Close'],
                'end_price': df.iloc[-1]['Close'],
                'buy_hold_return': buy_hold_return,
                'annualized_return': annualized_return,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'volatility': returns.std() * np.sqrt(252) if len(returns) > 0 else 0.0
            }
            
        except Exception as e:
            return {
                'ticker': ticker,
                'error': str(e)
            }
    
    def run_analysis(self) -> Dict:
        """运行完整分析"""
        results = {}
        
        print(f"正在分析 {', '.join(self.tickers)} 股票...")
        print(f"分析期间: {self.start_date} 至 {self.end_date}")
        print("=" * 80)
        
        for ticker in self.tickers:
            print(f"正在分析 {ticker}...")
            results[ticker] = self.analyze_stock(ticker)
        
        return results
    
    def print_results(self, results: Dict):
        """打印分析结果"""
        print("\n" + "=" * 80)
        print("股票分析结果")
        print("=" * 80)
        
        for ticker, data in results.items():
            print(f"\n【{ticker}】")
            
            if 'error' in data:
                print(f"错误: {data['error']}")
                continue
            
            print(f"分析期间: {data['start_date']} 至 {data['end_date']}")
            print(f"交易天数: {data['trading_days']} 天")
            print(f"起始价格: ${data['start_price']:.2f}")
            print(f"结束价格: ${data['end_price']:.2f}")
            print(f"Buy & Hold 收益率: {data['buy_hold_return']:.4%}")
            print(f"年化收益率: {data['annualized_return']:.4%}")
            print(f"夏普率: {data['sharpe_ratio']:.4f}")
            print(f"最大回撤: {data['max_drawdown']:.4%}")
            print(f"年化波动率: {data['volatility']:.4%}")
        
        # 汇总表格
        print("\n" + "=" * 80)
        print("汇总表格")
        print("=" * 80)
        print(f"{'股票':<6} {'Buy&Hold收益率':<15} {'年化收益率':<12} {'夏普率':<10} {'最大回撤':<10}")
        print("-" * 80)
        
        for ticker, data in results.items():
            if 'error' not in data:
                print(f"{ticker:<6} {data['buy_hold_return']:>13.2%} {data['annualized_return']:>10.2%} "
                      f"{data['sharpe_ratio']:>8.2f} {data['max_drawdown']:>8.2%}")


def main():
    """主函数"""
    analyzer = StockAnalyzer()
    results = analyzer.run_analysis()
    analyzer.print_results(results)


if __name__ == "__main__":
    main()