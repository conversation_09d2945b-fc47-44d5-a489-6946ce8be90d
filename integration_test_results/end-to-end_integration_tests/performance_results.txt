test_high_concurrency_scenarios (contribution_assessment.tests.test_performance_stability.PerformanceStabilityTestSuite.test_high_concurrency_scenarios)
测试高并发场景 ... FAIL
test_high_load_scenarios (contribution_assessment.tests.test_performance_stability.PerformanceStabilityTestSuite.test_high_load_scenarios)
测试高负载场景 ... ok
test_long_running_stability (contribution_assessment.tests.test_performance_stability.PerformanceStabilityTestSuite.test_long_running_stability)
测试长时间运行的稳定性 ... FAIL
test_memory_leak_detection (contribution_assessment.tests.test_performance_stability.PerformanceStabilityTestSuite.test_memory_leak_detection)
测试内存泄漏检测 ... ok
test_resource_optimization_verification (contribution_assessment.tests.test_performance_stability.PerformanceStabilityTestSuite.test_resource_optimization_verification)
测试资源使用优化验证 ... ok

======================================================================
FAIL: test_high_concurrency_scenarios (contribution_assessment.tests.test_performance_stability.PerformanceStabilityTestSuite.test_high_concurrency_scenarios)
测试高并发场景
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_performance_stability.py", line 407, in test_high_concurrency_scenarios
    self.assertGreaterEqual(
AssertionError: 0.0 not greater than or equal to 70 : Low success rate in concurrent execution: 0.0%

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_performance_stability.py", line 421, in test_high_concurrency_scenarios
    self.fail(f"High concurrency test failed: {e}")
AssertionError: High concurrency test failed: 0.0 not greater than or equal to 70 : Low success rate in concurrent execution: 0.0%

======================================================================
FAIL: test_long_running_stability (contribution_assessment.tests.test_performance_stability.PerformanceStabilityTestSuite.test_long_running_stability)
测试长时间运行的稳定性
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_performance_stability.py", line 200, in test_long_running_stability
    self.assertLess(
AssertionError: 0.0015690326690673828 not less than 0.0003454923629760742 : Execution time variance too high, indicating instability

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_performance_stability.py", line 220, in test_long_running_stability
    self.fail(f"Long-running stability test failed: {e}")
AssertionError: Long-running stability test failed: 0.0015690326690673828 not less than 0.0003454923629760742 : Execution time variance too high, indicating instability

----------------------------------------------------------------------
Ran 5 tests in 7.604s

FAILED (failures=2)
