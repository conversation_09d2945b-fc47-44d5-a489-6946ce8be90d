test_api_interface_compatibility (contribution_assessment.tests.test_compatibility_validation.CompatibilityValidationTestSuite.test_api_interface_compatibility)
测试API接口兼容性 ... FAIL
test_configuration_parameter_compatibility (contribution_assessment.tests.test_compatibility_validation.CompatibilityValidationTestSuite.test_configuration_parameter_compatibility)
测试配置参数兼容性 ... ok
test_exception_handling_compatibility (contribution_assessment.tests.test_compatibility_validation.CompatibilityValidationTestSuite.test_exception_handling_compatibility)
测试异常处理兼容性 ... ok
test_performance_comparison (contribution_assessment.tests.test_compatibility_validation.CompatibilityValidationTestSuite.test_performance_comparison)
测试性能对比 ... FAIL
test_return_value_format_compatibility (contribution_assessment.tests.test_compatibility_validation.CompatibilityValidationTestSuite.test_return_value_format_compatibility)
测试返回值格式兼容性 ... FAIL

======================================================================
FAIL: test_api_interface_compatibility (contribution_assessment.tests.test_compatibility_validation.CompatibilityValidationTestSuite.test_api_interface_compatibility)
测试API接口兼容性
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_compatibility_validation.py", line 109, in test_api_interface_compatibility
    self.assertTrue(
AssertionError: False is not true : Original assessor missing method: get_config

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_compatibility_validation.py", line 157, in test_api_interface_compatibility
    self.fail(f"API interface compatibility test failed: {e}")
AssertionError: API interface compatibility test failed: False is not true : Original assessor missing method: get_config

======================================================================
FAIL: test_performance_comparison (contribution_assessment.tests.test_compatibility_validation.CompatibilityValidationTestSuite.test_performance_comparison)
测试性能对比
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_compatibility_validation.py", line 510, in test_performance_comparison
    self.assertLess(
AssertionError: 4.699588477366255 not less than 2.0 : Refactored version is too slow: 4.70x slower than original

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_compatibility_validation.py", line 525, in test_performance_comparison
    self.fail(f"Performance comparison test failed: {e}")
AssertionError: Performance comparison test failed: 4.699588477366255 not less than 2.0 : Refactored version is too slow: 4.70x slower than original

======================================================================
FAIL: test_return_value_format_compatibility (contribution_assessment.tests.test_compatibility_validation.CompatibilityValidationTestSuite.test_return_value_format_compatibility)
测试返回值格式兼容性
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_compatibility_validation.py", line 202, in test_return_value_format_compatibility
    self.assertIn(key, original_result, f"Original result missing key: {key}")
AssertionError: 'shapley_analysis' not found in {'success': False, 'error': '交易智能体 TRA 不在智能体列表中', 'execution_time': 5.245208740234375e-06, 'shapley_values': {}, 'phase_results': {}} : Original result missing key: shapley_analysis

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_compatibility_validation.py", line 248, in test_return_value_format_compatibility
    self.fail(f"Return value format compatibility test failed: {e}")
AssertionError: Return value format compatibility test failed: 'shapley_analysis' not found in {'success': False, 'error': '交易智能体 TRA 不在智能体列表中', 'execution_time': 5.245208740234375e-06, 'shapley_values': {}, 'phase_results': {}} : Original result missing key: shapley_analysis

----------------------------------------------------------------------
Ran 5 tests in 0.004s

FAILED (failures=3)
