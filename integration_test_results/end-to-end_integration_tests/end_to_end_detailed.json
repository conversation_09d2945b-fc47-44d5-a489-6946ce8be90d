{"tests_run": 10, "failures": 7, "errors": 0, "success_rate": 30.0, "failure_details": ["(<contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite testMethod=test_backward_compatibility>, 'Traceback (most recent call last):\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py\", line 552, in test_backward_compatibility\\n    config = assessor.get_config()\\n             ^^^^^^^^^^^^^^^^^^^\\nAttributeError: \\'ContributionAssessor\\' object has no attribute \\'get_config\\'. Did you mean: \\'opro_config\\'?\\n\\nDuring handling of the above exception, another exception occurred:\\n\\nTraceback (most recent call last):\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py\", line 567, in test_backward_compatibility\\n    self.fail(f\"Backward compatibility test failed: {e}\")\\nAssertionError: Backward compatibility test failed: \\'ContributionAssessor\\' object has no attribute \\'get_config\\'\\n')", "(<contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite testMethod=test_complete_workflow_with_refactored_assessor>, 'Traceback (most recent call last):\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py\", line 149, in test_complete_workflow_with_refactored_assessor\\n    self.assertIn(\"shapley_analysis\", result)\\nAssertionError: \\'shapley_analysis\\' not found in {\\'success\\': False, \\'error\\': \"\\'TradingSimulator\\' object has no attribute \\'get_stats\\'\", \\'execution_time\\': 1.2655222415924072, \\'shapley_values\\': {}, \\'phase_results\\': {}}\\n\\nDuring handling of the above exception, another exception occurred:\\n\\nTraceback (most recent call last):\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py\", line 182, in test_complete_workflow_with_refactored_assessor\\n    self.fail(f\"Complete workflow test failed: {e}\")\\nAssertionError: Complete workflow test failed: \\'shapley_analysis\\' not found in {\\'success\\': False, \\'error\\': \"\\'TradingSimulator\\' object has no attribute \\'get_stats\\'\", \\'execution_time\\': 1.2655222415924072, \\'shapley_values\\': {}, \\'phase_results\\': {}}\\n')", "(<contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite testMethod=test_concurrent_execution_scenarios>, 'Traceback (most recent call last):\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py\", line 375, in test_concurrent_execution_scenarios\\n    self.assertTrue(result[\"success\"])\\nAssertionError: False is not true\\n\\nDuring handling of the above exception, another exception occurred:\\n\\nTraceback (most recent call last):\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py\", line 417, in test_concurrent_execution_scenarios\\n    self.fail(f\"Concurrent execution test failed: {e}\")\\nAssertionError: Concurrent execution test failed: False is not true\\n')", "(<contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite testMethod=test_configuration_driven_workflow>, 'Traceback (most recent call last):\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py\", line 197, in test_configuration_driven_workflow\\n    assessor = ConfigurationDrivenAssessor(config_file_path=config_file)\\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\nTypeError: ConfigurationDrivenAssessor.__init__() got an unexpected keyword argument \\'config_file_path\\'\\n\\nDuring handling of the above exception, another exception occurred:\\n\\nTraceback (most recent call last):\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py\", line 224, in test_configuration_driven_workflow\\n    self.fail(f\"Configuration-driven workflow test failed: {e}\")\\nAssertionError: Configuration-driven workflow test failed: ConfigurationDrivenAssessor.__init__() got an unexpected keyword argument \\'config_file_path\\'\\n')", "(<contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite testMethod=test_error_handling_and_recovery>, 'Traceback (most recent call last):\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py\", line 283, in test_error_handling_and_recovery\\n    with self.assertRaises(Exception):\\nAssertionError: Exception not raised\\n\\nDuring handling of the above exception, another exception occurred:\\n\\nTraceback (most recent call last):\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py\", line 343, in test_error_handling_and_recovery\\n    self.fail(f\"Error handling test failed: {e}\")\\nAssertionError: Error handling test failed: Exception not raised\\n')", "(<contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite testMethod=test_performance_benchmarks>, 'Traceback (most recent call last):\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py\", line 612, in test_performance_benchmarks\\n    self.assertTrue(result[\"success\"])\\nAssertionError: False is not true\\n\\nDuring handling of the above exception, another exception occurred:\\n\\nTraceback (most recent call last):\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py\", line 650, in test_performance_benchmarks\\n    self.fail(f\"Performance benchmark test failed: {e}\")\\nAssertionError: Performance benchmark test failed: False is not true\\n')", "(<contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite testMethod=test_service_factory_workflow>, 'Traceback (most recent call last):\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py\", line 237, in test_service_factory_workflow\\n    factory.configure_services(self.test_config)\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/service_factory.py\", line 36, in configure_services\\n    self._infra_factory.configure_services(config)\\n    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\\nAttributeError: \\'ServiceFactory\\' object has no attribute \\'configure_services\\'\\n\\nDuring handling of the above exception, another exception occurred:\\n\\nTraceback (most recent call last):\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py\", line 270, in test_service_factory_workflow\\n    self.fail(f\"Service factory workflow test failed: {e}\")\\nAssertionError: Service factory workflow test failed: \\'ServiceFactory\\' object has no attribute \\'configure_services\\'\\n')"], "error_details": []}