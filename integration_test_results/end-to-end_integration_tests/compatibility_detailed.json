{"tests_run": 5, "failures": 3, "errors": 0, "success_rate": 40.0, "failure_details": ["(<contribution_assessment.tests.test_compatibility_validation.CompatibilityValidationTestSuite testMethod=test_api_interface_compatibility>, 'Traceback (most recent call last):\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_compatibility_validation.py\", line 109, in test_api_interface_compatibility\\n    self.assertTrue(\\nAssertionError: False is not true : Original assessor missing method: get_config\\n\\nDuring handling of the above exception, another exception occurred:\\n\\nTraceback (most recent call last):\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_compatibility_validation.py\", line 157, in test_api_interface_compatibility\\n    self.fail(f\"API interface compatibility test failed: {e}\")\\nAssertionError: API interface compatibility test failed: False is not true : Original assessor missing method: get_config\\n')", "(<contribution_assessment.tests.test_compatibility_validation.CompatibilityValidationTestSuite testMethod=test_performance_comparison>, 'Traceback (most recent call last):\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_compatibility_validation.py\", line 510, in test_performance_comparison\\n    self.assertLess(\\nAssertionError: 4.699588477366255 not less than 2.0 : Refactored version is too slow: 4.70x slower than original\\n\\nDuring handling of the above exception, another exception occurred:\\n\\nTraceback (most recent call last):\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_compatibility_validation.py\", line 525, in test_performance_comparison\\n    self.fail(f\"Performance comparison test failed: {e}\")\\nAssertionError: Performance comparison test failed: 4.699588477366255 not less than 2.0 : Refactored version is too slow: 4.70x slower than original\\n')", "(<contribution_assessment.tests.test_compatibility_validation.CompatibilityValidationTestSuite testMethod=test_return_value_format_compatibility>, 'Traceback (most recent call last):\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_compatibility_validation.py\", line 202, in test_return_value_format_compatibility\\n    self.assertIn(key, original_result, f\"Original result missing key: {key}\")\\nAssertionError: \\'shapley_analysis\\' not found in {\\'success\\': False, \\'error\\': \\'交易智能体 TRA 不在智能体列表中\\', \\'execution_time\\': 5.245208740234375e-06, \\'shapley_values\\': {}, \\'phase_results\\': {}} : Original result missing key: shapley_analysis\\n\\nDuring handling of the above exception, another exception occurred:\\n\\nTraceback (most recent call last):\\n  File \"/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_compatibility_validation.py\", line 248, in test_return_value_format_compatibility\\n    self.fail(f\"Return value format compatibility test failed: {e}\")\\nAssertionError: Return value format compatibility test failed: \\'shapley_analysis\\' not found in {\\'success\\': False, \\'error\\': \\'交易智能体 TRA 不在智能体列表中\\', \\'execution_time\\': 5.245208740234375e-06, \\'shapley_values\\': {}, \\'phase_results\\': {}} : Original result missing key: shapley_analysis\\n')"], "error_details": []}