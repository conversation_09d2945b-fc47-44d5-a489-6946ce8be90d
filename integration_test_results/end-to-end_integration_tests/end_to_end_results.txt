test_backward_compatibility (contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite.test_backward_compatibility)
测试向后兼容性 ... FAIL
test_complete_workflow_with_refactored_assessor (contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite.test_complete_workflow_with_refactored_assessor)
测试重构版本的完整工作流 ... FAIL
test_concurrent_execution_scenarios (contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite.test_concurrent_execution_scenarios)
测试并发执行场景 ... FAIL
test_configuration_driven_workflow (contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite.test_configuration_driven_workflow)
测试配置驱动的工作流 ... FAIL
test_different_configuration_combinations (contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite.test_different_configuration_combinations)
测试不同配置组合 ... ok
test_error_handling_and_recovery (contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite.test_error_handling_and_recovery)
测试错误处理和恢复机制 ... FAIL
test_performance_benchmarks (contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite.test_performance_benchmarks)
测试性能基准 ... FAIL
test_resource_cleanup (contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite.test_resource_cleanup)
测试资源清理 ... ok
test_service_factory_workflow (contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite.test_service_factory_workflow)
测试服务工厂驱动的工作流 ... FAIL
test_stress_testing (contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite.test_stress_testing)
测试压力场景 ... ok

======================================================================
FAIL: test_backward_compatibility (contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite.test_backward_compatibility)
测试向后兼容性
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py", line 552, in test_backward_compatibility
    config = assessor.get_config()
             ^^^^^^^^^^^^^^^^^^^
AttributeError: 'ContributionAssessor' object has no attribute 'get_config'. Did you mean: 'opro_config'?

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py", line 567, in test_backward_compatibility
    self.fail(f"Backward compatibility test failed: {e}")
AssertionError: Backward compatibility test failed: 'ContributionAssessor' object has no attribute 'get_config'

======================================================================
FAIL: test_complete_workflow_with_refactored_assessor (contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite.test_complete_workflow_with_refactored_assessor)
测试重构版本的完整工作流
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py", line 149, in test_complete_workflow_with_refactored_assessor
    self.assertIn("shapley_analysis", result)
AssertionError: 'shapley_analysis' not found in {'success': False, 'error': "'TradingSimulator' object has no attribute 'get_stats'", 'execution_time': 1.2655222415924072, 'shapley_values': {}, 'phase_results': {}}

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py", line 182, in test_complete_workflow_with_refactored_assessor
    self.fail(f"Complete workflow test failed: {e}")
AssertionError: Complete workflow test failed: 'shapley_analysis' not found in {'success': False, 'error': "'TradingSimulator' object has no attribute 'get_stats'", 'execution_time': 1.2655222415924072, 'shapley_values': {}, 'phase_results': {}}

======================================================================
FAIL: test_concurrent_execution_scenarios (contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite.test_concurrent_execution_scenarios)
测试并发执行场景
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py", line 375, in test_concurrent_execution_scenarios
    self.assertTrue(result["success"])
AssertionError: False is not true

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py", line 417, in test_concurrent_execution_scenarios
    self.fail(f"Concurrent execution test failed: {e}")
AssertionError: Concurrent execution test failed: False is not true

======================================================================
FAIL: test_configuration_driven_workflow (contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite.test_configuration_driven_workflow)
测试配置驱动的工作流
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py", line 197, in test_configuration_driven_workflow
    assessor = ConfigurationDrivenAssessor(config_file_path=config_file)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
TypeError: ConfigurationDrivenAssessor.__init__() got an unexpected keyword argument 'config_file_path'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py", line 224, in test_configuration_driven_workflow
    self.fail(f"Configuration-driven workflow test failed: {e}")
AssertionError: Configuration-driven workflow test failed: ConfigurationDrivenAssessor.__init__() got an unexpected keyword argument 'config_file_path'

======================================================================
FAIL: test_error_handling_and_recovery (contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite.test_error_handling_and_recovery)
测试错误处理和恢复机制
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py", line 283, in test_error_handling_and_recovery
    with self.assertRaises(Exception):
AssertionError: Exception not raised

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py", line 343, in test_error_handling_and_recovery
    self.fail(f"Error handling test failed: {e}")
AssertionError: Error handling test failed: Exception not raised

======================================================================
FAIL: test_performance_benchmarks (contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite.test_performance_benchmarks)
测试性能基准
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py", line 612, in test_performance_benchmarks
    self.assertTrue(result["success"])
AssertionError: False is not true

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py", line 650, in test_performance_benchmarks
    self.fail(f"Performance benchmark test failed: {e}")
AssertionError: Performance benchmark test failed: False is not true

======================================================================
FAIL: test_service_factory_workflow (contribution_assessment.tests.test_end_to_end_integration.EndToEndIntegrationTestSuite.test_service_factory_workflow)
测试服务工厂驱动的工作流
----------------------------------------------------------------------
Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py", line 237, in test_service_factory_workflow
    factory.configure_services(self.test_config)
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/service_factory.py", line 36, in configure_services
    self._infra_factory.configure_services(config)
    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'ServiceFactory' object has no attribute 'configure_services'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/Code/Multi_Agent_Optimization/contribution_assessment/tests/test_end_to_end_integration.py", line 270, in test_service_factory_workflow
    self.fail(f"Service factory workflow test failed: {e}")
AssertionError: Service factory workflow test failed: 'ServiceFactory' object has no attribute 'configure_services'

----------------------------------------------------------------------
Ran 10 tests in 8.986s

FAILED (failures=7)
