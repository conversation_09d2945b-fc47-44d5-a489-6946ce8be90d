#!/usr/bin/env python3
"""
基于提取的JSON文件计算金融指标：
- 年化收益率
- 年化夏普率  
- 年化波动率
- 最大回撤

注意：收益率是每天的累计收益率，需要先转换为日收益率
"""

import json
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FinancialMetricsCalculator:
    def __init__(self, risk_free_rate: float = 0.02):
        """
        初始化计算器
        
        Args:
            risk_free_rate: 无风险利率（年化）
        """
        self.risk_free_rate = risk_free_rate
        self.trading_days_per_year = 252
    
    def load_json_data(self, json_file_path: str) -> List[Dict]:
        """加载JSON数据"""
        try:
            with open(json_file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data
        except Exception as e:
            logger.error(f"Error loading {json_file_path}: {e}")
            return []
    
    def find_continuous_segments(self, data: List[Dict]) -> List[List[Dict]]:
        """
        找到连续的交易段（处理重置问题）
        """
        if not data:
            return []
        
        # 按日期排序
        sorted_data = sorted(data, key=lambda x: x['date'])
        
        segments = []
        current_segment = []
        
        for i, item in enumerate(sorted_data):
            try:
                curr_cum = float(item['cumulative_return'])
            except (ValueError, TypeError):
                continue
                
            if i == 0:
                current_segment = [item]
            else:
                try:
                    prev_cum = float(sorted_data[i-1]['cumulative_return'])
                except (ValueError, TypeError):
                    prev_cum = 0.0
                
                # 检测重置：当前累计收益为0且前一个不为0，或大幅下跌
                is_reset = (curr_cum == 0.0 and prev_cum != 0.0) or \
                          (curr_cum < prev_cum and abs(curr_cum - prev_cum) > 0.2)
                
                if is_reset:
                    # 结束当前段，开始新段
                    if len(current_segment) > 3:  # 至少3个交易日
                        segments.append(current_segment)
                    current_segment = [item]
                else:
                    current_segment.append(item)
        
        # 添加最后一段
        if len(current_segment) > 3:
            segments.append(current_segment)
        
        return segments
    
    def calculate_daily_returns(self, segment: List[Dict]) -> np.ndarray:
        """
        从累计收益率计算日收益率
        """
        if len(segment) < 2:
            return np.array([])
        
        daily_returns = []
        
        for i, item in enumerate(segment):
            try:
                curr_cum = float(item['cumulative_return'])
            except (ValueError, TypeError):
                curr_cum = 0.0
            
            if i == 0:
                # 第一天的收益率就是累计收益率
                daily_return = curr_cum
            else:
                try:
                    prev_cum = float(segment[i-1]['cumulative_return'])
                except (ValueError, TypeError):
                    prev_cum = 0.0
                
                # 日收益率 = (1 + 今日累计收益) / (1 + 昨日累计收益) - 1
                if prev_cum == -1.0:  # 避免除零
                    daily_return = 0.0
                else:
                    daily_return = (1 + curr_cum) / (1 + prev_cum) - 1
            
            daily_returns.append(daily_return)
        
        return np.array(daily_returns)
    
    def calculate_annualized_return(self, daily_returns: np.ndarray) -> float:
        """计算年化收益率"""
        if len(daily_returns) == 0:
            return 0.0
        
        # 总收益率
        total_return = np.prod(1 + daily_returns) - 1
        
        # 年化
        num_days = len(daily_returns)
        if num_days == 0:
            return 0.0
        
        annualized_return = (1 + total_return) ** (self.trading_days_per_year / num_days) - 1
        return annualized_return
    
    def calculate_annualized_volatility(self, daily_returns: np.ndarray) -> float:
        """计算年化波动率"""
        if len(daily_returns) <= 1:
            return 0.0
        
        daily_vol = np.std(daily_returns, ddof=1)
        annualized_vol = daily_vol * np.sqrt(self.trading_days_per_year)
        return annualized_vol
    
    def calculate_annualized_sharpe_ratio(self, daily_returns: np.ndarray) -> float:
        """计算年化夏普率"""
        if len(daily_returns) <= 1:
            return 0.0
        
        # 计算年化收益率和波动率
        annualized_return = self.calculate_annualized_return(daily_returns)
        annualized_vol = self.calculate_annualized_volatility(daily_returns)
        
        if annualized_vol == 0:
            return 0.0
        
        # 夏普率 = (年化收益率 - 无风险利率) / 年化波动率
        sharpe_ratio = (annualized_return - self.risk_free_rate) / annualized_vol
        return sharpe_ratio
    
    def calculate_maximum_drawdown(self, daily_returns: np.ndarray) -> Tuple[float, int, int]:
        """
        计算最大回撤
        
        Returns:
            (最大回撤, 开始日索引, 结束日索引)
        """
        if len(daily_returns) == 0:
            return 0.0, 0, 0
        
        # 计算累计净值曲线
        cumulative_returns = np.cumprod(1 + daily_returns)
        
        # 计算历史最高点
        running_max = np.maximum.accumulate(cumulative_returns)
        
        # 计算回撤
        drawdown = (cumulative_returns - running_max) / running_max
        
        # 找到最大回撤
        max_dd_idx = np.argmin(drawdown)
        max_drawdown = abs(drawdown[max_dd_idx])
        
        # 找到最大回撤开始的峰值点
        peak_idx = np.argmax(running_max[:max_dd_idx + 1])
        
        return max_drawdown, peak_idx, max_dd_idx
    
    def analyze_stock(self, stock_symbol: str, json_file_path: str) -> Dict:
        """分析单个股票"""
        logger.info(f"分析 {stock_symbol}")
        
        # 加载数据
        data = self.load_json_data(json_file_path)
        if not data:
            logger.warning(f"{stock_symbol}: 无法加载数据")
            return {}
        
        # 找到连续段
        segments = self.find_continuous_segments(data)
        if not segments:
            logger.warning(f"{stock_symbol}: 没有找到有效的连续交易段")
            return {}
        
        # 选择最长的段进行分析
        best_segment = max(segments, key=len)
        logger.info(f"{stock_symbol}: 找到 {len(segments)} 个连续段，使用最长的段（{len(best_segment)} 天）")
        
        # 计算日收益率
        daily_returns = self.calculate_daily_returns(best_segment)
        if len(daily_returns) == 0:
            logger.warning(f"{stock_symbol}: 无法计算日收益率")
            return {}
        
        # 计算各项指标
        annualized_return = self.calculate_annualized_return(daily_returns)
        annualized_volatility = self.calculate_annualized_volatility(daily_returns)
        annualized_sharpe = self.calculate_annualized_sharpe_ratio(daily_returns)
        max_drawdown, dd_start, dd_end = self.calculate_maximum_drawdown(daily_returns)
        
        # 计算总收益率
        total_return = np.prod(1 + daily_returns) - 1
        
        # 统计信息
        positive_days = np.sum(daily_returns > 0)
        negative_days = np.sum(daily_returns < 0)
        zero_days = np.sum(daily_returns == 0)
        
        results = {
            'stock_symbol': stock_symbol,
            'analysis_period': {
                'start_date': best_segment[0]['date'],
                'end_date': best_segment[-1]['date'],
                'total_days': len(best_segment),
                'trading_days': len(daily_returns)
            },
            'returns': {
                'total_return': float(total_return),
                'annualized_return': float(annualized_return),
                'mean_daily_return': float(np.mean(daily_returns)),
                'median_daily_return': float(np.median(daily_returns))
            },
            'risk_metrics': {
                'annualized_volatility': float(annualized_volatility),
                'annualized_sharpe_ratio': float(annualized_sharpe),
                'maximum_drawdown': float(max_drawdown),
                'drawdown_duration': int(dd_end - dd_start + 1) if dd_end > dd_start else 0
            },
            'daily_statistics': {
                'positive_days': int(positive_days),
                'negative_days': int(negative_days),
                'zero_days': int(zero_days),
                'positive_days_ratio': float(positive_days / len(daily_returns)),
                'max_daily_return': float(np.max(daily_returns)),
                'min_daily_return': float(np.min(daily_returns))
            },
            'data_quality': {
                'total_segments_found': len(segments),
                'segment_lengths': [len(seg) for seg in segments],
                'selected_segment_rank': 1  # 总是选择最长的
            }
        }
        
        return results
    
    def analyze_all_stocks(self, test_results_dir: str) -> Dict[str, Dict]:
        """分析所有股票"""
        test_dir = Path(test_results_dir)
        results = {}
        
        # 查找所有提取的JSON文件
        json_files = list(test_dir.glob("*_tra_inputs_extracted.json"))
        
        logger.info(f"找到 {len(json_files)} 个TRA数据文件")
        
        for json_file in json_files:
            # 提取股票代码
            stock_symbol = json_file.stem.replace('_tra_inputs_extracted', '')
            
            # 分析股票
            stock_results = self.analyze_stock(stock_symbol, str(json_file))
            if stock_results:
                results[stock_symbol] = stock_results
        
        return results
    
    def generate_summary_table(self, all_results: Dict[str, Dict]) -> pd.DataFrame:
        """生成汇总表"""
        summary_data = []
        
        for stock_symbol, results in all_results.items():
            if not results:
                continue
                
            summary_data.append({
                'Stock': stock_symbol,
                'Trading Days': results['analysis_period']['trading_days'],
                'Date Range': f"{results['analysis_period']['start_date']} ~ {results['analysis_period']['end_date']}",
                'Total Return (%)': f"{results['returns']['total_return'] * 100:.2f}",
                'Annualized Return (%)': f"{results['returns']['annualized_return'] * 100:.2f}",
                'Annualized Volatility (%)': f"{results['risk_metrics']['annualized_volatility'] * 100:.2f}",
                'Annualized Sharpe Ratio': f"{results['risk_metrics']['annualized_sharpe_ratio']:.3f}",
                'Maximum Drawdown (%)': f"{results['risk_metrics']['maximum_drawdown'] * 100:.2f}",
                'Positive Days (%)': f"{results['daily_statistics']['positive_days_ratio'] * 100:.1f}",
                'Segments Found': results['data_quality']['total_segments_found']
            })
        
        return pd.DataFrame(summary_data)


def main():
    """主函数"""
    test_results_dir = "/Users/<USER>/Code/Multi_Agent_Optimization/test_results"
    output_dir = f"{test_results_dir}/financial_metrics_from_json"
    
    # 创建输出目录
    Path(output_dir).mkdir(exist_ok=True)
    
    logger.info("开始基于JSON文件计算金融指标")
    logger.info("=" * 80)
    
    # 初始化计算器
    calculator = FinancialMetricsCalculator(risk_free_rate=0.02)
    
    # 分析所有股票
    all_results = calculator.analyze_all_stocks(test_results_dir)
    
    if not all_results:
        logger.error("没有成功分析任何股票")
        return
    
    logger.info(f"成功分析了 {len(all_results)} 只股票")
    
    # 保存详细结果
    detailed_results_path = f"{output_dir}/detailed_metrics.json"
    with open(detailed_results_path, 'w', encoding='utf-8') as f:
        json.dump(all_results, f, indent=2, ensure_ascii=False)
    
    # 生成汇总表
    summary_df = calculator.generate_summary_table(all_results)
    summary_csv_path = f"{output_dir}/financial_metrics_summary.csv"
    summary_df.to_csv(summary_csv_path, index=False)
    
    # 输出结果
    print("\n" + "=" * 100)
    print("金融指标计算结果汇总")
    print("=" * 100)
    print(summary_df.to_string(index=False))
    print("\n" + "=" * 100)
    
    # 输出详细统计
    for stock_symbol, results in all_results.items():
        print(f"\n{stock_symbol} 详细信息:")
        print(f"  交易期间: {results['analysis_period']['start_date']} ~ {results['analysis_period']['end_date']}")
        print(f"  交易天数: {results['analysis_period']['trading_days']}")
        print(f"  总收益率: {results['returns']['total_return']*100:.2f}%")
        print(f"  年化收益率: {results['returns']['annualized_return']*100:.2f}%")
        print(f"  年化波动率: {results['risk_metrics']['annualized_volatility']*100:.2f}%")
        print(f"  年化夏普率: {results['risk_metrics']['annualized_sharpe_ratio']:.3f}")
        print(f"  最大回撤: {results['risk_metrics']['maximum_drawdown']*100:.2f}%")
        print(f"  数据段数: {results['data_quality']['total_segments_found']}")
    
    print(f"\n详细结果已保存到: {detailed_results_path}")
    print(f"汇总表已保存到: {summary_csv_path}")
    print("=" * 100)


if __name__ == "__main__":
    main()